import logging
import sys
sys.path
from minio import Minio
from minio.error import S3Error

logging.basicConfig(
    level=logging.INFO,
    filename='../notfindfeatureid.txt',
    filemode='a',
    format='%(asctime)s %(name)s %(levelname)s--%(message)s'
)

file_name = "/Users/<USER>/Downloads/notfindfeatureid.txt"


def upload_file(minioHost,minioBucket,minioObjectName,filePath,accessKey,secretKey):
    minioClient = Minio(
        minioHost,
        access_key=accessKey,
        secret_key=secretKey,
        secure=False
    )

    check_bucket = minioClient.bucket_exists(minioBucket)

    if not check_bucket:
        minioClient.make_bucket(minioBucket)
    try:
        logging.info("start upload file")
        minioClient.fput_object(bucket_name=minioBucket, object_name=minioObjectName,
                                file_path=filePath)
        logging.info("file {0} is successfully uploaded".format(filePath))
    except FileNotFoundError as err:
        logging.error('upload_failed: '+ str(err))
    except S3Error as err:
        logging.error("upload_failed:", err)


if __name__ == '__main__':
    if len(sys.argv) > 6:
        minioHost = sys.argv[1]
        minioBucket = sys.argv[2]
        minioObjectName = sys.argv[3]
        filePath =sys.argv[4]
        accessKey = sys.argv[5]
        secretKey = sys.argv[6] 
        upload_file(minioHost,minioBucket,minioObjectName,filePath,accessKey,secretKey)
    else:
        print ('args are less than 7,please run as python minio_upload.py minioHost(**************:31112) minioBucket(idata) minioObjectName(here/output/q2/notfindfeatureid.txt) filePath(/Users/<USER>/Downloads/notfindfeatureid.txt) accessKey secretKey')