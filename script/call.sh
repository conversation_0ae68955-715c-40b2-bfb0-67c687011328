#!/bin/sh

#Caution: must config the following options.
BASE_TXT_FILE_PATH=""
POI_XML_FILE_PATH=""
IS_COMPILE_TRANS="true"
CREATE_FLAG="true"
PORT="10093"

LINK_NODE_URL="localhost:$PORT/api/road/herelink/convert -X POST -d 'step=10000&rdfcflinkfilepath=$BASE_TXT_FILE_PATH/rdf_cf_link.txt&rdfcffilepath=$BASE_TXT_FILE_PATH/rdf_cf.txt&iscompilenode=true&rdfcfnodefilepath=$BASE_TXT_FILE_PATH/rdf_cf_node.txt&iscompiletranseng=$IS_COMPILE_TRANS&country=$2&area=$3'"
RELATION_URL="localhost:$PORT/api/road/herelinkreleation/convert -X POST -d 'step=10000&country=$2&area=$3'"
RULE_URL="localhost:$PORT/api/road/herelinkrule/convert -X POST -d 'step=10000&country=$2&area=$3'"
LEFT_RULE_URL="localhost:$PORT/api/road/herelink/leftruleconvert -X POST -d 'step=1900&filepath=$BASE_TXT_FILE_PATH/rdf_condition_divider.txt&country=$2&area=$3'"
UPDATE_NODE_URL="localhost:$PORT/api/road/herelink/updatemainsubnode -X POST -d 'rdfcflinkfilepath=$BASE_TXT_FILE_PATH/rdf_cf_link.txt&rdfcffilepath=$BASE_TXT_FILE_PATH/rdf_cf.txt&rdfcfnodefilepath=$BASE_TXT_FILE_PATH/rdf_cf_node.txt&country=$2&area=$3'"
LINK_RP_URL="-X GET localhost:$PORT/api/road/herelink/convert2rp?step=10000&country=$2&area=$3"
NODE_RP_URL="-X GET localhost:$PORT/common/nodeSw2021q133/convert2rp?step=10000&country=$2&area=$3"
POINT_ADDR_URL="localhost:$PORT/api/road/pointaddress/convert -X POST -d 'step=10000&version=22q1&isCompileTrans=$IS_COMPILE_TRANS&country=$2&area=$3'"
POI_URL="-X POST localhost:$PORT/api/poi/herepoi/import -d 'filePath=$POI_XML_FILE_PATH&create=$CREATE_FLAG&country=$2&area=$3'"

TEST_URL="-X GET http://localhost:8080/getTeacherInfo/1"

#当前所在目录
PROJECT_PATH=$(
  cd $(dirname $0)
  pwd
)

COMPILE_LINK_NODE_RES_LOCATION=0
getResponseOfLinkNodeCompile() {
  cd $PROJECT_PATH
  COMPILE_LINK_NODE_RES_LOCATION=`grep -c 'here streets convert finished,country is '$COUNTRY',area is '$AREA system.log `
}

UPDATE_NODE_RES_LOCATION=0
getResponseOfUpdateNode() {
  cd $PROJECT_PATH
  UPDATE_NODE_RES_LOCATION=`grep -c 'update main sub node finished,country is '$COUNTRY',area is '$AREA system.log`
}

COMPILE_RULE_RES_LOCATION=0
getResponseOfRuleCompile() {
  cd $PROJECT_PATH
  COMPILE_RULE_RES_LOCATION=`grep -c 'rule convert finished,country is '$COUNTRY',area is '$AREA system.log`
}
COUNTRY=$2
AREA=$3
#不分区编译方法调用
singleRegion() {

  if [ ! $BASE_TXT_FILE_PATH ]; then
      echo "base txt file path can not be null..."
      return
  fi
  if [ ! $POI_XML_FILE_PATH ]; then
      echo "poi xml file path can not be null..."
      return
  fi


  curl $LINK_NODE_URL
  curl $RELATION_URL
  curl $RULE_URL
  curl $POINT_ADDR_URL
  curl $POI_URL

  while [ $COMPILE_LINK_NODE_RES_LOCATION == 0 ]; do
    getResponseOfLinkNodeCompile
  done

  curl $UPDATE_NODE_URL

  while [ $UPDATE_NODE_RES_LOCATION == 0 ]; do
    getResponseOfUpdateNode
  done

  curl $LINK_RP_URL
  curl $NODE_RP_URL

  while [ $COMPILE_RULE_RES_LOCATION == 0 ]; do
    getResponseOfRuleCompile
  done

  curl $LEFT_RULE_URL

  cd $PROJECT_PATH
  echo "the all curl has been called in order.country is $COUNTRY,area is $AREA" >> system.log
}

test1(){
#  echo $LINK_NODE_URL
  curl $TEST_URL
}

#分区编译方法调用
multiRegion() {

  if [ ! $BASE_TXT_FILE_PATH ]; then
      echo "base txt file path can not be null..."
      return
  fi
  if [ ! $POI_XML_FILE_PATH ]; then
      echo "poi xml file path can not be null..."
      return
  fi

  curl $LINK_NODE_URL
  curl $RELATION_URL
  curl $RULE_URL
  curl $POINT_ADDR_URL
  curl $POI_URL

  while [ $COMPILE_LINK_NODE_RES_LOCATION == 0 ]; do
    getResponseOfLinkNodeCompile
  done

  curl $UPDATE_NODE_URL

  while [ $UPDATE_NODE_RES_LOCATION == 0 ]; do
    getResponseOfUpdateNode
  done

  curl $LINK_RP_URL
  curl $NODE_RP_URL

  while [ $COMPILE_RULE_RES_LOCATION == 0 ]; do
    getResponseOfRuleCompile
  done

  curl $LEFT_RULE_URL

  cd $PROJECT_PATH
  echo "the all curl has been called in order.country is $COUNTRY,area is $AREA" >> system.log
}

command=$1
if [ "$command" == "single" ]; then
  singleRegion
fi

if [ "$command" == "multi" ]; then
  multiRegion
fi

if [ "$command" == "test" ]; then
  test1
fi
