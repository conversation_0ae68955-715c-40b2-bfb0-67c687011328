import sys
import os
import time

areaappend =''
database =''
layerList=[]
path = ''
host = 'localhost'
port = '15999'
if len(sys.argv) > 4:
    area = sys.argv[1]
    if area == '0':
        areaappend = ''
    else:
        areaappend = '_area'+area
    database = sys.argv[2]
    lancode = sys.argv[3]
    layers = sys.argv[4]
    if sys.argv[5] != '':
        host = sys.argv[5]
    if sys.argv[6] != '':
        port = sys.argv[6]
    if sys.argv[7] != '':
        path = sys.argv[7]
    if layers == 'all':
        layerList = ['Streets','Zlevels','AltStreets','StreetTrans','Cdms','CdmsDtmod','CndMod','Rdms','MtdArea','MtdRef','PointAddress','PntAddrTrans','Adminbndy1','Adminbndy2','Adminbndy3','Adminbndy4','Adminbndy5','RailRds','WaterSeg','Islands','LandUseA','LandUseB','WaterPoly','Oceans','NamedPlc','Hamlet','Landmark','AdminLine1','AdminLine2']
    elif layers == 'line':
        layerList = ['Streets','Zlevels','AltStreets','StreetTrans','Cdms','CdmsDtmod','CndMod','Rdms']
    elif layers == 'poionly':
        layerList = ['Streets','MtdArea','MtdRef']
    elif layers == 'poiadd':
        layerList = ['MtdArea','MtdRef']
    elif layers == 'basemap':
        layerList = ['RailRds','WaterSeg','Islands','LandUseA','LandUseB','WaterPoly','Oceans','NamedPlc','Hamlet','Landmark','AdminLine1','AdminLine2']
    elif layers == 'tool':
        layerList = ['MtdArea','Adminbndy1','Adminbndy2','Adminbndy3','Adminbndy4','Adminbndy5']
    elif layers == 'rdf':
        layerList = ['rdf_nav_link','rdf_condition_divider','rdf_cf','rdf_cf_link','rdf_cf_node']
    else:
        layerList = layers.split(',')
    for layer in layerList:
        print ('start import '+ layer + '.shp')
        begin_time = time.time()
        if layer in ['rdf_nav_link','rdf_condition_divider','rdf_cf','rdf_cf_link','rdf_cf_node']:
#             command = (
#                 f"psql -h *************** -U postgres -d {database} -p {port} -c "
#                 f"\"\\copy {layer} FROM '{layer}.txt' DELIMITER E'\\t'\""
#             )
#             os.system(command)
#            psql -h *************** -p 5432 -U postgres -d here_hkg_q4 -c "\copy rdf_condition_divider FROM 'rdf_condition_divider.txt' DELIMITER E'\t'"
#           note:rdf need to create table first
            os.system('psql -h '+ host + ' -U postgres -d ' + database + ' -p ' + port + ' -c \"\\copy ' + layer + ' FROM \'' + path + layer + '.txt\'' + ' DELIMITER E\'\\t\'\"')
        else:
            os.system('shp2pgsql -s 4326 -W \"'+lancode+'\" '+ path + layer + '.shp public.'+layer.lower()+areaappend+' | psql -h '+ host +' -U postgres -d ' + database + ' -p ' + port)
        end_time = time.time()
        print ('finish import '+ layer + '.shp,time is(s):' ,end_time-begin_time)
else:
    print ('args are less than 5,please run as python import.py areanum(0-9) database lancode(LATIN1/utf-8) layers(\'Streets,Zlevels,AltStreets,StreetTrans,Cdms,CdmsDtmod,CndMod,Rdms,MtdArea,MtdRef\')')
