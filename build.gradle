/*
 * This file was generated by the Gradle 'init' task.
 *
 * This generated file contains a sample Java project to get you started.
 * For more details take a look at the Java Quickstart chapter in the Gradle
 * User Manual available at https://docs.gradle.org/6.5/userguide/tutorial_java_projects.html
 */

plugins {
	id 'org.springframework.boot' version '2.3.3.RELEASE' apply false
	id 'java'
}

ext {
	set('springBootVersion', '2.3.3.RELEASE')
	set('springCloudVersion', 'Hoxton.SR7')
	set('mybatisPlusVersion', '3.3.2')
	set('swaggerVersion', '2.9.2')
	set('swaggerAnnotationVersion', '1.5.20')
	set('lombokVersion', '1.18.12')
}


allprojects {
	task printInfo {
		doLast { task ->
			println "${task.project.group}:${task.project.name}:${task.project.version}"
		}
	}
	repositories {
		maven { url 'https://maven.aliyun.com/repository/public/' }
		maven { url 'https://maven.aliyun.com/repository/spring/' }
		maven { url 'https://maven.aliyun.com/repository/gradle-plugin/' }
		mavenLocal()
		mavenCentral()
		jcenter()
	}
}

subprojects {
	apply plugin: 'java'
	apply plugin: "io.spring.dependency-management"

	group = 'com.hll'
	version = '0.0.1-SNAPSHOT'
	sourceCompatibility = '8'

	dependencies {
		implementation platform("org.springframework.boot:spring-boot-dependencies:$springBootVersion")
		implementation platform("org.springframework.cloud:spring-cloud-dependencies:$springCloudVersion")
		implementation 'com.google.guava:guava:29.0-jre'
		// https://mvnrepository.com/artifact/com.squareup.okhttp3/okhttp
		implementation group: 'com.squareup.okhttp3', name: 'okhttp', version: '4.9.1'
		// https://mvnrepository.com/artifact/com.alibaba/easyexcel
		implementation group: 'com.alibaba', name: 'easyexcel', version: '3.0.5'
		// https://mvnrepository.com/artifact/cn.smallbun.screw/screw-core
		implementation group: 'cn.smallbun.screw', name: 'screw-core', version: '1.0.5'
		// https://mvnrepository.com/artifact/org.locationtech.jts/jts-core
		implementation("org.locationtech.jts:jts-core:1.19.0")
		// https://mvnrepository.com/artifact/com.uber/h3
		implementation("com.uber:h3:4.1.1")
		constraints {
			implementation "com.baomidou:mybatis-plus-boot-starter:$mybatisPlusVersion"
			implementation "com.baomidou:mybatis-plus-core:$mybatisPlusVersion"
			implementation "io.springfox:springfox-swagger2:$swaggerVersion"
			implementation "io.springfox:springfox-swagger-ui:$swaggerVersion"
			implementation "io.swagger:swagger-annotations:$swaggerAnnotationVersion"

			//poi
			implementation 'org.apache.poi:poi:4.1.2'
			implementation 'org.apache.poi:poi-ooxml:4.1.2'

			//utils
			compile 'org.projectlombok:lombok:1.18.12'
			annotationProcessor 'org.projectlombok:lombok:1.18.12'

			testCompile 'org.projectlombok:lombok:1.18.12'
			testAnnotationProcessor 'org.projectlombok:lombok:1.18.12'

			// fastjson
			compile 'com.alibaba:fastjson:1.2.83'

			compile 'io.jsonwebtoken:jjwt-api:0.11.2'
			runtime 'io.jsonwebtoken:jjwt-impl:0.11.2',
					// Uncomment the next line if you want to use RSASSA-PSS (PS256, PS384, PS512) algorithms:
					//'org.bouncycastle:bcprov-jdk15on:1.60',
					'io.jsonwebtoken:jjwt-jackson:0.11.2' // or 'io.jsonwebtoken:jjwt-gson:0.11.2' for gson

			compile group: 'org.apache.commons', name: 'commons-lang3', version: '3.11'
			implementation 'mysql:mysql-connector-java:5.1.49'
			compile group: 'com.zaxxer', name: 'HikariCP', version: '3.4.1'
			// https://mvnrepository.com/artifact/com.github.albfernandez/javadbf
			implementation group: 'com.github.albfernandez', name: 'javadbf', version: '1.13.1'
			// https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-configuration-processor
			implementation group: 'org.springframework.boot', name: 'spring-boot-configuration-processor', version: '2.5.3'
		}
	}

	configurations.all {
		resolutionStrategy {
			// force certain versions of dependencies (including transitive)
			//  *append new forced modules:
			force 'mysql:mysql-connector-java:5.1.49'
			//  *replace existing forced modules with new ones:
		}
	}
}

def genieVersion = 'v1.3.x'

task imagePrepare(type: Copy) {
	from 'business/Dockerfile'
	into 'business/build/libs'
}

task imageBuild(type: Exec) {
	workingDir 'business/build/libs'
	def commandArgs = ['docker', 'build', '-t', "oversea-compile-service:${genieVersion}", '.']
	commandLine commandArgs
}
imageBuild.dependsOn(imagePrepare)
// dev env
task tagImageForDev(type: Exec) {
	commandLine 'docker', 'tag', "oversea-compile-service:${genieVersion}", "${registryDev}/oversea-compile-service:${genieVersion}"
}

task pushImageForDev(type: Exec) {
	commandLine 'docker', 'push', "${registryDev}/oversea-compile-service:${genieVersion}"
}

task deployForDev(type:Exec) {
	println "kubectl set image deployment/oversea-compile-service --context=hll-dev -n work oversea=${registryDev}/oversea-compile-service:${genieVersion}"
	commandLine "sh","-c", "kubectl set image deployment/oversea-compile-service --context=hll-dev -n work oversea=${registryDev}/oversea-compile-service:${genieVersion}"
}
//prd env
task tagImageForPrd(type: Exec) {
	commandLine 'docker', 'tag', "oversea-compile-service:${genieVersion}", "${registryPrd}/oversea-compile-service:${genieVersion}"
}

task pushImageForPrd(type: Exec) {
	commandLine 'docker', 'push', "${registryPrd}/oversea-compile-service:${genieVersion}"
}
