plugins {
    id 'java'
}

group 'com.hll'
version '0.0.1-SNAPSHOT'
repositories {
    mavenCentral()
}

dependencies {

//    implementation 'org.projectlombok:lombok:1.18.16'
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.6.0'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine'
    implementation group: 'com.baomidou', name: 'mybatis-plus', version: '3.4.2'
    implementation group: 'com.baomidou', name: 'mybatis-plus-boot-starter', version: '3.4.2'
    implementation group: 'com.baomidou', name: 'mybatis-plus-extension', version: '3.4.2'
    compile 'org.postgresql:postgresql:42.2.19'
//    compileOnly group: 'org.projectlombok', name: 'lombok', version: '1.18.18'
    annotationProcessor("org.projectlombok:lombok:1.18.24")
    compileOnly("org.projectlombok:lombok:1.18.24")
    implementation group: 'cn.hutool', name: 'hutool-all', version: '5.5.9'
    implementation group: 'com.spring4all', name: 'swagger-spring-boot-starter', version: '1.9.1.RELEASE'
    // https://mvnrepository.com/artifact/org.projectlombok/lombok
//    compileOnly group: 'org.projectlombok', name: 'lombok', version: '1.18.18'
    implementation group: 'com.baomidou', name: 'dynamic-datasource-spring-boot-starter', version: '3.3.1'
    implementation group: 'org.postgis', name: 'postgis-jdbc', version: '1.3.3'
    // https://mvnrepository.com/artifact/com.vividsolutions/jts
    implementation group: 'com.vividsolutions', name: 'jts', version: '1.13'
    // https://mvnrepository.com/artifact/org.apache.poi/poi
    //implementation group: 'org.apache.poi', name: 'poi', version: '5.0.0'

    // https://mvnrepository.com/artifact/com.alibaba/fastjson
    implementation group: 'com.alibaba', name: 'fastjson', version: '1.2.83'

    // https://mvnrepository.com/artifact/org.json/json
    implementation group: 'org.json', name: 'json', version: '20210307'

    // https://mvnrepository.com/artifact/net.postgis/postgis-jdbc
    implementation group: 'net.postgis', name: 'postgis-jdbc', version: '2.5.0'

    // https://mvnrepository.com/artifact/com.github.albfernandez/javadbf
    implementation group: 'com.github.albfernandez', name: 'javadbf', version: '1.13.1'
    // https://mvnrepository.com/artifact/com.carrotsearch/hppc
    implementation group: 'com.carrotsearch', name: 'hppc', version: '0.9.1'
    // https://mvnrepository.com/artifact/com.graphhopper/graphhopper-core
    implementation group: 'com.graphhopper', name: 'graphhopper-core', version: '4.0'
    // https://mvnrepository.com/artifact/ch.ethz.ganymed/ganymed-ssh2
    implementation group: 'ch.ethz.ganymed', name: 'ganymed-ssh2', version: '262'
    // https://mvnrepository.com/artifact/io.minio/minio
    implementation group: 'io.minio', name: 'minio', version: '8.3.9'
    // https://mvnrepository.com/artifact/org.springframework/org.springframework.web
    implementation group: 'org.springframework', name: 'org.springframework.web', version: '3.2.2.RELEASE'
    // https://mvnrepository.com/artifact/com.drewnoakes/metadata-extractor
    implementation group: 'com.drewnoakes', name: 'metadata-extractor', version: '2.18.0'




}

test {
    useJUnitPlatform()
}