/**
  * Copyright 2021 bejson.com 
  */
package com.hll.mapdataservice.common.utils.po.hereplacenamepo;

/**
 * Auto-generated: 2021-04-02 11:27:34
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class BaseText {

    private String languageCode;
    private String type;
    private String content;


    private String languageType;

    public String getLanguageType() {
        return languageType;
    }

    public void setLanguageType(String languageType) {
        this.languageType = languageType;
    }

    public String getShortText() {
        return shortText;
    }

    public void setShortText(String shortText) {
        this.shortText = shortText;
    }

    private String shortText;
    public void setLanguageCode(String languageCode) {
         this.languageCode = languageCode;
     }

     public String getLanguageCode() {
         return languageCode;
     }

    public void setType(String type) {
         this.type = type;
     }
     public String getType() {
         return type;
     }

    public void setContent(String content) {
         this.content = content;
     }
     public String getContent() {
         return content;
     }

}