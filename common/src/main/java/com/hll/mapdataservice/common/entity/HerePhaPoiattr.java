package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-25
 */
@ApiModel(value="HerePhaPoiattr对象", description="")
public class HerePhaPoiattr extends Model<HerePhaPoiattr> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "gid", type = IdType.AUTO)
    private String gid;

    private Integer poiId;

    private String facType;

    private String attrType;

    private String attrValue;

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }
    public Integer getPoiId() {
        return poiId;
    }

    public void setPoiId(Integer poiId) {
        this.poiId = poiId;
    }
    public String getFacType() {
        return facType;
    }

    public void setFacType(String facType) {
        this.facType = facType;
    }
    public String getAttrType() {
        return attrType;
    }

    public void setAttrType(String attrType) {
        this.attrType = attrType;
    }
    public String getAttrValue() {
        return attrValue;
    }

    public void setAttrValue(String attrValue) {
        this.attrValue = attrValue;
    }

    @Override
    protected Serializable pkVal() {
        return this.gid;
    }

    @Override
    public String toString() {
        return "HerePhaPoiattr{" +
            "gid=" + gid +
            ", poiId=" + poiId +
            ", facType=" + facType +
            ", attrType=" + attrType +
            ", attrValue=" + attrValue +
        "}";
    }
}
