<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.PoiMatchResMapper">
  <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.PoiMatchRes">
    <!--@mbg.generated-->
    <!--@Table poi_match_res-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="poi_geo" jdbcType="OTHER" property="poiGeo" />
    <result column="latitude" jdbcType="NUMERIC" property="latitude" />
    <result column="longitude" jdbcType="NUMERIC" property="longitude" />
    <result column="google_poi_name" jdbcType="VARCHAR" property="googlePoiName" />
    <result column="here_poi_name" jdbcType="VARCHAR" property="herePoiName" />
    <result column="similarity" jdbcType="NUMERIC" property="similarity" />
    <result column="is_match" jdbcType="INTEGER" property="isMatch" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, poi_geo, latitude, longitude, google_poi_name, here_poi_name, similarity, is_match, 
    version, create_time, update_time
  </sql>

</mapper>