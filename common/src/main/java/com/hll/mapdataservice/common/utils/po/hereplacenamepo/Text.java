/**
  * Copyright 2021 bejson.com 
  */
package com.hll.mapdataservice.common.utils.po.hereplacenamepo;

/**
 * Auto-generated: 2021-04-02 11:27:34
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class Text {

    private String languageCode;
    private String content;

    public String getText() {
        return Text;
    }

    public void setText(String text) {
        Text = text;
    }

    private String Text;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    private String type;

    public BaseText getBaseText() {
        return baseText;
    }

    public void setBaseText(BaseText baseText) {
        this.baseText = baseText;
    }

    private BaseText baseText;

    public void setLanguageCode(String languageCode) {
         this.languageCode = languageCode;
     }
     public String getLanguageCode() {
         return languageCode;
     }

    public void setContent(String content) {
         this.content = content;
     }
     public String getContent() {
         return content;
     }

}