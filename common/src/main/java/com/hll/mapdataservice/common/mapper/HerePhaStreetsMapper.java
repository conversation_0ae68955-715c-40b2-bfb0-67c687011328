package com.hll.mapdataservice.common.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.hll.mapdataservice.common.entity.HerePhaStreets;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hll.mapdataservice.common.vo.LinkMainVo;
import com.hll.mapdataservice.common.vo.ManeuverVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-12
 */
//@DS("db5")
public interface HerePhaStreetsMapper extends BaseMapper<HerePhaStreets> {
    @Select("select link_id, cond_id as id, cond_type as feat_type from cdms ${ew.customSqlSegment}")
    List<ManeuverVo> getHereManeuver(@Param(Constants.WRAPPER) Wrapper wrapper);

    @Select("select r.man_linkid as feat_id, r.seq_number as maneuver_seq, st_astext(h.geometry) as geom from rdms r " +
            "LEFT JOIN here_pha_streets h on r.man_linkid=h.link_id " +
            "${ew.customSqlSegment}  order by r.seq_number")
    List<LinkMainVo> getHereManeuverLink(@Param(Constants.WRAPPER) Wrapper wrapper);
}