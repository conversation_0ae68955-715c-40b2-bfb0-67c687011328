/**
  * Copyright 2021 bejson.com 
  */
package com.hll.mapdataservice.common.utils.po.hereplacenamepo;

import java.util.List;

/**
 * Auto-generated: 2021-04-02 11:27:34
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class Name {

    private boolean primaryFlag;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    private String content;

//    public Text getText() {
//        return text;
//    }
//
//    public void setText(Text text) {
//        this.text = text;
//    }

//    private Text text;

    private List<Text> Text;
    public void setText(List<Text> Text) {
        this.Text = Text;
    }
    public List<Text> getText() {
        return Text;
    }
    private TextList TextList;
    public void setPrimaryFlag(boolean primaryFlag) {
         this.primaryFlag = primaryFlag;
     }
     public boolean getPrimaryFlag() {
         return primaryFlag;
     }

    public void setTextList(TextList TextList) {
         this.TextList = TextList;
     }
     public TextList getTextList() {
         return TextList;
     }

}