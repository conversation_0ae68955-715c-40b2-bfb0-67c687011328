package com.hll.mapdataservice.common;

import cn.hutool.core.collection.CollUtil;

import java.util.List;

public enum CountryAreaEnum {
    BRA("bra", CollUtil.newArrayList("area1","area2", "area3", "area4","area5")),
    THA("tha", CollUtil.newArrayList("area1","area2", "area3", "area4","area5")),
    MEX("mex", CollUtil.newArrayList("area1","area2", "area3", "area4","area5","area6","area7", "area8", "area9","area10")),
    TWN("twn", CollUtil.newArrayList("area1","area2", "area3")),
    VNM("vnm", CollUtil.newArrayList("area1","area2", "area3")),
    INDIA("india", CollUtil.newArrayList("area1","area2", "area3", "area4","area5","area6","area7", "area8", "area9","area10","area11", "area12", "area13","area14","area15","area16", "area17", "area18","area19","area20","area21")),
    PHL("phl", CollUtil.newArrayList()),
    HKG("hkg", CollUtil.newArrayList()),
    SGP("sgp", CollUtil.newArrayList()),
    MYS("mys", CollUtil.newArrayList()),
    BAN("ban", CollUtil.newArrayList()),
    IDN("idn", CollUtil.newArrayList("area1","area2", "area3", "area4","area5","area6")),
    TUR("tur", CollUtil.newArrayList("area1","area2", "area3","area4","area5","area6"));
    private String country;
    private List<String> areas;

    CountryAreaEnum(String country, List<String> areas) {
        this.country = country;
        this.areas = areas;
    }

    public String getCountry() {
        return country;
    }

    public List<String> getAreas() {
        return areas;
    }

    public static List<String> getAreaByCountry(String country) {
        if (country == null) {
            return null;
        }
        CountryAreaEnum[] values = CountryAreaEnum.values();
        for (CountryAreaEnum countryAreaEnum : values) {
            if (country.equals(countryAreaEnum.getCountry())) {
                return countryAreaEnum.getAreas();
            }
        }
        return null;
    }
}

