package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-09
 */
@ApiModel(value="HerePhaPlaces对象", description="")
public class HerePhaPlaces extends Model<HerePhaPlaces> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    private String placeid;

    private String placename;

    private String placelocationdisplay;

    private String placelocationdrive;

    private String placeaddress;

    private LocalDateTime createtime;

    private String category;

    @TableField("ST_AsText(geometry)")
    private String geometry;

    public String getId() {
        return id;
    }

    public String getGeometry() {
        return geometry;
    }

    public void setGeometry(String geometry) {
        this.geometry = geometry;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getPlaceid() {
        return placeid;
    }

    public void setPlaceid(String placeid) {
        this.placeid = placeid;
    }
    public String getPlacename() {
        return placename;
    }

    public void setPlacename(String placename) {
        this.placename = placename;
    }
    public String getPlacelocationdisplay() {
        return placelocationdisplay;
    }

    public void setPlacelocationdisplay(String placelocationdisplay) {
        this.placelocationdisplay = placelocationdisplay;
    }
    public String getPlacelocationdrive() {
        return placelocationdrive;
    }

    public void setPlacelocationdrive(String placelocationdrive) {
        this.placelocationdrive = placelocationdrive;
    }
    public String getPlaceaddress() {
        return placeaddress;
    }

    public void setPlaceaddress(String placeaddress) {
        this.placeaddress = placeaddress;
    }
    public LocalDateTime getCreatetime() {
        return createtime;
    }

    public void setCreatetime(LocalDateTime createtime) {
        this.createtime = createtime;
    }
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "HerePhaPlaces{" +
            "id=" + id +
            ", placeid=" + placeid +
            ", placename=" + placename +
            ", placelocationdisplay=" + placelocationdisplay +
            ", placelocationdrive=" + placelocationdrive +
            ", placeaddress=" + placeaddress +
            ", createtime=" + createtime +
            ", category=" + category +
        "}";
    }
}
