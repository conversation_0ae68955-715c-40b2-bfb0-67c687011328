package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;

public class Restriction extends Model<Restriction> {
    @TableField(exist = false)
    private String id;

    private String groupId;

    private String market;

    private String city;

    private String cityId;

    private String linkId;

    private String roadNameEn;

    private String roadNameMultiLang;

    private String geom;

    private String restrictedOrderVehicleId;

    private String restrictedInfo;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getLinkId() {
        return linkId;
    }

    public void setLinkId(String linkId) {
        this.linkId = linkId;
    }

    public String getRoadNameEn() {
        return roadNameEn;
    }

    public void setRoadNameEn(String roadNameEn) {
        this.roadNameEn = roadNameEn;
    }

    public String getRoadNameMultiLang() {
        return roadNameMultiLang;
    }

    public void setRoadNameMultiLang(String roadNameMultiLang) {
        this.roadNameMultiLang = roadNameMultiLang;
    }

    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }

    public String getRestrictedOrderVehicleId() {
        return restrictedOrderVehicleId;
    }

    public void setRestrictedOrderVehicleId(String restrictedOrderVehicleId) {
        this.restrictedOrderVehicleId = restrictedOrderVehicleId;
    }


    public String getRestrictedInfo() {
        return restrictedInfo;
    }

    public void setRestrictedInfo(String restrictedInfo) {
        this.restrictedInfo = restrictedInfo;
    }
}