package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Classname PointAddress
 * @Description here门址
 * @Date 2021/12/9 2:17 下午
 * @Created by qunfu
 */
@ApiModel(value = "PointAddress")
@TableName("pointaddress")
public class PointAddress  extends Model<PointAddress> {

    @ApiModelProperty(value = "")
    @TableId(value = "gid")
    private Integer gid;

    @ApiModelProperty(value = "")
    private Integer linkId;

    @ApiModelProperty(value = "")
    private Integer ptAddrId;

    @ApiModelProperty(value = "")
    private String side;

    @ApiModelProperty(value = "")
    private Integer featureId;

    @ApiModelProperty(value = "")
    private String paLangcd;

    @ApiModelProperty(value = "")
    private String address;

    @ApiModelProperty(value = "")
    private String addrType;

    @ApiModelProperty(value = "")
    private String dispLon;

    @ApiModelProperty(value = "")
    private String dispLat;

    @ApiModelProperty(value = "")
    private String bldgNm;

    @ApiModelProperty(value = "")
    private String arLinkId;

    @ApiModelProperty(value = "")
    private String arSide;

    @ApiModelProperty(value = "")
    private String enhanced;

    @ApiModelProperty(value = "")
    @TableField(value = "ST_AsText(geom)")
    private String geom;

    public Integer getGid() {
        return gid;
    }

    public void setGid(Integer gid) {
        this.gid = gid;
    }

    public Integer getLinkId() {
        return linkId;
    }

    public void setLinkId(Integer linkId) {
        this.linkId = linkId;
    }

    public Integer getPtAddrId() {
        return ptAddrId;
    }

    public void setPtAddrId(Integer ptAddrId) {
        this.ptAddrId = ptAddrId;
    }

    public String getSide() {
        return side;
    }

    public void setSide(String side) {
        this.side = side;
    }

    public Integer getFeatureId() {
        return featureId;
    }

    public void setFeatureId(Integer featureId) {
        this.featureId = featureId;
    }

    public String getPaLangcd() {
        return paLangcd;
    }

    public void setPaLangcd(String paLangcd) {
        this.paLangcd = paLangcd;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddrType() {
        return addrType;
    }

    public void setAddrType(String addrType) {
        this.addrType = addrType;
    }

    public String getDiapLon() {
        return dispLon;
    }

    public void setDiapLon(String diapLon) {
        this.dispLon = diapLon;
    }

    public String getDispLat() {
        return dispLat;
    }

    public void setDispLat(String dispLat) {
        this.dispLat = dispLat;
    }

    public String getBldgNm() {
        return bldgNm;
    }

    public void setBldgNm(String bldgNm) {
        this.bldgNm = bldgNm;
    }

    public String getArLinkId() {
        return arLinkId;
    }

    public void setArLinkId(String arLinkId) {
        this.arLinkId = arLinkId;
    }

    public String getArSide() {
        return arSide;
    }

    public void setArSide(String arSide) {
        this.arSide = arSide;
    }

    public String getEnHanced() {
        return enhanced;
    }

    public void setEnHanced(String enHanced) {
        this.enhanced = enHanced;
    }

    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }

    public String getDispLon() {
        return dispLon;
    }

    public void setDispLon(String dispLon) {
        this.dispLon = dispLon;
    }

    public String getEnhanced() {
        return enhanced;
    }

    public void setEnhanced(String enhanced) {
        this.enhanced = enhanced;
    }

    @Override
    public String toString() {
        return "PointAddress{" +
                "gid=" + gid +
                ", linkId=" + linkId +
                ", ptAddrId=" + ptAddrId +
                ", side='" + side + '\'' +
                ", featureId=" + featureId +
                ", paLangcd='" + paLangcd + '\'' +
                ", address='" + address + '\'' +
                ", addrType='" + addrType + '\'' +
                ", dispLon='" + dispLon + '\'' +
                ", dispLat='" + dispLat + '\'' +
                ", bldgNm='" + bldgNm + '\'' +
                ", arLinkId='" + arLinkId + '\'' +
                ", arSide='" + arSide + '\'' +
                ", enhanced='" + enhanced + '\'' +
                ", geom='" + geom + '\'' +
                '}';
    }
}
