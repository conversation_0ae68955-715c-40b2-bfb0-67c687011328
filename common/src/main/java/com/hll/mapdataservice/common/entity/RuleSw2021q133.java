package com.hll.mapdataservice.common.entity;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-01
 */
@ApiModel(value="RuleSw2021q133对象", description="")
@TableName(value = "rule")
//@DS("db8")
public class RuleSw2021q133 extends Model<RuleSw2021q133> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "rule_id", type = IdType.INPUT)
    private String ruleId;

    public String getInlinkId() {
        return inlinkId;
    }

    public void setInlinkId(String inlinkId) {
        this.inlinkId = inlinkId;
    }

    private String inlinkId;

    private String nodeId;

    public String getOutlinkId() {
        return outlinkId;
    }

    public void setOutlinkId(String outlinkId) {
        this.outlinkId = outlinkId;
    }

    private String outlinkId;

    private String pass;

    private String pass2;

    private Integer flag;

    private String vperiod;

    private String vehclType;

    private String vpdir;

    private String meshList;

    private String memo;

    private String cp;

    private String datasource;

    private LocalDateTime upDate;

    private Integer status;

    private String linkAngle;
    public String getLinkAngle() {
        return linkAngle;
    }

    public void setLinkAngle(String linkAngle) {
        this.linkAngle = linkAngle;
    }
    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public String getPass() {
        return pass;
    }

    public void setPass(String pass) {
        this.pass = pass;
    }
    public String getPass2() {
        return pass2;
    }

    public void setPass2(String pass2) {
        this.pass2 = pass2;
    }
    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }
    public String getVperiod() {
        return vperiod;
    }

    public void setVperiod(String vperiod) {
        this.vperiod = vperiod;
    }
    public String getVehclType() {
        return vehclType;
    }

    public void setVehclType(String vehclType) {
        this.vehclType = vehclType;
    }
    public String getVpdir() {
        return vpdir;
    }

    public void setVpdir(String vpdir) {
        this.vpdir = vpdir;
    }
    public String getMeshList() {
        return meshList;
    }

    public void setMeshList(String meshList) {
        this.meshList = meshList;
    }
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }
    public String getCp() {
        return cp;
    }

    public void setCp(String cp) {
        this.cp = cp;
    }
    public String getDatasource() {
        return datasource;
    }

    public void setDatasource(String datasource) {
        this.datasource = datasource;
    }
    public LocalDateTime getUpDate() {
        return upDate;
    }

    public void setUpDate(LocalDateTime upDate) {
        this.upDate = upDate;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    protected Serializable pkVal() {
        return this.ruleId;
    }

    @Override
    public String toString() {
        return "RuleSw2021q133{" +
                "ruleId='" + ruleId + '\'' +
                ", inlinkId='" + inlinkId + '\'' +
                ", nodeId='" + nodeId + '\'' +
                ", outlinkId='" + outlinkId + '\'' +
                ", pass='" + pass + '\'' +
                ", pass2='" + pass2 + '\'' +
                ", flag=" + flag +
                ", vperiod='" + vperiod + '\'' +
                ", vehclType='" + vehclType + '\'' +
                ", vpdir='" + vpdir + '\'' +
                ", meshList='" + meshList + '\'' +
                ", memo='" + memo + '\'' +
                ", cp='" + cp + '\'' +
                ", datasource='" + datasource + '\'' +
                ", upDate=" + upDate +
                ", status=" + status +
                ", linkAngle='" + linkAngle + '\'' +
                '}';
    }
}
