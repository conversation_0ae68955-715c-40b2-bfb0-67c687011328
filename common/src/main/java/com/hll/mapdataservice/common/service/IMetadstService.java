package com.hll.mapdataservice.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hll.mapdataservice.common.entity.LinkM;
import com.hll.mapdataservice.common.entity.Metadst;
import com.hll.mapdataservice.common.entity.Mtddst;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface IMetadstService extends IService<Metadst> {
    Integer convert(String country, String area, Map<String, Mtddst> srcMap, List<LinkM> linkMList,int step);

}
