<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.HerePhaHospitalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.HerePhaHospital">
        <id column="gid" property="gid" />
        <result column="link_id" property="linkId" />
        <result column="poi_id" property="poiId" />
        <result column="seq_num" property="seqNum" />
        <result column="fac_type" property="facType" />
        <result column="poi_name" property="poiName" />
        <result column="poi_langcd" property="poiLangcd" />
        <result column="poi_nmtype" property="poiNmtype" />
        <result column="poi_st_num" property="poiStNum" />
        <result column="st_num_ful" property="stNumFul" />
        <result column="st_nful_lc" property="stNfulLc" />
        <result column="st_name" property="stName" />
        <result column="st_langcd" property="stLangcd" />
        <result column="poi_st_sd" property="poiStSd" />
        <result column="acc_type" property="accType" />
        <result column="ph_number" property="phNumber" />
        <result column="chain_id" property="chainId" />
        <result column="nat_import" property="natImport" />
        <result column="private" property="privateInfo" />
        <result column="in_vicin" property="inVicin" />
        <result column="num_parent" property="numParent" />
        <result column="num_child" property="numChild" />
        <result column="percfrref" property="percfrref" />
        <result column="vancity_id" property="vancityId" />
        <result column="act_addr" property="actAddr" />
        <result column="act_langcd" property="actLangcd" />
        <result column="act_st_nam" property="actStNam" />
        <result column="act_st_num" property="actStNum" />
        <result column="act_admin" property="actAdmin" />
        <result column="act_postal" property="actPostal" />
        <result column="entr_type" property="entrType" />
        <result column="geom" property="geom" />
    </resultMap>

</mapper>
