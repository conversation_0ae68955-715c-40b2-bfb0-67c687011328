package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-02
 */
@ApiModel(value="Namedplc对象", description="")
public class Namedplc extends Model<Namedplc> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    private String facType;

    private String name;

    private String nmLangcd;

    private String nmEn;

    private String nmS;

    private String nmSLangcd;

    private String nmE;

    private String nmELangcd;

    private String population;

    private String capital;

    private String claimedBy;

    private String controlBy;

    private String geometry;

    public String getDatasource() {
        return datasource;
    }

    public void setDatasource(String datasource) {
        this.datasource = datasource;
    }

    public LocalDateTime getUpDate() {
        return upDate;
    }

    public void setUpDate(LocalDateTime upDate) {
        this.upDate = upDate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    private String datasource;

    private LocalDateTime upDate;

    private Integer status;

    private String sourceId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getFacType() {
        return facType;
    }

    public void setFacType(String facType) {
        this.facType = facType;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public String getNmLangcd() {
        return nmLangcd;
    }

    public void setNmLangcd(String nmLangcd) {
        this.nmLangcd = nmLangcd;
    }
    public String getNmEn() {
        return nmEn;
    }

    public void setNmEn(String nmEn) {
        this.nmEn = nmEn;
    }
    public String getNmS() {
        return nmS;
    }

    public void setNmS(String nmS) {
        this.nmS = nmS;
    }
    public String getNmSLangcd() {
        return nmSLangcd;
    }

    public void setNmSLangcd(String nmSLangcd) {
        this.nmSLangcd = nmSLangcd;
    }
    public String getNmE() {
        return nmE;
    }

    public void setNmE(String nmE) {
        this.nmE = nmE;
    }
    public String getNmELangcd() {
        return nmELangcd;
    }

    public void setNmELangcd(String nmELangcd) {
        this.nmELangcd = nmELangcd;
    }
    public String getPopulation() {
        return population;
    }

    public void setPopulation(String population) {
        this.population = population;
    }
    public String getCapital() {
        return capital;
    }

    public void setCapital(String capital) {
        this.capital = capital;
    }
    public String getClaimedBy() {
        return claimedBy;
    }

    public void setClaimedBy(String claimedBy) {
        this.claimedBy = claimedBy;
    }
    public String getControlBy() {
        return controlBy;
    }

    public void setControlBy(String controlBy) {
        this.controlBy = controlBy;
    }
    public String getGeometry() {
        return geometry;
    }

    public void setGeometry(String geometry) {
        this.geometry = geometry;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "Namedplc{" +
            "id=" + id +
            ", facType=" + facType +
            ", name=" + name +
            ", nmLangcd=" + nmLangcd +
            ", nmEn=" + nmEn +
            ", nmS=" + nmS +
            ", nmSLangcd=" + nmSLangcd +
            ", nmE=" + nmE +
            ", nmELangcd=" + nmELangcd +
            ", population=" + population +
            ", capital=" + capital +
            ", claimedBy=" + claimedBy +
            ", controlBy=" + controlBy +
            ", geometry=" + geometry +
        "}";
    }
}
