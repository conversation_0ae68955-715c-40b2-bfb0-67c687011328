package com.hll.mapdataservice.common.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
@ApiModel(value="Mtddst对象", description="")
public class Mtddst extends Model<Mtddst> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "gid", type = IdType.AUTO)
    private Integer gid;

    private Long areaId;

    private String timeZone;

    private String dstExist;

    private Integer dstStday;

    private Integer dstStwk;

    private Integer dstStmnth;

    private Integer dstSttime;

    private Integer dstEnday;

    private Integer dstEnwk;

    private Integer dstEnmnth;

    private Integer dstEntime;

    public Integer getGid() {
        return gid;
    }

    public void setGid(Integer gid) {
        this.gid = gid;
    }
    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }
    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }
    public String getDstExist() {
        return dstExist;
    }

    public void setDstExist(String dstExist) {
        this.dstExist = dstExist;
    }
    public Integer getDstStday() {
        return dstStday;
    }

    public void setDstStday(Integer dstStday) {
        this.dstStday = dstStday;
    }
    public Integer getDstStwk() {
        return dstStwk;
    }

    public void setDstStwk(Integer dstStwk) {
        this.dstStwk = dstStwk;
    }
    public Integer getDstStmnth() {
        return dstStmnth;
    }

    public void setDstStmnth(Integer dstStmnth) {
        this.dstStmnth = dstStmnth;
    }
    public Integer getDstSttime() {
        return dstSttime;
    }

    public void setDstSttime(Integer dstSttime) {
        this.dstSttime = dstSttime;
    }
    public Integer getDstEnday() {
        return dstEnday;
    }

    public void setDstEnday(Integer dstEnday) {
        this.dstEnday = dstEnday;
    }
    public Integer getDstEnwk() {
        return dstEnwk;
    }

    public void setDstEnwk(Integer dstEnwk) {
        this.dstEnwk = dstEnwk;
    }
    public Integer getDstEnmnth() {
        return dstEnmnth;
    }

    public void setDstEnmnth(Integer dstEnmnth) {
        this.dstEnmnth = dstEnmnth;
    }
    public Integer getDstEntime() {
        return dstEntime;
    }

    public void setDstEntime(Integer dstEntime) {
        this.dstEntime = dstEntime;
    }

    @Override
    protected Serializable pkVal() {
        return this.gid;
    }

    @Override
    public String toString() {
        return "MtddstArea1{" +
            "gid=" + gid +
            ", areaId=" + areaId +
            ", timeZone=" + timeZone +
            ", dstExist=" + dstExist +
            ", dstStday=" + dstStday +
            ", dstStwk=" + dstStwk +
            ", dstStmnth=" + dstStmnth +
            ", dstSttime=" + dstSttime +
            ", dstEnday=" + dstEnday +
            ", dstEnwk=" + dstEnwk +
            ", dstEnmnth=" + dstEnmnth +
            ", dstEntime=" + dstEntime +
        "}";
    }
}
