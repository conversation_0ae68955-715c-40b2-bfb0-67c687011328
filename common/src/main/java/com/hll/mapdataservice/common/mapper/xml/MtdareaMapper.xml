<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.MtdareaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.Mtdarea">
        <id column="gid" property="gid" />
        <result column="area_id" property="areaId" />
        <result column="areacode_1" property="areacode1" />
        <result column="areacode_2" property="areacode2" />
        <result column="areacode_3" property="areacode3" />
        <result column="areacode_4" property="areacode4" />
        <result column="areacode_5" property="areacode5" />
        <result column="areacode_6" property="areacode6" />
        <result column="areacode_7" property="areacode7" />
        <result column="admin_lvl" property="adminLvl" />
        <result column="area_name" property="areaName" />
        <result column="lang_code" property="langCode" />
        <result column="area_nm_tr" property="areaNmTr" />
        <result column="trans_type" property="transType" />
        <result column="area_type" property="areaType" />
        <result column="govt_code" property="govtCode" />
        <result column="motorc_req" property="motorcReq" />
        <result column="admwde_reg" property="admwdeReg" />
    </resultMap>

</mapper>
