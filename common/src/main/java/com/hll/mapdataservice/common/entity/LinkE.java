package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-28
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Link对象", description = "")
@Data
@ToString
public class LinkE extends Model<LinkE> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "hll_linkid", type = IdType.INPUT)
    private String hllLinkid;

    // @TableId(value = "link_id", type = IdType.INPUT)
    private String linkId;

    private String hllSNid;

    private String hllENid;

    private String kind;

    private String formway;

    private String dir;

    private String app;

    private String toll;

    private String adopt;

    private String md;

    private String devs;

    private String spet;

    private String funct;

    private String urban;

    private String pave;

    private Integer laneN;

    private Integer laneL;

    private Integer laneR;

    private String laneC;

    private String width;

    private String viad;

    private String lAdmin;

    private String rAdmin;

    private String tAdmin;

    private String timeZone;

    private String geometry;
    @TableField(value = "ST_AsText(\"geometry\")",insertStrategy = FieldStrategy.NEVER,updateStrategy = FieldStrategy.NEVER)
    private String geometryWkt;

    private Double len;

    private String fSpeed;

    private String tSpeed;

    private String spClass;

    @TableField(value = "ar_veh")
    private String arVeh;

    private String diciType;

    private String verifyflag;

    private String preLaunch;

    private String nameChO;

    private String nmChoLangcd;

    private String nameChA;

    private String nmChaLangcd;

    private String nameChF;

    private String nmChfLangcd;

    private String namePhO;

    private String namePhA;

    private String namePhF;

    private String nameEnO;

    private String nameEnA;

    private String nameEnF;

    private String namePo;

    private String nameCht;

    private String codeType;

    private String nameType;

    private String srcFlag;

    private String meshId;

    private String memo;

    private String cp;

    private String datasource;

    private LocalDateTime upDate;

    private Integer status;

    private String geomwkt;

    private String divider;
    private String dividerLeg;

    private String pubAccess;

    private String tileId;

    private Integer tileType;

    private String linkOld;

    private String linkNew;

    private Integer area = 0;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> name;

    private String taskId;

    private Long olv;
}
