package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-02
 */
@ApiModel(value="Building对象", description="")
public class Building extends Model<Building> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    private String classCode;

    private String show;

    private String height;

    private String cartoId;

    private String name;

    private String nmLangcd;

    private String nmTr;

    private String transType;

    private String geometry;

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getDatasource() {
        return datasource;
    }

    public void setDatasource(String datasource) {
        this.datasource = datasource;
    }

    public LocalDateTime getUpDate() {
        return upDate;
    }

    public void setUpDate(LocalDateTime upDate) {
        this.upDate = upDate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    private String sourceId;
    private String datasource;

    private LocalDateTime upDate;

    private Integer status;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getClassCode() {
        return classCode;
    }

    public void setClassCode(String classCode) {
        this.classCode = classCode;
    }
    public String getShow() {
        return show;
    }

    public void setShow(String show) {
        this.show = show;
    }
    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }
    public String getCartoId() {
        return cartoId;
    }

    public void setCartoId(String cartoId) {
        this.cartoId = cartoId;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public String getNmLangcd() {
        return nmLangcd;
    }

    public void setNmLangcd(String nmLangcd) {
        this.nmLangcd = nmLangcd;
    }
    public String getNmTr() {
        return nmTr;
    }

    public void setNmTr(String nmTr) {
        this.nmTr = nmTr;
    }
    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }
    public String getGeometry() {
        return geometry;
    }

    public void setGeometry(String geometry) {
        this.geometry = geometry;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "Building{" +
            "id=" + id +
            ", class=" + classCode +
            ", show=" + show +
            ", height=" + height +
            ", cartoId=" + cartoId +
            ", name=" + name +
            ", nmLangcd=" + nmLangcd +
            ", nmTr=" + nmTr +
            ", transType=" + transType +
            ", geometry=" + geometry +
        "}";
    }
}
