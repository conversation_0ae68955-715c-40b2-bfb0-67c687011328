package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
  * 第三方道路模型
  *
  * @Author: ares.chen
  * @Since: 2021/11/16
  */
@ApiModel(value="road")
public class Road {
    @ApiModelProperty(value="")
    @TableId("fid")
    private Integer id;

    @ApiModelProperty(value="")
    //@TableField("ST_AsText(the_geom)")
    @TableField("the_geom")
    private String geom;

    @ApiModelProperty(value="")
    @TableField(value = "ST_AsText(the_geom)"/*,insertStrategy = FieldStrategy.IGNORED,updateStrategy = FieldStrategy.IGNORED*/)
    private String geomWkt;

    @ApiModelProperty(value="")
    private Long osmId;

    @ApiModelProperty(value="")
    private Integer code;

    @ApiModelProperty(value="")
    private String fclass;

    @ApiModelProperty(value="")
    private String name;

    @ApiModelProperty(value="")
    private String ref;

    @ApiModelProperty(value="")
    private String oneway;

    @ApiModelProperty(value="")
    private Integer maxspeed;

    @ApiModelProperty(value="")
    private Integer layer;

    @ApiModelProperty(value="")
    private String bridge;

    @ApiModelProperty(value="")
    private String tunnel;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }

    public Long getOsmId() {
        return osmId;
    }

    public void setOsmId(Long osmId) {
        this.osmId = osmId;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getFclass() {
        return fclass;
    }

    public void setFclass(String fclass) {
        this.fclass = fclass;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRef() {
        return ref;
    }

    public void setRef(String ref) {
        this.ref = ref;
    }

    public String getOneway() {
        return oneway;
    }

    public void setOneway(String oneway) {
        this.oneway = oneway;
    }

    public Integer getMaxspeed() {
        return maxspeed;
    }

    public void setMaxspeed(Integer maxspeed) {
        this.maxspeed = maxspeed;
    }

    public Integer getLayer() {
        return layer;
    }

    public void setLayer(Integer layer) {
        this.layer = layer;
    }

    public String getBridge() {
        return bridge;
    }

    public void setBridge(String bridge) {
        this.bridge = bridge;
    }

    public String getTunnel() {
        return tunnel;
    }

    public void setTunnel(String tunnel) {
        this.tunnel = tunnel;
    }

    public String getGeomWkt() {
        return geomWkt;
    }

    public void setGeomWkt(String geomWkt) {
        this.geomWkt = geomWkt;
    }
}