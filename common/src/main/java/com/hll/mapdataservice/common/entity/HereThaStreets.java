package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.postgis.Geometry;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-12
 */
@ApiModel(value="HereThaStreets对象", description="\"")
public class HereThaStreets extends Model<HereThaStreets> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "index", type = IdType.AUTO)
    private String index;

    @TableField("\"LINK_ID\"")
    private Integer linkId;

    @TableField("\"ST_NAME\"")
    private String stName;

    @TableField("\"FEAT_ID\"")
    private String featId;

    @TableField("\"ST_LANGCD\"")
    private String stLangcd;

    @TableField("\"NUM_STNMES\"")
    private String numStnmes;

    @TableField("\"ST_NM_PREF\"")
    private String stNmPref;

    @TableField("\"ST_TYP_BEF\"")
    private String stTypBef;

    @TableField("\"ST_NM_BASE\"")
    private String stNmBase;

    @TableField("\"ST_NM_SUFF\"")
    private String stNmSuff;

    @TableField("\"ST_TYP_AFT\"")
    private String stTypAft;

    @TableField("\"ST_TYP_ATT\"")
    private String stTypAtt;

    @TableField("\"ADDR_TYPE\"")
    private String addrType;

    @TableField("\"L_REFADDR\"")
    private String lRefaddr;

    @TableField("\"L_NREFADDR\"")
    private String lNrefaddr;

    @TableField("\"L_ADDRSCH\"")
    private String lAddrsch;

    @TableField("\"L_ADDRFORM\"")
    private String lAddrform;

    @TableField("\"R_REFADDR\"")
    private String rRefaddr;

    @TableField("\"R_NREFADDR\"")
    private String rNrefaddr;

    @TableField("\"R_ADDRSCH\"")
    private String rAddrsch;

    @TableField("\"R_ADDRFORM\"")
    private String rAddrform;

    @TableField("\"REF_IN_ID\"")
    private String refInId;

    @TableField("\"NREF_IN_ID\"")
    private String nrefInId;

    @TableField("\"N_SHAPEPNT\"")
    private String nShapepnt;

    @TableField("\"FUNC_CLASS\"")
    private String funcClass;

    @TableField("\"SPEED_CAT\"")
    private String speedCat;

    @TableField("\"FR_SPD_LIM\"")
    private String frSpdLim;

    @TableField("\"TO_SPD_LIM\"")
    private String toSpdLim;

    @TableField("\"TO_LANES\"")
    private String toLanes;

    @TableField("\"FROM_LANES\"")
    private String fromLanes;

    @TableField("\"ENH_GEOM\"")
    private String enhGeom;

    @TableField("\"LANE_CAT\"")
    private String laneCat;

    @TableField("\"DIVIDER\"")
    private String divider;

    @TableField("\"DIR_TRAVEL\"")
    private String dirTravel;

    @TableField("\"L_AREA_ID\"")
    private String lAreaId;

    @TableField("\"R_AREA_ID\"")
    private String rAreaId;

    @TableField("\"L_POSTCODE\"")
    private String lPostcode;

    @TableField("\"R_POSTCODE\"")
    private String rPostcode;

    @TableField("\"L_NUMZONES\"")
    private String lNumzones;

    @TableField("\"R_NUMZONES\"")
    private String rNumzones;

    @TableField("\"NUM_AD_RNG\"")
    private String numAdRng;

    @TableField("\"AR_AUTO\"")
    private String arAuto;

    @TableField("\"AR_BUS\"")
    private String arBus;

    @TableField("\"AR_TAXIS\"")
    private String arTaxis;

    @TableField("\"AR_CARPOOL\"")
    private String arCarpool;

    @TableField("\"AR_PEDEST\"")
    private String arPedest;

    @TableField("\"AR_TRUCKS\"")
    private String arTrucks;

    @TableField("\"AR_TRAFF\"")
    private String arTraff;

    @TableField("\"AR_DELIV\"")
    private String arDeliv;

    @TableField("\"AR_EMERVEH\"")
    private String arEmerveh;

    @TableField("\"AR_MOTOR\"")
    private String arMotor;

    @TableField("\"PAVED\"")
    private String paved;

    private String privateinfo;

    @TableField("\"FRONTAGE\"")
    private String frontage;

    @TableField("\"BRIDGE\"")
    private String bridge;

    @TableField("\"TUNNEL\"")
    private String tunnel;

    @TableField("\"RAMP\"")
    private String ramp;

    @TableField("\"TOLLWAY\"")
    private String tollway;

    @TableField("\"POIACCESS\"")
    private String poiaccess;

    @TableField("\"CONTRACC\"")
    private String contracc;

    @TableField("\"ROUNDABOUT\"")
    private String roundabout;

    @TableField("\"INTERINTER\"")
    private String interinter;

    @TableField("\"UNDEFTRAFF\"")
    private String undeftraff;

    @TableField("\"FERRY_TYPE\"")
    private String ferryType;

    @TableField("\"MULTIDIGIT\"")
    private String multidigit;

    @TableField("\"MAXATTR\"")
    private String maxattr;

    @TableField("\"SPECTRFIG\"")
    private String spectrfig;

    @TableField("\"INDESCRIB\"")
    private String indescrib;

    @TableField("\"MANOEUVRE\"")
    private String manoeuvre;

    @TableField("\"DIVIDERLEG\"")
    private String dividerleg;

    @TableField("\"INPROCDATA\"")
    private String inprocdata;

    @TableField("\"FULL_GEOM\"")
    private String fullGeom;

    @TableField("\"URBAN\"")
    private String urban;

    @TableField("\"ROUTE_TYPE\"")
    private String routeType;

    @TableField("\"DIRONSIGN\"")
    private String dironsign;

    @TableField("\"EXPLICATBL\"")
    private String explicatbl;

    @TableField("\"NAMEONRDSN\"")
    private String nameonrdsn;

    @TableField("\"POSTALNAME\"")
    private String postalname;

    @TableField("\"STALENAME\"")
    private String stalename;

    @TableField("\"VANITYNAME\"")
    private String vanityname;

    @TableField("\"JUNCTIONNM\"")
    private String junctionnm;

    @TableField("\"EXITNAME\"")
    private String exitname;

    @TableField("\"SCENIC_RT\"")
    private String scenicRt;

    @TableField("\"SCENIC_NM\"")
    private String scenicNm;

    @TableField("\"FOURWHLDR\"")
    private String fourwhldr;

    @TableField("\"COVERIND\"")
    private String coverind;

    @TableField("\"PLOT_ROAD\"")
    private String plotRoad;

    @TableField("\"REVERSIBLE\"")
    private String reversible;

    @TableField("\"EXPR_LANE\"")
    private String exprLane;

    @TableField("\"CARPOOLRD\"")
    private String carpoolrd;

    @TableField("\"PHYS_LANES\"")
    private String physLanes;

    @TableField("\"VER_TRANS\"")
    private String verTrans;

    @TableField("\"PUB_ACCESS\"")
    private String pubAccess;

    @TableField("\"LOW_MBLTY\"")
    private String lowMblty;

    @TableField("\"PRIORITYRD\"")
    private String priorityrd;

    @TableField("\"SPD_LM_SRC\"")
    private String spdLmSrc;

    @TableField("\"EXPAND_INC\"")
    private String expandInc;

    @TableField("\"TRANS_AREA\"")
    private String transArea;

    @TableField("ST_AsText(\"geometry\")")
    private String geometry;

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }
    public Integer getLinkId() {
        return linkId;
    }

    public void setLinkId(Integer linkId) {
        this.linkId = linkId;
    }
    public String getStName() {
        return stName;
    }

    public void setStName(String stName) {
        this.stName = stName;
    }
    public String getFeatId() {
        return featId;
    }

    public void setFeatId(String featId) {
        this.featId = featId;
    }
    public String getStLangcd() {
        return stLangcd;
    }

    public void setStLangcd(String stLangcd) {
        this.stLangcd = stLangcd;
    }
    public String getNumStnmes() {
        return numStnmes;
    }

    public void setNumStnmes(String numStnmes) {
        this.numStnmes = numStnmes;
    }
    public String getStNmPref() {
        return stNmPref;
    }

    public void setStNmPref(String stNmPref) {
        this.stNmPref = stNmPref;
    }
    public String getStTypBef() {
        return stTypBef;
    }

    public void setStTypBef(String stTypBef) {
        this.stTypBef = stTypBef;
    }
    public String getStNmBase() {
        return stNmBase;
    }

    public void setStNmBase(String stNmBase) {
        this.stNmBase = stNmBase;
    }
    public String getStNmSuff() {
        return stNmSuff;
    }

    public void setStNmSuff(String stNmSuff) {
        this.stNmSuff = stNmSuff;
    }
    public String getStTypAft() {
        return stTypAft;
    }

    public void setStTypAft(String stTypAft) {
        this.stTypAft = stTypAft;
    }
    public String getStTypAtt() {
        return stTypAtt;
    }

    public void setStTypAtt(String stTypAtt) {
        this.stTypAtt = stTypAtt;
    }
    public String getAddrType() {
        return addrType;
    }

    public void setAddrType(String addrType) {
        this.addrType = addrType;
    }
    public String getlRefaddr() {
        return lRefaddr;
    }

    public void setlRefaddr(String lRefaddr) {
        this.lRefaddr = lRefaddr;
    }
    public String getlNrefaddr() {
        return lNrefaddr;
    }

    public void setlNrefaddr(String lNrefaddr) {
        this.lNrefaddr = lNrefaddr;
    }
    public String getlAddrsch() {
        return lAddrsch;
    }

    public void setlAddrsch(String lAddrsch) {
        this.lAddrsch = lAddrsch;
    }
    public String getlAddrform() {
        return lAddrform;
    }

    public void setlAddrform(String lAddrform) {
        this.lAddrform = lAddrform;
    }
    public String getrRefaddr() {
        return rRefaddr;
    }

    public void setrRefaddr(String rRefaddr) {
        this.rRefaddr = rRefaddr;
    }
    public String getrNrefaddr() {
        return rNrefaddr;
    }

    public void setrNrefaddr(String rNrefaddr) {
        this.rNrefaddr = rNrefaddr;
    }
    public String getrAddrsch() {
        return rAddrsch;
    }

    public void setrAddrsch(String rAddrsch) {
        this.rAddrsch = rAddrsch;
    }
    public String getrAddrform() {
        return rAddrform;
    }

    public void setrAddrform(String rAddrform) {
        this.rAddrform = rAddrform;
    }
    public String getRefInId() {
        return refInId;
    }

    public void setRefInId(String refInId) {
        this.refInId = refInId;
    }
    public String getNrefInId() {
        return nrefInId;
    }

    public void setNrefInId(String nrefInId) {
        this.nrefInId = nrefInId;
    }
    public String getnShapepnt() {
        return nShapepnt;
    }

    public void setnShapepnt(String nShapepnt) {
        this.nShapepnt = nShapepnt;
    }
    public String getFuncClass() {
        return funcClass;
    }

    public void setFuncClass(String funcClass) {
        this.funcClass = funcClass;
    }
    public String getSpeedCat() {
        return speedCat;
    }

    public void setSpeedCat(String speedCat) {
        this.speedCat = speedCat;
    }
    public String getFrSpdLim() {
        return frSpdLim;
    }

    public void setFrSpdLim(String frSpdLim) {
        this.frSpdLim = frSpdLim;
    }
    public String getToSpdLim() {
        return toSpdLim;
    }

    public void setToSpdLim(String toSpdLim) {
        this.toSpdLim = toSpdLim;
    }
    public String getToLanes() {
        return toLanes;
    }

    public void setToLanes(String toLanes) {
        this.toLanes = toLanes;
    }
    public String getFromLanes() {
        return fromLanes;
    }

    public void setFromLanes(String fromLanes) {
        this.fromLanes = fromLanes;
    }
    public String getEnhGeom() {
        return enhGeom;
    }

    public void setEnhGeom(String enhGeom) {
        this.enhGeom = enhGeom;
    }
    public String getLaneCat() {
        return laneCat;
    }

    public void setLaneCat(String laneCat) {
        this.laneCat = laneCat;
    }
    public String getDivider() {
        return divider;
    }

    public void setDivider(String divider) {
        this.divider = divider;
    }
    public String getDirTravel() {
        return dirTravel;
    }

    public void setDirTravel(String dirTravel) {
        this.dirTravel = dirTravel;
    }
    public String getlAreaId() {
        return lAreaId;
    }

    public void setlAreaId(String lAreaId) {
        this.lAreaId = lAreaId;
    }
    public String getrAreaId() {
        return rAreaId;
    }

    public void setrAreaId(String rAreaId) {
        this.rAreaId = rAreaId;
    }
    public String getlPostcode() {
        return lPostcode;
    }

    public void setlPostcode(String lPostcode) {
        this.lPostcode = lPostcode;
    }
    public String getrPostcode() {
        return rPostcode;
    }

    public void setrPostcode(String rPostcode) {
        this.rPostcode = rPostcode;
    }
    public String getlNumzones() {
        return lNumzones;
    }

    public void setlNumzones(String lNumzones) {
        this.lNumzones = lNumzones;
    }
    public String getrNumzones() {
        return rNumzones;
    }

    public void setrNumzones(String rNumzones) {
        this.rNumzones = rNumzones;
    }
    public String getNumAdRng() {
        return numAdRng;
    }

    public void setNumAdRng(String numAdRng) {
        this.numAdRng = numAdRng;
    }
    public String getArAuto() {
        return arAuto;
    }

    public void setArAuto(String arAuto) {
        this.arAuto = arAuto;
    }
    public String getArBus() {
        return arBus;
    }

    public void setArBus(String arBus) {
        this.arBus = arBus;
    }
    public String getArTaxis() {
        return arTaxis;
    }

    public void setArTaxis(String arTaxis) {
        this.arTaxis = arTaxis;
    }
    public String getArCarpool() {
        return arCarpool;
    }

    public void setArCarpool(String arCarpool) {
        this.arCarpool = arCarpool;
    }
    public String getArPedest() {
        return arPedest;
    }

    public void setArPedest(String arPedest) {
        this.arPedest = arPedest;
    }
    public String getArTrucks() {
        return arTrucks;
    }

    public void setArTrucks(String arTrucks) {
        this.arTrucks = arTrucks;
    }
    public String getArTraff() {
        return arTraff;
    }

    public void setArTraff(String arTraff) {
        this.arTraff = arTraff;
    }
    public String getArDeliv() {
        return arDeliv;
    }

    public void setArDeliv(String arDeliv) {
        this.arDeliv = arDeliv;
    }
    public String getArEmerveh() {
        return arEmerveh;
    }

    public void setArEmerveh(String arEmerveh) {
        this.arEmerveh = arEmerveh;
    }
    public String getArMotor() {
        return arMotor;
    }

    public void setArMotor(String arMotor) {
        this.arMotor = arMotor;
    }
    public String getPaved() {
        return paved;
    }

    public void setPaved(String paved) {
        this.paved = paved;
    }
    public String getPrivate() {
        return privateinfo;
    }

    public void setPrivate(String privateinfo) {
        this.privateinfo = privateinfo;
    }
    public String getFrontage() {
        return frontage;
    }

    public void setFrontage(String frontage) {
        this.frontage = frontage;
    }
    public String getBridge() {
        return bridge;
    }

    public void setBridge(String bridge) {
        this.bridge = bridge;
    }
    public String getTunnel() {
        return tunnel;
    }

    public void setTunnel(String tunnel) {
        this.tunnel = tunnel;
    }
    public String getRamp() {
        return ramp;
    }

    public void setRamp(String ramp) {
        this.ramp = ramp;
    }
    public String getTollway() {
        return tollway;
    }

    public void setTollway(String tollway) {
        this.tollway = tollway;
    }
    public String getPoiaccess() {
        return poiaccess;
    }

    public void setPoiaccess(String poiaccess) {
        this.poiaccess = poiaccess;
    }
    public String getContracc() {
        return contracc;
    }

    public void setContracc(String contracc) {
        this.contracc = contracc;
    }
    public String getRoundabout() {
        return roundabout;
    }

    public void setRoundabout(String roundabout) {
        this.roundabout = roundabout;
    }
    public String getInterinter() {
        return interinter;
    }

    public void setInterinter(String interinter) {
        this.interinter = interinter;
    }
    public String getUndeftraff() {
        return undeftraff;
    }

    public void setUndeftraff(String undeftraff) {
        this.undeftraff = undeftraff;
    }
    public String getFerryType() {
        return ferryType;
    }

    public void setFerryType(String ferryType) {
        this.ferryType = ferryType;
    }
    public String getMultidigit() {
        return multidigit;
    }

    public void setMultidigit(String multidigit) {
        this.multidigit = multidigit;
    }
    public String getMaxattr() {
        return maxattr;
    }

    public void setMaxattr(String maxattr) {
        this.maxattr = maxattr;
    }
    public String getSpectrfig() {
        return spectrfig;
    }

    public void setSpectrfig(String spectrfig) {
        this.spectrfig = spectrfig;
    }
    public String getIndescrib() {
        return indescrib;
    }

    public void setIndescrib(String indescrib) {
        this.indescrib = indescrib;
    }
    public String getManoeuvre() {
        return manoeuvre;
    }

    public void setManoeuvre(String manoeuvre) {
        this.manoeuvre = manoeuvre;
    }
    public String getDividerleg() {
        return dividerleg;
    }

    public void setDividerleg(String dividerleg) {
        this.dividerleg = dividerleg;
    }
    public String getInprocdata() {
        return inprocdata;
    }

    public void setInprocdata(String inprocdata) {
        this.inprocdata = inprocdata;
    }
    public String getFullGeom() {
        return fullGeom;
    }

    public void setFullGeom(String fullGeom) {
        this.fullGeom = fullGeom;
    }
    public String getUrban() {
        return urban;
    }

    public void setUrban(String urban) {
        this.urban = urban;
    }
    public String getRouteType() {
        return routeType;
    }

    public void setRouteType(String routeType) {
        this.routeType = routeType;
    }
    public String getDironsign() {
        return dironsign;
    }

    public void setDironsign(String dironsign) {
        this.dironsign = dironsign;
    }
    public String getExplicatbl() {
        return explicatbl;
    }

    public void setExplicatbl(String explicatbl) {
        this.explicatbl = explicatbl;
    }
    public String getNameonrdsn() {
        return nameonrdsn;
    }

    public void setNameonrdsn(String nameonrdsn) {
        this.nameonrdsn = nameonrdsn;
    }
    public String getPostalname() {
        return postalname;
    }

    public void setPostalname(String postalname) {
        this.postalname = postalname;
    }
    public String getStalename() {
        return stalename;
    }

    public void setStalename(String stalename) {
        this.stalename = stalename;
    }
    public String getVanityname() {
        return vanityname;
    }

    public void setVanityname(String vanityname) {
        this.vanityname = vanityname;
    }
    public String getJunctionnm() {
        return junctionnm;
    }

    public void setJunctionnm(String junctionnm) {
        this.junctionnm = junctionnm;
    }
    public String getExitname() {
        return exitname;
    }

    public void setExitname(String exitname) {
        this.exitname = exitname;
    }
    public String getScenicRt() {
        return scenicRt;
    }

    public void setScenicRt(String scenicRt) {
        this.scenicRt = scenicRt;
    }
    public String getScenicNm() {
        return scenicNm;
    }

    public void setScenicNm(String scenicNm) {
        this.scenicNm = scenicNm;
    }
    public String getFourwhldr() {
        return fourwhldr;
    }

    public void setFourwhldr(String fourwhldr) {
        this.fourwhldr = fourwhldr;
    }
    public String getCoverind() {
        return coverind;
    }

    public void setCoverind(String coverind) {
        this.coverind = coverind;
    }
    public String getPlotRoad() {
        return plotRoad;
    }

    public void setPlotRoad(String plotRoad) {
        this.plotRoad = plotRoad;
    }
    public String getReversible() {
        return reversible;
    }

    public void setReversible(String reversible) {
        this.reversible = reversible;
    }
    public String getExprLane() {
        return exprLane;
    }

    public void setExprLane(String exprLane) {
        this.exprLane = exprLane;
    }
    public String getCarpoolrd() {
        return carpoolrd;
    }

    public void setCarpoolrd(String carpoolrd) {
        this.carpoolrd = carpoolrd;
    }
    public String getPhysLanes() {
        return physLanes;
    }

    public void setPhysLanes(String physLanes) {
        this.physLanes = physLanes;
    }
    public String getVerTrans() {
        return verTrans;
    }

    public void setVerTrans(String verTrans) {
        this.verTrans = verTrans;
    }
    public String getPubAccess() {
        return pubAccess;
    }

    public void setPubAccess(String pubAccess) {
        this.pubAccess = pubAccess;
    }
    public String getLowMblty() {
        return lowMblty;
    }

    public void setLowMblty(String lowMblty) {
        this.lowMblty = lowMblty;
    }
    public String getPriorityrd() {
        return priorityrd;
    }

    public void setPriorityrd(String priorityrd) {
        this.priorityrd = priorityrd;
    }
    public String getSpdLmSrc() {
        return spdLmSrc;
    }

    public void setSpdLmSrc(String spdLmSrc) {
        this.spdLmSrc = spdLmSrc;
    }
    public String getExpandInc() {
        return expandInc;
    }

    public void setExpandInc(String expandInc) {
        this.expandInc = expandInc;
    }
    public String getTransArea() {
        return transArea;
    }

    public void setTransArea(String transArea) {
        this.transArea = transArea;
    }
    public String getGeometry() {
        return geometry;
    }

    public void setGeometry(String geometry) {
        this.geometry = geometry;
    }

    @Override
    protected Serializable pkVal() {
        return this.index;
    }

    @Override
    public String toString() {
        return "HereThaStreets{" +
            "index=" + index +
            ", linkId=" + linkId +
            ", stName=" + stName +
            ", featId=" + featId +
            ", stLangcd=" + stLangcd +
            ", numStnmes=" + numStnmes +
            ", stNmPref=" + stNmPref +
            ", stTypBef=" + stTypBef +
            ", stNmBase=" + stNmBase +
            ", stNmSuff=" + stNmSuff +
            ", stTypAft=" + stTypAft +
            ", stTypAtt=" + stTypAtt +
            ", addrType=" + addrType +
            ", lRefaddr=" + lRefaddr +
            ", lNrefaddr=" + lNrefaddr +
            ", lAddrsch=" + lAddrsch +
            ", lAddrform=" + lAddrform +
            ", rRefaddr=" + rRefaddr +
            ", rNrefaddr=" + rNrefaddr +
            ", rAddrsch=" + rAddrsch +
            ", rAddrform=" + rAddrform +
            ", refInId=" + refInId +
            ", nrefInId=" + nrefInId +
            ", nShapepnt=" + nShapepnt +
            ", funcClass=" + funcClass +
            ", speedCat=" + speedCat +
            ", frSpdLim=" + frSpdLim +
            ", toSpdLim=" + toSpdLim +
            ", toLanes=" + toLanes +
            ", fromLanes=" + fromLanes +
            ", enhGeom=" + enhGeom +
            ", laneCat=" + laneCat +
            ", divider=" + divider +
            ", dirTravel=" + dirTravel +
            ", lAreaId=" + lAreaId +
            ", rAreaId=" + rAreaId +
            ", lPostcode=" + lPostcode +
            ", rPostcode=" + rPostcode +
            ", lNumzones=" + lNumzones +
            ", rNumzones=" + rNumzones +
            ", numAdRng=" + numAdRng +
            ", arAuto=" + arAuto +
            ", arBus=" + arBus +
            ", arTaxis=" + arTaxis +
            ", arCarpool=" + arCarpool +
            ", arPedest=" + arPedest +
            ", arTrucks=" + arTrucks +
            ", arTraff=" + arTraff +
            ", arDeliv=" + arDeliv +
            ", arEmerveh=" + arEmerveh +
            ", arMotor=" + arMotor +
            ", paved=" + paved +
            ", private=" + privateinfo +
            ", frontage=" + frontage +
            ", bridge=" + bridge +
            ", tunnel=" + tunnel +
            ", ramp=" + ramp +
            ", tollway=" + tollway +
            ", poiaccess=" + poiaccess +
            ", contracc=" + contracc +
            ", roundabout=" + roundabout +
            ", interinter=" + interinter +
            ", undeftraff=" + undeftraff +
            ", ferryType=" + ferryType +
            ", multidigit=" + multidigit +
            ", maxattr=" + maxattr +
            ", spectrfig=" + spectrfig +
            ", indescrib=" + indescrib +
            ", manoeuvre=" + manoeuvre +
            ", dividerleg=" + dividerleg +
            ", inprocdata=" + inprocdata +
            ", fullGeom=" + fullGeom +
            ", urban=" + urban +
            ", routeType=" + routeType +
            ", dironsign=" + dironsign +
            ", explicatbl=" + explicatbl +
            ", nameonrdsn=" + nameonrdsn +
            ", postalname=" + postalname +
            ", stalename=" + stalename +
            ", vanityname=" + vanityname +
            ", junctionnm=" + junctionnm +
            ", exitname=" + exitname +
            ", scenicRt=" + scenicRt +
            ", scenicNm=" + scenicNm +
            ", fourwhldr=" + fourwhldr +
            ", coverind=" + coverind +
            ", plotRoad=" + plotRoad +
            ", reversible=" + reversible +
            ", exprLane=" + exprLane +
            ", carpoolrd=" + carpoolrd +
            ", physLanes=" + physLanes +
            ", verTrans=" + verTrans +
            ", pubAccess=" + pubAccess +
            ", lowMblty=" + lowMblty +
            ", priorityrd=" + priorityrd +
            ", spdLmSrc=" + spdLmSrc +
            ", expandInc=" + expandInc +
            ", transArea=" + transArea +
            ", geometry=" + geometry +
        "}";
    }
}
