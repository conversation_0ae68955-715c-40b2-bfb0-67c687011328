package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@ApiModel(value="DirectSw2021q133对象", description="")
public class DirectSw2021q133 extends Model<DirectSw2021q133> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "direct_id", type = IdType.AUTO)
    private String directId;

    private String swdirectid;

    private String inLinkid;

    private String nodeId;

    private String outLinkid;

    private String pass;

    private String pass2;

    private String flag;

    private String procFlag;

    private String meshList;

    private String memo;

    private String linkAngle;

    private String cp;

    private String datasource;

    private LocalDateTime upDate;

    private String status;

    public String getDirectId() {
        return directId;
    }

    public void setDirectId(String directId) {
        this.directId = directId;
    }
    public String getSwdirectid() {
        return swdirectid;
    }

    public void setSwdirectid(String swdirectid) {
        this.swdirectid = swdirectid;
    }
    public String getInLinkid() {
        return inLinkid;
    }

    public void setInLinkid(String inLinkid) {
        this.inLinkid = inLinkid;
    }
    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }
    public String getOutLinkid() {
        return outLinkid;
    }

    public void setOutLinkid(String outLinkid) {
        this.outLinkid = outLinkid;
    }
    public String getPass() {
        return pass;
    }

    public void setPass(String pass) {
        this.pass = pass;
    }
    public String getPass2() {
        return pass2;
    }

    public void setPass2(String pass2) {
        this.pass2 = pass2;
    }
    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }
    public String getProcFlag() {
        return procFlag;
    }

    public void setProcFlag(String procFlag) {
        this.procFlag = procFlag;
    }
    public String getMeshList() {
        return meshList;
    }

    public void setMeshList(String meshList) {
        this.meshList = meshList;
    }
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }
    public String getLinkAngle() {
        return linkAngle;
    }

    public void setLinkAngle(String linkAngle) {
        this.linkAngle = linkAngle;
    }
    public String getCp() {
        return cp;
    }

    public void setCp(String cp) {
        this.cp = cp;
    }
    public String getDatasource() {
        return datasource;
    }

    public void setDatasource(String datasource) {
        this.datasource = datasource;
    }
    public LocalDateTime getUpDate() {
        return upDate;
    }

    public void setUpDate(LocalDateTime upDate) {
        this.upDate = upDate;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    protected Serializable pkVal() {
        return this.directId;
    }

    @Override
    public String toString() {
        return "DirectSw2021q133{" +
            "directId=" + directId +
            ", swdirectid=" + swdirectid +
            ", inLinkid=" + inLinkid +
            ", nodeId=" + nodeId +
            ", outLinkid=" + outLinkid +
            ", pass=" + pass +
            ", pass2=" + pass2 +
            ", flag=" + flag +
            ", procFlag=" + procFlag +
            ", meshList=" + meshList +
            ", memo=" + memo +
            ", linkAngle=" + linkAngle +
            ", cp=" + cp +
            ", datasource=" + datasource +
            ", upDate=" + upDate +
            ", status=" + status +
        "}";
    }
}
