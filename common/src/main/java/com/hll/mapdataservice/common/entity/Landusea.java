package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-26
 */
@ApiModel(value="Landusea对象", description="")
public class Landusea extends Model<Landusea> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "gid", type = IdType.AUTO)
    private Integer gid;

    private Long polygonId;

    private String polygonNm;

    private String nmLangcd;

    private String polyNmTr;

    private String transType;

    private String featType;

    private String detailCty;

    private Long featCod;

    private String coverind;

    private String dispClass;

    private String expandInc;

    private String geom;

    public Integer getGid() {
        return gid;
    }

    public void setGid(Integer gid) {
        this.gid = gid;
    }
    public Long getPolygonId() {
        return polygonId;
    }

    public void setPolygonId(Long polygonId) {
        this.polygonId = polygonId;
    }
    public String getPolygonNm() {
        return polygonNm;
    }

    public void setPolygonNm(String polygonNm) {
        this.polygonNm = polygonNm;
    }
    public String getNmLangcd() {
        return nmLangcd;
    }

    public void setNmLangcd(String nmLangcd) {
        this.nmLangcd = nmLangcd;
    }
    public String getPolyNmTr() {
        return polyNmTr;
    }

    public void setPolyNmTr(String polyNmTr) {
        this.polyNmTr = polyNmTr;
    }
    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }
    public String getFeatType() {
        return featType;
    }

    public void setFeatType(String featType) {
        this.featType = featType;
    }
    public String getDetailCty() {
        return detailCty;
    }

    public void setDetailCty(String detailCty) {
        this.detailCty = detailCty;
    }
    public Long getFeatCod() {
        return featCod;
    }

    public void setFeatCod(Long featCod) {
        this.featCod = featCod;
    }
    public String getCoverind() {
        return coverind;
    }

    public void setCoverind(String coverind) {
        this.coverind = coverind;
    }
    public String getDispClass() {
        return dispClass;
    }

    public void setDispClass(String dispClass) {
        this.dispClass = dispClass;
    }
    public String getExpandInc() {
        return expandInc;
    }

    public void setExpandInc(String expandInc) {
        this.expandInc = expandInc;
    }
    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }

    @Override
    protected Serializable pkVal() {
        return this.gid;
    }

    @Override
    public String toString() {
        return "Landusea{" +
            "gid=" + gid +
            ", polygonId=" + polygonId +
            ", polygonNm=" + polygonNm +
            ", nmLangcd=" + nmLangcd +
            ", polyNmTr=" + polyNmTr +
            ", transType=" + transType +
            ", featType=" + featType +
            ", detailCty=" + detailCty +
            ", featCod=" + featCod +
            ", coverind=" + coverind +
            ", dispClass=" + dispClass +
            ", expandInc=" + expandInc +
            ", geom=" + geom +
        "}";
    }
}
