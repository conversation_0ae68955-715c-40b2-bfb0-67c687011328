package com.hll.mapdataservice.common.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.time.LocalDateTime;

/**
 * @Author: ares.chen
 * @Since: 2022/4/6
 */
public class AcDtlMatch extends Model<AcDtlMatch> {
    @ExcelProperty("args_city")
    private String city;
    @ExcelProperty("lang")
    private String lang;
    @ExcelProperty("ac_id")
    @TableId
    private String acId;
    @ExcelProperty("ac_name")
    private String acName;
    @ExcelProperty("ac_addr")
    private String acAddr;
    @ExcelProperty("dtl_id")
    private String dtlId;
    @ExcelProperty("dtl_name")
    private String dtlName;
    @ExcelProperty("dtl_addr")
    private String dtlAddr;
    @ExcelProperty("dtl_lat")
    private Double dtlLat;
    @ExcelProperty("dtl_lon")
    private Double dtlLon;
    @ExcelProperty("gc_id")
    private String gcId;
    @ExcelProperty("gc_addr")
    private String gcAddr;
    @ExcelProperty("gc_lat")
    private Double gcLat;
    @ExcelProperty("gc_lon")
    private Double gcLon;
    @ExcelProperty("dtl_gc_distance")
    private Integer dtlGcDistance;
    @ExcelProperty("ac_gc_addr_same")
    private Integer acGcAddrSame;

    @ExcelProperty("create_time")
    private LocalDateTime createTime;
    @ExcelProperty("dtl_gc_addr_same")
    private Integer dtlGcAddrSame;
    //@ExcelIgnore
    private Double acDtlNameMatch;
    //@ExcelIgnore
    private Double acDtlAddrMatch;
    //@ExcelIgnore
    private Double acDtlNameAddrMatch;
    private Double acGcAddrMatch;
    private Double dtlGcAddrMatch;


    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getAcId() {
        return acId;
    }

    public void setAcId(String acId) {
        this.acId = acId;
    }

    public String getAcName() {
        return acName;
    }

    public void setAcName(String acName) {
        this.acName = acName;
    }

    public String getAcAddr() {
        return acAddr;
    }

    public void setAcAddr(String acAddr) {
        this.acAddr = acAddr;
    }

    public String getDtlId() {
        return dtlId;
    }

    public void setDtlId(String dtlId) {
        this.dtlId = dtlId;
    }

    public String getDtlName() {
        return dtlName;
    }

    public void setDtlName(String dtlName) {
        this.dtlName = dtlName;
    }

    public String getDtlAddr() {
        return dtlAddr;
    }

    public void setDtlAddr(String dtlAddr) {
        this.dtlAddr = dtlAddr;
    }

    public Double getDtlLat() {
        return dtlLat;
    }

    public void setDtlLat(Double dtlLat) {
        this.dtlLat = dtlLat;
    }

    public Double getDtlLon() {
        return dtlLon;
    }

    public void setDtlLon(Double dtlLon) {
        this.dtlLon = dtlLon;
    }

    public String getGcId() {
        return gcId;
    }

    public void setGcId(String gcId) {
        this.gcId = gcId;
    }

    public String getGcAddr() {
        return gcAddr;
    }

    public void setGcAddr(String gcAddr) {
        this.gcAddr = gcAddr;
    }

    public Double getGcLat() {
        return gcLat;
    }

    public void setGcLat(Double gcLat) {
        this.gcLat = gcLat;
    }

    public Double getGcLon() {
        return gcLon;
    }

    public void setGcLon(Double gcLon) {
        this.gcLon = gcLon;
    }

    public Integer getDtlGcDistance() {
        return dtlGcDistance;
    }

    public void setDtlGcDistance(Integer dtlGcDistance) {
        this.dtlGcDistance = dtlGcDistance;
    }

    public Integer getAcGcAddrSame() {
        return acGcAddrSame;
    }

    public void setAcGcAddrSame(Integer acGcAddrSame) {
        this.acGcAddrSame = acGcAddrSame;
    }

    public Integer getDtlGcAddrSame() {
        return dtlGcAddrSame;
    }

    public void setDtlGcAddrSame(Integer dtlGcAddrSame) {
        this.dtlGcAddrSame = dtlGcAddrSame;
    }

    public Double getAcDtlNameMatch() {
        return acDtlNameMatch;
    }

    public void setAcDtlNameMatch(Double acDtlNameMatch) {
        this.acDtlNameMatch = acDtlNameMatch;
    }

    public Double getAcDtlAddrMatch() {
        return acDtlAddrMatch;
    }

    public void setAcDtlAddrMatch(Double acDtlAddrMatch) {
        this.acDtlAddrMatch = acDtlAddrMatch;
    }

    public Double getAcDtlNameAddrMatch() {
        return acDtlNameAddrMatch;
    }

    public void setAcDtlNameAddrMatch(Double acDtlNameAddrMatch) {
        this.acDtlNameAddrMatch = acDtlNameAddrMatch;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public Double getAcGcAddrMatch() {
        return acGcAddrMatch;
    }

    public void setAcGcAddrMatch(Double acGcAddrMatch) {
        this.acGcAddrMatch = acGcAddrMatch;
    }

    public Double getDtlGcAddrMatch() {
        return dtlGcAddrMatch;
    }

    public void setDtlGcAddrMatch(Double dtlGcAddrMatch) {
        this.dtlGcAddrMatch = dtlGcAddrMatch;
    }

    @Override
    public String toString() {
        return "AcDtlMatch{" +
                "city='" + city + '\'' +
                ", lang='" + lang + '\'' +
                ", acId='" + acId + '\'' +
                ", acName='" + acName + '\'' +
                ", acAddr='" + acAddr + '\'' +
                ", dtlId='" + dtlId + '\'' +
                ", dtlName='" + dtlName + '\'' +
                ", dtlAddr='" + dtlAddr + '\'' +
                ", dtlLat=" + dtlLat +
                ", dtlLon=" + dtlLon +
                ", gcId='" + gcId + '\'' +
                ", gcAddr='" + gcAddr + '\'' +
                ", gcLat=" + gcLat +
                ", gcLon=" + gcLon +
                ", dtlGcDistance=" + dtlGcDistance +
                ", acGcAddrSame=" + acGcAddrSame +
                ", createTime=" + createTime +
                ", dtlGcAddrSame=" + dtlGcAddrSame +
                ", acDtlNameMatch=" + acDtlNameMatch +
                ", acDtlAddrMatch=" + acDtlAddrMatch +
                ", acDtlNameAddrMatch=" + acDtlNameAddrMatch +
                ", acGcAddrMatch=" + acGcAddrMatch +
                ", dtlGcAddrMatch=" + dtlGcAddrMatch +
                '}';
    }
}
