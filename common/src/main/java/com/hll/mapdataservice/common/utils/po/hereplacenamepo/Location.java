/**
  * Copyright 2021 bejson.com 
  */
package com.hll.mapdataservice.common.utils.po.hereplacenamepo;
import java.util.List;

/**
 * Auto-generated: 2021-04-02 11:27:34
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class Location {

    private List<AdditionalData> AdditionalData;
    private GeoPositionList GeoPositionList;
    private Address Address;
    private String locationId;
    private String supplier;
    private String label;
    private String type;
    private MapReferenceList MapReferenceList;
    private boolean primary;
    public void setAdditionalData(List<AdditionalData> AdditionalData) {
         this.AdditionalData = AdditionalData;
     }
     public List<AdditionalData> getAdditionalData() {
         return AdditionalData;
     }

    public void setGeoPositionList(GeoPositionList GeoPositionList) {
         this.GeoPositionList = GeoPositionList;
     }
     public GeoPositionList getGeoPositionList() {
         return GeoPositionList;
     }

    public void setAddress(Address Address) {
         this.Address = Address;
     }
     public Address getAddress() {
         return Address;
     }

    public void setLocationId(String locationId) {
         this.locationId = locationId;
     }
     public String getLocationId() {
         return locationId;
     }

    public void setSupplier(String supplier) {
         this.supplier = supplier;
     }
     public String getSupplier() {
         return supplier;
     }

    public void setLabel(String label) {
         this.label = label;
     }
     public String getLabel() {
         return label;
     }

    public void setType(String type) {
         this.type = type;
     }
     public String getType() {
         return type;
     }

    public void setMapReferenceList(MapReferenceList MapReferenceList) {
         this.MapReferenceList = MapReferenceList;
     }
     public MapReferenceList getMapReferenceList() {
         return MapReferenceList;
     }

    public void setPrimary(boolean primary) {
         this.primary = primary;
     }
     public boolean getPrimary() {
         return primary;
     }

}