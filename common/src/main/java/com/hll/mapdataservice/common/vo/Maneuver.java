package com.hll.mapdataservice.common.vo;

import java.io.Serializable;

/*
 * <AUTHOR>
 * @date  3/3/21 12:40 PM
 * @Email:<EMAIL>
 */
public class Maneuver implements Serializable {
    /**
     * 线link
     */
    private String featId;

    /**
     * 显示等级
     */
    private String featType;

//    private String geom;

//    /**
//     * 导航等级
//     */
//    private String routingClass;
//
//    /**
//     * 道路形态
//     */
//    private String formOfWay;
//
//    private String noThroughTraffic;

    public String getFeatId() {
        return featId;
    }

    public void setFeatId(String featId) {
        this.featId = featId;
    }

    public String getFeatType() {
        return featType;
    }

    public void setFeatType(String featType) {
        this.featType = featType;
    }

//    public String getRoutingClass() {
//        return routingClass;
//    }
//
//    public void setRoutingClass(String routingClass) {
//        this.routingClass = routingClass;
//    }
//
//    public String getFormOfWay() {
//        return formOfWay;
//    }
//
//    public void setFormOfWay(String formOfWay) {
//        this.formOfWay = formOfWay;
//    }
//
//    public String getNoThroughTraffic() {
//        return noThroughTraffic;
//    }
//
//    public void setNoThroughTraffic(String noThroughTraffic) {
//        this.noThroughTraffic = noThroughTraffic;
//    }

//    @Override
//    public String toString() {
//        return "Maneuver{" +
//                "featId='" + featId + '\'' +
//                ", featType='" + featType + '\'' +
//                ", routingClass='" + routingClass + '\'' +
//                ", formOfWay='" + formOfWay + '\'' +
//                ", noThroughTraffic='" + noThroughTraffic + '\'' +
//                '}';
//    }
}
