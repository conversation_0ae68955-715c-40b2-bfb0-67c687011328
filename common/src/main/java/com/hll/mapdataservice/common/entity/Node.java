package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

/**
  *<p>
  *
  *</p>
  *
  * @Author: ares.chen
  * @Since: 2021/8/17
  */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="com-hll-mapdataservice-common-entity-NodeRp")
@Data
@ToString
public class Node extends Model<Node> {
    @ApiModelProperty(value="")
    private String id;

    @ApiModelProperty(value="")
    @TableId
    private String hllNodeid;

    @ApiModelProperty(value="")
    private String kind;

    @ApiModelProperty(value="")
    private String geometry;

    @ApiModelProperty(value="")
    private String nameCh;

    @ApiModelProperty(value="")
    private String nameFo;

    @ApiModelProperty(value="")
    private String nameCht;

    @ApiModelProperty(value="")
    private String namePh;

    @ApiModelProperty(value="")
    private String adjoinMid;

    @ApiModelProperty(value="")
    private String adjoinNid;

    @ApiModelProperty(value="")
    private String type;

    @ApiModelProperty(value="")
    private String mainnodeid;

    @ApiModelProperty(value="")
    private String subnodeid;

    @ApiModelProperty(value="")
    private String subnodeid2;

    @ApiModelProperty(value="")
    private String lightFlag;

    @ApiModelProperty(value="")
    private String isPbnode;

    @ApiModelProperty(value="")
    private String cp;

    @ApiModelProperty(value="")
    private String datasource;

    @ApiModelProperty(value="")
    private Date upDate;

    @ApiModelProperty(value="")
    private String memo;

    @ApiModelProperty(value="")
    private Integer status;

    @ApiModelProperty(value="")
    private String geomwkt;

    @ApiModelProperty(value="")
    private Integer area;
}