package com.hll.mapdataservice.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hll.mapdataservice.common.entity.RuleM;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-28
 */
public interface IRuleMService extends IService<RuleM> {

    void handleId(String area, String country, CountDownLatch countDownLatch, List<RuleM> ruleList);
}
