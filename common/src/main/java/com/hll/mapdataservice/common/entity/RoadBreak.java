package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import java.lang.String;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hll.mapdataservice.common.utils.MyGeometryTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
  * osm截断道路
  *
  * @Author: ares.chen
  * @Since: 2021/11/26
  */
@ApiModel(value="osm截断道路")
//@TableName("road_break2")
public class RoadBreak {
    @ApiModelProperty(value="")
    private String roadId;

    /**
    * 被截断的osmId
    */
    @ApiModelProperty(value="被截断的osmId")
    private String osmId;

    /**
    * 截断后的坐标
    */
    @ApiModelProperty(value="截断后的坐标")
    //@TableField(typeHandler = MyGeometryTypeHandler.class)
    private String roadGeom;

    @TableField(value = "ST_AsText(road_geom)")
    private String roadGeomWkt;

    /**
    * osm坐标
    */
    @ApiModelProperty(value="osm坐标")
    private String osmGeom;

    @TableField(value = "ST_AsText(osm_geom)")
    private String osmGeomWKt;

    @ApiModelProperty(value="")
    private Integer code;

    @ApiModelProperty(value="")
    private String fclass;

    @ApiModelProperty(value="")
    private String name;

    @ApiModelProperty(value="")
    private String ref;

    @ApiModelProperty(value="")
    private String oneway;

    @ApiModelProperty(value="")
    private Integer maxspeed;

    @ApiModelProperty(value="")
    private Long layer;

    @ApiModelProperty(value="")
    private String bridge;

    @ApiModelProperty(value="")
    private String tunnel;

    @ApiModelProperty(value="")
    private LocalDateTime createTime;

    @ApiModelProperty(value="")
    private LocalDateTime updateTime;

    /**
    * 版本号
    */
    @ApiModelProperty(value="版本号")
    private Long version;

    public String getRoadId() {
        return roadId;
    }

    public void setRoadId(String roadId) {
        this.roadId = roadId;
    }

    public String getOsmId() {
        return osmId;
    }

    public void setOsmId(String osmId) {
        this.osmId = osmId;
    }

    public String getRoadGeom() {
        return roadGeom;
    }

    public void setRoadGeom(String roadGeom) {
        this.roadGeom = roadGeom;
    }

    public String getOsmGeom() {
        return osmGeom;
    }

    public void setOsmGeom(String osmGeom) {
        this.osmGeom = osmGeom;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getFclass() {
        return fclass;
    }

    public void setFclass(String fclass) {
        this.fclass = fclass;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRef() {
        return ref;
    }

    public void setRef(String ref) {
        this.ref = ref;
    }

    public String getOneway() {
        return oneway;
    }

    public void setOneway(String oneway) {
        this.oneway = oneway;
    }

    public Integer getMaxspeed() {
        return maxspeed;
    }

    public void setMaxspeed(Integer maxspeed) {
        this.maxspeed = maxspeed;
    }

    public Long getLayer() {
        return layer;
    }

    public void setLayer(Long layer) {
        this.layer = layer;
    }

    public String getBridge() {
        return bridge;
    }

    public void setBridge(String bridge) {
        this.bridge = bridge;
    }

    public String getTunnel() {
        return tunnel;
    }

    public void setTunnel(String tunnel) {
        this.tunnel = tunnel;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String getRoadGeomWkt() {
        return roadGeomWkt;
    }

    public void setRoadGeomWkt(String roadGeomWkt) {
        this.roadGeomWkt = roadGeomWkt;
    }

    public String getOsmGeomWKt() {
        return osmGeomWKt;
    }

    public void setOsmGeomWKt(String osmGeomWKt) {
        this.osmGeomWKt = osmGeomWKt;
    }
}