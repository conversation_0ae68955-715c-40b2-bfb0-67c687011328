package com.hll.mapdataservice.common;

/**
 * @Author: ares.chen
 * @Since: 2021/9/28
 */
public enum LangEnum {
    HKG("hkg","CHT"),IND("idn","IND"),MYS("mys","MAY"),
    PHL("phl","ENG"),SGP("sgp","ENG"),THA("tha","THA"),
    TWN("twn","CHT"),VNM("vnm","VIE"),BRA("bra","POR"),
    MEX("mex","SPA"),INDIA("ind","ENG"),BAN("ban","ENG"),
    TUR("tur","TUR");

    private String countryName;
    private String defaultLang;

    LangEnum(String countryName, String defaultLang) {
        this.countryName = countryName;
        this.defaultLang = defaultLang;
    }

    public String getCountryName() {
        return countryName;
    }

    public String getDefaultLang() {
        return defaultLang;
    }

    public static String getLangByCName(String countryName) {
        if (countryName == null) {
            return null;
        }
        LangEnum[] values = LangEnum.values();
        for (LangEnum langEnum : values) {
            if (countryName.equals(langEnum.getCountryName())) {
                return langEnum.getDefaultLang();
            }
        }
        return null;
    }
}
