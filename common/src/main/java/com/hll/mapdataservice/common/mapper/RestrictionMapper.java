package com.hll.mapdataservice.common.mapper;

import com.hll.mapdataservice.common.entity.Restriction;

public interface RestrictionMapper extends RootMapper<Restriction> {
    int deleteByPrimaryKey(String id);

    int insert(Restriction record);

    int insertSelective(Restriction record);

    Restriction selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(Restriction record);

    int updateByPrimaryKey(Restriction record);
}