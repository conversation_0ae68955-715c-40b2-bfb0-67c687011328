package com.hll.mapdataservice.common.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.hll.mapdataservice.common.entity.HereThaStreets;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hll.mapdataservice.common.vo.LinkMainVo;
import com.hll.mapdataservice.common.vo.ManeuverVo;
import com.hll.mapdataservice.common.vo.RoadProperty;
import com.hll.mapdataservice.common.vo.hereManeuverVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-12
 */
@DS("db3")
public interface HereThaStreetsMapper extends BaseMapper<HereThaStreets> {

    @Select("Select * from ( SELECT c.link_id,c.cond_id,c.cond_type, r.seq_number,r.man_linkid,st_astext(h.geometry) as geom,h.\"ST_NAME\" as name FROM rdms r \n" +
            "left JOIN cdms c ON r.link_id=c.link_id and r.cond_id = c.cond_id\n" +
            "LEFT JOIN here_tha_streets h on r.man_linkid=h.\"LINK_ID\"\n" +
            "WHERE c.cond_type =7 order by c.link_id,c.cond_id,c.cond_type,r.seq_number) t ${ew.customSqlSegment}")
    List<hereManeuverVo> getRoadInfo(@Param(Constants.WRAPPER) Wrapper wrapper);

    @Select("select link_id, cond_id as id, cond_type as feat_type from cdms ${ew.customSqlSegment}")
    List<ManeuverVo> getHereManeuver(@Param(Constants.WRAPPER) Wrapper wrapper);

    @Select("select r.man_linkid as feat_id, r.seq_number as maneuver_seq, st_astext(h.geometry) as geom from rdms r " +
            "LEFT JOIN here_tha_streets h on r.man_linkid=h.\"LINK_ID\" \n" +
            "${ew.customSqlSegment}  order by r.seq_number")
    List<LinkMainVo> getHereManeuverLink(@Param(Constants.WRAPPER) Wrapper wrapper);
}
