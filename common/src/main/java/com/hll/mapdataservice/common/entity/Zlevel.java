package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="ZlevelM对象", description="")
@Data
@ToString
public class Zlevel extends Model<Zlevel> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String hllLinkid;

    private Integer pointNum;

    private String hllNodeid;

    private String geometry;

    private String z;

    private String linktype;

    private String intrsect;

    private Integer dotShape;

    private String aligned;

    private String levelId;

    private String datasource;

    private LocalDateTime upDate;

    private Integer status;

    private Integer area;
}

