package com.hll.mapdataservice.common.mapper;

import com.hll.mapdataservice.common.entity.NodeM;
import org.apache.ibatis.cursor.Cursor;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-28
 */
//@DS("db8")
public interface NodeMMapper extends RootMapper<NodeM> {
    Cursor<NodeM> streamQueryNode();

    void saveBatch(List<NodeM> nodes);

    List<NodeM> selectUnhandleIds(int step, int i);
}
