package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@TableName("rule_e")
public class RuleE extends Model<RuleE> {

    @TableId(value = "rule_id", type = IdType.INPUT)
    private String ruleId;
    private String inlinkId;
    private String nodeId;
    private String outlinkId;
    private String pass;
    private String pass2;
    private Integer flag;
    private String vperiod;
    private String vehclType;
    private String vpdir;
    private String meshList;
    private String memo;
    private String cp;
    private String datasource;
    private LocalDateTime upDate;
    private Integer status;
    private String linkAngle;
    private Long olv = 0L;
    private String tileId;
    private Integer tileType;
    private String taskId;
    private Integer ruleInfo;
}
