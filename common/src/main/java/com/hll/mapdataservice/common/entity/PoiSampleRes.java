package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 *<AUTHOR>
 *@Date 2024/9/9
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PoiSampleRes extends Model<PoiSampleRes> {
    @TableId
    private String pickupId;
    private String pickupNameHlang;
    private String pickupAddrHlang;
    private Double startLatitude;
    private Double startLongitude;
    private String googleName;
    private String googleAddress;
    private String googlePoiType;
    private Double googleLatitude;
    private Double googleLongitude;
    private String herePoi50m;
    private String herePoi100m;
    private String herePoi200m;
    private String herePoi200m8;
    private String hereAsPoi200m;
    private String hereHn50m;
    private String hereHn100m;
    private String hereHn200m;
    private String hereHn200m8;
}
