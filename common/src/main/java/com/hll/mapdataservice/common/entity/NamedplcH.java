package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-08
 */
@ApiModel(value="NamedplcH对象", description="")
public class NamedplcH extends Model<NamedplcH> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "gid", type = IdType.AUTO)
    private Integer gid;

    private Long linkId;

    private Long poiId;

    private Integer seqNum;

    private Integer facType;

    private String poiName;

    private String poiLangcd;

    private String poiNmtype;

    private String poiStNum;

    private String stName;

    private String stLangcd;

    private String poiStSd;

    private String accType;

    private String phNumber;

    private Long chainId;

    private String natImport;

    @TableField(value = "private")
    private String privateInfo;

    private String inVicin;

    private Integer numParent;

    private Integer numChild;

    private Integer percfrref;

    private Long population;

    private String capital;

    private Long vancityId;

    private String actAddr;

    private String actLangcd;

    private String actStNam;

    private String actStNum;

    private String actAdmin;

    private String actPostal;

    private String claimedBy;

    private String controlBy;

    private String geom;

    public Integer getGid() {
        return gid;
    }

    public void setGid(Integer gid) {
        this.gid = gid;
    }
    public Long getLinkId() {
        return linkId;
    }

    public void setLinkId(Long linkId) {
        this.linkId = linkId;
    }
    public Long getPoiId() {
        return poiId;
    }

    public void setPoiId(Long poiId) {
        this.poiId = poiId;
    }
    public Integer getSeqNum() {
        return seqNum;
    }

    public void setSeqNum(Integer seqNum) {
        this.seqNum = seqNum;
    }
    public Integer getFacType() {
        return facType;
    }

    public void setFacType(Integer facType) {
        this.facType = facType;
    }
    public String getPoiName() {
        return poiName;
    }

    public void setPoiName(String poiName) {
        this.poiName = poiName;
    }
    public String getPoiLangcd() {
        return poiLangcd;
    }

    public void setPoiLangcd(String poiLangcd) {
        this.poiLangcd = poiLangcd;
    }
    public String getPoiNmtype() {
        return poiNmtype;
    }

    public void setPoiNmtype(String poiNmtype) {
        this.poiNmtype = poiNmtype;
    }
    public String getPoiStNum() {
        return poiStNum;
    }

    public void setPoiStNum(String poiStNum) {
        this.poiStNum = poiStNum;
    }
    public String getStName() {
        return stName;
    }

    public void setStName(String stName) {
        this.stName = stName;
    }
    public String getStLangcd() {
        return stLangcd;
    }

    public void setStLangcd(String stLangcd) {
        this.stLangcd = stLangcd;
    }
    public String getPoiStSd() {
        return poiStSd;
    }

    public void setPoiStSd(String poiStSd) {
        this.poiStSd = poiStSd;
    }
    public String getAccType() {
        return accType;
    }

    public void setAccType(String accType) {
        this.accType = accType;
    }
    public String getPhNumber() {
        return phNumber;
    }

    public void setPhNumber(String phNumber) {
        this.phNumber = phNumber;
    }
    public Long getChainId() {
        return chainId;
    }

    public void setChainId(Long chainId) {
        this.chainId = chainId;
    }
    public String getNatImport() {
        return natImport;
    }

    public void setNatImport(String natImport) {
        this.natImport = natImport;
    }
    public String getPrivateInfo() {
        return privateInfo;
    }

    public void setPrivateInfo(String privateInfo) {
        this.privateInfo = privateInfo;
    }
    public String getInVicin() {
        return inVicin;
    }

    public void setInVicin(String inVicin) {
        this.inVicin = inVicin;
    }
    public Integer getNumParent() {
        return numParent;
    }

    public void setNumParent(Integer numParent) {
        this.numParent = numParent;
    }
    public Integer getNumChild() {
        return numChild;
    }

    public void setNumChild(Integer numChild) {
        this.numChild = numChild;
    }
    public Integer getPercfrref() {
        return percfrref;
    }

    public void setPercfrref(Integer percfrref) {
        this.percfrref = percfrref;
    }
    public Long getPopulation() {
        return population;
    }

    public void setPopulation(Long population) {
        this.population = population;
    }
    public String getCapital() {
        return capital;
    }

    public void setCapital(String capital) {
        this.capital = capital;
    }
    public Long getVancityId() {
        return vancityId;
    }

    public void setVancityId(Long vancityId) {
        this.vancityId = vancityId;
    }
    public String getActAddr() {
        return actAddr;
    }

    public void setActAddr(String actAddr) {
        this.actAddr = actAddr;
    }
    public String getActLangcd() {
        return actLangcd;
    }

    public void setActLangcd(String actLangcd) {
        this.actLangcd = actLangcd;
    }
    public String getActStNam() {
        return actStNam;
    }

    public void setActStNam(String actStNam) {
        this.actStNam = actStNam;
    }
    public String getActStNum() {
        return actStNum;
    }

    public void setActStNum(String actStNum) {
        this.actStNum = actStNum;
    }
    public String getActAdmin() {
        return actAdmin;
    }

    public void setActAdmin(String actAdmin) {
        this.actAdmin = actAdmin;
    }
    public String getActPostal() {
        return actPostal;
    }

    public void setActPostal(String actPostal) {
        this.actPostal = actPostal;
    }
    public String getClaimedBy() {
        return claimedBy;
    }

    public void setClaimedBy(String claimedBy) {
        this.claimedBy = claimedBy;
    }
    public String getControlBy() {
        return controlBy;
    }

    public void setControlBy(String controlBy) {
        this.controlBy = controlBy;
    }
    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }

    @Override
    protected Serializable pkVal() {
        return this.gid;
    }

    @Override
    public String toString() {
        return "NamedplcH{" +
            "gid=" + gid +
            ", linkId=" + linkId +
            ", poiId=" + poiId +
            ", seqNum=" + seqNum +
            ", facType=" + facType +
            ", poiName=" + poiName +
            ", poiLangcd=" + poiLangcd +
            ", poiNmtype=" + poiNmtype +
            ", poiStNum=" + poiStNum +
            ", stName=" + stName +
            ", stLangcd=" + stLangcd +
            ", poiStSd=" + poiStSd +
            ", accType=" + accType +
            ", phNumber=" + phNumber +
            ", chainId=" + chainId +
            ", natImport=" + natImport +
            ", private=" + privateInfo +
            ", inVicin=" + inVicin +
            ", numParent=" + numParent +
            ", numChild=" + numChild +
            ", percfrref=" + percfrref +
            ", population=" + population +
            ", capital=" + capital +
            ", vancityId=" + vancityId +
            ", actAddr=" + actAddr +
            ", actLangcd=" + actLangcd +
            ", actStNam=" + actStNam +
            ", actStNum=" + actStNum +
            ", actAdmin=" + actAdmin +
            ", actPostal=" + actPostal +
            ", claimedBy=" + claimedBy +
            ", controlBy=" + controlBy +
            ", geom=" + geom +
        "}";
    }
}
