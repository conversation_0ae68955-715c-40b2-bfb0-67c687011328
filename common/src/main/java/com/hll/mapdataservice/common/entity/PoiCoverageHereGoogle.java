package com.hll.mapdataservice.common.entity;

import java.sql.Timestamp;
import java.time.LocalDateTime;

public class PoiCoverageHereGoogle {
    private String id;
    private String type;
    private String market;
    private String orderPoiName;
    private Double orderLongitude;
    private Double orderLatitude;
    private String herePoiName;
    private String herePoiNameList;
    private String googlePoiName;
    private String googlePoiNameList;
    private Double hereGooglePoiNameSimilarity=0.0;
    private Integer hereGooglePoiNameMatch=0;
    private String radius;
    private Double similarity;
    private LocalDateTime upTime;

    // Constructors, Getters, and Setters
    public PoiCoverageHereGoogle() {}

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getOrderPoiName() {
        return orderPoiName;
    }

    public void setOrderPoiName(String orderPoiName) {
        this.orderPoiName = orderPoiName;
    }

    public Double getOrderLongitude() {
        return orderLongitude;
    }

    public void setOrderLongitude(Double orderLongitude) {
        this.orderLongitude = orderLongitude;
    }

    public Double getOrderLatitude() {
        return orderLatitude;
    }

    public void setOrderLatitude(Double orderLatitude) {
        this.orderLatitude = orderLatitude;
    }

    public String getHerePoiName() {
        return herePoiName;
    }

    public void setHerePoiName(String herePoiName) {
        this.herePoiName = herePoiName;
    }

    public String getHerePoiNameList() {
        return herePoiNameList;
    }

    public void setHerePoiNameList(String herePoiNameList) {
        this.herePoiNameList = herePoiNameList;
    }

    public String getGooglePoiName() {
        return googlePoiName;
    }

    public void setGooglePoiName(String googlePoiName) {
        this.googlePoiName = googlePoiName;
    }

    public String getGooglePoiNameList() {
        return googlePoiNameList;
    }

    public void setGooglePoiNameList(String googlePoiNameList) {
        this.googlePoiNameList = googlePoiNameList;
    }

    public Double getHereGooglePoiNameSimilarity() {
        return hereGooglePoiNameSimilarity;
    }

    public void setHereGooglePoiNameSimilarity(Double hereGooglePoiNameSimilarity) {
        this.hereGooglePoiNameSimilarity = hereGooglePoiNameSimilarity;
    }

    public Integer getHereGooglePoiNameMatch() {
        return hereGooglePoiNameMatch;
    }

    public void setHereGooglePoiNameMatch(Integer hereGooglePoiNameMatch) {
        this.hereGooglePoiNameMatch = hereGooglePoiNameMatch;
    }

    public String getRadius() {
        return radius;
    }

    public void setRadius(String radius) {
        this.radius = radius;
    }

    public Double getSimilarity() {
        return similarity;
    }

    public void setSimilarity(Double similarity) {
        this.similarity = similarity;
    }

    public LocalDateTime getUpTime() {
        return upTime;
    }

    public void setUpTime(LocalDateTime upTime) {
        this.upTime = upTime;
    }
}
