package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Classname HnPointAddress
 * @Description HLL 门址
 * @Date 2021/12/9 3:47 下午
 * @Created by qunfu
 */
@ApiModel(value = "HnPointAddress")
@TableName("hn_point_address")
public class HnPointAddress extends Model<HnPointAddress> {

    @ApiModelProperty(value = "")
    @TableId(value = "hn_id")
    private String hnId;

    @ApiModelProperty(value = "")
    private String sourceId;

    @ApiModelProperty(value = "")
    private String linkId;

    @ApiModelProperty(value = "")
    private String side;

    @ApiModelProperty(value = "")
    private String streetName;

    @ApiModelProperty(value = "")
    private String stNameTrans;

    @ApiModelProperty(value = "")
    private String stNameAlias;

    @ApiModelProperty(value = "")
    private String stNameAliasTrans;

    @ApiModelProperty(value = "")
    private String stNameStale;

    @ApiModelProperty(value = "")
    private String stNameStaleTrans;

    @ApiModelProperty(value = "")
    private String address;

    @ApiModelProperty(value = "")
    private String bldgNm;

    @ApiModelProperty(value = "")
    private String bldgNmTrans;

    @ApiModelProperty(value = "")
    private String transType;

    @ApiModelProperty(value = "")
    private String addressType;

    @ApiModelProperty(value = "")
    private String languageCode;

    @ApiModelProperty(value = "")
    private String dispLon;

    @ApiModelProperty(value = "")
    private String dispLat;

    @ApiModelProperty(value = "")
    private String adminLevel1Name;

    @ApiModelProperty(value = "")
    private String adminLevel1Govncode;

    @ApiModelProperty(value = "")
    private String adminLevel2Name;

    @ApiModelProperty(value = "")
    private String adminLevel2Govncode;

    @ApiModelProperty(value = "")
    private String adminLevel3Name;

    @ApiModelProperty(value = "")
    private String adminLevel3Govncode;

    @ApiModelProperty(value = "")
    private String adminLevel4Name;

    @ApiModelProperty(value = "")
    private String adminLevel4Govncode;

    @ApiModelProperty(value = "")
    private String adminLevel5Name;

    @ApiModelProperty(value = "")
    private String adminLevel5Govncode;

    @ApiModelProperty(value = "")
    @TableField(value = "ar_link_id")
    private String arLinkId;

    @ApiModelProperty(value = "")
    private String arSide;

    @ApiModelProperty(value = "")
    private String version;

    @ApiModelProperty(value = "")
    private String geom;


    public String getHnId() {
        return hnId;
    }

    public void setHnId(String hnId) {
        this.hnId = hnId;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getLinkId() {
        return linkId;
    }

    public void setLinkId(String linkId) {
        this.linkId = linkId;
    }

    public String getSide() {
        return side;
    }

    public void setSide(String side) {
        this.side = side;
    }

    public String getStreetName() {
        return streetName;
    }

    public void setStreetName(String streetName) {
        this.streetName = streetName;
    }

    public String getStNameTrans() {
        return stNameTrans;
    }

    public void setStNameTrans(String stNameTrans) {
        this.stNameTrans = stNameTrans;
    }

    public String getStNameAlias() {
        return stNameAlias;
    }

    public void setStNameAlias(String stNameAlias) {
        this.stNameAlias = stNameAlias;
    }

    public String getStNameAliasTrans() {
        return stNameAliasTrans;
    }

    public void setStNameAliasTrans(String stNameAliasTrans) {
        this.stNameAliasTrans = stNameAliasTrans;
    }

    public String getStNameStale() {
        return stNameStale;
    }

    public void setStNameStale(String stNameStale) {
        this.stNameStale = stNameStale;
    }

    public String getStNameStaleTrans() {
        return stNameStaleTrans;
    }

    public void setStNameStaleTrans(String stNameStaleTrans) {
        this.stNameStaleTrans = stNameStaleTrans;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getBldgNm() {
        return bldgNm;
    }

    public void setBldgNm(String bldgNm) {
        this.bldgNm = bldgNm;
    }

    public String getBldgNmTrans() {
        return bldgNmTrans;
    }

    public void setBldgNmTrans(String bldgNmTrans) {
        this.bldgNmTrans = bldgNmTrans;
    }

    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }

    public String getAddressType() {
        return addressType;
    }

    public void setAddressType(String addressType) {
        this.addressType = addressType;
    }

    public String getLanguageCode() {
        return languageCode;
    }

    public void setLanguageCode(String languageCode) {
        this.languageCode = languageCode;
    }

    public String getDispLon() {
        return dispLon;
    }

    public void setDispLon(String dispLon) {
        this.dispLon = dispLon;
    }

    public String getDispLat() {
        return dispLat;
    }

    public void setDispLat(String dispLat) {
        this.dispLat = dispLat;
    }

    public String getAdminLevel1Name() {
        return adminLevel1Name;
    }

    public void setAdminLevel1Name(String adminLevel1Name) {
        this.adminLevel1Name = adminLevel1Name;
    }

    public String getAdminLevel1Govncode() {
        return adminLevel1Govncode;
    }

    public void setAdminLevel1Govncode(String adminLevel1Govncode) {
        this.adminLevel1Govncode = adminLevel1Govncode;
    }

    public String getAdminLevel2Name() {
        return adminLevel2Name;
    }

    public void setAdminLevel2Name(String adminLevel2Name) {
        this.adminLevel2Name = adminLevel2Name;
    }

    public String getAdminLevel2Govncode() {
        return adminLevel2Govncode;
    }

    public void setAdminLevel2Govncode(String adminLevel2Govncode) {
        this.adminLevel2Govncode = adminLevel2Govncode;
    }

    public String getAdminLevel3Name() {
        return adminLevel3Name;
    }

    public void setAdminLevel3Name(String adminLevel3Name) {
        this.adminLevel3Name = adminLevel3Name;
    }

    public String getAdminLevel3Govncode() {
        return adminLevel3Govncode;
    }

    public void setAdminLevel3Govncode(String adminLevel3Govncode) {
        this.adminLevel3Govncode = adminLevel3Govncode;
    }

    public String getAdminLevel4Name() {
        return adminLevel4Name;
    }

    public void setAdminLevel4Name(String adminLevel4Name) {
        this.adminLevel4Name = adminLevel4Name;
    }

    public String getAdminLevel4Govncode() {
        return adminLevel4Govncode;
    }

    public void setAdminLevel4Govncode(String adminLevel4Govncode) {
        this.adminLevel4Govncode = adminLevel4Govncode;
    }

    public String getAdminLevel5Name() {
        return adminLevel5Name;
    }

    public void setAdminLevel5Name(String adminLevel5Name) {
        this.adminLevel5Name = adminLevel5Name;
    }

    public String getAdminLevel5Govncode() {
        return adminLevel5Govncode;
    }

    public void setAdminLevel5Govncode(String adminLevel5Govncode) {
        this.adminLevel5Govncode = adminLevel5Govncode;
    }

    public String getArLinkId() {
        return arLinkId;
    }

    public void setArLinkId(String arLinkId) {
        this.arLinkId = arLinkId;
    }

    public String getArSide() {
        return arSide;
    }

    public void setArSide(String arSide) {
        this.arSide = arSide;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }

    @Override
    public String toString() {
        return "HnPointAddress{" +
                "hnId='" + hnId + '\'' +
                ", sourceId='" + sourceId + '\'' +
                ", linkId='" + linkId + '\'' +
                ", side='" + side + '\'' +
                ", streetName='" + streetName + '\'' +
                ", stNameTrans='" + stNameTrans + '\'' +
                ", stNameAlias='" + stNameAlias + '\'' +
                ", stNameAliasTrans='" + stNameAliasTrans + '\'' +
                ", stNameStale='" + stNameStale + '\'' +
                ", stNameStaleTrans='" + stNameStaleTrans + '\'' +
                ", address='" + address + '\'' +
                ", bldgNm='" + bldgNm + '\'' +
                ", bldgNmTrans='" + bldgNmTrans + '\'' +
                ", transType='" + transType + '\'' +
                ", addressType='" + addressType + '\'' +
                ", languageCode='" + languageCode + '\'' +
                ", dispLon='" + dispLon + '\'' +
                ", dispLat='" + dispLat + '\'' +
                ", adminLevel1Name='" + adminLevel1Name + '\'' +
                ", adminLevel1Govncode='" + adminLevel1Govncode + '\'' +
                ", adminLevel2Name='" + adminLevel2Name + '\'' +
                ", adminLevel2Govncode='" + adminLevel2Govncode + '\'' +
                ", adminLevel3Name='" + adminLevel3Name + '\'' +
                ", adminLevel3Govncode='" + adminLevel3Govncode + '\'' +
                ", adminLevel4Name='" + adminLevel4Name + '\'' +
                ", adminLevel4Govncode='" + adminLevel4Govncode + '\'' +
                ", adminLevel5Name='" + adminLevel5Name + '\'' +
                ", adminLevel5Govncode='" + adminLevel5Govncode + '\'' +
                ", arLinkId='" + arLinkId + '\'' +
                ", arSide='" + arSide + '\'' +
                ", version='" + version + '\'' +
                ", geom='" + geom + '\'' +
                '}';
    }
}
