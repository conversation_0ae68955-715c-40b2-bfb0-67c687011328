package com.hll.mapdataservice.common.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
  *
  *
  * @Author: ares.chen
  * @Since: 2022/5/23
  */
@ApiModel(value="road_restriction_info")
@Data
public class RoadRestrictionInfo {
    @ApiModelProperty(value="")
    @ExcelProperty("Market")
    private String market;

    @ApiModelProperty(value="")
    @ExcelProperty("City")
    private String city;

    @ApiModelProperty(value="")
    @ExcelProperty("City id")
    private String cityId;

    @ApiModelProperty(value="")
    @ExcelProperty("name")
    private String restrictedRoadName;

    @ApiModelProperty(value="")
    @ExcelProperty("Restricted time")
    private String restrictedTime;

    @ApiModelProperty(value="")
    @ExcelProperty("WKT")
    private String roadWkt;

    @ApiModelProperty(value="")
    @ExcelProperty("Restricted service type")
    private String restrictedServiceType;

    @ApiModelProperty(value="")
    @ExcelProperty("Params(departure&weight)")
    private String callParams;

    @ApiModelProperty(value="")
    @ExcelProperty("Segment id")
    private String segmentId;
    @ApiModelProperty(value="")
    @ExcelProperty("Original Here route is correct(Y/N)")
    private String hereOriginalRouteRes;

    @ApiModelProperty(value="")
    @ExcelProperty("Final route is correct(Y/N)")
    private String finalRouteRes;

    @ApiModelProperty(value="")
    @ExcelProperty("second include ids")
    private String secondIncludeIds;

    @ApiModelProperty(value="")
    @ExcelProperty("third include ids")
    private String thirdIncludeIds;

    @ApiModelProperty(value="")
    @ExcelProperty("road_wkt_switch")
    private String roadWktSwitch;

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getRestrictedRoadName() {
        return restrictedRoadName;
    }

    public void setRestrictedRoadName(String restrictedRoadName) {
        this.restrictedRoadName = restrictedRoadName;
    }

    public String getRestrictedTime() {
        return restrictedTime;
    }

    public void setRestrictedTime(String restrictedTime) {
        this.restrictedTime = restrictedTime;
    }

    public String getRestrictedServiceType() {
        return restrictedServiceType;
    }

    public void setRestrictedServiceType(String restrictedServiceType) {
        this.restrictedServiceType = restrictedServiceType;
    }

    public String getRoadWkt() {
        return roadWkt;
    }

    public void setRoadWkt(String roadWkt) {
        this.roadWkt = roadWkt;
    }

    public String getSegmentId() {
        return segmentId;
    }

    public void setSegmentId(String segmentId) {
        this.segmentId = segmentId;
    }

    public String getCallParams() {
        return callParams;
    }

    public void setCallParams(String callParams) {
        this.callParams = callParams;
    }

    public String getHereOriginalRouteRes() {
        return hereOriginalRouteRes;
    }

    public void setHereOriginalRouteRes(String hereOriginalRouteRes) {
        this.hereOriginalRouteRes = hereOriginalRouteRes;
    }

    public String getFinalRouteRes() {
        return finalRouteRes;
    }

    public void setFinalRouteRes(String finalRouteRes) {
        this.finalRouteRes = finalRouteRes;
    }

    public String getSecondIncludeIds() {
        return secondIncludeIds;
    }

    public void setSecondIncludeIds(String secondIncludeIds) {
        this.secondIncludeIds = secondIncludeIds;
    }

    public String getThirdIncludeIds() {
        return thirdIncludeIds;
    }

    public void setThirdIncludeIds(String thirdIncludeIds) {
        this.thirdIncludeIds = thirdIncludeIds;
    }

    public String getRoadWktSwitch() {
        return roadWktSwitch;
    }

    public void setRoadWktSwitch(String roadWktSwitch) {
        this.roadWktSwitch = roadWktSwitch;
    }
}