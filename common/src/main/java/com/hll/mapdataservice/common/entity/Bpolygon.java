package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-02
 */
@ApiModel(value="Bpolygon对象", description="")
public class Bpolygon extends Model<Bpolygon> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getDatasource() {
        return datasource;
    }

    public void setDatasource(String datasource) {
        this.datasource = datasource;
    }

    public LocalDateTime getUpDate() {
        return upDate;
    }

    public void setUpDate(LocalDateTime upDate) {
        this.upDate = upDate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    private String sourceId;

    private String kind;

    private String name;

    private String nmLangcd;

    private String nmTr;

    private String transType;

    private String dispClass;

    private String poiId;

    private String geometry;

    private Integer level;

    private String datasource;

    private LocalDateTime upDate;

    private Integer status;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getKind() {
        return kind;
    }

    public void setKind(String kind) {
        this.kind = kind;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public String getNmLangcd() {
        return nmLangcd;
    }

    public void setNmLangcd(String nmLangcd) {
        this.nmLangcd = nmLangcd;
    }
    public String getNmTr() {
        return nmTr;
    }

    public void setNmTr(String nmTr) {
        this.nmTr = nmTr;
    }
    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }
    public String getDispClass() {
        return dispClass;
    }

    public void setDispClass(String dispClass) {
        this.dispClass = dispClass;
    }
    public String getPoiId() {
        return poiId;
    }

    public void setPoiId(String poiId) {
        this.poiId = poiId;
    }
    public String getGeometry() {
        return geometry;
    }

    public void setGeometry(String geometry) {
        this.geometry = geometry;
    }
    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "Bpolygon{" +
            "id=" + id +
            ", kind=" + kind +
            ", name=" + name +
            ", nmLangcd=" + nmLangcd +
            ", nmTr=" + nmTr +
            ", transType=" + transType +
            ", dispClass=" + dispClass +
            ", poiId=" + poiId +
            ", geometry=" + geometry +
            ", level=" + level +
        "}";
    }
}
