package com.hll.mapdataservice.common.utils;

import ch.ethz.ssh2.Session;
import ch.ethz.ssh2.StreamGobbler;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.toolkit.AES;
import com.hll.mapdataservice.common.entity.Location;
import com.uber.h3core.H3Core;
import com.uber.h3core.util.LatLng;
import com.vividsolutions.jts.algorithm.match.HausdorffSimilarityMeasure;
import com.vividsolutions.jts.geom.Coordinate;
import com.vividsolutions.jts.geom.Geometry;
import com.vividsolutions.jts.io.ParseException;
import com.vividsolutions.jts.io.WKTReader;
import io.minio.BucketExistsArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.UploadObjectArgs;
import org.locationtech.jts.geom.GeometryFactory;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import ch.ethz.ssh2.Connection;
import org.springframework.web.*;
import org.springframework.web.multipart.MultipartFile;

import static cn.hutool.core.util.NumberUtil.*;

public class CommonUtils {
    public final static boolean isNumeric(String s) {
        if (s != null && !"".equals(s.trim())) {
            return s.matches("^[0-9]*$");
        } else {
            return false;
        }
    }

    public final static boolean isEng(String s) {
        if (s != null && !"".equals(s.trim())) {
            return s.matches("^[a-zA-Z]*$");
        } else {
            return false;
        }
    }

    public static String getDsbyCountry(String country, boolean isSource) {
        if (isSource) {
            String ds = "";
            switch (country) {
                case "tom_phl":
                    return "db29";
                case "phl":
                    return "db1";
                case "tha":
                    return "db2";
                case "vnm":
                    return "db3";
                case "hkg":
                    return "db7";
                case "twn":
                    return "db8";
                case "sgp":
                    return "db11";
                case "mys":
                    return "db12";
                case "idn":
                    return "db15";
                case "bra":
                    return "db17";
                case "mex":
                    return "db19";
                case "phl-tt":
                    return "db22";
                case "ind":
                    return "db23";
                case "ban":
                    return "db25";
                case "tur":
                    return "db27";
                default:
                    return "";
            }
        } else {
            switch (country) {
                case "phl":
                    return "db4";
                case "phl-tt":
                    return "db4";
                case "tha":
                    return "db5";
                case "vnm":
                    return "db6";
                case "vnm-o":
                    return "db10";
                case "hkg":
                    return "db9";
                case "twn":
                    return "db10";
                case "sgp":
                    return "db13";
                case "mys":
                    return "db14";
                case "idn":
                    return "db16";
                //case "bra":
                //    return "db18";
                case "mex":
                    return "db20";
                case "bra":
                    return "db18";
                case "ind":
                    return "db24";
                case "ban":
                    return "db26";
                case "phl-o":
                    return "db22";
                case "tur":
                    return "db28";
                case "base":
                    return "db99";
                default:
                    return "";
            }
        }
    }


    public static String getDsbyCountry(String country, String dataVersion, boolean isSource) {
        if (isSource) {
            if ("2024_q1".equals(dataVersion)){
                switch (country) {
                    case "phl":
                        return "db1";
                    case "tha":
                        return "db2";
                    case "vnm":
                        return "db3";
                    case "hkg":
                        return "db7";
                    case "twn":
                        return "db8";
                    case "sgp":
                        return "db11";
                    case "mys":
                        return "db12";
                    case "idn":
                        return "db15";
                    case "bra":
                        return "db17";
                    case "mex":
                        return "db19";
                    case "ind":
                        return "db23";
                    default:
                        return "";
                }
            }
            if ("2023_q2".equals(dataVersion)){
                switch (country) {
                    case "phl":
                        return "db25";
                    case "tha":
                        return "db26";
                    case "vnm":
                        return "db27";
                    case "hkg":
                        return "db28";
                    case "twn":
                        return "db29";
                    case "sgp":
                        return "db30";
                    case "mys":
                        return "db31";
                    case "idn":
                        return "db32";
                    case "bra":
                        return "db33";
                    case "mex":
                        return "db34";
                    case "ind":
                        return "db35";
                    default:
                        return "";
                }
            }
        } else {
            if("2025_q1".equals(dataVersion)){
                switch (country) {
                    case "phl":
                        return "db101";
                    case "tha":
                        return "db102";
                    case "vnm":
                        return "db103";
                    case "hkg":
                        return "db104";
                    case "twn":
                        return "db105";
                    case "sgp":
                        return "db106";
                    case "mys":
                        return "db107";
                    case "ind":
                        return "db108";
                    //case "bra":
                    //    return "db18";
                    case "mex":
                        return "db109";
                    case "bra":
                        return "db110";
                    case "india":
                        return "db111";
                    case "ban":
                        return "db112";
                    case "phl-o":
                        return "db22";
                    default:
                        return "";
                }
            }
            if ("2025_q2".equals(dataVersion)){
                switch (country) {
                    case "phl":
                        return "db121";
                    case "tha":
                        return "db122";
                    case "vnm":
                        return "db123";
                    case "vnm-o":
                        return "db10";
                    case "hkg":
                        return "db124";
                    case "twn":
                        return "db125";
                    case "sgp":
                        return "db126";
                    case "mys":
                        return "db127";
                    case "ind":
                        return "db128";
                    //case "bra":
                    //    return "db18";
                    case "mex":
                        return "db129";
                    case "bra":
                        return "db130";
                    case "india":
                        return "db131";
                    case "ban":
                        return "db132";
                    case "tur":
                        return "db133";
                    case "phl-o":
                        return "db22";
                    default:
                        return "";
                }
            }
            if ("2025_q3".equals(dataVersion)){
                switch (country) {
                    case "phl":
                        return "db141";
                    case "tha":
                        return "db142";
                    case "vnm":
                        return "db143";
                    case "vnm-o":
                        return "db10";
                    case "hkg":
                        return "db144";
                    case "twn":
                        return "db145";
                    case "sgp":
                        return "db146";
                    case "mys":
                        return "db147";
                    case "ind":
                        return "db148";
                    //case "bra":
                    //    return "db18";
                    case "mex":
                        return "db149";
                    case "bra":
                        return "db150";
                    case "india":
                        return "db151";
                    case "ban":
                        return "db152";
                    case "tur":
                        return "db153";
                    case "phl-o":
                        return "db22";
                    default:
                        return "";
                }
            }
            if ("2024_q4".equals(dataVersion)){
                switch (country) {
                    case "phl":
                        return "db161";
                    case "tha":
                        return "db162";
                    case "vnm":
                        return "db163";
                    case "vnm-o":
                        return "db10";
                    case "hkg":
                        return "db164";
                    case "twn":
                        return "db165";
                    case "sgp":
                        return "db166";
                    case "mys":
                        return "db167";
                    case "ind":
                        return "db168";
                    //case "bra":
                    //    return "db18";
                    case "mex":
                        return "db169";
                    case "bra":
                        return "db170";
                    case "india":
                        return "db171";
                    case "ban":
                        return "db172";
                    case "tur":
                        return "db173";
                    case "phl-o":
                        return "db22";
                    default:
                        return "";
                }
            }
        }
        return "";
    }

    public static String getCityLevelByCountry(String country) {
        String cityLevel = "";
        switch (country) {
            case "hkg":
            case "ind":
            case "mys":
            case "mex":
            case "sgp":
            case "tha":
            case "tur":
            case "vnm":
                return "l3";
            case "phl":
            case "bra":
            case "india":
                return "l4";
            case "twn":
                return "l2";
            default:
                return "";
        }
    }
    public static float levenshtein(String str1, String str2) {
        if (StrUtil.isEmpty(str1)||StrUtil.isEmpty(str2)) {
            return 0;
        }
        //计算两个字符串的长度。
        int len1 = str1.length();
        int len2 = str2.length();
        //建立上面说的数组，比字符长度大一个空间
        int[][] dif = new int[len1 + 1][len2 + 1];
        //赋初值，步骤B。
        for (int a = 0; a <= len1; a++) {
            dif[a][0] = a;
        }
        for (int a = 0; a <= len2; a++) {
            dif[0][a] = a;
        }
        //计算两个字符是否一样，计算左上的值
        char[] ch1 = str1.toCharArray();
        char[] ch2 = str2.toCharArray();
        int temp;
        for (int i = 1; i <= len1; i++) {
            for (int j = 1; j <= len2; j++) {
                if (ch1[i - 1] == ch2[j - 1]) {
                    temp = 0;
                } else {
                    temp = 1;
                }
                //取三个值中最小的
                int temp1 = dif[i - 1][j - 1] + temp;
                int temp2 = dif[i][j - 1] + 1;
                int temp3 = dif[i - 1][j] + 1;
                int arr[] = new int[]{temp1, temp2, temp3};

                dif[i][j] = min(arr);
            }
        }
        //计算相似度
        float similarity = 1 - (float) dif[len1][len2] / Math.max(str1.length(), str2.length());
        return similarity;
    }

    public static synchronized double calRoadSimilarity(String line1, String line2) {
        HausdorffSimilarityMeasure hausdorffSimilarityMeasure = new HausdorffSimilarityMeasure();
        WKTReader wktReader = new WKTReader();
        Geometry geometry1 = null;
        Geometry geometry2 = null;
        try {
            geometry1 = wktReader.read(line1);
            geometry2 = wktReader.read(line2);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return hausdorffSimilarityMeasure.measure(geometry1, geometry2);
    }

    /**
     * 提取wkt形式的坐标，取起点与终点，转为Location
     *
     * @param geomWkt   MULTILINESTRING((108.19109 12.30391,108.19136 12.30389,108.19178 12.30428))
     * @param isReverse 反转标识，true为反转，false不反转
     * @return 返回新的起点，终点坐标集合
     */
    public static List<Location> extractCoord(String geomWkt, boolean isReverse) {
        List<Location> resList = new ArrayList<>();
        String subStr = StrUtil.subBetween(geomWkt, "(", ")");
        if (subStr.contains("(") || subStr.contains(")")) {
            subStr = StrUtil.removeAny(subStr, "(", ")");
        }
        String[] splitCoord = subStr.split(",");
        String beginStr = splitCoord[0];
        String endStr = splitCoord[splitCoord.length - 1];

        Location startLocation = new Location();
        startLocation.setLatitude(Double.valueOf(isReverse ? endStr.split(" ")[1] : beginStr.split(" ")[1]));
        startLocation.setLongitude(Double.valueOf(isReverse ? endStr.split(" ")[0] : beginStr.split(" ")[0]));
        Location endLocation = new Location();
        endLocation.setLatitude(Double.valueOf(isReverse ? beginStr.split(" ")[1] : endStr.split(" ")[1]));
        endLocation.setLongitude(Double.valueOf(isReverse ? beginStr.split(" ")[0] : endStr.split(" ")[0]));
        resList.add(startLocation);
        resList.add(endLocation);
        return resList;
    }

    public static void main(String[] args) {

        sshDbdump();

        List<String> linkIds = Arrays.asList("111111111,22222222,3333333,4444444".split(",").clone());

        long[] result = linkIds.stream().mapToLong(Long::valueOf).toArray();

        List<Long> resultLont0 = CollectionUtils.arrayToList(result);

        List<Long> resultLong1 = linkIds.stream().map(s -> Long.parseLong(s)).collect(Collectors.toList());

        //List<Long> resultLont = Arrays.asList(result);
        //String a = "MULTILINESTRING((108.19109 12.30391,108.19136 12.30389,108.19178 12.30428))";
        //System.out.println(extractCoord(a, true));

        //System.out.println(calAngle(12.30391, 108.19109, 12.30428, 108.19178));
        //System.out.println(calRoadSimilarity("MULTILINESTRING((106.6413849 10.6824461,106.6413062 10.6824016))","MULTILINESTRING((106.64094 10.6822,106.64174 10.68264))"));
        String str = "7ps47042eb62b5e00947b2ec6d688d72429";
        String encrypt = encrypttoNumber("608jx7ps-47042eb62b5e00947b2ec6d688d72429");
        String xor = xor("608jx7ps-47042eb62b5e00947b2ec6d688d72429");
        String aesEncrypt = AES.encrypt("608jx7ps-47042eb62b5e00947b2ec6d688d72429", "1111111111111111");
        String base64Encrypt = Base64.encode("608jx7ps-47042eb62b5e00947b2ec6d688d72429");

        String aesDecrypt = AES.decrypt(aesEncrypt, "1111111111111111");
        System.out.println(toHexString(str).replaceAll("%", ""));
        System.out.println(toStringHex(toHexString(str).replaceAll("%", "")));
        System.out.println(encrypt);
        System.out.println(levenshtein("American Club", "American Club Hanoi"));


        //test backup&restore
        Date d = new Date(); // 备份文件名称
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = sdf.format(d) + ".tar";

        String dbname = "ipam";
        String username = "postgres";
        String dbpath = "d:\\Program Files\\PostgreSQL\\9.1\\bin\\";
        String backuppath = "d:\\" + filename;
        String host="";
        String port="";

        backupDB(dbname, username, host, port, backuppath, dbpath);

        backuppath = "d:\\20120826095554.tar";
        restoreDB(dbname, username, host,port,backuppath, dbpath);

    }

    public static double calAngle(double lat1, double lng1, double lat2, double lng2) {
        double x1 = lng1;
        double y1 = lat1;
        double x2 = lng2;
        double y2 = lat2;
        double pi = Math.PI;
        double w1 = y1 / 180 * pi;
        double j1 = x1 / 180 * pi;
        double w2 = y2 / 180 * pi;
        double j2 = x2 / 180 * pi;
        double ret;
        if (j1 == j2) {
            if (w1 > w2) {
                return 270; //北半球的情况，南半球忽略
            } else if (w1 < w2) {
                return 90;
            } else {
                return -1;//位置完全相同
            }
        }
        ret = 4 * Math.pow(Math.sin((w1 - w2) / 2), 2) - Math.pow(Math.sin((j1 - j2) / 2) * (Math.cos(w1) - Math.cos(w2)), 2);
        ret = Math.sqrt(ret);
        double temp = (Math.sin(Math.abs(j1 - j2) / 2) * (Math.cos(w1) + Math.cos(w2)));
        ret = ret / temp;
        ret = Math.atan(ret) / pi * 180;
        if (j1 > j2) // 1为参考点坐标
        {
            if (w1 > w2) {
                ret += 180;
            } else {
                ret = 180 - ret;
            }
        } else if (w1 > w2) {
            ret = 360 - ret;
        }
        return ret;
    }

    /**
     * 获取集合中重复的元素
     *
     * @param srcList
     * @return
     */
    public static List<String> getRepeatElement(List<String> srcList) {
        List<String> resList = new ArrayList<>();
        Map<String, List<String>> collect = srcList.stream().collect(Collectors.groupingBy(s -> s));
        for (Map.Entry<String, List<String>> entry : collect.entrySet()) {
            if (entry.getValue().size() > 1) {
                resList.add(entry.getKey());
            }
        }
        return resList;
    }

    /**
     * 获取集合中重复的元素
     *
     * @param srcList
     * @return
     */
    public static List<String> removeDuplicateElement(List<String> srcList) {
        List<String> resList = new ArrayList<>();
        Map<String, List<String>> collect = srcList.stream().collect(Collectors.groupingBy(s -> s));
        for (Map.Entry<String, List<String>> entry : collect.entrySet()) {
            if (entry.getValue().size()==1) {
                resList.add(entry.getKey());
            }
        }
        return resList;
    }

    public static String encrypttoNumber(String toEncrypt) {
        String[] toEncryptList = toEncrypt.split("");
        String encryptNumber = "";
        for (String chartoEncrypt : toEncryptList
        ) {
            if (isNumeric(chartoEncrypt)) {
                encryptNumber += "0" + chartoEncrypt;
            } else if (isEng(chartoEncrypt)) {
                encryptNumber += chartoEncrypt.getBytes()[0] - 86;
            } else {
                encryptNumber += 99;
            }
        }
        return encryptNumber;
    }

    public static String decrypttoEngNumber(String toDecrypt) {
        String decrypt = "";
        for (byte b : toDecrypt.getBytes()) {
            if (b < 10) {
            }
        }
        return decrypt;
    }

    public static String xor(String input) {
        char[] chs = input.toCharArray();//因为是对每一个字符进行加密，则需要转成数组
        for (int i = 0; i < chs.length; i++) {
            chs[i] = (char) (chs[i] ^ 100);//对每一位字符进行加密，即每个元素与100进行异或
        }
        return new String(chs);
    }

    /**
     * convert String to Hex
     *
     * @param str
     * @return
     */
    public static String toHexString(String str) {
        byte[] byStr = str.getBytes();

        return parseArr(byStr);
    }

    /**
     * convert Hex to String
     *
     * @param s
     * @return
     */
    public static String toStringHex(String s) {
        byte[] byStr = new byte[s.length() / 2];
        for (int i = 0; i < byStr.length; i++) {
            try {
                byStr[i] = (byte) (0xff & Integer.parseInt(s.substring(
                        i * 2, i * 2 + 2), 16));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        try {
            s = new String(byStr, "utf-8");// UTF-16le:Not
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        return s;
    }

    private static String parseArr(byte[] byStr) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < byStr.length; i++) {
            sb.append("%" + Integer.toHexString(byStr[i]));
        }

        return sb.toString();
    }

    public static Boolean backupDB(String dbname, String username, String host,String port,String backuppath,
                                   String dbpath) {
        // 本地测试用
        // username = "postgres";
        // String dbpath = "d:\\Program Files\\PostgreSQL\\9.1\\bin\\";

        boolean flag = true;// 备份是否成功
        //pg_dump -U postgres -h ************** -p 15999 -Ft here_vnm_q3 > /data/oversea/databasebackup/backup.tar
        Runtime rt = Runtime.getRuntime();// 得到jvm的运行环境
        Process process;
        StringBuffer cmdbuf = new StringBuffer();
        cmdbuf.append(dbpath);
        cmdbuf.append("pg_dump -U ");// 用户名
        cmdbuf.append(username);
        cmdbuf.append(" -h ");
        cmdbuf.append(host);
        cmdbuf.append(" -p ");
        cmdbuf.append(port);
        cmdbuf.append(" -E utf8 ");// 编码
        cmdbuf.append(dbname);
        cmdbuf.append(" -f ");
        cmdbuf.append(backuppath);

        try {
            // 调用 cmd:
            System.out.println(cmdbuf);
            process = rt.exec(cmdbuf.toString());

//            BufferedReader r = new BufferedReader(new InputStreamReader(process.getInputStream(),"gbk"));
//            String line = r.readLine();
//            while (line != null){
//                System.out.println(line);
//                line= r.readLine();
//            }
//
//            BufferedReader errread = new BufferedReader(new InputStreamReader(process.getErrorStream(),"gbk"));
//            String errLine = r.readLine();
//            while (errLine != null){
//                System.out.println(errLine);
//                line= r.readLine();
//            }
//            System.out.println("~~~~~~~~~~~~~~~~~~~~~~~~~~~");
//
//            //线程,用于输出外部程序的输出内容,防止阻塞
//            StreamGobbler errorGobbler = new StreamGobbler(process.getErrorStream());
//
//            //线程,用于输出外部程序的输出内容,防止阻塞
//            StreamGobbler outputGobbler = new StreamGobbler(process.getInputStream());
//
//            System.out.println( "+++++++++++++++++++++++++++");
//            errorGobbler.read();
//            outputGobbler.read();

            int exitVal = process.waitFor();
            InputStream in = process.getInputStream();
            BufferedReader read = new BufferedReader(new InputStreamReader(in));
            String line = null;
            while((line = read.readLine())!=null){
                System.out.println(line);
            }

            System.out.println(exitVal == 0 ? "备份成功" : "备份失败");

        } catch (Exception e) {
            flag = false;
            e.printStackTrace();
        }
        return flag;
    }

    public static boolean restoreDB(String dbname, String username, String host, String port, String backuppath,
                                    String dbpath) {

        boolean flag = true;// 恢复是否成功
        Runtime rt = Runtime.getRuntime();// 得到jvm的运行环境
        Process process;
        StringBuffer cmdTemp = new StringBuffer();// 命令模版
        cmdTemp.append(dbpath);
        cmdTemp.append("pg_restore -U ");
        cmdTemp.append(username);
        cmdTemp.append(" -h ");
        cmdTemp.append(host);
        cmdTemp.append(" -p ");
        cmdTemp.append(port);
        cmdTemp.append(" -c -d ");
        cmdTemp.append(" " + dbname + " ");
        cmdTemp.append(backuppath);
        try {
            process = rt.exec(cmdTemp.toString());// 还原数据库
        } catch (Exception e) {
            flag = false;
            e.printStackTrace();
        }
        return flag;
    }
    //通过ssh方式连接到服务器，执行dump命令；前提是可以ssh到服务器
    public static boolean sshDbdump() {
        String host = "**************";
        //int port = 15999;
        Connection conn = null;
        Session session = null;
        BufferedReader br = null;
        try {
            //建立连接
            conn = new Connection(host);
            conn.connect();
            //用户名和密码
            boolean status = conn.authenticateWithPassword("shawn2.zhang", "Zsh@198262>05");
            //链接是否成功
            if (status) {
                session = conn.openSession();
                //运行的命令
                //pg_dump -U postgres oesv11 -t oes.poi -f /home/<USER>/01.数据备份/poi.sql
                session.execCommand("pg_dump -U postgres -d hll_oversea_o_phl_q4 -f /data/oversea/databasebackup/hll_oversea_o_phl_q4.sql");
                InputStream stdout = new StreamGobbler(session.getStdout());
                br = new BufferedReader(new InputStreamReader(stdout));
                String line = null;
                while ((line = br.readLine()) != null) {
                    System.out.println(line);
                    System.out.println();
                }
                //return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (br != null) {
                try {
                    br.close();
                    session.close();
                    conn.close();
                    return true;
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return false;
    }

    public static Boolean minioUpload(String endPoint,String accessKey,String secretKey,String bucketName,
                                      String objectName,
                                      String filePath){
        try {
            System.out.println("create client");
            MinioClient minioClient = MinioClient.builder()
                    .endpoint(endPoint)
                    .credentials(accessKey,secretKey)
                    .build();
            System.out.println("create client success;endPoint is:" + endPoint + " client is:" + minioClient.toString());

            boolean found = minioClient.bucketExists(BucketExistsArgs.
                    builder().bucket(bucketName).build());
            if (!found){
                System.out.println("the bucket "+ bucketName + " not found; create new bucket");
                // 新建一个桶
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
            }


            System.out.println("start upload,bucket is:" + bucketName + ",object is:" + objectName + ",file is:" + filePath);
            //MultipartFile uploadFile = new MultipartFile(filePath);
            //minioClient.putObject(bucketName,objectName,new File(filePath).)
            minioClient.uploadObject(
                    UploadObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .filename(filePath)
                            .build()
            );

            System.out.println("上传成功");
            return true;

        }catch (Exception e){
            e.printStackTrace();
        }
        return false;
    }


    /**
     * 根据传入的几何（WKT）求质心，再根据分辨率计算 H3 索引
     *
     * @param wktGeometry 例如一个 Polygon 的 WKT
     * @param resolution  分辨率 (0 ~ 15), 例如 7
     * @return            返回 H3 hex string 索引，比如 "87283082fffffff"
     */
    public static String getH3IndexByCentroid(String wktGeometry, int resolution)  {
        // 1. 解析几何
        org.locationtech.jts.geom.GeometryFactory geometryFactory = new org.locationtech.jts.geom.GeometryFactory();
        org.locationtech.jts.io.WKTReader wktReader = new org.locationtech.jts.io.WKTReader(geometryFactory);
        org.locationtech.jts.geom.Geometry geometry = null;
        try {
            geometry = wktReader.read(wktGeometry);
        } catch (org.locationtech.jts.io.ParseException e) {
            throw new RuntimeException(e);
        }

        // 2. 获取质心坐标
        org.locationtech.jts.geom.Coordinate centroidCoord = geometry.getCentroid().getCoordinate();
        double lon = centroidCoord.x; // 经度
        double lat = centroidCoord.y; // 纬度

        // 3. 计算 H3 索引
        H3Core h3 = null;   // 可能抛出 IOException
        try {
            h3 = H3Core.newInstance();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        String h3Index = h3.latLngToCellAddress(lat, lon, resolution);

        return h3Index;
    }

    public static Long getH3IndexLongByCentroid(String wktGeometry, int resolution) throws Exception {
        // 1. 解析几何
        org.locationtech.jts.geom.GeometryFactory geometryFactory = new org.locationtech.jts.geom.GeometryFactory();
        org.locationtech.jts.io.WKTReader wktReader = new org.locationtech.jts.io.WKTReader(geometryFactory);
        org.locationtech.jts.geom.Geometry geometry = wktReader.read(wktGeometry);

        // 2. 获取质心坐标
        org.locationtech.jts.geom.Coordinate centroidCoord = geometry.getCentroid().getCoordinate();
        double lon = centroidCoord.x; // 经度
        double lat = centroidCoord.y; // 纬度

        // 3. 计算 H3 索引
        H3Core h3 = H3Core.newInstance();   // 可能抛出 IOException
        Long h3Index = h3.latLngToCell(lat, lon, resolution);

        return h3Index;
    }


    /**
     * 将单个 H3 索引转换为对应多边形的 WKT
     * @param h3Index 例如 "87283082fffffff"
     * @return        例如 "POLYGON((lon1 lat1, ..., lonN latN, lon1 lat1))"
     */
    public static String h3IndexToWKT(String h3Index) throws IOException {
        // 1. 创建 H3Core 实例
        H3Core h3 = H3Core.newInstance();

        // 2. 获取此 H3 单元对应的多边形边界（经纬度坐标）
        // List<GeoCoord> boundary = h3.h3ToGeoBoundaryAddress(h3Index);
        // 此方法也支持传入 H3 索引的 long 值
        List<LatLng> boundary = h3.cellToBoundary(h3Index);

        // 3. 组装成 WKT POLYGON
        StringBuilder sb = new StringBuilder("POLYGON((");
        for (int i = 0; i < boundary.size(); i++) {
            LatLng latLng = boundary.get(i);
            // 注意：WKT 里通常是 "lon lat"
            sb.append(latLng.lng)
                    .append(" ")
                    .append(latLng.lat);

            if (i < boundary.size() - 1) {
                sb.append(", ");
            }
        }

        // 闭合多边形：补回首个点坐标
        LatLng first = boundary.get(0);
        sb.append(", ")
                .append(first.lng)
                .append(" ")
                .append(first.lat);

        sb.append("))");
        return sb.toString();
    }

    /**
     * 将单个 H3 索引转换为对应多边形的 WKT
     * @param h3Index 例如 "87283082fffffff"
     * @return        例如 "POLYGON((lon1 lat1, ..., lonN latN, lon1 lat1))"
     */
    public static String h3IndexToWKT(Long h3Index) throws IOException {
        // 1. 创建 H3Core 实例
        H3Core h3 = H3Core.newInstance();

        // 2. 获取此 H3 单元对应的多边形边界（经纬度坐标）
        // List<GeoCoord> boundary = h3.h3ToGeoBoundaryAddress(h3Index);
        List<LatLng> boundary = h3.cellToBoundary(h3Index);

        // 3. 组装成 WKT POLYGON
        StringBuilder sb = new StringBuilder("POLYGON((");
        for (int i = 0; i < boundary.size(); i++) {
            LatLng latLng = boundary.get(i);
            // 注意：WKT 里通常是 "lon lat"
            sb.append(latLng.lng)
                    .append(" ")
                    .append(latLng.lat);

            if (i < boundary.size() - 1) {
                sb.append(", ");
            }
        }

        // 闭合多边形：补回首个点坐标
        LatLng first = boundary.get(0);
        sb.append(", ")
                .append(first.lng)
                .append(" ")
                .append(first.lat);

        sb.append("))");
        return sb.toString();
    }
}
