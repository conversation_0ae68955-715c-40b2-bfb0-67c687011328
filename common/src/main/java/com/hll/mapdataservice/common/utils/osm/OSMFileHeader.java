/*
 *  Licensed to GraphHopper GmbH under one or more contributor
 *  license agreements. See the NOTICE file distributed with this work for
 *  additional information regarding copyright ownership.
 *
 *  GraphHopper GmbH licenses this file to you under the Apache License,
 *  Version 2.0 (the "License"); you may not use this file except in
 *  compliance with the License. You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.hll.mapdataservice.common.utils.osm;

import javax.xml.stream.XMLStreamConstants;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamReader;

/**
 * Represents an OSM file header
 * <p>
 *
 * <AUTHOR>
 */
public class OSMFileHeader extends ReaderElement {
    public OSMFileHeader() {
        super(0, FILEHEADER);
    }

    /**
     * Constructor for XML Parser
     */
    public static OSMFileHeader create(long id, XMLStreamReader parser) throws XMLStreamException {
        OSMFileHeader header = new OSMFileHeader();
        parser.nextTag();
        return header;
    }

    protected void readFileHeader(XMLStreamReader parser) throws XMLStreamException {
        int event = parser.getEventType();
        while (event != XMLStreamConstants.END_DOCUMENT && parser.getLocalName().equals("osm")) {
            event = parser.nextTag();
        }
    }

    @Override
    public String toString() {
        return "OSM File header:" + super.toString();
    }
}
