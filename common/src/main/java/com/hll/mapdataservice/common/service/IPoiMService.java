package com.hll.mapdataservice.common.service;

import com.hll.mapdataservice.common.entity.Place;
import com.hll.mapdataservice.common.entity.Poi;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hll.mapdataservice.common.entity.PoiM;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-24
 */
public interface IPoiMService extends IService<PoiM> {

    /**
     * 国家poi信息匹配和google
     *
     * @param placeList
     * @param countryPrefix
     * @return
     */
    int googlePoiMatch(List<Place> placeList,String countryPrefix);

    /**
     * 国家poi信息匹配和here
     *
     * @param placeList
     * @param country
     * @return
     */
    int herePoiMatch(List<Place> placeList, String country);

    /**
     * here的数据与google进行匹配
     *
     * @param poiList
     * @param country
     * @return
     */
    int hereGooglePoiMatch(List<PoiM> poiList, String country);

    /**
     * 处理 poi中的link_id为继承性
     * @param area
     * @param country
     * @param countDownLatch
     * @param poiList
     */
    void handleId(String area, String country, CountDownLatch countDownLatch, List<PoiM> poiList);
}
