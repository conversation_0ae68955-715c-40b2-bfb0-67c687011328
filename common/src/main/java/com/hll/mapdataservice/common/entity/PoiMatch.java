package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
  *
  *
  * @Author: ares.chen
  * @Since: 2021/12/8
  */
@ApiModel(value="poi_match")
@Data
public class PoiMatch {
    @ApiModelProperty(value="")
    private String id;

    /**
    * poi坐标
    */
    @ApiModelProperty(value="poi坐标")
    private Object poiGeo;

    /**
    * 纬度
    */
    @ApiModelProperty(value="纬度")
    private BigDecimal latitude;

    /**
    * 经度
    */
    @ApiModelProperty(value="经度")
    private BigDecimal longitude;

    /**
    * 国家前缀
    */
    @ApiModelProperty(value="国家前缀")
    private String countryPrefix;

    /**
    * poi名称
    */
    @ApiModelProperty(value="poi名称")
    private String poiName;

    /**
    * 谷歌poi名称
    */
    @ApiModelProperty(value="谷歌poi名称")
    private String googlePoiName;

    /**
     * here poi名称
     */
    @ApiModelProperty(value="here poi名称")
    private String herePoiName;

    /**
     * 匹配源
     */
    @ApiModelProperty(value="匹配源")
    private String matchSrc;

    /**
    * 相似度
    */
    @ApiModelProperty(value="相似度")
    private BigDecimal similarity;

    /**
    * 是否匹配，1匹配，0不匹配
    */
    @ApiModelProperty(value="是否匹配，1匹配，0不匹配")
    private Integer isMatch;

    /**
    * 版本号，区分批次数据
    */
    @ApiModelProperty(value="版本号，区分批次数据")
    private Integer version;

    /**
    * 创建时间
    */
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createTime;

    /**
    * 修改时间
    */
    @ApiModelProperty(value="修改时间")
    private LocalDateTime updateTime;

    /**
     * poi_id
     */
    @ApiModelProperty(value="poi_id")
    private String poiId;

    /**
     * here_poi_id
     */
    @ApiModelProperty(value="here_poi_id")
    private String herePoiId;

    /**
     * here纬度
     */
    @ApiModelProperty(value="here纬度")
    private BigDecimal hereLatitude;

    /**
     * here经度
     */
    @ApiModelProperty(value="here经度")
    private BigDecimal hereLongitude;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Object getPoiGeo() {
        return poiGeo;
    }

    public void setPoiGeo(Object poiGeo) {
        this.poiGeo = poiGeo;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public String getCountryPrefix() {
        return countryPrefix;
    }

    public void setCountryPrefix(String countryPrefix) {
        this.countryPrefix = countryPrefix;
    }

    public String getPoiName() {
        return poiName;
    }

    public void setPoiName(String poiName) {
        this.poiName = poiName;
    }

    public String getGooglePoiName() {
        return googlePoiName;
    }

    public void setGooglePoiName(String googlePoiName) {
        this.googlePoiName = googlePoiName;
    }

    public BigDecimal getSimilarity() {
        return similarity;
    }

    public void setSimilarity(BigDecimal similarity) {
        this.similarity = similarity;
    }

    public Integer getIsMatch() {
        return isMatch;
    }

    public void setIsMatch(Integer isMatch) {
        this.isMatch = isMatch;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getHerePoiName() {
        return herePoiName;
    }

    public void setHerePoiName(String herePoiName) {
        this.herePoiName = herePoiName;
    }

    public String getMatchSrc() {
        return matchSrc;
    }

    public void setMatchSrc(String matchSrc) {
        this.matchSrc = matchSrc;
    }

    public String getPoiId() {
        return poiId;
    }

    public void setPoiId(String poiId) {
        this.poiId = poiId;
    }

    public String getHerePoiId() {
        return herePoiId;
    }

    public void setHerePoiId(String herePoiId) {
        this.herePoiId = herePoiId;
    }

    public BigDecimal getHereLatitude() {
        return hereLatitude;
    }

    public void setHereLatitude(BigDecimal hereLatitude) {
        this.hereLatitude = hereLatitude;
    }

    public BigDecimal getHereLongitude() {
        return hereLongitude;
    }

    public void setHereLongitude(BigDecimal hereLongitude) {
        this.hereLongitude = hereLongitude;
    }
}