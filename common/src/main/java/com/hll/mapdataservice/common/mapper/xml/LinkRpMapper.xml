<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.LinkRpMapper">
  <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.LinkRp">
    <!--@mbg.generated-->
    <!--@Table link_rp-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="hll_linkid" jdbcType="VARCHAR" property="hllLinkid" />
    <result column="hll_s_nid" jdbcType="VARCHAR" property="hllSNid" />
    <result column="hll_e_nid" jdbcType="VARCHAR" property="hllENid" />
    <result column="kind" jdbcType="VARCHAR" property="kind" />
    <result column="formway" jdbcType="VARCHAR" property="formway" />
    <result column="direction" jdbcType="VARCHAR" property="direction" />
    <result column="const_st" jdbcType="VARCHAR" property="constSt" />
    <result column="toll" jdbcType="VARCHAR" property="toll" />
    <result column="adopt" jdbcType="VARCHAR" property="adopt" />
    <result column="md" jdbcType="VARCHAR" property="md" />
    <result column="detailcity" jdbcType="VARCHAR" property="detailcity" />
    <result column="special" jdbcType="VARCHAR" property="special" />
    <result column="funcclass" jdbcType="VARCHAR" property="funcclass" />
    <result column="uflag" jdbcType="VARCHAR" property="uflag" />
    <result column="road_cond" jdbcType="VARCHAR" property="roadCond" />
    <result column="lanenumsum" jdbcType="INTEGER" property="lanenumsum" />
    <result column="lanenums2e" jdbcType="INTEGER" property="lanenums2e" />
    <result column="lanenume2s" jdbcType="INTEGER" property="lanenume2s" />
    <result column="lanenumc" jdbcType="VARCHAR" property="lanenumc" />
    <result column="width" jdbcType="VARCHAR" property="width" />
    <result column="elevated" jdbcType="VARCHAR" property="elevated" />
    <result column="admincodel" jdbcType="VARCHAR" property="admincodel" />
    <result column="admincoder" jdbcType="VARCHAR" property="admincoder" />
    <result column="geom" jdbcType="OTHER" property="geom" />
    <result column="len" jdbcType="DOUBLE" property="len" />
    <result column="spdlmts2e" jdbcType="VARCHAR" property="spdlmts2e" />
    <result column="spdlmte2s" jdbcType="VARCHAR" property="spdlmte2s" />
    <result column="speedclass" jdbcType="VARCHAR" property="speedclass" />
    <result column="dc_type" jdbcType="VARCHAR" property="dcType" />
    <result column="verifyflag" jdbcType="VARCHAR" property="verifyflag" />
    <result column="pre_launch" jdbcType="VARCHAR" property="preLaunch" />
    <result column="name_ch_o" jdbcType="VARCHAR" property="nameChO" />
    <result column="name_ch_a" jdbcType="VARCHAR" property="nameChA" />
    <result column="name_ch_f" jdbcType="VARCHAR" property="nameChF" />
    <result column="name_ph_o" jdbcType="VARCHAR" property="namePhO" />
    <result column="name_ph_a" jdbcType="VARCHAR" property="namePhA" />
    <result column="name_ph_f" jdbcType="VARCHAR" property="namePhF" />
    <result column="name_en_o" jdbcType="VARCHAR" property="nameEnO" />
    <result column="name_en_a" jdbcType="VARCHAR" property="nameEnA" />
    <result column="name_en_f" jdbcType="VARCHAR" property="nameEnF" />
    <result column="name_po" jdbcType="VARCHAR" property="namePo" />
    <result column="name_cht" jdbcType="VARCHAR" property="nameCht" />
    <result column="code_type" jdbcType="VARCHAR" property="codeType" />
    <result column="name_type" jdbcType="VARCHAR" property="nameType" />
    <result column="src_flag" jdbcType="VARCHAR" property="srcFlag" />
    <result column="mesh_id" jdbcType="VARCHAR" property="meshId" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="cp" jdbcType="VARCHAR" property="cp" />
    <result column="datasource" jdbcType="VARCHAR" property="datasource" />
    <result column="up_date" jdbcType="TIMESTAMP" property="upDate" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="geomwkt" jdbcType="VARCHAR" property="geomwkt" />
  </resultMap>
</mapper>