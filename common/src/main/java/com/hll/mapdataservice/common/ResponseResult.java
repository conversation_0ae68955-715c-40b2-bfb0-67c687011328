package com.hll.mapdataservice.common;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.base.Strings;
import lombok.*;

import java.io.Serializable;

/**
 * <p>
 * 封装返回信息
 *
 * <AUTHOR>
 * @date 2021/3/4 20:35
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
@Setter
@ToString
public class ResponseResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否成功
     */
    private boolean success;
    /**
     * 返回的对象
     */
    private T data;
    /**
     * 返回的编码
     */
    private String code;
    /**
     * 返回的信息
     */
    private String message;

    /**
     * 用于只返回处理状态的数据（状态码：000000）
     *
     * @return 响应结果
     */
    public static ResponseResult<String> OK(boolean success) {
        return packageObject(StringUtils.EMPTY, GlobalCodeEnum.GL_SUCC_000000, success);
    }

    /**
     * 正常返回数据（状态码：000000）
     *
     * @param data 返回的数据
     * @param <T>  返回的数据类型
     * @return 响应结果
     */
    public static <T> ResponseResult<T> OK(T data,boolean success) {
        return packageObject(data, GlobalCodeEnum.GL_SUCC_000000, success);
    }


    public static <T> ResponseResult<T> ERROR(String msg,T data) {
        return packageObject(data,"500", msg, false);
    }



    /**
     * 对返回的消息进行打包
     *
     * @param data           返回的数据
     * @param globalCodeEnum 自定义的返回码枚举类型
     * @param <T>            返回的数据类型
     * @return 响应结果
     */
    public static <T> ResponseResult<T> packageObject(T data, GlobalCodeEnum globalCodeEnum,boolean success) {
        ResponseResult<T> responseResult = new ResponseResult<>();
        responseResult.setCode(globalCodeEnum.getCode());
        responseResult.setMessage(globalCodeEnum.getDesc());
        responseResult.setData(data);
        responseResult.setSuccess(success);
        return responseResult;
    }

    /**
     * 对返回的消息进行打包
     *
     * @param data    返回的数据
     * @param code    返回的状态码
     * @param message 返回的消息
     * @param success 返回成功与否状态
     * @param <T>     返回的数据类型
     * @return 响应结果
     */
    public static <T> ResponseResult<T> packageObject(T data, String code, String message,boolean success) {
        ResponseResult<T> responseResult = new ResponseResult<>();
        responseResult.setCode(code);
        responseResult.setMessage(message);
        responseResult.setData(data);
        responseResult.setSuccess(success);
        return responseResult;
    }

    /**
     * 校验入参有误，不满足接口入参要求
     *
     * @param globalCodeEnum 入参有误的返回码枚举类型
     * @param <T>            返回的数据类型
     * @return 响应结果
     */
    public static <T> ResponseResult<T> paramsError(GlobalCodeEnum globalCodeEnum,boolean success) {
        return packageObject(null, globalCodeEnum, success);
    }

    /**
     * 返回其它信息：若调用第三方接口返回失败
     *
     * @param globalCodeEnum 入参有误的返回码枚举类型
     * @param message        返回的消息
     * @param <T>            返回的数据类型
     * @return 响应结果
     */
    public static <T> ResponseResult<T> otherInfo(GlobalCodeEnum globalCodeEnum, String message,boolean success) {
        return packageObject(null, globalCodeEnum.getCode(), message,success);
    }
    /**
     * 返回其它信息：（内部调用String.format进行格式化）
     *
     * @param globalCodeEnum 入参有误的返回码枚举类型（带有需要格式化的信息：%s）
     * @param message        返回的消息（即哪些消息进行格式化）
     * @param <T>            返回的数据类型
     * @return 响应结果
     */
    public static <T> ResponseResult<T> otherInfoOfFormat(GlobalCodeEnum globalCodeEnum, boolean success,Object... message) {
        return packageObject(null, globalCodeEnum.getCode(), String.format(globalCodeEnum.getDesc(),message),success);
    }



    /**
     * 返回其它信息：若调用第三方接口返回失败
     *
     * @param globalCodeEnum 入参有误的返回码枚举类型
     * @param <T>            返回的数据类型
     * @return 响应结果
     */
    public static <T> ResponseResult<T> otherInfo(GlobalCodeEnum globalCodeEnum,boolean success) {
        return packageObject(null, globalCodeEnum.getCode(), globalCodeEnum.getDesc(),success);
    }
    /**
     * 返回其它信息：若调用第三方接口返回失败
     *
     * @param t 返回对象
     * @param globalCodeEnum 入参有误的返回码枚举类型
     * @param <T>            返回的数据类型
     * @return 响应结果
     */
    public static <T> ResponseResult<T> otherInfo(T t, GlobalCodeEnum globalCodeEnum,boolean success) {
        return packageObject(t, globalCodeEnum.getCode(), globalCodeEnum.getDesc(),success);
    }

    /**
     * 校验入参有误，不满足接口入参要求
     *
     * @param globalCodeEnum 入参有误的返回码枚举类型
     * @param message        返回的消息
     * @param <T>            返回的数据类型
     * @return 响应结果
     */
    public static <T> ResponseResult<T> paramsError(GlobalCodeEnum globalCodeEnum, String message,boolean success) {
        return packageObject(null, globalCodeEnum.getCode(), message,success);
    }

    /**
     * 校验入参有误，不满足接口入参要求
     *
     * @param code    入参有误的返回码
     * @param message 返回的消息
     * @param <T>     返回的数据类型
     * @return 响应结果
     */
    public static <T> ResponseResult<T> otherInfo(String code, String message,boolean success) {
        return packageObject(null, code, message,success);
    }

    /**
     * 系统服务不可用
     *
     * @param globalCodeEnum Feign依赖服务不可用的返回码枚举类型
     * @param <T>            返回的数据类型
     * @return 响应结果
     */
    public static <T> ResponseResult<T> systemError(GlobalCodeEnum globalCodeEnum,boolean success) {
        return packageObject(null, globalCodeEnum, success);
    }

    /**
     * 系统服务不可用
     *
     * @param globalCodeEnum Feign依赖服务不可用的返回码枚举类型
     * @param message        返回消息
     * @param <T>            返回的数据类型
     * @return 响应结果
     */
    public static <T> ResponseResult<T> exceptionInfo(GlobalCodeEnum globalCodeEnum, String message,boolean success) {
        return packageObject(null, globalCodeEnum.getCode(), message,success);
    }

    /**
     * 未查询到相关的数据
     *
     * @param globalCodeEnum 未查询到相关信息的返回码枚举类型
     * @param <T>            返回的数据类型
     * @return 响应结果
     */
    public static <T> ResponseResult<T> noData(GlobalCodeEnum globalCodeEnum,boolean success) {
        return packageObject(null, globalCodeEnum, success);
    }

    /**
     * 系统异常（使用默认的异常返回码）
     *
     * @param <T> 返回的数据类型
     * @return 响应结果
     */
    public static <T> ResponseResult<T> systemException(boolean success) {
        return packageObject(null, GlobalCodeEnum.GL_FAIL_999999, success);
    }

    /**
     * 系统异常
     *
     * @param globalCodeEnum 异常返回码枚举类型
     * @param <T>            返回的数据类型
     * @return 响应结果
     */
    public static <T> ResponseResult<T> systemException(GlobalCodeEnum globalCodeEnum,boolean success) {
        return packageObject(null, globalCodeEnum, success);
    }


    /**
     * 自定义系统异常信息
     *
     * @param globalCodeEnum 异常返回码枚举类型
     * @param message        自定义消息
     * @param <T>            返回的数据类型
     * @return 响应结果
     */
    public static <T> ResponseResult<T> systemException(GlobalCodeEnum globalCodeEnum, String message,boolean success) {
        return packageObject(null, globalCodeEnum.getCode(), Strings.isNullOrEmpty(message) ? globalCodeEnum.getDesc() : message,success);
    }


    /**
     * 自定义系统异常信息
     *
     * @param code
     * @param message 自定义消息
     * @param <T>
     * @return
     */
    public static <T> ResponseResult<T> systemException(String code, String message,boolean success) {
        return packageObject(null, code, message,success);
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
