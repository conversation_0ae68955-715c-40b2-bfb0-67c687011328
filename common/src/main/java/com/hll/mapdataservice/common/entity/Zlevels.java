package com.hll.mapdataservice.common.entity;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-23
 */
@ApiModel(value="Zlevels对象", description="")
//@DS("db4")
public class Zlevels extends Model<Zlevels> {

    private static final long serialVersionUID = 1L;
    @TableId(value = "gid", type = IdType.AUTO)
    private Integer gid;

    private Long linkId;

    private Integer pointNum;

    private Long nodeId;

    private Integer zLevel;

    private String intrsect;

    private Integer dotShape;

    private String aligned;

    private String geom;

    public String getGeomwkt() {
        return geomwkt;
    }

    public void setGeomwkt(String geomwkt) {
        this.geomwkt = geomwkt;
    }

    @TableField("st_astext(\"geom\")")
    private String geomwkt;

    public Integer getGid() {
        return gid;
    }

    public void setGid(Integer gid) {
        this.gid = gid;
    }
    public Long getLinkId() {
        return linkId;
    }

    public void setLinkId(Long linkId) {
        this.linkId = linkId;
    }
    public Integer getPointNum() {
        return pointNum;
    }

    public void setPointNum(Integer pointNum) {
        this.pointNum = pointNum;
    }
    public Long getNodeId() {
        return nodeId;
    }

    public void setNodeId(Long nodeId) {
        this.nodeId = nodeId;
    }
    public Integer getzLevel() {
        return zLevel;
    }

    public void setzLevel(Integer zLevel) {
        this.zLevel = zLevel;
    }
    public String getIntrsect() {
        return intrsect;
    }

    public void setIntrsect(String intrsect) {
        this.intrsect = intrsect;
    }
    public Integer getDotShape() {
        return dotShape;
    }

    public void setDotShape(Integer dotShape) {
        this.dotShape = dotShape;
    }
    public String getAligned() {
        return aligned;
    }

    public void setAligned(String aligned) {
        this.aligned = aligned;
    }
    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }

    @Override
    protected Serializable pkVal() {
        return this.gid;
    }

    @Override
    public String toString() {
        return "Zlevels{" +
            "gid=" + gid +
            ", linkId=" + linkId +
            ", pointNum=" + pointNum +
            ", nodeId=" + nodeId +
            ", zLevel=" + zLevel +
            ", intrsect=" + intrsect +
            ", dotShape=" + dotShape +
            ", aligned=" + aligned +
            ", geom=" + geom +
        "}";
    }
}
