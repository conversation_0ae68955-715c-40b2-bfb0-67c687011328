package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-03
 */
@ApiModel(value="Mtdarea对象", description="")
public class Mtdarea extends Model<Mtdarea> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "gid", type = IdType.AUTO)
    private Integer gid;

    private Long areaId;

    @TableField("areacode_1")
    private Integer areacode1;
    @TableField("areacode_2")
    private Integer areacode2;
    @TableField("areacode_3")
    private Integer areacode3;
    @TableField("areacode_4")
    private Integer areacode4;
    @TableField("areacode_5")
    private Integer areacode5;
    @TableField("areacode_6")
    private Integer areacode6;
    @TableField("areacode_7")
    private Integer areacode7;

    private Integer adminLvl;

    private String areaName;

    private String langCode;

    private String areaNmTr;

    private String transType;

    private String areaType;

    private Long govtCode;

    private String motorcReq;

    private String admwdeReg;

    public Integer getGid() {
        return gid;
    }

    public void setGid(Integer gid) {
        this.gid = gid;
    }
    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }
    public Integer getAreacode1() {
        return areacode1;
    }

    public void setAreacode1(Integer areacode1) {
        this.areacode1 = areacode1;
    }
    public Integer getAreacode2() {
        return areacode2;
    }

    public void setAreacode2(Integer areacode2) {
        this.areacode2 = areacode2;
    }
    public Integer getAreacode3() {
        return areacode3;
    }

    public void setAreacode3(Integer areacode3) {
        this.areacode3 = areacode3;
    }
    public Integer getAreacode4() {
        return areacode4;
    }

    public void setAreacode4(Integer areacode4) {
        this.areacode4 = areacode4;
    }
    public Integer getAreacode5() {
        return areacode5;
    }

    public void setAreacode5(Integer areacode5) {
        this.areacode5 = areacode5;
    }
    public Integer getAreacode6() {
        return areacode6;
    }

    public void setAreacode6(Integer areacode6) {
        this.areacode6 = areacode6;
    }
    public Integer getAreacode7() {
        return areacode7;
    }

    public void setAreacode7(Integer areacode7) {
        this.areacode7 = areacode7;
    }
    public Integer getAdminLvl() {
        return adminLvl;
    }

    public void setAdminLvl(Integer adminLvl) {
        this.adminLvl = adminLvl;
    }
    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }
    public String getLangCode() {
        return langCode;
    }

    public void setLangCode(String langCode) {
        this.langCode = langCode;
    }
    public String getAreaNmTr() {
        return areaNmTr;
    }

    public void setAreaNmTr(String areaNmTr) {
        this.areaNmTr = areaNmTr;
    }
    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }
    public String getAreaType() {
        return areaType;
    }

    public void setAreaType(String areaType) {
        this.areaType = areaType;
    }
    public Long getGovtCode() {
        return govtCode;
    }

    public void setGovtCode(Long govtCode) {
        this.govtCode = govtCode;
    }
    public String getMotorcReq() {
        return motorcReq;
    }

    public void setMotorcReq(String motorcReq) {
        this.motorcReq = motorcReq;
    }
    public String getAdmwdeReg() {
        return admwdeReg;
    }

    public void setAdmwdeReg(String admwdeReg) {
        this.admwdeReg = admwdeReg;
    }

    @Override
    protected Serializable pkVal() {
        return this.gid;
    }

    @Override
    public String toString() {
        return "Mtdarea{" +
            "gid=" + gid +
            ", areaId=" + areaId +
            ", areacode1=" + areacode1 +
            ", areacode2=" + areacode2 +
            ", areacode3=" + areacode3 +
            ", areacode4=" + areacode4 +
            ", areacode5=" + areacode5 +
            ", areacode6=" + areacode6 +
            ", areacode7=" + areacode7 +
            ", adminLvl=" + adminLvl +
            ", areaName=" + areaName +
            ", langCode=" + langCode +
            ", areaNmTr=" + areaNmTr +
            ", transType=" + transType +
            ", areaType=" + areaType +
            ", govtCode=" + govtCode +
            ", motorcReq=" + motorcReq +
            ", admwdeReg=" + admwdeReg +
        "}";
    }
}
