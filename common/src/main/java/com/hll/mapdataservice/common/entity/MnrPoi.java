package com.hll.mapdataservice.common.entity;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-19
 */
@ApiModel(value="MnrPoi对象", description="")
//@DS("db2")
@TableName(value = "mnr_poi",schema = "poi")
public class MnrPoi extends Model<MnrPoi> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "feat_id", type = IdType.AUTO)
    private String featId;

    private String featType;

    private String featSubType;

    private String serviceGroup;

    private String gav;

    private String name;

    private String langCode;

    private String telephoneNum;

    private String faxNum;

    private String email;

    private String internet;

    private String poiAddressId;

    private String inCarImportance;

    @TableField("ST_AsText(\"geom\")")
    private String geom;

    public String getFeatId() {
        return featId;
    }

    public void setFeatId(String featId) {
        this.featId = featId;
    }
    public String getFeatType() {
        return featType;
    }

    public void setFeatType(String featType) {
        this.featType = featType;
    }
    public String getFeatSubType() {
        return featSubType;
    }

    public void setFeatSubType(String featSubType) {
        this.featSubType = featSubType;
    }
    public String getServiceGroup() {
        return serviceGroup;
    }

    public void setServiceGroup(String serviceGroup) {
        this.serviceGroup = serviceGroup;
    }
    public String getGav() {
        return gav;
    }

    public void setGav(String gav) {
        this.gav = gav;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public String getLangCode() {
        return langCode;
    }

    public void setLangCode(String langCode) {
        this.langCode = langCode;
    }
    public String getTelephoneNum() {
        return telephoneNum;
    }

    public void setTelephoneNum(String telephoneNum) {
        this.telephoneNum = telephoneNum;
    }
    public String getFaxNum() {
        return faxNum;
    }

    public void setFaxNum(String faxNum) {
        this.faxNum = faxNum;
    }
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
    public String getInternet() {
        return internet;
    }

    public void setInternet(String internet) {
        this.internet = internet;
    }
    public String getPoiAddressId() {
        return poiAddressId;
    }

    public void setPoiAddressId(String poiAddressId) {
        this.poiAddressId = poiAddressId;
    }
    public String getInCarImportance() {
        return inCarImportance;
    }

    public void setInCarImportance(String inCarImportance) {
        this.inCarImportance = inCarImportance;
    }
    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }

    @Override
    protected Serializable pkVal() {
        return this.featId;
    }

    @Override
    public String toString() {
        return "MnrPoi{" +
            "featId=" + featId +
            ", featType=" + featType +
            ", featSubType=" + featSubType +
            ", serviceGroup=" + serviceGroup +
            ", gav=" + gav +
            ", name=" + name +
            ", langCode=" + langCode +
            ", telephoneNum=" + telephoneNum +
            ", faxNum=" + faxNum +
            ", email=" + email +
            ", internet=" + internet +
            ", poiAddressId=" + poiAddressId +
            ", inCarImportance=" + inCarImportance +
            ", geom=" + geom +
        "}";
    }
}
