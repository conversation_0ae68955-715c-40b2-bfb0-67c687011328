package com.hll.mapdataservice.common.utils;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.postgis.Geometry;
import org.postgis.LineString;
import org.postgis.PGgeometry;
import org.postgresql.geometric.PGline;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

//@MappedTypes({LineString.class})
//@MappedJdbcTypes(JdbcType.BLOB)
//public class MyGeometryTypeHandler extends BaseTypeHandler<LineString> {
//    @Override
//    public void setNonNullParameter(PreparedStatement ps, int i, LineString parameter, JdbcType jdbcType) throws SQLException {
//        //PGgeometry pGgeometry = new PGgeometry(parameter);
//        //parameter.setType("line");
//        ps.setObject(i, parameter);
//    }
//
//    @Override
//    public LineString getNullableResult(ResultSet rs, String columnName) throws SQLException {
//        PGgeometry pGgeometry = new PGgeometry(rs.getString(columnName));
//        if (pGgeometry == null) {
//            return null;
//        }
//        return (LineString) rs.getObject(columnName);
//    }
//
//    @Override
//    public LineString getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
//        PGgeometry pGgeometry = new PGgeometry(rs.getString(columnIndex));
//        if (pGgeometry == null) {
//            return null;
//        }
//        pGgeometry.setType("Line");
//        return (LineString) rs.getObject(columnIndex);
//    }
//
//    @Override
//    public LineString getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
//
//        PGgeometry pGgeometry = new PGgeometry(cs.getString(columnIndex));
//        if (pGgeometry == null) {
//            return null;
//        }
//        return (LineString) cs.getObject(columnIndex);
//    }
//}



@MappedTypes({String.class})
public class MyGeometryTypeHandler extends BaseTypeHandler<String> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {
        PGgeometry pGgeometry = new PGgeometry(parameter);
        ps.setObject(i, pGgeometry);
    }

    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
        PGgeometry pGgeometry = new PGgeometry(rs.getString(columnName));
        if (pGgeometry == null) {
            return null;
        }
        return pGgeometry.toString();
    }

    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        PGgeometry pGgeometry = new PGgeometry(rs.getString(columnIndex));
        if (pGgeometry == null) {
            return null;
        }
        return pGgeometry.toString();
    }

    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {

        PGgeometry pGgeometry = new PGgeometry(cs.getString(columnIndex));
        if (pGgeometry == null) {
            return null;
        }
        return pGgeometry.toString();
    }
}