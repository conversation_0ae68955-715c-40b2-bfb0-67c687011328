package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-02
 */
@ApiModel(value="Admin对象", description="")
public class Admin extends Model<Admin> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    private String areaId;

    private String nmAreaId;

    private String polygonName;

    private String nmLangcd;

    private String nmTr;

    private String transType;

    private String featType;

    private String detailCty;

    private String featCode;

    private String coverind;

    private String claimedBy;

    private String controlBy;

    private Integer adminLev;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }
    public String getNmAreaId() {
        return nmAreaId;
    }

    public void setNmAreaId(String nmAreaId) {
        this.nmAreaId = nmAreaId;
    }
    public String getPolygonName() {
        return polygonName;
    }

    public void setPolygonName(String polygonName) {
        this.polygonName = polygonName;
    }
    public String getNmLangcd() {
        return nmLangcd;
    }

    public void setNmLangcd(String nmLangcd) {
        this.nmLangcd = nmLangcd;
    }
    public String getNmTr() {
        return nmTr;
    }

    public void setNmTr(String nmTr) {
        this.nmTr = nmTr;
    }
    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }
    public String getFeatType() {
        return featType;
    }

    public void setFeatType(String featType) {
        this.featType = featType;
    }
    public String getDetailCty() {
        return detailCty;
    }

    public void setDetailCty(String detailCty) {
        this.detailCty = detailCty;
    }
    public String getFeatCode() {
        return featCode;
    }

    public void setFeatCode(String featCode) {
        this.featCode = featCode;
    }
    public String getCoverind() {
        return coverind;
    }

    public void setCoverind(String coverind) {
        this.coverind = coverind;
    }
    public String getClaimedBy() {
        return claimedBy;
    }

    public void setClaimedBy(String claimedBy) {
        this.claimedBy = claimedBy;
    }
    public String getControlBy() {
        return controlBy;
    }

    public void setControlBy(String controlBy) {
        this.controlBy = controlBy;
    }
    public Integer getAdminLev() {
        return adminLev;
    }

    public void setAdminLev(Integer adminLev) {
        this.adminLev = adminLev;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "Admin{" +
            "id=" + id +
            ", areaId=" + areaId +
            ", nmAreaId=" + nmAreaId +
            ", polygonName=" + polygonName +
            ", nmLangcd=" + nmLangcd +
            ", nmTr=" + nmTr +
            ", transType=" + transType +
            ", featType=" + featType +
            ", detailCty=" + detailCty +
            ", featCode=" + featCode +
            ", coverind=" + coverind +
            ", claimedBy=" + claimedBy +
            ", controlBy=" + controlBy +
            ", adminLev=" + adminLev +
        "}";
    }
}
