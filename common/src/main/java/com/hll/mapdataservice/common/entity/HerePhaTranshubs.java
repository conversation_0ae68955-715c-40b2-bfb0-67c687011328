package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-25
 */
@ApiModel(value="HerePhaTranshubs对象", description="")
public class HerePhaTranshubs extends Model<HerePhaTranshubs> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "gid", type = IdType.AUTO)
    private String gid;

    private String linkId;

    private Integer poiId;

    private String seqNum;

    private String facType;

    private String poiName;

    private String poiLangcd;

    private String poiNmtype;

    private String poiStNum;

    private String stNumFul;

    private String stNfulLc;

    private String stName;

    private String stLangcd;

    private String poiStSd;

    private String accType;

    private String phNumber;

    private String chainId;

    private String natImport;

    public String getPrivateInfo() {
        return privateInfo;
    }

    public void setPrivateInfo(String privateInfo) {
        this.privateInfo = privateInfo;
    }

    @TableField(value = "private")
    private String privateInfo;

    private String inVicin;

    private String numParent;

    private String numChild;

    private String percfrref;

    private String vancityId;

    private String actAddr;

    private String actLangcd;

    private String actStNam;

    private String actStNum;

    private String actAdmin;

    private String actPostal;

    private String airptType;

    @TableField("ST_AsText(\"geom\")")
    private String geom;

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }
    public String getLinkId() {
        return linkId;
    }

    public void setLinkId(String linkId) {
        this.linkId = linkId;
    }
    public Integer getPoiId() {
        return poiId;
    }

    public void setPoiId(Integer poiId) {
        this.poiId = poiId;
    }
    public String getSeqNum() {
        return seqNum;
    }

    public void setSeqNum(String seqNum) {
        this.seqNum = seqNum;
    }
    public String getFacType() {
        return facType;
    }

    public void setFacType(String facType) {
        this.facType = facType;
    }
    public String getPoiName() {
        return poiName;
    }

    public void setPoiName(String poiName) {
        this.poiName = poiName;
    }
    public String getPoiLangcd() {
        return poiLangcd;
    }

    public void setPoiLangcd(String poiLangcd) {
        this.poiLangcd = poiLangcd;
    }
    public String getPoiNmtype() {
        return poiNmtype;
    }

    public void setPoiNmtype(String poiNmtype) {
        this.poiNmtype = poiNmtype;
    }
    public String getPoiStNum() {
        return poiStNum;
    }

    public void setPoiStNum(String poiStNum) {
        this.poiStNum = poiStNum;
    }
    public String getStNumFul() {
        return stNumFul;
    }

    public void setStNumFul(String stNumFul) {
        this.stNumFul = stNumFul;
    }
    public String getStNfulLc() {
        return stNfulLc;
    }

    public void setStNfulLc(String stNfulLc) {
        this.stNfulLc = stNfulLc;
    }
    public String getStName() {
        return stName;
    }

    public void setStName(String stName) {
        this.stName = stName;
    }
    public String getStLangcd() {
        return stLangcd;
    }

    public void setStLangcd(String stLangcd) {
        this.stLangcd = stLangcd;
    }
    public String getPoiStSd() {
        return poiStSd;
    }

    public void setPoiStSd(String poiStSd) {
        this.poiStSd = poiStSd;
    }
    public String getAccType() {
        return accType;
    }

    public void setAccType(String accType) {
        this.accType = accType;
    }
    public String getPhNumber() {
        return phNumber;
    }

    public void setPhNumber(String phNumber) {
        this.phNumber = phNumber;
    }
    public String getChainId() {
        return chainId;
    }

    public void setChainId(String chainId) {
        this.chainId = chainId;
    }
    public String getNatImport() {
        return natImport;
    }

    public void setNatImport(String natImport) {
        this.natImport = natImport;
    }

    public String getInVicin() {
        return inVicin;
    }

    public void setInVicin(String inVicin) {
        this.inVicin = inVicin;
    }
    public String getNumParent() {
        return numParent;
    }

    public void setNumParent(String numParent) {
        this.numParent = numParent;
    }
    public String getNumChild() {
        return numChild;
    }

    public void setNumChild(String numChild) {
        this.numChild = numChild;
    }
    public String getPercfrref() {
        return percfrref;
    }

    public void setPercfrref(String percfrref) {
        this.percfrref = percfrref;
    }
    public String getVancityId() {
        return vancityId;
    }

    public void setVancityId(String vancityId) {
        this.vancityId = vancityId;
    }
    public String getActAddr() {
        return actAddr;
    }

    public void setActAddr(String actAddr) {
        this.actAddr = actAddr;
    }
    public String getActLangcd() {
        return actLangcd;
    }

    public void setActLangcd(String actLangcd) {
        this.actLangcd = actLangcd;
    }
    public String getActStNam() {
        return actStNam;
    }

    public void setActStNam(String actStNam) {
        this.actStNam = actStNam;
    }
    public String getActStNum() {
        return actStNum;
    }

    public void setActStNum(String actStNum) {
        this.actStNum = actStNum;
    }
    public String getActAdmin() {
        return actAdmin;
    }

    public void setActAdmin(String actAdmin) {
        this.actAdmin = actAdmin;
    }
    public String getActPostal() {
        return actPostal;
    }

    public void setActPostal(String actPostal) {
        this.actPostal = actPostal;
    }
    public String getAirptType() {
        return airptType;
    }

    public void setAirptType(String airptType) {
        this.airptType = airptType;
    }
    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }

    @Override
    protected Serializable pkVal() {
        return this.gid;
    }

    @Override
    public String toString() {
        return "HerePhaTranshubs{" +
            "gid=" + gid +
            ", linkId=" + linkId +
            ", poiId=" + poiId +
            ", seqNum=" + seqNum +
            ", facType=" + facType +
            ", poiName=" + poiName +
            ", poiLangcd=" + poiLangcd +
            ", poiNmtype=" + poiNmtype +
            ", poiStNum=" + poiStNum +
            ", stNumFul=" + stNumFul +
            ", stNfulLc=" + stNfulLc +
            ", stName=" + stName +
            ", stLangcd=" + stLangcd +
            ", poiStSd=" + poiStSd +
            ", accType=" + accType +
            ", phNumber=" + phNumber +
            ", chainId=" + chainId +
            ", natImport=" + natImport +
            ", private=" + privateInfo +
            ", inVicin=" + inVicin +
            ", numParent=" + numParent +
            ", numChild=" + numChild +
            ", percfrref=" + percfrref +
            ", vancityId=" + vancityId +
            ", actAddr=" + actAddr +
            ", actLangcd=" + actLangcd +
            ", actStNam=" + actStNam +
            ", actStNum=" + actStNum +
            ", actAdmin=" + actAdmin +
            ", actPostal=" + actPostal +
            ", airptType=" + airptType +
            ", geom=" + geom +
        "}";
    }
}
