<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.MnrPoiMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.MnrPoi">
        <id column="feat_id" property="featId" />
        <result column="feat_type" property="featType" />
        <result column="feat_sub_type" property="featSubType" />
        <result column="service_group" property="serviceGroup" />
        <result column="gav" property="gav" />
        <result column="name" property="name" />
        <result column="lang_code" property="langCode" />
        <result column="telephone_num" property="telephoneNum" />
        <result column="fax_num" property="faxNum" />
        <result column="email" property="email" />
        <result column="internet" property="internet" />
        <result column="poi_address_id" property="poiAddressId" />
        <result column="in_car_importance" property="inCarImportance" />
        <result column="geom" property="geom" />
    </resultMap>

</mapper>
