package com.hll.mapdataservice.common.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.hll.mapdataservice.common.utils.MyGeometryTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-31
 */
@ApiModel(value="PoiHPha对象", description="")
public class PoiHPha extends Model<PoiHPha> {

    private static final long serialVersionUID = 1L;

    private String poiId;

    @TableId(value = "source_id",type=IdType.INPUT)
    private String sourceId;

    private String meshIdLink;

    private String meshId;

    private String kindCode;

    private String name;

    private String namePy;

    private String nameEng;

    private String alias;

    private String aliasPy;

    private String aliasEng;

    private String address;

    private String addressPy;

    private String addressEng;

    private BigDecimal longitude;

    private BigDecimal latitude;

    private BigDecimal longitudeWgs84;

    private BigDecimal latitudeWgs84;

    private BigDecimal longitudeBd09;

    private BigDecimal latitudeBd09;

    private BigDecimal lonGuide;

    private BigDecimal latGuide;

    private String linkId;

    private String side;

    private String importance;

    private String vadminCode;

    private String zipCode;

    private String telephone;

    private String telType;

    private String poiClass;

    private String starRating;

    private String tgType;

    private String accessFlag;

    private String truckFlag;

    private String spVenue;

    private String gate;

    private String aoiId;

    private String isAoi;

    private String bzoneId;

    private String priorAuth;

    private String foodType;

    private String brand;

    private String naviLoc;

    private String provCode;

    private String provName;

    private String provNamePy;

    private String provNameEng;

    private String cityCode;

    private String cityName;

    private String cityNamePy;

    private String cityNameEng;

    private String adCode;

    private String adName;

    private String adNamePy;

    private String adNameEng;

    private String rank;

    @TableField(value = "class")
    private String classInfo;

    private String dupList;

    private String isDup;

    private String parent;

    private String isParent;

    private String children;

    private String isChild;

    private String poiSame;

    private String heat;

    private String rePrice;

    private String openStatus;

    private String accessNum;

    private String parkNum;

    private String memo;

    private String createTime;

    private LocalDateTime updateTime;

    @TableField(value = "poi_geo",typeHandler = MyGeometryTypeHandler.class)
    private String poiGeo;

    private String swProname;

    private String kind;

    private String nameS;

    private String nameSEng;

    private String countryCode;

    private String countryName;

    private String countryNameEng;

    private String regionCode;

    private String regionName;

    private String regionNameEng;

    private Integer status;

    public String getPoiId() {
        return poiId;
    }

    public void setPoiId(String poiId) {
        this.poiId = poiId;
    }
    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }
    public String getMeshIdLink() {
        return meshIdLink;
    }

    public void setMeshIdLink(String meshIdLink) {
        this.meshIdLink = meshIdLink;
    }
    public String getMeshId() {
        return meshId;
    }

    public void setMeshId(String meshId) {
        this.meshId = meshId;
    }
    public String getKindCode() {
        return kindCode;
    }

    public void setKindCode(String kindCode) {
        this.kindCode = kindCode;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public String getNamePy() {
        return namePy;
    }

    public void setNamePy(String namePy) {
        this.namePy = namePy;
    }
    public String getNameEng() {
        return nameEng;
    }

    public void setNameEng(String nameEng) {
        this.nameEng = nameEng;
    }
    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }
    public String getAliasPy() {
        return aliasPy;
    }

    public void setAliasPy(String aliasPy) {
        this.aliasPy = aliasPy;
    }
    public String getAliasEng() {
        return aliasEng;
    }

    public void setAliasEng(String aliasEng) {
        this.aliasEng = aliasEng;
    }
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
    public String getAddressPy() {
        return addressPy;
    }

    public void setAddressPy(String addressPy) {
        this.addressPy = addressPy;
    }
    public String getAddressEng() {
        return addressEng;
    }

    public void setAddressEng(String addressEng) {
        this.addressEng = addressEng;
    }
    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
    public BigDecimal getLongitudeWgs84() {
        return longitudeWgs84;
    }

    public void setLongitudeWgs84(BigDecimal longitudeWgs84) {
        this.longitudeWgs84 = longitudeWgs84;
    }
    public BigDecimal getLatitudeWgs84() {
        return latitudeWgs84;
    }

    public void setLatitudeWgs84(BigDecimal latitudeWgs84) {
        this.latitudeWgs84 = latitudeWgs84;
    }
    public BigDecimal getLongitudeBd09() {
        return longitudeBd09;
    }

    public void setLongitudeBd09(BigDecimal longitudeBd09) {
        this.longitudeBd09 = longitudeBd09;
    }
    public BigDecimal getLatitudeBd09() {
        return latitudeBd09;
    }

    public void setLatitudeBd09(BigDecimal latitudeBd09) {
        this.latitudeBd09 = latitudeBd09;
    }
    public BigDecimal getLonGuide() {
        return lonGuide;
    }

    public void setLonGuide(BigDecimal lonGuide) {
        this.lonGuide = lonGuide;
    }
    public BigDecimal getLatGuide() {
        return latGuide;
    }

    public void setLatGuide(BigDecimal latGuide) {
        this.latGuide = latGuide;
    }
    public String getLinkId() {
        return linkId;
    }

    public void setLinkId(String linkId) {
        this.linkId = linkId;
    }
    public String getSide() {
        return side;
    }

    public void setSide(String side) {
        this.side = side;
    }
    public String getImportance() {
        return importance;
    }

    public void setImportance(String importance) {
        this.importance = importance;
    }
    public String getVadminCode() {
        return vadminCode;
    }

    public void setVadminCode(String vadminCode) {
        this.vadminCode = vadminCode;
    }
    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }
    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }
    public String getTelType() {
        return telType;
    }

    public void setTelType(String telType) {
        this.telType = telType;
    }
    public String getPoiClass() {
        return poiClass;
    }

    public void setPoiClass(String poiClass) {
        this.poiClass = poiClass;
    }
    public String getStarRating() {
        return starRating;
    }

    public void setStarRating(String starRating) {
        this.starRating = starRating;
    }
    public String getTgType() {
        return tgType;
    }

    public void setTgType(String tgType) {
        this.tgType = tgType;
    }
    public String getAccessFlag() {
        return accessFlag;
    }

    public void setAccessFlag(String accessFlag) {
        this.accessFlag = accessFlag;
    }
    public String getTruckFlag() {
        return truckFlag;
    }

    public void setTruckFlag(String truckFlag) {
        this.truckFlag = truckFlag;
    }
    public String getSpVenue() {
        return spVenue;
    }

    public void setSpVenue(String spVenue) {
        this.spVenue = spVenue;
    }
    public String getGate() {
        return gate;
    }

    public void setGate(String gate) {
        this.gate = gate;
    }
    public String getAoiId() {
        return aoiId;
    }

    public void setAoiId(String aoiId) {
        this.aoiId = aoiId;
    }
    public String getIsAoi() {
        return isAoi;
    }

    public void setIsAoi(String isAoi) {
        this.isAoi = isAoi;
    }
    public String getBzoneId() {
        return bzoneId;
    }

    public void setBzoneId(String bzoneId) {
        this.bzoneId = bzoneId;
    }
    public String getPriorAuth() {
        return priorAuth;
    }

    public void setPriorAuth(String priorAuth) {
        this.priorAuth = priorAuth;
    }
    public String getFoodType() {
        return foodType;
    }

    public void setFoodType(String foodType) {
        this.foodType = foodType;
    }
    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }
    public String getNaviLoc() {
        return naviLoc;
    }

    public void setNaviLoc(String naviLoc) {
        this.naviLoc = naviLoc;
    }
    public String getProvCode() {
        return provCode;
    }

    public void setProvCode(String provCode) {
        this.provCode = provCode;
    }
    public String getProvName() {
        return provName;
    }

    public void setProvName(String provName) {
        this.provName = provName;
    }
    public String getProvNamePy() {
        return provNamePy;
    }

    public void setProvNamePy(String provNamePy) {
        this.provNamePy = provNamePy;
    }
    public String getProvNameEng() {
        return provNameEng;
    }

    public void setProvNameEng(String provNameEng) {
        this.provNameEng = provNameEng;
    }
    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }
    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }
    public String getCityNamePy() {
        return cityNamePy;
    }

    public void setCityNamePy(String cityNamePy) {
        this.cityNamePy = cityNamePy;
    }
    public String getCityNameEng() {
        return cityNameEng;
    }

    public void setCityNameEng(String cityNameEng) {
        this.cityNameEng = cityNameEng;
    }
    public String getAdCode() {
        return adCode;
    }

    public void setAdCode(String adCode) {
        this.adCode = adCode;
    }
    public String getAdName() {
        return adName;
    }

    public void setAdName(String adName) {
        this.adName = adName;
    }
    public String getAdNamePy() {
        return adNamePy;
    }

    public void setAdNamePy(String adNamePy) {
        this.adNamePy = adNamePy;
    }
    public String getAdNameEng() {
        return adNameEng;
    }

    public void setAdNameEng(String adNameEng) {
        this.adNameEng = adNameEng;
    }
    public String getRank() {
        return rank;
    }

    public void setRank(String rank) {
        this.rank = rank;
    }
    public String getClassInfo() {
        return classInfo;
    }

    public void setClassInfo(String classInfo) {
        this.classInfo = classInfo;
    }
    public String getDupList() {
        return dupList;
    }

    public void setDupList(String dupList) {
        this.dupList = dupList;
    }
    public String getIsDup() {
        return isDup;
    }

    public void setIsDup(String isDup) {
        this.isDup = isDup;
    }
    public String getParent() {
        return parent;
    }

    public void setParent(String parent) {
        this.parent = parent;
    }
    public String getIsParent() {
        return isParent;
    }

    public void setIsParent(String isParent) {
        this.isParent = isParent;
    }
    public String getChildren() {
        return children;
    }

    public void setChildren(String children) {
        this.children = children;
    }
    public String getIsChild() {
        return isChild;
    }

    public void setIsChild(String isChild) {
        this.isChild = isChild;
    }
    public String getPoiSame() {
        return poiSame;
    }

    public void setPoiSame(String poiSame) {
        this.poiSame = poiSame;
    }
    public String getHeat() {
        return heat;
    }

    public void setHeat(String heat) {
        this.heat = heat;
    }
    public String getRePrice() {
        return rePrice;
    }

    public void setRePrice(String rePrice) {
        this.rePrice = rePrice;
    }
    public String getOpenStatus() {
        return openStatus;
    }

    public void setOpenStatus(String openStatus) {
        this.openStatus = openStatus;
    }
    public String getAccessNum() {
        return accessNum;
    }

    public void setAccessNum(String accessNum) {
        this.accessNum = accessNum;
    }
    public String getParkNum() {
        return parkNum;
    }

    public void setParkNum(String parkNum) {
        this.parkNum = parkNum;
    }
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }
    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    public String getPoiGeo() {
        return poiGeo;
    }

    public void setPoiGeo(String poiGeo) {
        this.poiGeo = poiGeo;
    }
    public String getSwProname() {
        return swProname;
    }

    public void setSwProname(String swProname) {
        this.swProname = swProname;
    }
    public String getKind() {
        return kind;
    }

    public void setKind(String kind) {
        this.kind = kind;
    }
    public String getNameS() {
        return nameS;
    }

    public void setNameS(String nameS) {
        this.nameS = nameS;
    }
    public String getNameSEng() {
        return nameSEng;
    }

    public void setNameSEng(String nameSEng) {
        this.nameSEng = nameSEng;
    }
    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }
    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }
    public String getCountryNameEng() {
        return countryNameEng;
    }

    public void setCountryNameEng(String countryNameEng) {
        this.countryNameEng = countryNameEng;
    }
    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }
    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }
    public String getRegionNameEng() {
        return regionNameEng;
    }

    public void setRegionNameEng(String regionNameEng) {
        this.regionNameEng = regionNameEng;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    protected Serializable pkVal() {
        return this.poiId;
    }

    @Override
    public String toString() {
        return "PoiHPha{" +
            "poiId=" + poiId +
            ", sourceId=" + sourceId +
            ", meshIdLink=" + meshIdLink +
            ", meshId=" + meshId +
            ", kindCode=" + kindCode +
            ", name=" + name +
            ", namePy=" + namePy +
            ", nameEng=" + nameEng +
            ", alias=" + alias +
            ", aliasPy=" + aliasPy +
            ", aliasEng=" + aliasEng +
            ", address=" + address +
            ", addressPy=" + addressPy +
            ", addressEng=" + addressEng +
            ", longitude=" + longitude +
            ", latitude=" + latitude +
            ", longitudeWgs84=" + longitudeWgs84 +
            ", latitudeWgs84=" + latitudeWgs84 +
            ", longitudeBd09=" + longitudeBd09 +
            ", latitudeBd09=" + latitudeBd09 +
            ", lonGuide=" + lonGuide +
            ", latGuide=" + latGuide +
            ", linkId=" + linkId +
            ", side=" + side +
            ", importance=" + importance +
            ", vadminCode=" + vadminCode +
            ", zipCode=" + zipCode +
            ", telephone=" + telephone +
            ", telType=" + telType +
            ", poiClass=" + poiClass +
            ", starRating=" + starRating +
            ", tgType=" + tgType +
            ", accessFlag=" + accessFlag +
            ", truckFlag=" + truckFlag +
            ", spVenue=" + spVenue +
            ", gate=" + gate +
            ", aoiId=" + aoiId +
            ", isAoi=" + isAoi +
            ", bzoneId=" + bzoneId +
            ", priorAuth=" + priorAuth +
            ", foodType=" + foodType +
            ", brand=" + brand +
            ", naviLoc=" + naviLoc +
            ", provCode=" + provCode +
            ", provName=" + provName +
            ", provNamePy=" + provNamePy +
            ", provNameEng=" + provNameEng +
            ", cityCode=" + cityCode +
            ", cityName=" + cityName +
            ", cityNamePy=" + cityNamePy +
            ", cityNameEng=" + cityNameEng +
            ", adCode=" + adCode +
            ", adName=" + adName +
            ", adNamePy=" + adNamePy +
            ", adNameEng=" + adNameEng +
            ", rank=" + rank +
            ", class=" + classInfo +
            ", dupList=" + dupList +
            ", isDup=" + isDup +
            ", parent=" + parent +
            ", isParent=" + isParent +
            ", children=" + children +
            ", isChild=" + isChild +
            ", poiSame=" + poiSame +
            ", heat=" + heat +
            ", rePrice=" + rePrice +
            ", openStatus=" + openStatus +
            ", accessNum=" + accessNum +
            ", parkNum=" + parkNum +
            ", memo=" + memo +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", poiGeo=" + poiGeo +
            ", swProname=" + swProname +
            ", kind=" + kind +
            ", nameS=" + nameS +
            ", nameSEng=" + nameSEng +
            ", countryCode=" + countryCode +
            ", countryName=" + countryName +
            ", countryNameEng=" + countryNameEng +
            ", regionCode=" + regionCode +
            ", regionName=" + regionName +
            ", regionNameEng=" + regionNameEng +
            ", status=" + status +
        "}";
    }
}
