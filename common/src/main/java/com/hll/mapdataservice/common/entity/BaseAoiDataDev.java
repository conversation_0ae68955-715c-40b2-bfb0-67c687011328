package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-31
 */
@ApiModel(value="BaseAoiDataDev对象", description="")
public class BaseAoiDataDev extends Model<BaseAoiDataDev> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "aoi_id", type = IdType.AUTO)
    private String aoiId;

    private String name;

    private String address;

    private String type;

    private String status;

    private String cityname;

    private String citycode;

    private String adname;

    private String adcode;

    private String parent;

    private String children;

    private String area;

    private String longitude;

    private String latitude;

    private String shape;

    private String createTime;

    private String updateTime;

    private String geometry;

    private String id;

    @ApiModelProperty(value = "数据的来源，通过融合时的数据来源赋值 0：高德 1：百度 2、四维 3、采集 4、BD绘制 5、……")
    private String source;

    @ApiModelProperty(value = "数据是否使用-1 不在使用")
    private String datastatus;

    private String tycode;

    @ApiModelProperty(value = "标明和上一版本相比是否更新")
    private String isupdate;

    public String getAoiId() {
        return aoiId;
    }

    public void setAoiId(String aoiId) {
        this.aoiId = aoiId;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public String getCityname() {
        return cityname;
    }

    public void setCityname(String cityname) {
        this.cityname = cityname;
    }
    public String getCitycode() {
        return citycode;
    }

    public void setCitycode(String citycode) {
        this.citycode = citycode;
    }
    public String getAdname() {
        return adname;
    }

    public void setAdname(String adname) {
        this.adname = adname;
    }
    public String getAdcode() {
        return adcode;
    }

    public void setAdcode(String adcode) {
        this.adcode = adcode;
    }
    public String getParent() {
        return parent;
    }

    public void setParent(String parent) {
        this.parent = parent;
    }
    public String getChildren() {
        return children;
    }

    public void setChildren(String children) {
        this.children = children;
    }
    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }
    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }
    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }
    public String getShape() {
        return shape;
    }

    public void setShape(String shape) {
        this.shape = shape;
    }
    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
    public String getGeometry() {
        return geometry;
    }

    public void setGeometry(String geometry) {
        this.geometry = geometry;
    }
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }
    public String getDatastatus() {
        return datastatus;
    }

    public void setDatastatus(String datastatus) {
        this.datastatus = datastatus;
    }
    public String getTycode() {
        return tycode;
    }

    public void setTycode(String tycode) {
        this.tycode = tycode;
    }
    public String getIsupdate() {
        return isupdate;
    }

    public void setIsupdate(String isupdate) {
        this.isupdate = isupdate;
    }

    @Override
    protected Serializable pkVal() {
        return this.aoiId;
    }

    @Override
    public String toString() {
        return "BaseAoiDataDev{" +
            "aoiId=" + aoiId +
            ", name=" + name +
            ", address=" + address +
            ", type=" + type +
            ", status=" + status +
            ", cityname=" + cityname +
            ", citycode=" + citycode +
            ", adname=" + adname +
            ", adcode=" + adcode +
            ", parent=" + parent +
            ", children=" + children +
            ", area=" + area +
            ", longitude=" + longitude +
            ", latitude=" + latitude +
            ", shape=" + shape +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", geometry=" + geometry +
            ", id=" + id +
            ", source=" + source +
            ", datastatus=" + datastatus +
            ", tycode=" + tycode +
            ", isupdate=" + isupdate +
        "}";
    }
}
