<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.DirectSw2021q133Mapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.DirectSw2021q133">
        <id column="direct_id" property="directId" />
        <result column="swdirectid" property="swdirectid" />
        <result column="in_linkid" property="inLinkid" />
        <result column="node_id" property="nodeId" />
        <result column="out_linkid" property="outLinkid" />
        <result column="pass" property="pass" />
        <result column="pass2" property="pass2" />
        <result column="flag" property="flag" />
        <result column="proc_flag" property="procFlag" />
        <result column="mesh_list" property="meshList" />
        <result column="memo" property="memo" />
        <result column="link_angle" property="linkAngle" />
        <result column="cp" property="cp" />
        <result column="datasource" property="datasource" />
        <result column="up_date" property="upDate" />
        <result column="status" property="status" />
    </resultMap>

</mapper>
