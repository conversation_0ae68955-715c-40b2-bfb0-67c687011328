package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2024/9/9
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PoiSample extends Model<PoiSample> {
    @TableId
    private String pickupId;
    private String pickupNameHlang;
    private String pickupAddrHlang;
    private Double startLatitude;
    private Double startLongitude;
}
