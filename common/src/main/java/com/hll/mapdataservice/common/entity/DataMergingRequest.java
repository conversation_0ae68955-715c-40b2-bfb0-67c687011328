package com.hll.mapdataservice.common.entity;

import lombok.Data;

/**
 * Request DTO for data merging API
 * 
 * @since 2025-01-28
 */
@Data
public class DataMergingRequest {
    
    /**
     * Country prefix (e.g., "twn", "bra", "mex")
     * Must match the country codes defined in CountryAreaEnum
     */
    private String countryPrefix;
    private String year;
    private String quarter;

    /**
     * Whether to perform dry run (validation only, no actual merging)
     * Default: false
     */
    private boolean dryRun = false;
    private boolean mergem2e = true;

    /**
     * Whether to continue merging other tables if one fails
     * Default: true (continue on error)
     */
    private boolean continueOnError = true;
    
    /**
     * Batch size for processing (optional, uses default if not specified)
     */
    private Integer batchSize;
    
    /**
     * Timeout in seconds for the entire operation (optional)
     */
    private Integer timeoutSeconds;
}
