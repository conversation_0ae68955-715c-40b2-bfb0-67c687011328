package com.hll.mapdataservice.common.service;

import com.hll.mapdataservice.common.entity.NodeM;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-28
 */
public interface INodeMService extends IService<NodeM> {

    void handleId(String area, String country, CountDownLatch countDownLatch, List<NodeM> nodeList);
}
