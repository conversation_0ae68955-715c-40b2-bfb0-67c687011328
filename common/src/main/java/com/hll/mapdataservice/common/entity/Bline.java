package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-10
 */
@ApiModel(value="Bline对象", description="")
public class Bline extends Model<Bline> {

    private static final long serialVersionUID = 1L;

    private String id;

    private String snodeId;

    private String enodeId;

    private String kind;

    private String name;

    private String nmLangcd;

    private String nmTr;

    private String transType;

    private String geometry;

    private String lineCntry;

    private String claimedBy;

    private String controlBy;

    private String dispClass;

    private Integer level;

    private String sourceId;

    private LocalDateTime upDate;

    private Integer status;

    private String datasource;

    private String source;

    private String areaId;

    @TableId(value = "id_source_area", type = IdType.INPUT)
    private String idSourceArea;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getSnodeId() {
        return snodeId;
    }

    public void setSnodeId(String snodeId) {
        this.snodeId = snodeId;
    }
    public String getEnodeId() {
        return enodeId;
    }

    public void setEnodeId(String enodeId) {
        this.enodeId = enodeId;
    }
    public String getKind() {
        return kind;
    }

    public void setKind(String kind) {
        this.kind = kind;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public String getNmLangcd() {
        return nmLangcd;
    }

    public void setNmLangcd(String nmLangcd) {
        this.nmLangcd = nmLangcd;
    }
    public String getNmTr() {
        return nmTr;
    }

    public void setNmTr(String nmTr) {
        this.nmTr = nmTr;
    }
    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }
    public String getGeometry() {
        return geometry;
    }

    public void setGeometry(String geometry) {
        this.geometry = geometry;
    }
    public String getLineCntry() {
        return lineCntry;
    }

    public void setLineCntry(String lineCntry) {
        this.lineCntry = lineCntry;
    }
    public String getClaimedBy() {
        return claimedBy;
    }

    public void setClaimedBy(String claimedBy) {
        this.claimedBy = claimedBy;
    }
    public String getControlBy() {
        return controlBy;
    }

    public void setControlBy(String controlBy) {
        this.controlBy = controlBy;
    }
    public String getDispClass() {
        return dispClass;
    }

    public void setDispClass(String dispClass) {
        this.dispClass = dispClass;
    }
    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }
    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }
    public LocalDateTime getUpDate() {
        return upDate;
    }

    public void setUpDate(LocalDateTime upDate) {
        this.upDate = upDate;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public String getDatasource() {
        return datasource;
    }

    public void setDatasource(String datasource) {
        this.datasource = datasource;
    }
    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }
    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }
    public String getIdSourceArea() {
        return idSourceArea;
    }

    public void setIdSourceArea(String idSourceArea) {
        this.idSourceArea = idSourceArea;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "Bline{" +
            "id=" + id +
            ", snodeId=" + snodeId +
            ", enodeId=" + enodeId +
            ", kind=" + kind +
            ", name=" + name +
            ", nmLangcd=" + nmLangcd +
            ", nmTr=" + nmTr +
            ", transType=" + transType +
            ", geometry=" + geometry +
            ", lineCntry=" + lineCntry +
            ", claimedBy=" + claimedBy +
            ", controlBy=" + controlBy +
            ", dispClass=" + dispClass +
            ", level=" + level +
            ", sourceId=" + sourceId +
            ", upDate=" + upDate +
            ", status=" + status +
            ", datasource=" + datasource +
            ", source=" + source +
            ", areaId=" + areaId +
            ", idSourceArea=" + idSourceArea +
        "}";
    }
}
