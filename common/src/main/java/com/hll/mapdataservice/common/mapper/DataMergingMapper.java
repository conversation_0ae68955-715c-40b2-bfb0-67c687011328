package com.hll.mapdataservice.common.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * Mapper for executing data merging operations
 * Handles raw SQL execution for merging partition tables
 * 
 * @since 2025-01-28
 */
@Mapper
public interface DataMergingMapper {
    
    /**
     * Execute INSERT INTO ... SELECT * FROM ... ON CONFLICT DO NOTHING
     * for merging partition table data into consolidated table
     * 
     * @param targetTable The target consolidated table name
     * @param sourceTable The source partition table name
     * @return Number of records inserted (excluding conflicts)
     */
    int mergePartitionTable(@Param("targetTable") String targetTable, 
                           @Param("sourceTable") String sourceTable);
    
    /**
     * Check if a table exists in the current database
     * 
     * @param tableName The table name to check
     * @return 1 if table exists, 0 if not
     */
    int checkTableExists(@Param("tableName") String tableName);
    
    /**
     * Get the count of records in a table
     * 
     * @param tableName The table name
     * @return Number of records in the table
     */
    long getTableRecordCount(@Param("tableName") String tableName);
    
    /**
     * Execute a custom merge operation with specific conflict resolution
     * This allows for more complex merging scenarios if needed
     * 
     * @param targetTable The target table
     * @param sourceTable The source table
     * @param conflictColumns Comma-separated list of columns for conflict detection
     * @return Number of records processed
     */
    int mergeWithCustomConflictResolution(@Param("targetTable") String targetTable,
                                        @Param("sourceTable") String sourceTable,
                                        @Param("conflictColumns") String conflictColumns);

    int transferRelationM2Relation();

    int transferRuleM2Rule();
}
