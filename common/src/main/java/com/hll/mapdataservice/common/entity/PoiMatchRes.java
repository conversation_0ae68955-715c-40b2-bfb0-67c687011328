package com.hll.mapdataservice.common.entity;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.TableField;
import com.hll.mapdataservice.common.utils.MyGeometryTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;

/**
  *
  *
  * @Author: ares.chen
  * @Since: 2021/10/12
  */
@ApiModel(value="poi_match_res")
public class PoiMatchRes {
    @ApiModelProperty(value="")
    private String id;

    /**
    * poi坐标
    */
    @ApiModelProperty(value="poi坐标")
    @TableField(typeHandler = MyGeometryTypeHandler.class)
    private String poiGeo;

    /**
    * 纬度
    */
    @ApiModelProperty(value="纬度")
    private BigDecimal latitude;

    /**
    * 经度
    */
    @ApiModelProperty(value="经度")
    private BigDecimal longitude;

    /**
    * 谷歌poi名称
    */
    @ApiModelProperty(value="谷歌poi名称")
    private String googlePoiName;

    /**
    * here poi名称
    */
    @ApiModelProperty(value="here poi名称")
    private String herePoiName;

    /**
    * 相似度
    */
    @ApiModelProperty(value="相似度")
    private BigDecimal similarity;

    /**
    * 是否匹配，1匹配，0不匹配
    */
    @ApiModelProperty(value="是否匹配，1匹配，0不匹配")
    private Integer isMatch;

    /**
    * 版本号，区分批次数据
    */
    @ApiModelProperty(value="版本号，区分批次数据")
    private Integer version;

    /**
    * 创建时间
    */
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createTime;

    /**
    * 修改时间
    */
    @ApiModelProperty(value="修改时间")
    private LocalDateTime updateTime;

    /**
     * hll place id
     */
    @ApiModelProperty(value="hll order poi id")
    private String hllOrderPoiId;

    /**
     * google poi id
     */
    @ApiModelProperty(value="谷歌匹配数据id")
    private String googleMatchPoiId;

    /**
     * here poi id
     */
    @ApiModelProperty(value="here匹配数据id")
    private String hereMatchPoiId;

    public String getGooglePoiId() {
        return googlePoiId;
    }

    public void setGooglePoiId(String googlePoiId) {
        this.googlePoiId = googlePoiId;
    }

    /**
     * google place_id
     */
    @ApiModelProperty(value="google poi id")
    private String googlePoiId;

    public String getHerePoiId() {
        return herePoiId;
    }

    public void setHerePoiId(String herePoiId) {
        this.herePoiId = herePoiId;
    }

    /**
     * here poi id
     */
    @ApiModelProperty(value="here poi id")
    private String herePoiId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPoiGeo() {
        return poiGeo;
    }

    public void setPoiGeo(String poiGeo) {
        this.poiGeo = poiGeo;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public String getGooglePoiName() {
        return googlePoiName;
    }

    public void setGooglePoiName(String googlePoiName) {
        this.googlePoiName = googlePoiName;
    }

    public String getHerePoiName() {
        return herePoiName;
    }

    public void setHerePoiName(String herePoiName) {
        this.herePoiName = herePoiName;
    }

    public BigDecimal getSimilarity() {
        return similarity;
    }

    public void setSimilarity(BigDecimal similarity) {
        this.similarity = similarity;
    }

    public Integer getIsMatch() {
        return isMatch;
    }

    public void setIsMatch(Integer isMatch) {
        this.isMatch = isMatch;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getHllOrderPoiId() {
        return hllOrderPoiId;
    }

    public void setHllOrderPoiId(String hllOrderPoiId) {
        this.hllOrderPoiId = hllOrderPoiId;
    }

    public String getGoogleMatchPoiId() {
        return googleMatchPoiId;
    }

    public void setGoogleMatchPoiId(String googleMatchPoiId) {
        this.googleMatchPoiId = googleMatchPoiId;
    }

    public String getHereMatchPoiId() {
        return hereMatchPoiId;
    }

    public void setHereMatchPoiId(String hereMatchPoiId) {
        this.hereMatchPoiId = hereMatchPoiId;
    }
}