package com.hll.mapdataservice.common.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.hll.mapdataservice.common.entity.MnrPhaNetwGeoLink;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hll.mapdataservice.common.vo.LinkMainVo;
import com.hll.mapdataservice.common.vo.Maneuver;
import com.hll.mapdataservice.common.vo.RoadProperty;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-19
 */
@DS("db6")
public interface MnrPhaNetwGeoLinkMapper extends BaseMapper<MnrPhaNetwGeoLink> {
    @Select("SELECT n.feat_id, ST_AsText(n.geom) as geom, n.centimeters, r.display_class, r.routing_class,r.form_of_way, r.simple_traffic_direction,r.num_of_lanes,r.speedmax_neg,\n" +
            "r.speedmax_pos, r.part_of_structure,r.freeway,r.ramp,r.ownership,r.name,r.road_condition,r.no_through_traffic, \n" +
            "(select 1 from (SELECT netw_id as feat_id, restriction_id FROM core.mnr_netw2restriction) n LEFT JOIN " +
            "core.mnr_restriction rr on n.restriction_id = rr.restriction_id \n" +
            " ${ew.customSqlSegment} AND rr.restriction_type=1) as jszt, '' AS zdzzl," +
//            "(select rr.limitation from (SELECT netw_id as feat_id, restriction_id FROM logistics.mnr_netw2restriction) n " +
//            "LEFT JOIN logistics.mnr_restriction rr on n.restriction_id = rr.restriction_id \n" +
//            " ${ew.customSqlSegment} AND rr.restriction_type=101) as zdzzl,\n" +
            "(select rr.limitation from (SELECT netw_id as feat_id, restriction_id FROM logistics.mnr_netw2restriction) n " +
            "LEFT JOIN logistics.mnr_restriction rr on n.restriction_id = rr.restriction_id \n" +
            " ${ew.customSqlSegment}  AND rr.restriction_type=102) as zdzz,\n" +
            "(select rr.limitation from (SELECT netw_id as feat_id, restriction_id FROM logistics.mnr_netw2restriction) n " +
            "LEFT JOIN logistics.mnr_restriction rr on n.restriction_id = rr.restriction_id \n" +
            " ${ew.customSqlSegment}  AND rr.restriction_type=103) as zdyxcd,\n" +
            "(select rr.limitation from (SELECT netw_id as feat_id, restriction_id FROM logistics.mnr_netw2restriction) n " +
            "LEFT JOIN logistics.mnr_restriction rr on n.restriction_id = rr.restriction_id \n" +
            " ${ew.customSqlSegment}  AND rr.restriction_type=104) as zdyxkd,\n" +
            "(select rr.limitation from (SELECT netw_id as feat_id, restriction_id FROM logistics.mnr_netw2restriction) n " +
            "LEFT JOIN logistics.mnr_restriction rr on n.restriction_id = rr.restriction_id \n" +
            " ${ew.customSqlSegment}  AND rr.restriction_type=105) as zdyxgd \n" +
            "FROM core.mnr_pha_netw_geo_link n left JOIN core.mnr_netw_route_link r on n.feat_id = r.netw_geo_id ${ew.customSqlSegment}")
    RoadProperty getRoadInfo(@Param(Constants.WRAPPER) Wrapper wrapper);

    @Select("select feat_id,feat_type from core.mnr_maneuver n ${ew.customSqlSegment}")
    Maneuver getManeuverBase(@Param(Constants.WRAPPER)Wrapper wrapper);

    @Select("select p.maneuver_seq, ST_AsText(nn.geom) as geom, nn.feat_id from core.mnr_maneuver_path_idx p left join core.mnr_pha_netw_geo_link nn on p.netw_id=nn.feat_id \n" +
            "left join core.mnr_maneuver n on p.maneuver_id=n.feat_id " +
            "${ew.customSqlSegment}")
    List<LinkMainVo> getManeuverLink(@Param(Constants.WRAPPER)Wrapper wrapper);
}
