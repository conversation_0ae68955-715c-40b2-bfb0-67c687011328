package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-25
 */
@ApiModel(value="Mtdref对象", description="")
public class Mtdref extends Model<Mtdref> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "\"REF_CLASS\"", type = IdType.AUTO)
    private String refClass;

    @TableField("\"CODE\"")
    private String code;

    @TableField("\"CODE_TR\"")
    private String codeTr;

    @TableField("\"DESCRIPT\"")
    private String descript;

    @TableField("\"LANG_CODE\"")
    private String langCode;

    @TableField("\"DESC_TR\"")
    private String descTr;

    @TableField("\"TRANS_TYPE\"")
    private String transType;

    public String getRefClass() {
        return refClass;
    }

    public void setRefClass(String refClass) {
        this.refClass = refClass;
    }
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
    public String getCodeTr() {
        return codeTr;
    }

    public void setCodeTr(String codeTr) {
        this.codeTr = codeTr;
    }
    public String getDescript() {
        return descript;
    }

    public void setDescript(String descript) {
        this.descript = descript;
    }
    public String getLangCode() {
        return langCode;
    }

    public void setLangCode(String langCode) {
        this.langCode = langCode;
    }
    public String getDescTr() {
        return descTr;
    }

    public void setDescTr(String descTr) {
        this.descTr = descTr;
    }
    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }

    @Override
    protected Serializable pkVal() {
        return this.refClass;
    }

    @Override
    public String toString() {
        return "Mtdref{" +
            "refClass=" + refClass +
            ", code=" + code +
            ", codeTr=" + codeTr +
            ", descript=" + descript +
            ", langCode=" + langCode +
            ", descTr=" + descTr +
            ", transType=" + transType +
        "}";
    }
}
