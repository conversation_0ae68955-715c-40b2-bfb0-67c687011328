package com.hll.mapdataservice.common.vo;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/*
 * <AUTHOR>
 * @date  3/3/21 2:21 PM
 * @Email:<EMAIL>
 */
@Data
@Getter
@Setter
public class PoiInfo implements Serializable {
    private String id;
    private String name;
    private String address;
    private String lon;
    private String lat;
    private String poiGeom;
    private String foursquareValid;
    private String trueId;
    private String trueAddress;
    private String trueGeom;

    private String address1;

    public String getAddress1() {
        return address1;
    }

    public void setAddress1(String address1) {
        this.address1 = address1;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getLon() {
        return lon;
    }

    public void setLon(String lon) {
        this.lon = lon;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getFoursquareValid() {
        return foursquareValid;
    }

    public void setFoursquareValid(String foursquareValid) {
        this.foursquareValid = foursquareValid;
    }

    public String getTrueId() {
        return trueId;
    }

    public void setTrueId(String trueId) {
        this.trueId = trueId;
    }

    public String getPoiGeom() {
        return poiGeom;
    }

    public void setPoiGeom(String poiGeom) {
        this.poiGeom = poiGeom;
    }

    public String getTrueAddress() {
        return trueAddress;
    }

    public void setTrueAddress(String trueAddress) {
        this.trueAddress = trueAddress;
    }

    public String getTrueGeom() {
        return trueGeom;
    }

    public void setTrueGeom(String trueGeom) {
        this.trueGeom = trueGeom;
    }
}
