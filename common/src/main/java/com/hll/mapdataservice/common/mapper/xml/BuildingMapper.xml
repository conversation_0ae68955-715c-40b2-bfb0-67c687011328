<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.BuildingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.Building">
        <id column="id" property="id" />
        <result column="class_code" property="classCode" />
        <result column="show" property="show" />
        <result column="height" property="height" />
        <result column="carto_id" property="cartoId" />
        <result column="name" property="name" />
        <result column="nm_langcd" property="nmLangcd" />
        <result column="nm_tr" property="nmTr" />
        <result column="trans_type" property="transType" />
        <result column="geometry" property="geometry" />
    </resultMap>

</mapper>
