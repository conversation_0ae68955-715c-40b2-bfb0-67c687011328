package com.hll.mapdataservice.common.entity;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.hll.mapdataservice.common.utils.MyGeometryTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@ApiModel(value="NodeSw2021q133对象", description="")
@TableName(value = "node_sw2021q1_33")
//@DS("db8")
public class NodeSw2021q133 extends Model<NodeSw2021q133> {

    private static final long serialVersionUID = 1L;

    //@TableId(value = "hll_nodeid")
    private String hllNodeid;

    private String kind;

    @TableField(value = "geom",typeHandler = MyGeometryTypeHandler.class)
    private String geom;

    private String nameCh;

    private String nameFo;

    private String nameCht;

    private String namePh;

    private String adjoinMid;

    private String adjoinNid;

    private String type;

    private String mainnodeid;

    private String subnodeid;

    private String subnodeid2;

    private String light;

    private String isPbnode;

    private String cp;

    private String datasource;

    @TableId(value = "node_id",type=IdType.INPUT)
    private String nodeId;

    private LocalDateTime upDate;

    private String memo;

    private String status;

    public String getGeomwkt() {
        return geomwkt;
    }

    public void setGeomwkt(String geomwkt) {
        this.geomwkt = geomwkt;
    }

    @TableField(value = "geom_wkt")
    private String geomwkt;

    public String getHllNodeid() {
        return hllNodeid;
    }

    public void setHllNodeid(String hllNodeid) {
        this.hllNodeid = hllNodeid;
    }
    public String getKind() {
        return kind;
    }

    public void setKind(String kind) {
        this.kind = kind;
    }
    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }
    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }
    public String getNameFo() {
        return nameFo;
    }

    public void setNameFo(String nameFo) {
        this.nameFo = nameFo;
    }
    public String getNameCht() {
        return nameCht;
    }

    public void setNameCht(String nameCht) {
        this.nameCht = nameCht;
    }
    public String getNamePh() {
        return namePh;
    }

    public void setNamePh(String namePh) {
        this.namePh = namePh;
    }
    public String getAdjoinMid() {
        return adjoinMid;
    }

    public void setAdjoinMid(String adjoinMid) {
        this.adjoinMid = adjoinMid;
    }
    public String getAdjoinNid() {
        return adjoinNid;
    }

    public void setAdjoinNid(String adjoinNid) {
        this.adjoinNid = adjoinNid;
    }
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
    public String getMainnodeid() {
        return mainnodeid;
    }

    public void setMainnodeid(String mainnodeid) {
        this.mainnodeid = mainnodeid;
    }
    public String getSubnodeid() {
        return subnodeid;
    }

    public void setSubnodeid(String subnodeid) {
        this.subnodeid = subnodeid;
    }
    public String getSubnodeid2() {
        return subnodeid2;
    }

    public void setSubnodeid2(String subnodeid2) {
        this.subnodeid2 = subnodeid2;
    }
    public String getLight() {
        return light;
    }

    public void setLight(String light) {
        this.light = light;
    }
    public String getIsPbnode() {
        return isPbnode;
    }

    public void setIsPbnode(String isPbnode) {
        this.isPbnode = isPbnode;
    }
    public String getCp() {
        return cp;
    }

    public void setCp(String cp) {
        this.cp = cp;
    }
    public String getDatasource() {
        return datasource;
    }

    public void setDatasource(String datasource) {
        this.datasource = datasource;
    }
    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }
    public LocalDateTime getUpDate() {
        return upDate;
    }

    public void setUpDate(LocalDateTime upDate) {
        this.upDate = upDate;
    }
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    protected Serializable pkVal() {
        return this.hllNodeid;
    }

    @Override
    public String toString() {
        return "NodeSw2021q133{" +
            "hllNodeid=" + hllNodeid +
            ", kind=" + kind +
            ", geom=" + geom +
            ", nameCh=" + nameCh +
            ", nameFo=" + nameFo +
            ", nameCht=" + nameCht +
            ", namePh=" + namePh +
            ", adjoinMid=" + adjoinMid +
            ", adjoinNid=" + adjoinNid +
            ", type=" + type +
            ", mainnodeid=" + mainnodeid +
            ", subnodeid=" + subnodeid +
            ", subnodeid2=" + subnodeid2 +
            ", light=" + light +
            ", isPbnode=" + isPbnode +
            ", cp=" + cp +
            ", datasource=" + datasource +
            ", nodeId=" + nodeId +
            ", upDate=" + upDate +
            ", memo=" + memo +
            ", status=" + status +
        "}";
    }
}
