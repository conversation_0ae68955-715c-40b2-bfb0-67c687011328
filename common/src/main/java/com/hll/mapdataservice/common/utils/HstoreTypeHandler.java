package com.hll.mapdataservice.common.utils;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

public class HstoreTypeHandler extends BaseTypeHandler<Map<String, String>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Map<String, String> parameter, JdbcType jdbcType) throws SQLException {
        // 将 Map<String, String> 转换为 hstore 格式字符串
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : parameter.entrySet()) {
            sb.append(entry.getKey()).append(" => ").append(entry.getValue()).append(", ");
        }
        if (sb.length() > 0) {
            sb.setLength(sb.length() - 2); // 去掉最后的 ", "
        }
        ps.setString(i, sb.toString());
    }

    @Override
    public Map<String, String> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String hstoreStr = rs.getString(columnName);
        return parseHstore(hstoreStr);
    }

    @Override
    public Map<String, String> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String hstoreStr = rs.getString(columnIndex);
        return parseHstore(hstoreStr);
    }

    @Override
    public Map<String, String> getNullableResult(java.sql.CallableStatement cs, int columnIndex) throws SQLException {
        String hstoreStr = cs.getString(columnIndex);
        return parseHstore(hstoreStr);
    }

    private Map<String, String> parseHstore(String hstoreStr) {
        Map<String, String> map = new HashMap<>();
        if (hstoreStr != null && !hstoreStr.isEmpty()) {
            // 解析 hstore 格式字符串，格式: key1 => value1, key2 => value2
            String[] entries = hstoreStr.split(", ");
            for (String entry : entries) {
                String[] keyValue = entry.split("=>");
                if (keyValue.length == 2) {
                    map.put(keyValue[0].replace("\"",""), keyValue[1].replace("\"",""));
                }
            }
        }
        return map;
    }
}