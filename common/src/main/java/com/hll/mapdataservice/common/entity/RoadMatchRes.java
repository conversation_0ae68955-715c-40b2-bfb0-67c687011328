package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hll.mapdataservice.common.utils.MyGeometryTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
  *道路匹配结果
  *
  * @Author: ares.chen
  * @Since: 2021/11/18
  */
@ApiModel(value="道路匹配结果")
//@TableName("road_match_res2")
public class RoadMatchRes {
    @ApiModelProperty(value="")
    private String id;

    /**
    * link主键
    */
    @ApiModelProperty(value="link主键")
    private String linkId;

    /**
     * 截断后的osmId
     */
    @ApiModelProperty(value="截断后的osmId")
    private String roadId;

    /**
     * osm主键
     */
    @ApiModelProperty(value="osm主键")
    private String osmId;

    /**
    * link坐标
    */
    @ApiModelProperty(value="link坐标")
    private String linkGeom;

    @TableField(value = "ST_AsText(link_geom)",exist = false)
    private String linkGeomWkt;

    /**
    * link方向
    */
    @ApiModelProperty(value="link方向")
    private String linkDir;

    /**
    * osm坐标
    */
    @ApiModelProperty(value="osm坐标")
    //@TableField(typeHandler = MyGeometryTypeHandler.class)
    private String roadGeom;

    @TableField(value = "ST_AsText(road_geom)",exist = false)
    private String roadGeomWkt;

    /**
    * osm方向
    */
    @ApiModelProperty(value="osm方向")
    private String roadDir;

    /**
    * 匹配结果
    */
    @ApiModelProperty(value="匹配结果")
    private BigDecimal matchRes;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value="修改时间")
    private LocalDateTime updateTime;

    /**
     * 版本号
     */
    @ApiModelProperty(value="版本号")
    private Long version;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLinkId() {
        return linkId;
    }

    public void setLinkId(String linkId) {
        this.linkId = linkId;
    }

    public String getLinkGeom() {
        return linkGeom;
    }

    public void setLinkGeom(String linkGeom) {
        this.linkGeom = linkGeom;
    }

    public String getLinkDir() {
        return linkDir;
    }

    public void setLinkDir(String linkDir) {
        this.linkDir = linkDir;
    }

    public String getRoadGeom() {
        return roadGeom;
    }

    public void setRoadGeom(String roadGeom) {
        this.roadGeom = roadGeom;
    }

    public String getRoadDir() {
        return roadDir;
    }

    public void setRoadDir(String roadDir) {
        this.roadDir = roadDir;
    }

    public BigDecimal getMatchRes() {
        return matchRes;
    }

    public void setMatchRes(BigDecimal matchRes) {
        this.matchRes = matchRes;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getRoadId() {
        return roadId;
    }

    public void setRoadId(String roadId) {
        this.roadId = roadId;
    }

    public String getOsmId() {
        return osmId;
    }

    public void setOsmId(String osmId) {
        this.osmId = osmId;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String getLinkGeomWkt() {
        return linkGeomWkt;
    }

    public void setLinkGeomWkt(String linkGeomWkt) {
        this.linkGeomWkt = linkGeomWkt;
    }

    public String getRoadGeomWkt() {
        return roadGeomWkt;
    }

    public void setRoadGeomWkt(String roadGeomWkt) {
        this.roadGeomWkt = roadGeomWkt;
    }
}