<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.PoiHPhaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.PoiHPha">
        <id column="poi_id" property="poiId" />
        <result column="source_id" property="sourceId" />
        <result column="mesh_id_link" property="meshIdLink" />
        <result column="mesh_id" property="meshId" />
        <result column="kind_code" property="kindCode" />
        <result column="name" property="name" />
        <result column="name_py" property="namePy" />
        <result column="name_eng" property="nameEng" />
        <result column="alias" property="alias" />
        <result column="alias_py" property="aliasPy" />
        <result column="alias_eng" property="aliasEng" />
        <result column="address" property="address" />
        <result column="address_py" property="addressPy" />
        <result column="address_eng" property="addressEng" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
        <result column="longitude_wgs84" property="longitudeWgs84" />
        <result column="latitude_wgs84" property="latitudeWgs84" />
        <result column="longitude_bd09" property="longitudeBd09" />
        <result column="latitude_bd09" property="latitudeBd09" />
        <result column="lon_guide" property="lonGuide" />
        <result column="lat_guide" property="latGuide" />
        <result column="link_id" property="linkId" />
        <result column="side" property="side" />
        <result column="importance" property="importance" />
        <result column="vadmin_code" property="vadminCode" />
        <result column="zip_code" property="zipCode" />
        <result column="telephone" property="telephone" />
        <result column="tel_type" property="telType" />
        <result column="poi_class" property="poiClass" />
        <result column="star_rating" property="starRating" />
        <result column="tg_type" property="tgType" />
        <result column="access_flag" property="accessFlag" />
        <result column="truck_flag" property="truckFlag" />
        <result column="sp_venue" property="spVenue" />
        <result column="gate" property="gate" />
        <result column="aoi_id" property="aoiId" />
        <result column="is_aoi" property="isAoi" />
        <result column="bzone_id" property="bzoneId" />
        <result column="prior_auth" property="priorAuth" />
        <result column="food_type" property="foodType" />
        <result column="brand" property="brand" />
        <result column="navi_loc" property="naviLoc" />
        <result column="prov_code" property="provCode" />
        <result column="prov_name" property="provName" />
        <result column="prov_name_py" property="provNamePy" />
        <result column="prov_name_eng" property="provNameEng" />
        <result column="city_code" property="cityCode" />
        <result column="city_name" property="cityName" />
        <result column="city_name_py" property="cityNamePy" />
        <result column="city_name_eng" property="cityNameEng" />
        <result column="ad_code" property="adCode" />
        <result column="ad_name" property="adName" />
        <result column="ad_name_py" property="adNamePy" />
        <result column="ad_name_eng" property="adNameEng" />
        <result column="rank" property="rank" />
        <result column="class" property="classInfo" />
        <result column="dup_list" property="dupList" />
        <result column="is_dup" property="isDup" />
        <result column="parent" property="parent" />
        <result column="is_parent" property="isParent" />
        <result column="children" property="children" />
        <result column="is_child" property="isChild" />
        <result column="poi_same" property="poiSame" />
        <result column="heat" property="heat" />
        <result column="re_price" property="rePrice" />
        <result column="open_status" property="openStatus" />
        <result column="access_num" property="accessNum" />
        <result column="park_num" property="parkNum" />
        <result column="memo" property="memo" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="poi_geo" property="poiGeo" />
        <result column="sw_proname" property="swProname" />
        <result column="kind" property="kind" />
        <result column="name_s" property="nameS" />
        <result column="name_s_eng" property="nameSEng" />
        <result column="country_code" property="countryCode" />
        <result column="country_name" property="countryName" />
        <result column="country_name_eng" property="countryNameEng" />
        <result column="region_code" property="regionCode" />
        <result column="region_name" property="regionName" />
        <result column="region_name_eng" property="regionNameEng" />
        <result column="status" property="status" />
    </resultMap>

</mapper>
