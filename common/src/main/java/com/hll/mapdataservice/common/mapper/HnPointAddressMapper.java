package com.hll.mapdataservice.common.mapper;

import com.hll.mapdataservice.common.entity.HnPointAddress;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Classname HnPointAddressMapper
 * @Description TODO
 * @Date 2021/12/9 4:14 下午
 * @Created by qunfu
 */
@Repository
public interface HnPointAddressMapper extends RootMapper<HnPointAddress> {
    List<HnPointAddress> selectUnhandleIds(int step, int i);
}
