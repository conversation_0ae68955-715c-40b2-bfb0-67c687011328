/**
  * Copyright 2021 bejson.com 
  */
package com.hll.mapdataservice.common.utils.po.hereplacenamepo;

import java.util.List;

/**
 * Auto-generated: 2021-04-02 11:27:34
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class AdditionalContactInfo {

    private long LocalNumber;
    private List<AdditionalData> AdditionalData;
    private int AreaCode;
    private String StandardNumber;
    private int CountryCode;
    public void setLocalNumber(long LocalNumber) {
         this.LocalNumber = LocalNumber;
     }
     public long getLocalNumber() {
         return LocalNumber;
     }

    public void setAdditionalData(List<AdditionalData> AdditionalData) {
         this.AdditionalData = AdditionalData;
     }
     public List<AdditionalData> getAdditionalData() {
         return AdditionalData;
     }

    public void setAreaCode(int AreaCode) {
         this.AreaCode = AreaCode;
     }
     public int getAreaCode() {
         return AreaCode;
     }

    public void setStandardNumber(String StandardNumber) {
         this.StandardNumber = StandardNumber;
     }
     public String getStandardNumber() {
         return StandardNumber;
     }

    public void setCountryCode(int CountryCode) {
         this.CountryCode = CountryCode;
     }
     public int getCountryCode() {
         return CountryCode;
     }

}