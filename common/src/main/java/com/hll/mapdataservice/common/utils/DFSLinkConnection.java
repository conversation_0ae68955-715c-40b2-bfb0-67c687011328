package com.hll.mapdataservice.common.utils;


import java.util.*;
import java.util.logging.Logger;

import static com.hll.mapdataservice.common.utils.ExcelUtils.readExcel;

public class DFSLinkConnection {
    private static Logger logger = Logger.getLogger(DFSLinkConnection.class.getName());
    private Map<Integer, List<Integer>> graph; // 道路网络的图表示
    private List<List<Integer>> paths; // 存储所有连接路径的列表

    /**
     * 构建连接道路
     *
     * @param roads     道路网络的道路列表
     * @param direction 道路网络的方向 0:单向 1:双向
     * @param showAll   是否显示所有路径
     * @param strategy  策略0，默认；策略1，剪枝策略
     * @param limitPathNum 限制最少的路径数量，大于此数量的路径才会被展示
     */
    public List<List<Integer>> connectRoads(int[][] roads, int direction, boolean showAll, int limitPathNum, int strategy) {
        graph = new HashMap<>();
        paths = new ArrayList<>();

        // 构建道路网络的图表示
        buildGraph(roads, direction);
        System.out.println("graph size: " + graph.size());

//        Set<Integer> visited = new HashSet<>();
        Set<Integer> currentPath = new HashSet<>();

        // 执行DFS算法，找到所有连接路径
        for (int start : graph.keySet()) {
            System.out.println("processing: " + start);
            logger.info("processing: " + start);
//            boolean[] visited = new boolean[graph.size()];
            Set<Integer> visited = new HashSet<>();
//            List<Integer> visited = new ArrayList<>();
            if(start == 1189404913){
                System.out.println("start: " + start);
            }

            List<Integer> path = new ArrayList<>();
            if (strategy == 1){
                dfs(start, visited, currentPath, path, direction, showAll, limitPathNum);
            } else {
                dfs(start, visited, path, direction, showAll, limitPathNum);
            }

//            System.out.println("start: " + start + " paths: " + paths.size());
//            System.out.println("paths.0:" + paths.get(0));
//            System.out.println("paths.size()-1:" + (paths.size()-1) + "," + paths.get(paths.size()-1));
            System.out.println("process:" + start + " finished!");
            logger.info("process:" + start + " finished!");
            System.out.println("paths size: " + paths.size());
        }

        return paths;
    }
    /**
     * 构建连接道路
     *
     * @param roads     道路网络的道路列表
     * @param direction 道路网络的方向 0:单向 1:双向
     */
    private void buildGraph(int[][] roads, int direction) {
        for (int[] road : roads) {
            int from = road[0];
            int to = road[1];
            //single direction
            if (direction == 0) {
                graph.putIfAbsent(from, new ArrayList<>());
                graph.get(from).add(to);
            } else {
                graph.putIfAbsent(from, new ArrayList<>());
                graph.putIfAbsent(to, new ArrayList<>());
                graph.get(from).add(to);
                graph.get(to).add(from);
            }

        }
    }

    private void dfs(int current, Set<Integer> visited, Set<Integer> currentPath, List<Integer> path, int direction, boolean showAll, int limitPathNum) {
        visited.add(current);
        currentPath.add(current);
        path.add(current);

        if (direction == 0) {//single direction
            if (graph.get(current) != null) {
                for (int neighbor : graph.get(current)) {
                    if (!visited.contains(neighbor) && !currentPath.contains(neighbor)) {
                        dfs(neighbor, visited, currentPath, path, direction, showAll, limitPathNum);
                    }
                }
            } else {
                if (showAll) {
                    if (path.size() > limitPathNum) {
                        paths.add(new ArrayList<>(path));
                    }
                } else {
                    if (!listContainsAll(paths, path)) {
                        if (path.size() > limitPathNum) {
                            paths.add(new ArrayList<>(path));
                        }
                    }
                }
            }
            visited.remove(current);
            path.remove(path.size() - 1);
            currentPath.remove(current);
        } else {
            if (graph.get(current).size() == 1 && visited.size() > 1) {
                // 当前节点是末端节点，添加连接路径到结果列表
                if (showAll) {
                    if (path.size() > limitPathNum) {
                        paths.add(new ArrayList<>(path));
                    }
                } else {
                    if (!listContainsAll(paths, path)) {
                        if (path.size() > limitPathNum) {
                            paths.add(new ArrayList<>(path));
                        }
                    }

                }
            } else {
                for (int neighbor : graph.get(current)) {
                    if (!visited.contains(neighbor) && !currentPath.contains(neighbor)) {
                        dfs(neighbor, visited, currentPath, path, direction, showAll, limitPathNum);
                    }
                }
                if (showAll) {
                    if (path.size() > limitPathNum) {
                        paths.add(new ArrayList<>(path));
                    }
                } else {
                    if (!listContainsAll(paths, path)) {
                        if (path.size() > limitPathNum) {
                            paths.add(new ArrayList<>(path));
                        }
                    }
                }


            }
            visited.remove(current);
            path.remove(path.size() - 1);
            currentPath.remove(current);
        }
    }


    private void dfs(int current, Set<Integer> visited, List<Integer> path, int direction, boolean showAll, int limitPathNum) {
//        if(current == 1147348619){
//            System.out.println("current: " + current);
//        }
        visited.add(current);
        path.add(current);

        if (direction == 0) {//single direction
            if (graph.get(current) != null) {
                for (int neighbor : graph.get(current)) {
                    if (!visited.contains(neighbor)) {
                        dfs(neighbor, visited, path, direction, showAll, limitPathNum);
                    }
                }
            } else {
                if (showAll) {
                    if (path.size() > limitPathNum) {
                        paths.add(new ArrayList<>(path));
                    }
                } else {
                    if (!listContainsAll(paths, path)) {
                        if (path.size() > limitPathNum) {
                            paths.add(new ArrayList<>(path));
                        }
                    }
                }
            }
            visited.remove(current);
            path.remove(path.size() - 1);
        } else {
            if (graph.get(current).size() == 1 && visited.size() > 1) {
                // 当前节点是末端节点，添加连接路径到结果列表
                if (showAll) {
                    if (path.size() > limitPathNum) {
                        paths.add(new ArrayList<>(path));
                    }
                } else {
                    if (!listContainsAll(paths, path)) {
                        if (path.size() > limitPathNum) {
                            paths.add(new ArrayList<>(path));
                        }
                    }

                }
            } else {
                for (int neighbor : graph.get(current)) {
                    if (!visited.contains(neighbor)) {
                        dfs(neighbor, visited, path, direction, showAll, limitPathNum);
                    }
                }
                if (showAll) {
                    if (path.size() > limitPathNum) {
                        paths.add(new ArrayList<>(path));
                    }
                } else {
                    if (!listContainsAll(paths, path)) {
                        if (path.size() > limitPathNum) {
                            paths.add(new ArrayList<>(path));
                        }
                    }
                }


            }
            visited.remove(current);
            path.remove(path.size() - 1);
        }
    }
    private boolean listContainsAll(List<List<Integer>> paths, List<Integer> path) {

        Iterator<List<Integer>> iterator = paths.iterator();
        while (iterator.hasNext()) {
            List<Integer> p = iterator.next();
            if (containsAllOrdered(p,path)) {
//            if(p.containsAll(path)){
                return true;
            }
            if (containsAllOrdered(path,p)) {
                //remove already exist path which is subset of new path
//                System.out.println("path contains remove path: " + path);
                iterator.remove();
//                System.out.println("remove path: " + p);
            }
        }
//        for (List<Integer> p : paths) {
//            if (containsAllOrdered(p,path)) {
////            if(p.containsAll(path)){
//                return true;
//            }
//            if (containsAllOrdered(path,p)) {
//                //remove already exist path which is subset of new path
//                paths.remove(p);
//                System.out.println("remove path: " + p);
////                return false;
//            }
//        }
        return false;
    }
    private static <T> boolean isSubsetWithOrderAndNoExtraElements(List<T> list1, List<T> list2) {
        int index1 = 0;
        int index2 = 0;

        while (index1 < list1.size() && index2 < list2.size()) {
            if (!list1.get(index1).equals(list2.get(index2))) {
                return false;  // 如果有不一样的元素，则返回 false
            }
            index1++;
            index2++;
        }

        return index2 == list2.size();
    }
    private static <T> boolean containsAllOrdered(List<T> list1, List<T> list2) {
        int index = 0;
        int list1index = 0;
        boolean start = false;

        for (T element : list1) {
            if(start){
                list1index++;
            }
            if (element.equals(list2.get(index))) {
                index++;
                start = true;
                if (index == list2.size()) {
                    start = false;
                    if(index == list1index + 1){
                        return true;
                    }
//                    return true;  // list1 包含了 list2 的所有元素并按顺序出现
                }
            }
        }

        return false;
    }

    private static <T> boolean isSubsetWithOrder(List<T> list1, List<T> list2) {
        int index1 = 0;
        int index2 = 0;

        while (index1 < list1.size() && index2 < list2.size()) {
            if (list1.get(index1).equals(list2.get(index2))) {
                index2++;
            }
            index1++;
        }

        return index2 == list2.size();
    }

    public static void main(String[] args) {
        Object[][] data = readExcel("/Users/<USER>/Downloads/result_1687257182473.xlsx", "sheet4");
//        int[][] roads = new int[data.length-1][2];
//        for (int i = 1; i < data.length; i++) {
        int[][] roads = new int[7000][2];
        for (int i = 1; i < 7001; i++) {
//            System.out.println(data[i][0] + "," + data[i][1]);
            roads[i - 1][0] = Integer.parseInt((String) data[i][0]);
            roads[i - 1][1] = Integer.parseInt((String) data[i][1]);
        }
        System.out.println(roads.length);
//        int[][] roads = {
//                {1, 2},
//                {2, 3},
//                {3, 4},
//                {4, 5},
//                {5, 11},
//                {5, 6},
//                {6, 7},
//                {7, 8},
//                {8, 10},
//                {8, 9},
//                {11, 12},
//                {5, 7}
//
//        };

        Arrays.sort(roads, new Comparator<int[]>() {
            @Override
            public int compare(int[] row1, int[] row2) {
                return row1[0] - row2[0]; // 按照第一列进行排序
            }
        });
        DFSLinkConnection roadConnection = new DFSLinkConnection();
        List<List<Integer>> paths = roadConnection.connectRoads(roads, 0, false,5, 0);

        for (List<Integer> path : paths) {
//            if(roadConnection.listContainsAll(paths, path)){
//                paths.remove(path);
//                System.out.println("remove");
//            }
            System.out.println(path);
            logger.info(path.toString());
        }

        System.out.println("paths size: " + paths.size());
//        logger.info("paths size: " + paths.size());
    }
}
