/**
  * Copyright 2021 bejson.com 
  */
package com.hll.mapdataservice.common.utils.po.hereplacenamepo;

/**
 * Auto-generated: 2021-04-02 11:27:34
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class Category {

    private String CategoryId;
    private Description Description;
    private String categorySystem;
    private CategoryName CategoryName;
    private boolean primaryFlag;
    public void setCategoryId(String CategoryId) {
         this.CategoryId = CategoryId;
     }
     public String getCategoryId() {
         return CategoryId;
     }

    public void setDescription(Description Description) {
         this.Description = Description;
     }
     public Description getDescription() {
         return Description;
     }

    public void setCategorySystem(String categorySystem) {
         this.categorySystem = categorySystem;
     }
     public String getCategorySystem() {
         return categorySystem;
     }

    public void setCategoryName(CategoryName CategoryName) {
         this.CategoryName = CategoryName;
     }
     public CategoryName getCategoryName() {
         return CategoryName;
     }

    public void setPrimaryFlag(boolean primaryFlag) {
         this.primaryFlag = primaryFlag;
     }
     public boolean getPrimaryFlag() {
         return primaryFlag;
     }

}