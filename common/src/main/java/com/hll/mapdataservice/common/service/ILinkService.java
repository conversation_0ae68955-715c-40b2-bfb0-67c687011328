package com.hll.mapdataservice.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hll.mapdataservice.common.entity.Link;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <p>
 *
 * </p>
 *
 * @Author: ares.chen
 * @Since: 2021/8/16
 */
public interface ILinkService extends IService<Link> {
    void updateLinkConstSt(String area, String country, CountDownLatch countDownLatch, List<Link> linkList);


    /**
     * table link to link_rp
     */
    //void convert2rp(String area,String country,CountDownLatch countDownLatch,List<Link> linkList);
}
