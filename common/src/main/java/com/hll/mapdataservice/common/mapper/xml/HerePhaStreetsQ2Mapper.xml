<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.HerePhaStreetsQ2Mapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.HerePhaStreetsQ2">
        <id column="gid" property="gid" />
        <result column="link_id" property="linkId" />
        <result column="st_name" property="stName" />
        <result column="feat_id" property="featId" />
        <result column="st_langcd" property="stLangcd" />
        <result column="num_stnmes" property="numStnmes" />
        <result column="st_nm_pref" property="stNmPref" />
        <result column="st_typ_bef" property="stTypBef" />
        <result column="st_nm_base" property="stNmBase" />
        <result column="st_nm_suff" property="stNmSuff" />
        <result column="st_typ_aft" property="stTypAft" />
        <result column="st_typ_att" property="stTypAtt" />
        <result column="addr_type" property="addrType" />
        <result column="l_refaddr" property="lRefaddr" />
        <result column="l_nrefaddr" property="lNrefaddr" />
        <result column="l_addrsch" property="lAddrsch" />
        <result column="l_addrform" property="lAddrform" />
        <result column="r_refaddr" property="rRefaddr" />
        <result column="r_nrefaddr" property="rNrefaddr" />
        <result column="r_addrsch" property="rAddrsch" />
        <result column="r_addrform" property="rAddrform" />
        <result column="ref_in_id" property="refInId" />
        <result column="nref_in_id" property="nrefInId" />
        <result column="n_shapepnt" property="nShapepnt" />
        <result column="func_class" property="funcClass" />
        <result column="speed_cat" property="speedCat" />
        <result column="fr_spd_lim" property="frSpdLim" />
        <result column="to_spd_lim" property="toSpdLim" />
        <result column="to_lanes" property="toLanes" />
        <result column="from_lanes" property="fromLanes" />
        <result column="enh_geom" property="enhGeom" />
        <result column="lane_cat" property="laneCat" />
        <result column="divider" property="divider" />
        <result column="dir_travel" property="dirTravel" />
        <result column="l_area_id" property="lAreaId" />
        <result column="r_area_id" property="rAreaId" />
        <result column="l_postcode" property="lPostcode" />
        <result column="r_postcode" property="rPostcode" />
        <result column="l_numzones" property="lNumzones" />
        <result column="r_numzones" property="rNumzones" />
        <result column="num_ad_rng" property="numAdRng" />
        <result column="ar_auto" property="arAuto" />
        <result column="ar_bus" property="arBus" />
        <result column="ar_taxis" property="arTaxis" />
        <result column="ar_carpool" property="arCarpool" />
        <result column="ar_pedest" property="arPedest" />
        <result column="ar_trucks" property="arTrucks" />
        <result column="ar_traff" property="arTraff" />
        <result column="ar_deliv" property="arDeliv" />
        <result column="ar_emerveh" property="arEmerveh" />
        <result column="ar_motor" property="arMotor" />
        <result column="paved" property="paved" />
        <result column="private" property="privateInfo" />
        <result column="frontage" property="frontage" />
        <result column="bridge" property="bridge" />
        <result column="tunnel" property="tunnel" />
        <result column="ramp" property="ramp" />
        <result column="tollway" property="tollway" />
        <result column="poiaccess" property="poiaccess" />
        <result column="contracc" property="contracc" />
        <result column="roundabout" property="roundabout" />
        <result column="interinter" property="interinter" />
        <result column="undeftraff" property="undeftraff" />
        <result column="ferry_type" property="ferryType" />
        <result column="multidigit" property="multidigit" />
        <result column="maxattr" property="maxattr" />
        <result column="spectrfig" property="spectrfig" />
        <result column="indescrib" property="indescrib" />
        <result column="manoeuvre" property="manoeuvre" />
        <result column="dividerleg" property="dividerleg" />
        <result column="inprocdata" property="inprocdata" />
        <result column="full_geom" property="fullGeom" />
        <result column="urban" property="urban" />
        <result column="route_type" property="routeType" />
        <result column="dironsign" property="dironsign" />
        <result column="explicatbl" property="explicatbl" />
        <result column="nameonrdsn" property="nameonrdsn" />
        <result column="postalname" property="postalname" />
        <result column="stalename" property="stalename" />
        <result column="vanityname" property="vanityname" />
        <result column="junctionnm" property="junctionnm" />
        <result column="exitname" property="exitname" />
        <result column="scenic_rt" property="scenicRt" />
        <result column="scenic_nm" property="scenicNm" />
        <result column="fourwhldr" property="fourwhldr" />
        <result column="coverind" property="coverind" />
        <result column="plot_road" property="plotRoad" />
        <result column="reversible" property="reversible" />
        <result column="expr_lane" property="exprLane" />
        <result column="carpoolrd" property="carpoolrd" />
        <result column="phys_lanes" property="physLanes" />
        <result column="ver_trans" property="verTrans" />
        <result column="pub_access" property="pubAccess" />
        <result column="low_mblty" property="lowMblty" />
        <result column="priorityrd" property="priorityrd" />
        <result column="spd_lm_src" property="spdLmSrc" />
        <result column="expand_inc" property="expandInc" />
        <result column="trans_area" property="transArea" />
        <result column="geom" property="geom" />
    </resultMap>

</mapper>
