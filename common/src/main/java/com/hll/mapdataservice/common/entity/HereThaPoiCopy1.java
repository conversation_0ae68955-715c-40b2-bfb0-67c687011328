package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-11
 */
@ApiModel(value="HereThaPoiCopy1对象", description="")
@TableName(value = "here_tha_poi_copy1")
public class HereThaPoiCopy1 extends Model<HereThaPoiCopy1> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("\"Action\"")
    private String Action;

    @TableField("\"SupplierID\"")
    private String SupplierID;

    @TableField("\"POI_Entity_ID\"")
    private String poiEntityId;

    @TableField("\"POI_Name\"")
    private String poiName;

    @TableField("\"POI_Name_THE\"")
    private String poiNameThe;

    @TableField("\"Chain_ID\"")
    private String chainId;

    @TableField("\"Category_ID\"")
    private String categoryId;

    @TableField("\"Product_Type\"")
    private String productType;

    @TableField("\"StreetName_THA\"")
    private String streetnameTha;

    @TableField("\"StreetType_THA\"")
    private String streettypeTha;

    @TableField("\"StreetName_THE\"")
    private String streetnameThe;

    @TableField("\"StreetType_THE\"")
    private String streettypeThe;

    @TableField("\"PlaceLevel2_THA\"")
    private String placelevel2Tha;

    @TableField("\"PlaceLevel2_THE\"")
    private String placelevel2The;

    @TableField("\"PlaceLevel3_THA\"")
    private String placelevel3Tha;

    @TableField("\"PlaceLevel3_THE\"")
    private String placelevel3The;

    @TableField("\"PlaceLevel4_THA\"")
    private String placelevel4Tha;

    @TableField("\"PlaceLevel4_THE\"")
    private String placelevel4The;

    @TableField("\"Zone_THA\"")
    private String zoneTha;

    @TableField("\"Zone_THE\"")
    private String zoneThe;

    @TableField("\"NT_Postal\"")
    private String ntPostal;

    @TableField("\"CountryCode\"")
    private String CountryCode;

    @TableField("\"Latitude\"")
    private String Latitude;

    @TableField("\"Longitude\"")
    private String Longitude;

    @TableField("\"LinkID\"")
    private String LinkID;

    @TableField("\"Side_of_Street\"")
    private String sideOfStreet;

    @TableField("\"Percent_from_RefNode\"")
    private String percentFromRefnode;

    @TableField("\"Match_Level\"")
    private String matchLevel;

    @TableField("\"National_Importance\"")
    private String nationalImportance;

    @TableField("\"Private_Access\"")
    private String privateAccess;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getAction() {
        return Action;
    }

    public void setAction(String Action) {
        this.Action = Action;
    }
    public String getSupplierID() {
        return SupplierID;
    }

    public void setSupplierID(String SupplierID) {
        this.SupplierID = SupplierID;
    }
    public String getPoiEntityId() {
        return poiEntityId;
    }

    public void setPoiEntityId(String poiEntityId) {
        this.poiEntityId = poiEntityId;
    }
    public String getPoiName() {
        return poiName;
    }

    public void setPoiName(String poiName) {
        this.poiName = poiName;
    }
    public String getPoiNameThe() {
        return poiNameThe;
    }

    public void setPoiNameThe(String poiNameThe) {
        this.poiNameThe = poiNameThe;
    }
    public String getChainId() {
        return chainId;
    }

    public void setChainId(String chainId) {
        this.chainId = chainId;
    }
    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }
    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }
    public String getStreetnameTha() {
        return streetnameTha;
    }

    public void setStreetnameTha(String streetnameTha) {
        this.streetnameTha = streetnameTha;
    }
    public String getStreettypeTha() {
        return streettypeTha;
    }

    public void setStreettypeTha(String streettypeTha) {
        this.streettypeTha = streettypeTha;
    }
    public String getStreetnameThe() {
        return streetnameThe;
    }

    public void setStreetnameThe(String streetnameThe) {
        this.streetnameThe = streetnameThe;
    }
    public String getStreettypeThe() {
        return streettypeThe;
    }

    public void setStreettypeThe(String streettypeThe) {
        this.streettypeThe = streettypeThe;
    }
    public String getPlacelevel2Tha() {
        return placelevel2Tha;
    }

    public void setPlacelevel2Tha(String placelevel2Tha) {
        this.placelevel2Tha = placelevel2Tha;
    }
    public String getPlacelevel2The() {
        return placelevel2The;
    }

    public void setPlacelevel2The(String placelevel2The) {
        this.placelevel2The = placelevel2The;
    }
    public String getPlacelevel3Tha() {
        return placelevel3Tha;
    }

    public void setPlacelevel3Tha(String placelevel3Tha) {
        this.placelevel3Tha = placelevel3Tha;
    }
    public String getPlacelevel3The() {
        return placelevel3The;
    }

    public void setPlacelevel3The(String placelevel3The) {
        this.placelevel3The = placelevel3The;
    }
    public String getPlacelevel4Tha() {
        return placelevel4Tha;
    }

    public void setPlacelevel4Tha(String placelevel4Tha) {
        this.placelevel4Tha = placelevel4Tha;
    }
    public String getPlacelevel4The() {
        return placelevel4The;
    }

    public void setPlacelevel4The(String placelevel4The) {
        this.placelevel4The = placelevel4The;
    }
    public String getZoneTha() {
        return zoneTha;
    }

    public void setZoneTha(String zoneTha) {
        this.zoneTha = zoneTha;
    }
    public String getZoneThe() {
        return zoneThe;
    }

    public void setZoneThe(String zoneThe) {
        this.zoneThe = zoneThe;
    }
    public String getNtPostal() {
        return ntPostal;
    }

    public void setNtPostal(String ntPostal) {
        this.ntPostal = ntPostal;
    }
    public String getCountryCode() {
        return CountryCode;
    }

    public void setCountryCode(String CountryCode) {
        this.CountryCode = CountryCode;
    }
    public String getLatitude() {
        return Latitude;
    }

    public void setLatitude(String Latitude) {
        this.Latitude = Latitude;
    }
    public String getLongitude() {
        return Longitude;
    }

    public void setLongitude(String Longitude) {
        this.Longitude = Longitude;
    }
    public String getLinkID() {
        return LinkID;
    }

    public void setLinkID(String LinkID) {
        this.LinkID = LinkID;
    }
    public String getSideOfStreet() {
        return sideOfStreet;
    }

    public void setSideOfStreet(String sideOfStreet) {
        this.sideOfStreet = sideOfStreet;
    }
    public String getPercentFromRefnode() {
        return percentFromRefnode;
    }

    public void setPercentFromRefnode(String percentFromRefnode) {
        this.percentFromRefnode = percentFromRefnode;
    }
    public String getMatchLevel() {
        return matchLevel;
    }

    public void setMatchLevel(String matchLevel) {
        this.matchLevel = matchLevel;
    }
    public String getNationalImportance() {
        return nationalImportance;
    }

    public void setNationalImportance(String nationalImportance) {
        this.nationalImportance = nationalImportance;
    }
    public String getPrivateAccess() {
        return privateAccess;
    }

    public void setPrivateAccess(String privateAccess) {
        this.privateAccess = privateAccess;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "HereThaPoiCopy1{" +
            "id=" + id +
            ", Action=" + Action +
            ", SupplierID=" + SupplierID +
            ", poiEntityId=" + poiEntityId +
            ", poiName=" + poiName +
            ", poiNameThe=" + poiNameThe +
            ", chainId=" + chainId +
            ", categoryId=" + categoryId +
            ", productType=" + productType +
            ", streetnameTha=" + streetnameTha +
            ", streettypeTha=" + streettypeTha +
            ", streetnameThe=" + streetnameThe +
            ", streettypeThe=" + streettypeThe +
            ", placelevel2Tha=" + placelevel2Tha +
            ", placelevel2The=" + placelevel2The +
            ", placelevel3Tha=" + placelevel3Tha +
            ", placelevel3The=" + placelevel3The +
            ", placelevel4Tha=" + placelevel4Tha +
            ", placelevel4The=" + placelevel4The +
            ", zoneTha=" + zoneTha +
            ", zoneThe=" + zoneThe +
            ", ntPostal=" + ntPostal +
            ", CountryCode=" + CountryCode +
            ", Latitude=" + Latitude +
            ", Longitude=" + Longitude +
            ", LinkID=" + LinkID +
            ", sideOfStreet=" + sideOfStreet +
            ", percentFromRefnode=" + percentFromRefnode +
            ", matchLevel=" + matchLevel +
            ", nationalImportance=" + nationalImportance +
            ", privateAccess=" + privateAccess +
        "}";
    }
}
