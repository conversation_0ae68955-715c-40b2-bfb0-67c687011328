package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-28
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="Node对象", description="")
@Data
@ToString
public class NodeM extends Model<NodeM> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "hll_nodeid", type = IdType.INPUT)
    private String hllNodeid;

    private String kind;

    private String geometry;

    private String nameCh;

    private String nameFo;

    private String nameCht;

    private String namePh;

    private String adjoinMid;

    private String adjoinNid;

    private String type;

    private String mainnodeid;

    private String subnodeid;

    private String subnodeid2;

    private String light;

    private String isPbnode;

    private String cp;

    private String datasource;

    // @TableId(value = "node_id",type=IdType.INPUT)
    private String nodeId;

    private LocalDateTime upDate;

    private String memo;

    private Integer status;

    private String geomwkt;

    private String tileId;

    private Integer tileType;

    private Integer area = 0;

    private String taskId;

    private Long olv = 0L;
}
