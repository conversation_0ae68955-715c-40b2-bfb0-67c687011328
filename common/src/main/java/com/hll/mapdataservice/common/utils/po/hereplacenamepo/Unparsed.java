/**
  * Copyright 2021 bejson.com 
  */
package com.hll.mapdataservice.common.utils.po.hereplacenamepo;

/**
 * Auto-generated: 2021-04-02 11:27:34
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class Unparsed {

    private boolean defaultLanguage;
    private String languageCode;
    private String content;

    public String getLanguageType() {
        return languageType;
    }

    public void setLanguageType(String languageType) {
        this.languageType = languageType;
    }

    private String languageType;
    public void setDefaultLanguage(boolean defaultLanguage) {
         this.defaultLanguage = defaultLanguage;
     }
     public boolean getDefaultLanguage() {
         return defaultLanguage;
     }

    public void setLanguageCode(String languageCode) {
         this.languageCode = languageCode;
     }
     public String getLanguageCode() {
         return languageCode;
     }

    public void setContent(String content) {
         this.content = content;
     }
     public String getContent() {
         return content;
     }

}