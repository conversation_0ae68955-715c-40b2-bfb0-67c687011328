package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-19
 */
public class ReportPoi extends Model<ReportPoi> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    private String auditPoiId;

    private String name;

    private String address;

    private String lon;

    private String lat;

    private String city;

    private String adname;

    private String adcode;

    private String userid;

    private String auditUserid;

    private String tag;

    private String tel;

    private String remarks;

    private String photosInfo;

    private LocalDateTime createTime;

    private LocalDateTime accpetTime;

    private LocalDateTime sendTime;

    private LocalDateTime onlineTime;

    private LocalDateTime updateTime;

    private LocalDateTime auditTime;

    private String status;

    private String ustatus;

    private String cause;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAuditPoiId() {
        return auditPoiId;
    }

    public void setAuditPoiId(String auditPoiId) {
        this.auditPoiId = auditPoiId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getLon() {
        return lon;
    }

    public void setLon(String lon) {
        this.lon = lon;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getAdname() {
        return adname;
    }

    public void setAdname(String adname) {
        this.adname = adname;
    }

    public String getAdcode() {
        return adcode;
    }

    public void setAdcode(String adcode) {
        this.adcode = adcode;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getAuditUserid() {
        return auditUserid;
    }

    public void setAuditUserid(String auditUserid) {
        this.auditUserid = auditUserid;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getPhotosInfo() {
        return photosInfo;
    }

    public void setPhotosInfo(String photosInfo) {
        this.photosInfo = photosInfo;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getAccpetTime() {
        return accpetTime;
    }

    public void setAccpetTime(LocalDateTime accpetTime) {
        this.accpetTime = accpetTime;
    }

    public LocalDateTime getSendTime() {
        return sendTime;
    }

    public void setSendTime(LocalDateTime sendTime) {
        this.sendTime = sendTime;
    }

    public LocalDateTime getOnlineTime() {
        return onlineTime;
    }

    public void setOnlineTime(LocalDateTime onlineTime) {
        this.onlineTime = onlineTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public LocalDateTime getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(LocalDateTime auditTime) {
        this.auditTime = auditTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUstatus() {
        return ustatus;
    }

    public void setUstatus(String ustatus) {
        this.ustatus = ustatus;
    }

    public String getCause() {
        return cause;
    }

    public void setCause(String cause) {
        this.cause = cause;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "ReportPoi{" +
        "id=" + id +
        ", auditPoiId=" + auditPoiId +
        ", name=" + name +
        ", address=" + address +
        ", lon=" + lon +
        ", lat=" + lat +
        ", city=" + city +
        ", adname=" + adname +
        ", adcode=" + adcode +
        ", userid=" + userid +
        ", auditUserid=" + auditUserid +
        ", tag=" + tag +
        ", tel=" + tel +
        ", remarks=" + remarks +
        ", photosInfo=" + photosInfo +
        ", createTime=" + createTime +
        ", accpetTime=" + accpetTime +
        ", sendTime=" + sendTime +
        ", onlineTime=" + onlineTime +
        ", updateTime=" + updateTime +
        ", auditTime=" + auditTime +
        ", status=" + status +
        ", ustatus=" + ustatus +
        ", cause=" + cause +
        "}";
    }
}
