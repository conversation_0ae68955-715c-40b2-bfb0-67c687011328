/**
  * Copyright 2021 bejson.com 
  */
package com.hll.mapdataservice.common.utils.po.hereplacenamepo;

/**
 * Auto-generated: 2021-04-02 11:27:34
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class Base {

    private NameList NameList;
    private CategoryList CategoryList;
    private ExternalReferenceList ExternalReferenceList;
    private ContactList ContactList;
    private ChainList ChainList;

    public com.hll.mapdataservice.common.utils.po.hereplacenamepo.RelationshipList getRelationshipList() {
        return RelationshipList;
    }

    public void setRelationshipList(com.hll.mapdataservice.common.utils.po.hereplacenamepo.RelationshipList relationshipList) {
        RelationshipList = relationshipList;
    }

    private RelationshipList RelationshipList;
    public void setNameList(NameList NameList) {
         this.NameList = NameList;
     }
     public NameList getNameList() {
         return NameList;
     }

    public void setCategoryList(CategoryList CategoryList) {
         this.CategoryList = CategoryList;
     }
     public CategoryList getCategoryList() {
         return CategoryList;
     }

    public void setExternalReferenceList(ExternalReferenceList ExternalReferenceList) {
         this.ExternalReferenceList = ExternalReferenceList;
     }
     public ExternalReferenceList getExternalReferenceList() {
         return ExternalReferenceList;
     }

    public void setContactList(ContactList ContactList) {
         this.ContactList = ContactList;
     }
     public ContactList getContactList() {
         return ContactList;
     }

    public void setChainList(ChainList ChainList) {
        this.ChainList = ChainList;
    }
    public ChainList getChainList() {
        return ChainList;
    }

}