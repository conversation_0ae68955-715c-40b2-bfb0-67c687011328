package com.hll.mapdataservice.common;

public enum AdminlineEnum {

    IDN("idn",1),
    MEX("mex",1),
    S<PERSON>("sgp",1),
    T<PERSON>("tha",1),
    VNM("vnm",1),
    TWN("twn",1),
    HKG("hkg",2),
    BR<PERSON>("bra",2),
    BAN("ban",2),
    IND("ind",2),
    MYS("mys",2),
    PHL("phl",2);
    private String areaName;
    private Integer adminline;

    AdminlineEnum(String areaName, Integer adminline) {
        this.areaName = areaName;
        this.adminline = adminline;
    }

    public static Integer getAdminlineByAreaName(String areaName) {
        if (areaName == null) {
            return null;
        }
        AdminlineEnum[] values = AdminlineEnum.values();
        for (AdminlineEnum langEnum : values) {
            if (areaName.equals(langEnum.getAreaName())) {
                return langEnum.getAdminline();
            }
        }
        return null;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Integer getAdminline() {
        return adminline;
    }

    public void setAdminline(Integer adminline) {
        this.adminline = adminline;
    }
}
