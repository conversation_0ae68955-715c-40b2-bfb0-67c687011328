package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;

/**
 * poi匹配测试模型
 *
 * @Author: ares.chen
 * @Since: 2021/12/8
 */
@ApiModel(value="Place对象", description="")
public class Place extends Model<Place> {
    private String placeId;
    private String placeName;
    private Double longitude;
    private Double latitude;

    public String getPlaceName() {
        return placeName;
    }

    public void setPlaceName(String placeName) {
        this.placeName = placeName;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public String getPlaceId() {
        return placeId;
    }

    public void setPlaceId(String placeId) {
        this.placeId = placeId;
    }
}
