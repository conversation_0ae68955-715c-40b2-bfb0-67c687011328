package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2024/10/28
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class PoiSrc extends Model<PoiSrc> {
    // @TableId(type = IdType.AUTO)
    // private String id;
    private String srcContent;
}
