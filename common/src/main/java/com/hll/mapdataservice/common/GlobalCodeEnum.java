package com.hll.mapdataservice.common;

/**
 * @package com.hll.mapdataservice.common
 * @Description: 全局返回码
 * @author: shawn2
 * @CreateDate: 2021/3/4 20:45
 * @Version: 1.0
 */
public enum GlobalCodeEnum {

    /**
     * httpStatus状态码
     */
    /**
     * {@code 429 Too Many Requests}.
     *
     * @see <a href="http://tools.ietf.org/html/rfc6585#section-4">Additional HTTP Status Codes</a>
     */
    TOO_MANY_REQUESTS("429", "Too Many Requests"),

    /**
     * 全局返回码定义 - 0000开头
     */
    GL_SUCC_000000("000000", "成功"),
    GL_FAIL_999999("999999", "失败"),
    GL_BUSINESS_EXCEPTION_000001("000001", "业务异常"),
    GL_PARAM_ERROR_000002("000002", "参数错误"),
    GL_TOKEN_INVALID_000003("000003", "Token失效"),
    GL_REQUEST_INVALID_000004("000004", "You request url or method is invalid."),
    GL_EMPTY_QUERY_RESULT_000005("000005", "未查询到相关的信息"),
    GL_REPEAT_PUSH_INFO_000006("000006", "重复推送信息"),
    GL_REPEAT_REQUEST_INFO_000007("000007", "重复请求"),
    GL_REPEAT_REQUEST_INFO_000008("000008", "redis key不存在，请先预处理！");

    /**
     * 编码
     */
    private String code;

    /**
     * 描述
     */
    private String desc;


    GlobalCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据编码获取枚举类型
     *
     * @param code 编码
     * @return
     */
    public static GlobalCodeEnum getByCode(String code) {
        //判空
        if (code == null) {
            return null;
        }
        //循环处理
        GlobalCodeEnum[] values = GlobalCodeEnum.values();
        for (GlobalCodeEnum value : values) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
