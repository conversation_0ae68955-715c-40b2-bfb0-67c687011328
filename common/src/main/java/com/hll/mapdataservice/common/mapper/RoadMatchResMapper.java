package com.hll.mapdataservice.common.mapper;

import com.hll.mapdataservice.common.entity.RoadMatchRes;

import java.util.List;

/**
  *
  *
  * @Author: ares.chen
  * @Since: 2021/11/18
  */
public interface RoadMatchResMapper extends RootMapper<RoadMatchRes> {

    int getAloneMergeRoadNum();

    List<RoadMatchRes> getAloneMergeRoad(int limit, int offset);

    List<RoadMatchRes> getIndependentRoad();

    int getBranchRoadChangedNum();

    List<String> getBranchRoadChangedId(int limit, int offset);

    int getTreeBranchMergeNum();

    List<String> getTreeBranchMergeOsmId(int step, int i);

    List<RoadMatchRes> selectListByOsmId(String osmId);
}