<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.MnrPhaNetwGeoLinkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.MnrPhaNetwGeoLink">
        <id column="feat_id" property="featId" />
        <result column="ft_road_element" property="ftRoadElement" />
        <result column="ft_ferry_element" property="ftFerryElement" />
        <result column="ft_address_area_boundary_element" property="ftAddressAreaBoundaryElement" />
        <result column="ft_railway_element" property="ftRailwayElement" />
        <result column="country_left" property="countryLeft" />
        <result column="country_right" property="countryRight" />
        <result column="centimeters" property="centimeters" />
        <result column="positional_accuracy" property="positionalAccuracy" />
        <result column="ada_compliant" property="adaCompliant" />
        <result column="in_car_importance" property="inCarImportance" />
        <result column="geom" property="geom" />
    </resultMap>

</mapper>
