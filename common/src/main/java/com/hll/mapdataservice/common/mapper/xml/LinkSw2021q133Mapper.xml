<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.LinkSw2021q133Mapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.LinkSw2021q133">
        <id column="hll_linkid" property="hllLinkid" />
        <result column="link_id" property="linkId" />
        <result column="hll_s_nid" property="hllSNid" />
        <result column="hll_e_nid" property="hllENid" />
        <result column="kind" property="kind" />
        <result column="formway" property="formway" />
        <result column="dir" property="dir" />
        <result column="app" property="app" />
        <result column="toll" property="toll" />
        <result column="adopt" property="adopt" />
        <result column="md" property="md" />
        <result column="devs" property="devs" />
        <result column="spet" property="spet" />
        <result column="funct" property="funct" />
        <result column="urban" property="urban" />
        <result column="pave" property="pave" />
        <result column="lane_n" property="laneN" />
        <result column="lane_l" property="laneL" />
        <result column="lane_r" property="laneR" />
        <result column="lane_c" property="laneC" />
        <result column="width" property="width" />
        <result column="viad" property="viad" />
        <result column="l_admin" property="lAdmin" />
        <result column="r_admin" property="rAdmin" />
        <result column="geom" property="geom" />
        <result column="len" property="len" />
        <result column="f_speed" property="fSpeed" />
        <result column="t_speed" property="tSpeed" />
        <result column="sp_class" property="spClass" />
        <result column="dici_type" property="diciType" />
        <result column="verifyflag" property="verifyflag" />
        <result column="pre_launch" property="preLaunch" />
        <result column="name_ch_o" property="nameChO" />
        <result column="name_ch_a" property="nameChA" />
        <result column="name_ch_f" property="nameChF" />
        <result column="name_ph_o" property="namePhO" />
        <result column="name_ph_a" property="namePhA" />
        <result column="name_ph_f" property="namePhF" />
        <result column="name_en_o" property="nameEnO" />
        <result column="name_en_a" property="nameEnA" />
        <result column="name_en_f" property="nameEnF" />
        <result column="name_po" property="namePo" />
        <result column="name_cht" property="nameCht" />
        <result column="code_type" property="codeType" />
        <result column="name_type" property="nameType" />
        <result column="src_flag" property="srcFlag" />
        <result column="mesh_id" property="meshId" />
        <result column="memo" property="memo" />
        <result column="cp" property="cp" />
        <result column="datasource" property="datasource" />
        <result column="up_date" property="upDate" />
        <result column="status" property="status" />
    </resultMap>

</mapper>
