package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.type.ArrayTypeHandler;
import org.apache.ibatis.type.JdbcType;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
@ApiModel(value="PlanetOsmWays对象", description="")
@TableName(value = "planet_osm_ways", autoResultMap = true)
public class PlanetOsmWays extends Model<PlanetOsmWays> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(value = "nodes", typeHandler = ArrayTypeHandler.class, jdbcType = JdbcType.ARRAY)
    private Long[] nodes;

    private String tags;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public Long[] getNodes() {
        return nodes;
    }

    public void setNodes(Long[] nodes) {
        this.nodes = nodes;
    }
    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "PlanetOsmWays{" +
            "id=" + id +
            ", nodes=" + nodes +
            ", tags=" + tags +
        "}";
    }
}
