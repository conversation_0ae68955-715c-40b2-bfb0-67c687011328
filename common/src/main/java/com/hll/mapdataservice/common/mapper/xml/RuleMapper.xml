<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.RuleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.Rule">
        <id column="rule_id" property="ruleId" />
        <result column="inlink_id" property="inlinkId" />
        <result column="node_id" property="nodeId" />
        <result column="outlink_id" property="outlinkId" />
        <result column="pass" property="pass" />
        <result column="pass2" property="pass2" />
        <result column="flag" property="flag" />
        <result column="vperiod" property="vperiod" />
        <result column="vehcl_type" property="vehclType" />
        <result column="vpdir" property="vpdir" />
        <result column="mesh_list" property="meshList" />
        <result column="memo" property="memo" />
        <result column="cp" property="cp" />
        <result column="datasource" property="datasource" />
        <result column="up_date" property="upDate" />
        <result column="status" property="status" />
        <result column="link_angle" property="linkAngle" />
    </resultMap>

</mapper>
