package com.hll.mapdataservice.common.utils;


//import sun.misc.BASE64Decoder;
//import sun.misc.BASE64Encoder;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.util.UUID;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.zip.CRC32;

import static java.lang.Math.abs;


public class EnDecryptUtils {

    //初始向量(AES 为16bytes. DES 为8bytes)
    public static final String VIPARA = "";
    //编码方式
    public static final String bm = "UTF-8";
    //私钥(AES固定格式为128/192/256 bits.即：16/24/32bytes;DES固定格式为128bits，即8bytes)
    private static final String ASE_KEY = "";
    private static final Logger log = LoggerFactory.getLogger(EnDecryptUtils.class);

    /**
     * 加密
     *
     * @param
     * @return
     */
//    public static String encrypt(String cleartext) {
//        //加密方式： AES128(CBC/PKCS5Padding) + Base64, 私钥：
//        try {
//            IvParameterSpec zeroIv = new IvParameterSpec(VIPARA.getBytes());
//            //两个参数，第一个为私钥字节数组， 第二个为加密方式 AES或者DES
//            SecretKeySpec key = new SecretKeySpec(ASE_KEY.getBytes(), "AES");
//            //实例化加密类，参数为加密方式，要写全(PKCS5Padding比PKCS7Padding效率高，PKCS7Padding可支持IOS加解密)
//            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
//            //初始化，此方法可以采用三种方式，按加密算法要求来添加。（1）无第三个参数（2）第三个参数为SecureRandom random = new SecureRandom();中random对象，随机数。(AES不可采用这种方法)（3）采用此代码中的IVParameterSpec
//            cipher.init(Cipher.ENCRYPT_MODE, key, zeroIv);
//            //加密操作,返回加密后的字节数组，然后需要编码。主要编解码方式有Base64, HEX, UUE,7bit等等。此处看服务器需要什么编码方式
//            byte[] encryptedData = cipher.doFinal(cleartext.getBytes(bm));
//
//            return new BASE64Encoder().encode(encryptedData);
//        } catch (Exception e) {
//            e.printStackTrace();
//            return "";
//        }
//    }
    public static Long convertUUID2Long(String uuid) {
        String uuidReplace = uuid.replace("-", "");
        if (uuidReplace.length() < 16 && uuidReplace.length() > 0) {
            return Long.parseLong(uuidReplace, 16);
        } else if (uuidReplace.length() != 32) {
            return 0L;
        } else {
            String uuid16 = uuidReplace.substring(0, 16);
            String uuidleft = uuidReplace.substring(16);
            Long uuid16Long = Long.parseLong(uuid16, 16);
            Long uuidleftLong = Long.parseLong(uuidleft, 16);
            return uuid16Long ^ uuidleftLong;
        }
    }

    public static String convertUUID2LongString(String uuid) {
        String uuidReplace = uuid.replace("-", "").toLowerCase();
        if (uuidReplace.length() < 16 && uuidReplace.length() > 0) {
            return Long.parseLong(uuidReplace, 16) + ",0,0";
        } else if (uuidReplace.length() != 32) {
            byte[] decodedBytes = Base64.getDecoder().decode(uuid.replace("-", ""));
            // 将字节数组转换为非负 Long 型
            long result = bytesToLong(decodedBytes);
            System.out.println("result:" + result);
            return (result + ",0,0").replace("-", "");
        } else {
            String first = "0", left = "0";
            String uuid16 = uuidReplace.substring(0, 16);
            int firstFirstChar = Integer.parseInt(transformString(String.valueOf(uuid16.charAt(0))), 16);
            if (firstFirstChar > 7) {
                int diff = firstFirstChar - 7;
                if (diff > 7) {
                    diff = diff - 7;
                    first = "1|" + diff;
                } else {
                    first = diff + "";
                }
                uuid16 = diff + "" + uuid16.substring(1);
            }
            String uuidleft = uuidReplace.substring(16);
            int leftFirstChar = Integer.parseInt(transformString(String.valueOf(uuidleft.charAt(0))), 16);
            if (leftFirstChar > 7) {
                int diff = leftFirstChar - 7;
                if (diff > 7) {
                    diff = diff - 7;
                    left = "1|" + diff;
                } else {
                    left = diff + "";
                }
                uuidleft = diff + "" + uuidleft.substring(1);
            }
            //adjust the case uuid16 or uuidlest has char large than f
            Long uuid16Long = 0L;
            if (hasInvalidCharacter(uuid16)){
                uuid16Long = Long.parseLong(transformString(uuid16),16);
            } else {
                uuid16Long = Long.parseLong(uuid16, 16);
            }
            Long uuidleftLong = 0L;
            if (hasInvalidCharacter(uuidleft)){
                uuidleftLong = Long.parseLong(transformString(uuidleft), 16);
            } else {
               uuidleftLong =  Long.parseLong(uuidleft, 16);
            }
            Long result = uuid16Long ^ uuidleftLong;
            return result + "," + first + "," + left;
        }
    }

    public static boolean hasInvalidCharacter(String input) {
        for (char c : input.toCharArray()) {
            // Check if character is greater than 'f'
            if (c > 'f') {
                return true;
            }
        }
        return false;
    }

    public static String transformString(String input) {
        StringBuilder result = new StringBuilder();

        for (char c : input.toCharArray()) {
            if (c > 'f') {
                int diff = c - 'f';
                if (diff >= 0 && diff < 10) {
                    result.append(diff);
                } else if (diff > 15) {
                    diff = diff - 15;
                    if (diff >= 0 && diff < 10) {
                        result.append(diff);
                    } else {
                        char newChar = (char) ('a' + (diff - 10));
                        result.append(newChar);
                    }
                } else {
                    char newChar = (char) ('a' + (diff - 10));
                    result.append(newChar);
                }
            } else {
                result.append(c);
            }
        }

        return result.toString();
    }

    public static Long convertHerePoiUUID2Long(String herePoiUuid) {
        String prefix = herePoiUuid.split("-")[0];
        String uuid = herePoiUuid.split("-")[1];

        Long uuidLong = convertUUID2Long(uuid);
        Long prefixLong = Long.parseLong(prefix, 36);

        return uuidLong ^ prefixLong;
    }

    public static String convertHerePoiUUID2LongString(String herePoiUuid) {
        String prefix = herePoiUuid.split("-")[0];
        String uuid = herePoiUuid.split("-")[1];

        String uuidLongString = convertUUID2LongString(uuid);
        Long uuidLong = Long.parseLong(uuidLongString.split(",")[0]);
        Long prefixLong = Long.parseLong(prefix, 36);

        Long reslut = uuidLong ^ prefixLong;

        return reslut + "," + uuidLongString.split(",")[1] + "," + uuidLongString.split(",")[2];
    }

    public static String convertHerePoiUUID2IDLongString(String herePoiUuid) {
        try {
            String prefix = herePoiUuid.split("-")[0];
            String uuid = herePoiUuid.split("-")[1];

            // System.out.println("herePoiUuid:" + herePoiUuid);
            String uuidLongString = convertUUID2LongString(uuid);
            Long prefixLong = 0L;

            Long uuidLong = Long.parseLong(uuidLongString.split(",")[0]);
            //adjust the case of prefix length > 12 and adbapt the old version
            if (prefix.length() > 12) {
                prefixLong = abs(convertSpecilIdToLong(prefix));
            } else {
                prefixLong = Long.parseLong(prefix, 36);
            }

            Long result = uuidLong ^ prefixLong;

            String resultString = result + "";
            if (resultString.length() > 16) {
                String secondResult = resultString.substring(resultString.length() - 16, resultString.length());
                // System.out.println("secondResult:" + secondResult);
                //String firstResult = resultString.substring(0,resultString.length()-13);
                String firstResult = resultString.substring(0, 16);
                // System.out.println("firstResult:" + firstResult);
                String leftString = resultString.substring(16);
                String firstLeftString = resultString.substring(0, resultString.length() - 16);
                Long resultId =     Long.parseLong(firstResult) ^ Long.parseLong(secondResult) ^ Long.parseLong(leftString)
                        ^ Long.parseLong(firstLeftString);

                return resultId + "," + result + "," + uuidLongString.split(",")[1] + "," + uuidLongString.split(",")[2];
            } else {
                return resultString + "," + result + "," + uuidLongString.split(",")[1] + "," + uuidLongString.split(",")[2];
            }
        } catch (Exception e) {
            log.error("convertHerePoiUUID2IDLongString error, herePoiUuid:{}", herePoiUuid);
            // return null;
            throw new RuntimeException(e);
        }

    }

    public static String convertHerePoiUUID2IDLongStringOnly(String herePoiUuid) {
        try {
            String prefix = herePoiUuid.split("-")[0];
            String uuid = herePoiUuid.split("-")[1];

            // System.out.println("herePoiUuid:" + herePoiUuid);
            String uuidLongString = convertUUID2LongString(uuid);
            Long prefixLong = 0L;

            Long uuidLong = Long.parseLong(uuidLongString.split(",")[0]);
            //adjust the case of prefix length > 12 and adbapt the old version
            if (prefix.length() > 12) {
                prefixLong = abs(convertSpecilIdToLong(prefix));
            } else {
                prefixLong = Long.parseLong(prefix, 36);
            }

            Long result = uuidLong ^ prefixLong;

            String resultString = result + "";
            if (resultString.length() > 16) {
                String secondResult = resultString.substring(resultString.length() - 16, resultString.length());
                // System.out.println("secondResult:" + secondResult);
                //String firstResult = resultString.substring(0,resultString.length()-13);
                String firstResult = resultString.substring(0, 16);
                // System.out.println("firstResult:" + firstResult);
                String leftString = resultString.substring(16);
                String firstLeftString = resultString.substring(0, resultString.length() - 16);
                Long resultId =     Long.parseLong(firstResult) ^ Long.parseLong(secondResult) ^ Long.parseLong(leftString)
                        ^ Long.parseLong(firstLeftString);

                return resultId + "";
            } else {
                return resultString;
            }
        } catch (Exception e) {
            log.error("convertHerePoiUUID2IDLongString error, herePoiUuid:{}", herePoiUuid);
            // return null;
            throw new RuntimeException(e);
        }

    }

    public static String convertLong2HerePoiUUID(Long longResult, String herePoiUuidPrefix, String herePoiUuidFirstHalf) {

        Long uuidLong = longResult ^ Long.parseLong(herePoiUuidPrefix, 36);
        Long leftLong = uuidLong ^ Long.parseLong(herePoiUuidFirstHalf, 16);

        String leftLongString = Long.toHexString(leftLong);


        return herePoiUuidPrefix + "-" + herePoiUuidFirstHalf + leftLongString;
    }

    public static String convertLong2HerePoiUUIDString(String longResult, String herePoiUuidPrefix, String herePoiUuidFirstHalf) {

        Long uuidLong = Long.parseLong(longResult.split(",")[0]) ^ Long.parseLong(herePoiUuidPrefix, 36);

        //parse first half > 7 case
        int firstFirstChar = Integer.parseInt(String.valueOf(herePoiUuidFirstHalf.charAt(0)), 16);
        String herePoiUuidFirstHalfCom = "";
        if (longResult.split(",")[1].contains("|")) {
            herePoiUuidFirstHalfCom = Integer.toHexString(firstFirstChar - 7 - 7) + herePoiUuidFirstHalf.substring(1);
        } else if (Integer.parseInt(longResult.split(",")[1]) > 0) {
            herePoiUuidFirstHalfCom = Integer.toHexString(firstFirstChar - 7) + herePoiUuidFirstHalf.substring(1);
        }
        Long leftLong = uuidLong ^ Long.parseLong(herePoiUuidFirstHalfCom, 16);

        //parse left uuid first > 7 case
        String leftLongString = "";
        if (longResult.split(",")[2].contains("|")) {
            leftLongString = Long.toHexString(leftLong);
            leftLongString = Integer.toHexString(Integer.parseInt(String.valueOf(leftLongString.charAt(0))) + 7 + 7) + leftLongString.substring(1);
        } else if (Integer.parseInt(longResult.split(",")[2]) > 0) {
            //94aa7cfa3ce8216a
            leftLongString = Long.toHexString(leftLong);
            leftLongString = Integer.toHexString(Integer.parseInt(String.valueOf(leftLongString.charAt(0))) + 7) + leftLongString.substring(1);
        } else {
            leftLongString = Long.toHexString(leftLong);
        }


        return herePoiUuidPrefix + "-" + herePoiUuidFirstHalf + leftLongString;
    }

    public static String convertLong2HerePoiUUID(Long longResult, Long herePoiUuidPrefix, Long herePoiUuidFirstHalf) {

        Long uuidLong = longResult ^ herePoiUuidPrefix;
        Long leftLong = uuidLong ^ herePoiUuidFirstHalf;

        String leftLongString = Long.toHexString(leftLong);


        return herePoiUuidPrefix + "-" + herePoiUuidFirstHalf + leftLongString;
    }

    public static String convertLong2HerePoiUUID(Long longResult, String herePoiUuidPrefix, Long herePoiUuidFirstHalf) {

        Long uuidLong = longResult ^ Long.parseLong(herePoiUuidPrefix, 36);
        Long leftLong = uuidLong ^ herePoiUuidFirstHalf;

        String leftLongString = Long.toHexString(leftLong);


        return herePoiUuidPrefix + "-" + herePoiUuidFirstHalf + leftLongString;
    }

    public static String convertLong2HerePoiUUID(Long longResult, Long herePoiUuidPrefix, String herePoiUuidFirstHalf) {

        Long uuidLong = longResult ^ herePoiUuidPrefix;
        Long leftLong = uuidLong ^ Long.parseLong(herePoiUuidFirstHalf, 16);

        String leftLongString = Long.toHexString(leftLong);


        return herePoiUuidPrefix + "-" + herePoiUuidFirstHalf + leftLongString;
    }

    public static long uuidToLong(String uuidString) {
        UUID uuid = UUID.fromString(uuidString);
        long mostSigBits = uuid.getMostSignificantBits();
        long leastSigBits = uuid.getLeastSignificantBits();

        // 将Most Significant Bits 和 Least Significant Bits 合并成一个long值
        return (mostSigBits & 0xFFFFFFFFL) << 32 | (leastSigBits & 0xFFFFFFFFL);
    }

    public static String longToUuid(long longValue) {
        // 将long值分解为Most Significant Bits 和 Least Significant Bits
        long mostSigBits = longValue >>> 32;
        long leastSigBits = longValue & 0xFFFFFFFFL;

        // 使用构造函数创建UUID对象
        UUID uuid = new UUID(mostSigBits, leastSigBits);
        return uuid.toString();
    }

    public static long convertSpecilIdToLong(String input) {
        try {
            // 获取 SHA-256 哈希函数实例
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(input.getBytes());

            // 从哈希值的前 8 个字节中提取 long 值
            ByteBuffer buffer = ByteBuffer.wrap(hash);
            return buffer.getLong();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    private static long bytesToLong(byte[] bytes) {
        if (bytes.length < 8) {
            throw new IllegalArgumentException("字节数组长度必须至少为 8");
        }
        long value = 0;
        for (int i = 0; i < 8; i++) {
            value = (value << 8) | (bytes[i] & 0xFF);
        }
        return value;
    }

//    /**
//     * 解密
//     *
//     * @param encrypted
//     * @return
//     */
//    public static String decrypt(String encrypted) {
//        try {
//            byte[] byteMi = new BASE64Decoder().decodeBuffer(encrypted);
//            IvParameterSpec zeroIv = new IvParameterSpec(VIPARA.getBytes());
//            SecretKeySpec key = new SecretKeySpec(
//                    ASE_KEY.getBytes(), "AES");
//            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
//            //与加密时不同MODE:Cipher.DECRYPT_MODE
//            cipher.init(Cipher.DECRYPT_MODE, key, zeroIv);
//            byte[] decryptedData = cipher.doFinal(byteMi);
//            return new String(decryptedData, bm);
//        } catch (Exception e) {
//            e.printStackTrace();
//            return "";
//        }
//    }

    public static long convertTo13DigitLong(String input) {
        CRC32 crc = new CRC32();
        crc.update(input.getBytes());

        // 得到 13 位长的值
        long hashValue = crc.getValue();

        // 将其限制为 13 位数
        return hashValue % 1_000_000_000_000L; // 13 位数字
    }

    /**
     * 测试
     *
     * @param args
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {
//        String content = "资料MeshLinkTemplate打断：MeshLinkTemplate{hllLinkid='702311462954369024', dLinkid='702311462954369024', hllSNid='null', hllENid='null', kind=8, formway='1', dir=1, app=1, toll=0, adopt=2, md=0, devs=2, spet=0, funct=5, urban=0, pave=1, laneN=2, laneL=1, laneR=1, laneC=2, width='55', viad=2, lAdmin='null', rAdmin='null', geom='LINESTRING (119.575256 25.870728, 119.57606 25.869992)', len=114, fSpeed='150,0,1', tSpeed='150,0,1', spClass='7', diciType=0, verifyflag=2, preLaunch='null', nameChO='null', nameChA='null', nameChF='null', namePhO='null', namePhA='null', namePhF='null', nameEnO='null', nameEnA='null', nameEnF='null', namePo='null', nameCht='null', codeType=0, nameType=0, srcFlag=0, meshId='385964', memo='null', cp='null', datasource='9', upDate=2022-01-12T10:24:44.651158, status=0, collectVersion='V_1', workStatus=0, subId='21', taskId='null'} ,挂接MeshLink: MeshLink{id=1192805, hllLinkid='72057594091961415', dLinkid='54033479', hllSNid='72057594056929440', hllENid='72057594038371489', kind=3, formway='81', dir=2, app=1, toll=2, adopt=null, md=1, devs=1, spet=0, funct=3, urban=0, pave=1, laneN=4, laneL=4, laneR=0, laneC=3, width='130', viad=0, lAdmin='350112', rAdmin='350112', geom='LINESTRING (119.57726 25.87106, 119.57687 25.8707, 119.57616 25.87008, 119.57606 25.869992, 119.57553 25.86952)', len=243.49000549316406, fSpeed='600,1,', tSpeed='null', spClass='5', diciType=0, verifyflag=2, preLaunch='null', nameChO='Ｇ２２８', nameChA='null', nameChF='null', namePhO='G228', namePhA='null', namePhF='null', nameEnO='G228', nameEnA='null', nameEnF='null', namePo='null', nameCht='null', codeType=1, nameType=0, srcFlag=6, meshId='385964', memo='null', cp='fujian', datasource='1', upDate=2021-11-04T06:16:58, status=0, linkSource='null', linkDestination='null', collectVersion='V_1', checkStatus=0, changeStatus=0, taskId='4242'}";
//        // 加密
//        System.out.println("加密前：" + content);


//        Long prefixLong = Long.parseLong("344wecnv", 36);
//        String uuid = "188e1803e3b806b72adef3fb672434bd";
//        Long uuidLong = convertUUID2Long(uuid);
//        System.out.println("uuidLong:" + uuidLong);

//        String herePoiUuid = "3448lxx5-188e1803e3b806b72adef3fb672434bd";
//        String herePoiUuid = "344wecnv-3812ea0b838c45fe94aa7cfa3ce8216a";
//        String herePoiUuid = "356tehx2-28aa6f4d3bc9e4dd59685f6614512ad7";
//        String herePoiUuid = "evcp0-YWQ2NjcyMzItNTA4NS0xMWVkLWE1NDktNDIwMTBhYTQwZmMw";
//        String herePoiUuid = "392xp59uw1kw8-aGVyZS1ldjpnY286MjAwNTU5MTczNw";
//        String herePoiUuid = "704w6gy6qsnx7-aGVyZS1ldjp2aW5mYXN0OjU5OTA0NzQy";
        String herePoiUuid = "3601r4ve-e636c05eeb870e1ad4ffb4be89e3f2ef";
        String base64String = "aGVyZS1ldjpnY286MjAwNTU5MTczNw";
        String herePoiUuid1 = "356tejp0-f160c50ee386bf592d30c9738613e5f3";
        System.out.println("herePoiUuid:" + herePoiUuid);
//        String herePoiUuid = "3812ea0b-838c-45fe-94aa-7cfa3ce8216a";
//        long herepoilong = uuidToLong(herePoiUuid);
//        String long2herepoi = longToUuid(herepoilong);
//        Long herepoilongall = prefixLong ^ herepoilong;
//        String herePoiUuidLongString = convertHerePoiUUID2LongString(herePoiUuid);
//        System.out.println("uuidLong:" + herePoiUuidLongString);
//        String herePoiUuidStringnew = convertLong2HerePoiUUIDString(herePoiUuidLongString, "608w9zs9", "fac9856c245a4692");
//        System.out.println("uuidLongString:" + herePoiUuidStringnew);
//        String herePoiUuidLongId = convertHerePoiUUID2IDLongString(herePoiUuid);
//        System.out.println("herePoiUuidLongId:" + herePoiUuidLongId);
//        String herePoiUuidLongId1 = convertHerePoiUUID2IDLongString(herePoiUuid1);
//        System.out.println("herePoiUuidLongId:" + herePoiUuidLongId1);
//        Long herePoiUuidLong = convertHerePoiUUID2Long(herePoiUuid);
//        System.out.println("uuidLong:" + herePoiUuidLong);
//        String encryptResult = encrypt(content);

//        System.out.println("加密后：" + new String(encryptResult));
//        // 解密
//        String decryptResult = decrypt(encryptResult);
//        System.out.println("解密后：" + new String(decryptResult));
//        String herePoiUuidString = convertLong2HerePoiUUID(herePoiUuidLong,"608w9zs9","fac9856c245a4692" );
//        System.out.println("uuidLong:" + herePoiUuidString);
//        Long herePoiUuidLong = convertSpecilIdToLong(herePoiUuid);
//        System.out.println("uuidLong:" + herePoiUuidLong);
        String resultLong = convertHerePoiUUID2IDLongString(herePoiUuid);
        System.out.println("resultLong:" + resultLong);

        byte[] decodedBytes = Base64.getDecoder().decode(base64String);

        // 将字节数组转换为非负 Long 型
        long result = bytesToLong(decodedBytes);

        System.out.println("非负 Long 型值: " + result);
    }
}
