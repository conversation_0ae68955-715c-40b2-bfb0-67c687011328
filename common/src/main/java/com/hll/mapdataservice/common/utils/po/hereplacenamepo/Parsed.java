/**
  * Copyright 2021 bejson.com 
  */
package com.hll.mapdataservice.common.utils.po.hereplacenamepo;

/**
 * Auto-generated: 2021-04-02 11:27:34
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class Parsed {

    private StreetName StreetName;
    private String HouseNumber;
    private boolean defaultLanguage;
    private String PostalCode;
    private String languageCode;
    private String CountryCode;
    private String FullStreetName;

    public com.hll.mapdataservice.common.utils.po.hereplacenamepo.Admin getAdmin() {
        return Admin;
    }

    public void setAdmin(com.hll.mapdataservice.common.utils.po.hereplacenamepo.Admin admin) {
        Admin = admin;
    }

    private Admin Admin;
    public void setStreetName(StreetName StreetName) {
         this.StreetName = StreetName;
     }
     public StreetName getStreetName() {
         return StreetName;
     }

    public void setHouseNumber(String HouseNumber) {
         this.HouseNumber = HouseNumber;
     }
     public String getHouseNumber() {
         return HouseNumber;
     }

    public void setDefaultLanguage(boolean defaultLanguage) {
         this.defaultLanguage = defaultLanguage;
     }
     public boolean getDefaultLanguage() {
         return defaultLanguage;
     }

    public void setPostalCode(String PostalCode) {
         this.PostalCode = PostalCode;
     }
     public String getPostalCode() {
         return PostalCode;
     }

    public void setLanguageCode(String languageCode) {
         this.languageCode = languageCode;
     }
     public String getLanguageCode() {
         return languageCode;
     }

    public void setCountryCode(String CountryCode) {
         this.CountryCode = CountryCode;
     }
     public String getCountryCode() {
         return CountryCode;
     }

    public void setFullStreetName(String FullStreetName) {
         this.FullStreetName = FullStreetName;
     }
     public String getFullStreetName() {
         return FullStreetName;
     }

}