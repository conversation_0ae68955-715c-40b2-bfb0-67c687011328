package com.hll.mapdataservice.common.utils;

import com.drew.imaging.ImageMetadataReader;
import com.drew.imaging.ImageProcessingException;
import com.drew.imaging.jpeg.JpegMetadataReader;
import com.drew.imaging.jpeg.JpegProcessingException;
import com.drew.lang.GeoLocation;
import com.drew.metadata.Directory;
import com.drew.metadata.Metadata;
import com.drew.metadata.Tag;
import com.drew.metadata.exif.GpsDirectory;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class ReadPictures {
    public static void main(String[] args) throws Exception {
        List<String> fileList = new ArrayList<>();
        String path = "/Users/<USER>/Downloads/test-luce-2";
        //遍历文件夹，获取文件夹下所有文件
        getFiles(path,fileList);
        //保存结果的csv文件
        String csvPath = "/Users/<USER>/Downloads/test-luce-2.csv";
        FileWriter csvWriter = new FileWriter(new File(csvPath));
        //csv文件头
        csvWriter.append("文件名,纬度,经度,gcj02纬度,gcj02经度\n");
        for (String file : fileList) {
            File picFile = new File(file);
            csvWriter.append(picFile.getName()+",");
            //获取图片的经纬度并写到csv文件
            printImageTags(picFile,csvWriter);
            //readPic(file);
        }
        csvWriter.close();

//        printImageTags(new File("/Users/<USER>/Downloads/20230403-140058.jpeg"));
////        readPic2("/Users/<USER>/Downloads/路测-测试图片0403/20230403-124909.jpeg");
//        readPic("/Users/<USER>/Downloads/20230403-140058.jpeg");
    }

    private static void readPic2(String pathname) throws JpegProcessingException, IOException {
        File jpegFile = new File(pathname);
        Metadata metadata = JpegMetadataReader.readMetadata(jpegFile);
        GpsDirectory gpsDirectory = (GpsDirectory) metadata.getDirectories();

        if (Objects.nonNull(gpsDirectory)) {
            GeoLocation geoLocation = gpsDirectory.getGeoLocation();
            System.out.println(geoLocation.getLongitude());
            System.out.println(geoLocation.getLatitude());
        }
    }

    public static void readPic(String pathname) throws JpegProcessingException, IOException {
        File jpegFile = new File(pathname);
        Metadata metadata = JpegMetadataReader.readMetadata(jpegFile);
        //获取图片所有EXIF信息
        Iterable<Directory> directories = metadata.getDirectories();
        for (Directory directory : directories) {
            for (Tag tag : directory.getTags()) {
                System.out.println(tag);
            }
        }
    }

    private static void printImageTags(File file,FileWriter csvWriter) throws ImageProcessingException, Exception {
        Metadata metadata = ImageMetadataReader.readMetadata(file);
        double latitude = 0.0;
        double longitude = 0.0;

        for (Directory directory : metadata.getDirectories()) {
            for (Tag tag : directory.getTags()) {
                String tagName = tag.getTagName();  //标签名
                String desc = tag.getDescription(); //标签信息
                if (tagName.equals("Image Height")) {
                    System.out.println("图片高度: " + desc);
//                    csvWriter.append(desc);
//                    csvWriter.append(',');
                } else if (tagName.equals("Image Width")) {
                    System.out.println("图片宽度: " + desc);
//                    csvWriter.append(desc);
//                    csvWriter.append(',');
                } else if (tagName.equals("Date/Time Original")) {
                    System.out.println("拍摄时间: " + desc);
//                    csvWriter.append(desc);
//                    csvWriter.append(',');
                } else if (tagName.equals("GPS Longitude")) {
                    System.out.println("经度: " + desc);
                    String lon = pointToLatlong(desc);
                    System.out.println("经度(度分秒格式): "+ lon);
                    csvWriter.append(lon);
                    longitude = Double.parseDouble(lon);
                    csvWriter.append(',');
                } else if (tagName.equals("GPS Latitude")) {
                    System.out.println("纬度 : " + desc);
                    String lat = pointToLatlong(desc);
                    latitude = Double.parseDouble(lat);
                    System.out.println("纬度(度分秒格式) : "+ lat);
                    csvWriter.append(lat);
                    csvWriter.append(',');
                }
            }
//            csvWriter.close();
        }
        if (latitude != 0.0 && longitude != 0.0) {
            System.out.println("经纬度: " + longitude + "," + latitude);
            Double latGcj02 = CoordinateConverter.wgs84ToGcj02(latitude, longitude)[0];
            Double lonGcj02 = CoordinateConverter.wgs84ToGcj02(latitude, longitude)[1];
            System.out.println("火星坐标: " + lonGcj02 + "," + latGcj02);
            csvWriter.append(latGcj02.toString());
            csvWriter.append(',');
            csvWriter.append(lonGcj02.toString());
            csvWriter.append(',');
        }
        csvWriter.append('\n');
        csvWriter.flush();
    }

    /**
     * 经纬度格式  转换为  度分秒格式 ,如果需要的话可以调用该方法进行转换
     *
     * @param point 坐标点
     * @return
     */
    public static String pointToLatlong(String point) {
        Double du = Double.parseDouble(point.substring(0, point.indexOf("°")).trim());
        Double fen = Double.parseDouble(point.substring(point.indexOf("°") + 1, point.indexOf("'")).trim());
        Double miao = Double.parseDouble(point.substring(point.indexOf("'") + 1, point.indexOf("\"")).trim());
        Double duStr = du + fen / 60 + miao / 60 / 60;
        return duStr.toString();
    }


    /*
     * 通过递归得到某一路径下所有的目录及其文件
     */
    static void getFiles(String filePath, List<String> filelist){
        File root = new File(filePath);
        File[] files = root.listFiles();
        for(File file:files){
            if(file.isDirectory()){
                /*
                 * 递归调用
                 */
                getFiles(file.getAbsolutePath(),filelist);
                System.out.println("显示"+filePath+"下所有子目录及其文件"+file.getAbsolutePath());
            }else{
                filelist.add(file.getAbsolutePath());
                System.out.println("显示"+filePath+"下所有子目录"+file.getAbsolutePath());
            }
        }
    }
}