package com.hll.mapdataservice.common;

/**
 * @Author: ares.chen
 * @Since: 2021/9/28
 */
public enum DirEnum {
    B("B","1"),F("F","2"),T("T","3"),
    N("N","4");

    private String dirName;
    private String dirValue;

    DirEnum(String dirName, String dirValue) {
        this.dirName = dirName;
        this.dirValue = dirValue;
    }

    public String getDirName() {
        return dirName;
    }

    public String getDirValue() {
        return dirValue;
    }

    public static String getDirValueByDName(String dirName) {
        if (dirName == null) {
            return null;
        }
        DirEnum[] values = DirEnum.values();
        for (DirEnum langEnum : values) {
            if (dirName.equals(langEnum.getDirName())) {
                return langEnum.getDirValue();
            }
        }
        return null;
    }
}
