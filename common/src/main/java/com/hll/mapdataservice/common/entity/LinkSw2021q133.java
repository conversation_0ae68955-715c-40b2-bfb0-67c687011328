package com.hll.mapdataservice.common.entity;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.sql.JDBCType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.hll.mapdataservice.common.utils.MyGeometryTypeHandler;
import io.swagger.annotations.ApiModel;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.postgis.Geometry;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@ApiModel(value="LinkSw2021q133对象", description="")
//@DS("db8")
@TableName(value = "link_sw2021q1_33",autoResultMap = true)
public class LinkSw2021q133 extends Model<LinkSw2021q133> {

    private static final long serialVersionUID = 1L;

    //@TableId(value = "hll_linkid")
    private String hllLinkid;

    //@TableId(value = "id",type=IdType.INPUT)
    private String linkId;

    private String hllSNid;

    private String hllENid;

    private String kind;

    private String formway;

    //@TableField(value = "direction")
    private String dir;

    //@TableField(value = "const_st")
    private String app;

    private String toll;

    private String adopt;

    private String md;

    //@TableField(value = "detailcity")
    private String devs;

    //@TableField(value = "special")
    private String spet;

    //@TableField(value = "funcclass")
    private String funct;

    //@TableField(value = "uflag")
    private String urban;

    //@TableField(value = "road_cond")
    private String pave;

    //@TableField(value = "lanenumsum")
    private Integer laneN;

    //@TableField(value = "lanenums2e")
    private Integer laneL;

    //@TableField(value = "lanenume2s")
    private Integer laneR;

    //@TableField(value = "lanenumc")
    private String laneC;

    private String width;

    private String viad;

    private String lAdmin;

    private String rAdmin;

    @TableField(value = "geom",typeHandler = MyGeometryTypeHandler.class)
    private String geom;

    private Double len;

    private String fSpeed;

    private String tSpeed;

    private String spClass;

    private String diciType;

    private String verifyflag;

    private String preLaunch;

    private String nameChO;

    private String nameChA;

    private String nameChF;

    private String namePhO;

    private String namePhA;

    private String namePhF;

    private String nameEnO;

    private String nameEnA;

    private String nameEnF;

    private String namePo;

    private String nameCht;

    private String codeType;

    private String nameType;

    private String srcFlag;

    private String meshId;

    private String memo;

    private String cp;

    private String datasource;

    private LocalDateTime upDate;

    private String status;

    public String getGeomwkt() {
        return geomwkt;
    }

    public void setGeomwkt(String geomwkt) {
        this.geomwkt = geomwkt;
    }

    private String geomwkt;

    @Override
    protected Serializable pkVal() {
        return this.hllLinkid;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getHllLinkid() {
        return hllLinkid;
    }

    public void setHllLinkid(String hllLinkid) {
        this.hllLinkid = hllLinkid;
    }

    public String getLinkId() {
        return linkId;
    }

    public void setLinkId(String linkId) {
        this.linkId = linkId;
    }

    public String getHllSNid() {
        return hllSNid;
    }

    public void setHllSNid(String hllSNid) {
        this.hllSNid = hllSNid;
    }

    public String getHllENid() {
        return hllENid;
    }

    public void setHllENid(String hllENid) {
        this.hllENid = hllENid;
    }

    public String getKind() {
        return kind;
    }

    public void setKind(String kind) {
        this.kind = kind;
    }

    public String getFormway() {
        return formway;
    }

    public void setFormway(String formway) {
        this.formway = formway;
    }

    public String getDir() {
        return dir;
    }

    public void setDir(String dir) {
        this.dir = dir;
    }

    public String getApp() {
        return app;
    }

    public void setApp(String app) {
        this.app = app;
    }

    public String getToll() {
        return toll;
    }

    public void setToll(String toll) {
        this.toll = toll;
    }

    public String getAdopt() {
        return adopt;
    }

    public void setAdopt(String adopt) {
        this.adopt = adopt;
    }

    public String getMd() {
        return md;
    }

    public void setMd(String md) {
        this.md = md;
    }

    public String getDevs() {
        return devs;
    }

    public void setDevs(String devs) {
        this.devs = devs;
    }

    public String getSpet() {
        return spet;
    }

    public void setSpet(String spet) {
        this.spet = spet;
    }

    public String getFunct() {
        return funct;
    }

    public void setFunct(String funct) {
        this.funct = funct;
    }

    public String getUrban() {
        return urban;
    }

    public void setUrban(String urban) {
        this.urban = urban;
    }

    public String getPave() {
        return pave;
    }

    public void setPave(String pave) {
        this.pave = pave;
    }

    public Integer getLaneN() {
        return laneN;
    }

    public void setLaneN(Integer laneN) {
        this.laneN = laneN;
    }

    public Integer getLaneL() {
        return laneL;
    }

    public void setLaneL(Integer laneL) {
        this.laneL = laneL;
    }

    public Integer getLaneR() {
        return laneR;
    }

    public void setLaneR(Integer laneR) {
        this.laneR = laneR;
    }

    public String getLaneC() {
        return laneC;
    }

    public void setLaneC(String laneC) {
        this.laneC = laneC;
    }

    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width;
    }

    public String getViad() {
        return viad;
    }

    public void setViad(String viad) {
        this.viad = viad;
    }

    public String getlAdmin() {
        return lAdmin;
    }

    public void setlAdmin(String lAdmin) {
        this.lAdmin = lAdmin;
    }

    public String getrAdmin() {
        return rAdmin;
    }

    public void setrAdmin(String rAdmin) {
        this.rAdmin = rAdmin;
    }

    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }

    public Double getLen() {
        return len;
    }

    public void setLen(Double len) {
        this.len = len;
    }

    public String getfSpeed() {
        return fSpeed;
    }

    public void setfSpeed(String fSpeed) {
        this.fSpeed = fSpeed;
    }

    public String gettSpeed() {
        return tSpeed;
    }

    public void settSpeed(String tSpeed) {
        this.tSpeed = tSpeed;
    }

    public String getSpClass() {
        return spClass;
    }

    public void setSpClass(String spClass) {
        this.spClass = spClass;
    }

    public String getDiciType() {
        return diciType;
    }

    public void setDiciType(String diciType) {
        this.diciType = diciType;
    }

    public String getVerifyflag() {
        return verifyflag;
    }

    public void setVerifyflag(String verifyflag) {
        this.verifyflag = verifyflag;
    }

    public String getPreLaunch() {
        return preLaunch;
    }

    public void setPreLaunch(String preLaunch) {
        this.preLaunch = preLaunch;
    }

    public String getNameChO() {
        return nameChO;
    }

    public void setNameChO(String nameChO) {
        this.nameChO = nameChO;
    }

    public String getNameChA() {
        return nameChA;
    }

    public void setNameChA(String nameChA) {
        this.nameChA = nameChA;
    }

    public String getNameChF() {
        return nameChF;
    }

    public void setNameChF(String nameChF) {
        this.nameChF = nameChF;
    }

    public String getNamePhO() {
        return namePhO;
    }

    public void setNamePhO(String namePhO) {
        this.namePhO = namePhO;
    }

    public String getNamePhA() {
        return namePhA;
    }

    public void setNamePhA(String namePhA) {
        this.namePhA = namePhA;
    }

    public String getNamePhF() {
        return namePhF;
    }

    public void setNamePhF(String namePhF) {
        this.namePhF = namePhF;
    }

    public String getNameEnO() {
        return nameEnO;
    }

    public void setNameEnO(String nameEnO) {
        this.nameEnO = nameEnO;
    }

    public String getNameEnA() {
        return nameEnA;
    }

    public void setNameEnA(String nameEnA) {
        this.nameEnA = nameEnA;
    }

    public String getNameEnF() {
        return nameEnF;
    }

    public void setNameEnF(String nameEnF) {
        this.nameEnF = nameEnF;
    }

    public String getNamePo() {
        return namePo;
    }

    public void setNamePo(String namePo) {
        this.namePo = namePo;
    }

    public String getNameCht() {
        return nameCht;
    }

    public void setNameCht(String nameCht) {
        this.nameCht = nameCht;
    }

    public String getCodeType() {
        return codeType;
    }

    public void setCodeType(String codeType) {
        this.codeType = codeType;
    }

    public String getNameType() {
        return nameType;
    }

    public void setNameType(String nameType) {
        this.nameType = nameType;
    }

    public String getSrcFlag() {
        return srcFlag;
    }

    public void setSrcFlag(String srcFlag) {
        this.srcFlag = srcFlag;
    }

    public String getMeshId() {
        return meshId;
    }

    public void setMeshId(String meshId) {
        this.meshId = meshId;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getCp() {
        return cp;
    }

    public void setCp(String cp) {
        this.cp = cp;
    }

    public String getDatasource() {
        return datasource;
    }

    public void setDatasource(String datasource) {
        this.datasource = datasource;
    }

    public LocalDateTime getUpDate() {
        return upDate;
    }

    public void setUpDate(LocalDateTime upDate) {
        this.upDate = upDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "LinkSw2021q133{" +
                "hllLinkid='" + hllLinkid + '\'' +
                ", linkId='" + linkId + '\'' +
                ", hllSNid='" + hllSNid + '\'' +
                ", hllENid='" + hllENid + '\'' +
                ", kind='" + kind + '\'' +
                ", formway='" + formway + '\'' +
                ", dir='" + dir + '\'' +
                ", app='" + app + '\'' +
                ", toll='" + toll + '\'' +
                ", adopt='" + adopt + '\'' +
                ", md='" + md + '\'' +
                ", devs='" + devs + '\'' +
                ", spet='" + spet + '\'' +
                ", funct='" + funct + '\'' +
                ", urban='" + urban + '\'' +
                ", pave='" + pave + '\'' +
                ", laneN=" + laneN +
                ", laneL=" + laneL +
                ", laneR=" + laneR +
                ", laneC='" + laneC + '\'' +
                ", width='" + width + '\'' +
                ", viad='" + viad + '\'' +
                ", lAdmin='" + lAdmin + '\'' +
                ", rAdmin='" + rAdmin + '\'' +
                ", geom='" + geom + '\'' +
                ", len=" + len +
                ", fSpeed='" + fSpeed + '\'' +
                ", tSpeed='" + tSpeed + '\'' +
                ", spClass='" + spClass + '\'' +
                ", diciType='" + diciType + '\'' +
                ", verifyflag='" + verifyflag + '\'' +
                ", preLaunch='" + preLaunch + '\'' +
                ", nameChO='" + nameChO + '\'' +
                ", nameChA='" + nameChA + '\'' +
                ", nameChF='" + nameChF + '\'' +
                ", namePhO='" + namePhO + '\'' +
                ", namePhA='" + namePhA + '\'' +
                ", namePhF='" + namePhF + '\'' +
                ", nameEnO='" + nameEnO + '\'' +
                ", nameEnA='" + nameEnA + '\'' +
                ", nameEnF='" + nameEnF + '\'' +
                ", namePo='" + namePo + '\'' +
                ", nameCht='" + nameCht + '\'' +
                ", codeType='" + codeType + '\'' +
                ", nameType='" + nameType + '\'' +
                ", srcFlag='" + srcFlag + '\'' +
                ", meshId='" + meshId + '\'' +
                ", memo='" + memo + '\'' +
                ", cp='" + cp + '\'' +
                ", datasource='" + datasource + '\'' +
                ", upDate=" + upDate +
                ", status='" + status + '\'' +
                ", geomwkt='" + geomwkt + '\'' +
                '}';
    }
}
