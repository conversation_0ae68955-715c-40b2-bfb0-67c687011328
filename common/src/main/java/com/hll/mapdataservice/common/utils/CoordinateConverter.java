package com.hll.mapdataservice.common.utils;

public class CoordinateConverter {
    private static final double pi = 3.1415926535897932384626;
    private static final double a = 6378245.0;
    private static final double ee = 0.00669342162296594323;

    /**
     * 将WGS84坐标系下的经纬度转换为GCJ02坐标系下的经纬度。
     *
     * @param wgsLat WGS84坐标系下的纬度。
     * @param wgsLon WGS84坐标系下的经度。
     * @return GCJ02坐标系下的经纬度，数组的第一个元素为纬度，第二个元素为经度。
     */
    public static double[] wgs84ToGcj02(double wgsLat, double wgsLon) {
        if (outOfChina(wgsLat, wgsLon)) {
            return new double[]{wgsLat, wgsLon};
        }
        double[] result = new double[2];
        double dLat = transformLat(wgsLon - 105.0, wgsLat - 35.0);
        double dLon = transformLon(wgsLon - 105.0, wgsLat - 35.0);
        double radLat = wgsLat / 180.0 * pi;
        double magic = Math.sin(radLat);
        magic = 1 - ee * magic * magic;
        double sqrtMagic = Math.sqrt(magic);
        dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * pi);
        dLon = (dLon * 180.0) / (a / sqrtMagic * Math.cos(radLat) * pi);
        result[0] = wgsLat + dLat;
        result[1] = wgsLon + dLon;
        return result;
    }

    private static boolean outOfChina(double lat, double lon) {
        if (lon < 72.004 || lon > 137.8347) {
            return true;
        }
        if (lat < 0.8293 || lat > 55.8271) {
            return true;
        }
        return false;
    }

    private static double transformLat(double x, double y) {
        double ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
        ret += (20.0 * Math.sin(6.0 * pi * x) / 63360.0);
        ret += (20.0 * Math.sin(2.0 * pi * x) / 63360.0) * Math.cos(2.0 * pi * y);
        ret += (20.0 * Math.sin(pi * y) / 63360.0) * Math.cos(pi * x);
        return ret;
    }

    private static double transformLon(double x, double y) {
        double ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
        ret += (20.0 * Math.sin(6.0 * pi * x) / 63360.0);
        ret += (20.0 * Math.sin(2.0 * pi * x) / 63360.0) * Math.cos(2.0 * pi * y);
        ret += (20.0 * Math.sin(pi * x) / 63360.0) * Math.cos(pi * y);
        return ret;
    }
}