package com.hll.mapdataservice.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hll.mapdataservice.common.entity.LinkBreak;
import com.hll.mapdataservice.common.entity.RoadBreak;
import com.hll.mapdataservice.common.entity.RoadMatchRes;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * @Author: ares.chen
 * @Since: 2021/11/18
 */
public interface IRoadMatchResService extends IService<RoadMatchRes> {
    /**
     * osm here道路匹配
     *
     * @param roadBreaks
     * @param country
     * @param area
     * @param countDownLatch
     */
    void roadMatch(List<RoadBreak> roadBreaks, String country, String area, CountDownLatch countDownLatch) ;

    /**
     * 获取孤立道路新增道路数量
     * @return
     */
    int getAloneMergeRoadNum();

    /**
     * 分页获取孤立新增道路
     *
     * @param limit
     * @param offset
     * @return
     */
    List<RoadMatchRes> getAloneMergeRoad(int limit, int offset);

    /**
     * 获取所有单独孤立新增的道路
     *
     * @return
     */
    List<RoadMatchRes> getIndependentRoad();

    /**
     * 获取支路有变化的道路数量
     *
     * @return
     */
    int getBranchRoadChangedNum();

    /**
     * 分批获取支路有变化的道路(osmId)
     *
     * @param limit
     * @param offset
     * @return
     */
    List<String> getBranchRoadChangedId(int limit, int offset);

    /**
     * 获取树状支路的匹配数量
     *
     * @return
     */
    int getTreeBranchMergeNum();

    /**
     * 分页获取树状支路的匹配道路(osmId)
     *
     * @param step
     * @param i
     * @return
     */
    List<String> getTreeBranchMergeOsmId(int step, int i);

    /**
     * osm link与here link进行匹配
     *
     * @param batchList
     * @param country
     * @param area
     * @param countDownLatch
     */
    void linkMatch(List<LinkBreak> batchList, String country, String area, CountDownLatch countDownLatch);
}
