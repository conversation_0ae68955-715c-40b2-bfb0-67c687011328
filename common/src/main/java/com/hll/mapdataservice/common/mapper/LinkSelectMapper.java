package com.hll.mapdataservice.common.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.hll.mapdataservice.common.entity.LinkSelect;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hll.mapdataservice.common.vo.RoadProperty;
import com.hll.mapdataservice.common.vo.RoadSelectVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-26
 */
@DS("db2")
public interface LinkSelectMapper extends BaseMapper<LinkSelect> {

    @Select("select order_id, '' as order_name from link_select ${ew.customSqlSegment} group by order_id")
    List<RoadSelectVo> getRoads(@Param(Constants.WRAPPER) Wrapper wrapper);


    @Select("SELECT n.feat_id, ST_AsText(n.geom) as geom, r.display_class, r.routing_class,r.form_of_way, r.simple_traffic_direction,r.num_of_lanes,r.speedmax_neg,\n" +
            "r.speedmax_pos, r.part_of_structure,r.freeway,r.ramp,r.ownership,r.name,r.road_condition,r.no_through_traffic\n" +
            "FROM core.mnr_netw_geo_link n left join public.link_select s on n.feat_id::VARCHAR = s.linkid " +
            "left JOIN core.mnr_netw_route_link r on n.feat_id = r.netw_geo_id ${ew.customSqlSegment}")
    List<RoadProperty> getRoadInfo(@Param(Constants.WRAPPER) Wrapper wrapper);

    @Select("SELECT n.feat_id, ST_AsText(n.geom) as geom, r.display_class, r.routing_class,r.form_of_way, r.simple_traffic_direction,r.num_of_lanes,r.speedmax_neg,\n" +
            "r.speedmax_pos, r.part_of_structure,r.freeway,r.ramp,r.ownership,r.name,r.road_condition,r.no_through_traffic, \n" +
            "(select 1 from (SELECT netw_id as feat_id, restriction_id FROM core.mnr_netw2restriction) n LEFT JOIN " +
            "core.mnr_restriction rr on n.restriction_id = rr.restriction_id \n" +
            " ${ew.customSqlSegment} AND rr.restriction_type=1) as jszt, '' AS zdzzl," +
//            "(select rr.limitation from (SELECT netw_id as feat_id, restriction_id FROM logistics.mnr_netw2restriction) n " +
//            "LEFT JOIN logistics.mnr_restriction rr on n.restriction_id = rr.restriction_id \n" +
//            " ${ew.customSqlSegment} AND rr.restriction_type=101) as zdzzl,\n" +
            "(select rr.limitation from (SELECT netw_id as feat_id, restriction_id FROM logistics.mnr_netw2restriction) n " +
            "LEFT JOIN logistics.mnr_restriction rr on n.restriction_id = rr.restriction_id \n" +
            " ${ew.customSqlSegment}  AND rr.restriction_type=102) as zdzz,\n" +
            "(select rr.limitation from (SELECT netw_id as feat_id, restriction_id FROM logistics.mnr_netw2restriction) n " +
            "LEFT JOIN logistics.mnr_restriction rr on n.restriction_id = rr.restriction_id \n" +
            " ${ew.customSqlSegment}  AND rr.restriction_type=103) as zdyxcd,\n" +
            "(select rr.limitation from (SELECT netw_id as feat_id, restriction_id FROM logistics.mnr_netw2restriction) n " +
            "LEFT JOIN logistics.mnr_restriction rr on n.restriction_id = rr.restriction_id \n" +
            " ${ew.customSqlSegment}  AND rr.restriction_type=104) as zdyxkd,\n" +
            "(select rr.limitation from (SELECT netw_id as feat_id, restriction_id FROM logistics.mnr_netw2restriction) n " +
            "LEFT JOIN logistics.mnr_restriction rr on n.restriction_id = rr.restriction_id \n" +
            " ${ew.customSqlSegment}  AND rr.restriction_type=105) as zdyxgd \n" +
            "FROM core.mnr_netw_geo_link n left JOIN core.mnr_netw_route_link r on n.feat_id = r.netw_geo_id " +
            "${ew.customSqlSegment}")
    RoadProperty getRoadProperty(@Param(Constants.WRAPPER) Wrapper wrapper);
}
