package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2024/2/4
 */
@ApiModel
public class PoiCoverageRes extends Model<PoiCoverageRes> {
    private String id;
    private String type;
    private String market;
    private String name;
    private String address;
    private Double longitude;
    private Double latitude;

    private String nameHerePoiName;
    private Double orderNamePoiNameSimilarity=0.0;
    private Integer orderNamePoiNameMatch=0;

    private String nameHerePoiAliasEng;
    private Double orderNamePoiAliasSimilarity=0.0;
    private Integer orderNamePoiAliasMatch=0;

    private String nameHerePointAddressName;
    private Double orderNamePaNameSimilarity=0.0;
    private Integer orderNamePaNameMatch=0;


    private String addressHerePoiName;
    private Double orderAddressPoiNameSimilarity=0.0;
    private Integer orderAddressPoiNameMatch=0;

    private String addressHerePoiAliasEng;
    private Double orderAddressPoiAliasSimilarity=0.0;
    private Integer orderAddressPoiAliasMatch=0;

    private String addressHerePointAddressName;
    private Double orderAddressPaNameSimilarity=0.0;
    private Integer orderAddressPaNameMatch=0;

    private String nameGooglePoiName;
    private Double orderNameGooglePoiNameSimilarity=0.0;
    private Integer orderNameGooglePoiNameMatch=0;

    private String radius;
    private Double similarity;
    private LocalDateTime upDate;

    public String getNameGooglePoiName() {
        return nameGooglePoiName;
    }

    public void setNameGooglePoiName(String nameGooglePoiName) {
        this.nameGooglePoiName = nameGooglePoiName;
    }

    public Double getOrderNameGooglePoiNameSimilarity() {
        return orderNameGooglePoiNameSimilarity;
    }

    public void setOrderNameGooglePoiNameSimilarity(Double orderNameGooglePoiNameSimilarity) {
        this.orderNameGooglePoiNameSimilarity = orderNameGooglePoiNameSimilarity;
    }

    public Integer getOrderNameGooglePoiNameMatch() {
        return orderNameGooglePoiNameMatch;
    }

    public void setOrderNameGooglePoiNameMatch(Integer orderNameGooglePoiNameMatch) {
        this.orderNameGooglePoiNameMatch = orderNameGooglePoiNameMatch;
    }

    public String getNameHerePoiAliasEng() {
        return nameHerePoiAliasEng;
    }

    public void setNameHerePoiAliasEng(String nameHerePoiAliasEng) {
        this.nameHerePoiAliasEng = nameHerePoiAliasEng;
    }

    public Double getOrderNamePoiAliasSimilarity() {
        return orderNamePoiAliasSimilarity;
    }

    public void setOrderNamePoiAliasSimilarity(Double orderNamePoiAliasSimilarity) {
        this.orderNamePoiAliasSimilarity = orderNamePoiAliasSimilarity;
    }

    public Integer getOrderNamePoiAliasMatch() {
        return orderNamePoiAliasMatch;
    }

    public void setOrderNamePoiAliasMatch(Integer orderNamePoiAliasMatch) {
        this.orderNamePoiAliasMatch = orderNamePoiAliasMatch;
    }

    public String getAddressHerePoiAliasEng() {
        return addressHerePoiAliasEng;
    }

    public void setAddressHerePoiAliasEng(String addressHerePoiAliasEng) {
        this.addressHerePoiAliasEng = addressHerePoiAliasEng;
    }

    public Double getOrderAddressPoiAliasSimilarity() {
        return orderAddressPoiAliasSimilarity;
    }

    public void setOrderAddressPoiAliasSimilarity(Double orderAddressPoiAliasSimilarity) {
        this.orderAddressPoiAliasSimilarity = orderAddressPoiAliasSimilarity;
    }

    public Integer getOrderAddressPoiAliasMatch() {
        return orderAddressPoiAliasMatch;
    }

    public void setOrderAddressPoiAliasMatch(Integer orderAddressPoiAliasMatch) {
        this.orderAddressPoiAliasMatch = orderAddressPoiAliasMatch;
    }

    public Double getSimilarity() {
        return similarity;
    }

    public void setSimilarity(Double similarity) {
        this.similarity = similarity;
    }

    public String getRadius() {
        return radius;
    }

    public void setRadius(String radius) {
        this.radius = radius;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public String getNameHerePoiName() {
        return nameHerePoiName;
    }

    public void setNameHerePoiName(String nameHerePoiName) {
        this.nameHerePoiName = nameHerePoiName;
    }

    public Double getOrderNamePoiNameSimilarity() {
        return orderNamePoiNameSimilarity;
    }

    public void setOrderNamePoiNameSimilarity(Double orderNamePoiNameSimilarity) {
        this.orderNamePoiNameSimilarity = orderNamePoiNameSimilarity;
    }

    public Integer getOrderNamePoiNameMatch() {
        return orderNamePoiNameMatch;
    }

    public void setOrderNamePoiNameMatch(Integer orderNamePoiNameMatch) {
        this.orderNamePoiNameMatch = orderNamePoiNameMatch;
    }

    public String getNameHerePointAddressName() {
        return nameHerePointAddressName;
    }

    public void setNameHerePointAddressName(String nameHerePointAddressName) {
        this.nameHerePointAddressName = nameHerePointAddressName;
    }

    public Double getOrderNamePaNameSimilarity() {
        return orderNamePaNameSimilarity;
    }

    public void setOrderNamePaNameSimilarity(Double orderNamePaNameSimilarity) {
        this.orderNamePaNameSimilarity = orderNamePaNameSimilarity;
    }

    public Integer getOrderNamePaNameMatch() {
        return orderNamePaNameMatch;
    }

    public void setOrderNamePaNameMatch(Integer orderNamePaNameMatch) {
        this.orderNamePaNameMatch = orderNamePaNameMatch;
    }

    public String getAddressHerePoiName() {
        return addressHerePoiName;
    }

    public void setAddressHerePoiName(String addressHerePoiName) {
        this.addressHerePoiName = addressHerePoiName;
    }

    public Double getOrderAddressPoiNameSimilarity() {
        return orderAddressPoiNameSimilarity;
    }

    public void setOrderAddressPoiNameSimilarity(Double orderAddressPoiNameSimilarity) {
        this.orderAddressPoiNameSimilarity = orderAddressPoiNameSimilarity;
    }

    public Integer getOrderAddressPoiNameMatch() {
        return orderAddressPoiNameMatch;
    }

    public void setOrderAddressPoiNameMatch(Integer orderAddressPoiNameMatch) {
        this.orderAddressPoiNameMatch = orderAddressPoiNameMatch;
    }

    public String getAddressHerePointAddressName() {
        return addressHerePointAddressName;
    }

    public void setAddressHerePointAddressName(String addressHerePointAddressName) {
        this.addressHerePointAddressName = addressHerePointAddressName;
    }

    public Double getOrderAddressPaNameSimilarity() {
        return orderAddressPaNameSimilarity;
    }

    public void setOrderAddressPaNameSimilarity(Double orderAddressPaNameSimilarity) {
        this.orderAddressPaNameSimilarity = orderAddressPaNameSimilarity;
    }

    public Integer getOrderAddressPaNameMatch() {
        return orderAddressPaNameMatch;
    }

    public void setOrderAddressPaNameMatch(Integer orderAddressPaNameMatch) {
        this.orderAddressPaNameMatch = orderAddressPaNameMatch;
    }

    public LocalDateTime getUpDate() {
        return upDate;
    }

    public void setUpDate(LocalDateTime upDate) {
        this.upDate = upDate;
    }

    @Override
    public String toString() {
        return "PoiCoverageRes{" +
                "id='" + id + '\'' +
                ", type='" + type + '\'' +
                ", market='" + market + '\'' +
                ", name='" + name + '\'' +
                ", address='" + address + '\'' +
                ", longitude=" + longitude +
                ", latitude=" + latitude +
                ", nameHerePoiName='" + nameHerePoiName + '\'' +
                ", orderNamePoiNameSimilarity=" + orderNamePoiNameSimilarity +
                ", orderNamePoiNameMatch=" + orderNamePoiNameMatch +
                ", nameHerePointAddressName='" + nameHerePointAddressName + '\'' +
                ", orderNamePaNameSimilarity=" + orderNamePaNameSimilarity +
                ", orderNamePaNameMatch=" + orderNamePaNameMatch +
                ", addressHerePoiName='" + addressHerePoiName + '\'' +
                ", orderAddressPoiNameSimilarity=" + orderAddressPoiNameSimilarity +
                ", orderAddressPoiNameMatch=" + orderAddressPoiNameMatch +
                ", addressHerePointAddressName='" + addressHerePointAddressName + '\'' +
                ", orderAddressPaNameSimilarity=" + orderAddressPaNameSimilarity +
                ", orderAddressPaNameMatch=" + orderAddressPaNameMatch +
                ", upDate=" + upDate +
                '}';
    }
}
