package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.hll.mapdataservice.common.utils.MyGeometryTypeHandler;
import io.swagger.annotations.ApiModel;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-24
 */
@ApiModel(value="Poi对象", description="")
//@TableName(value = "poi_h_pha")
//@DS("db8")
@ToString
public class PoiM extends Model<PoiM> {

    private static final long serialVersionUID = 1L;

    @TableField(value = "poi_id")
    private String poiId;

    private String poiMiddleId;

    @TableId(value = "source_id")
    private String sourceId;

    public String getKind() {
        return kind;
    }

    public void setKind(String kind) {
        this.kind = kind;
    }

    private String kind;

    private String kindCode;

    private String name;

    //2021/11/23新增
    private String languageCode;

    private String nameEng;

    //2021/11/23新增
    private String nameTrans;

    //2021/11/23新增
    private String languageType;

    //2021/11/23新增
    private String transLanguage;

    //2021/11/23新增

    @TableField(value = "name_s_language")
    private String nameSlanguage;

    private String alias;

    private String aliasEng;

    //2021/11/23新增
    private String aliasLanguage;

    //2021/11/23新增
    private String exonym;

    //2021/11/23新增
    private String exonymLanguage;

    //2021/11/23新增
    private String streetName;

    //2021/11/23新增
    private String houseNumber;

    private String address;

    private String addressEng;


    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getLanguageCode() {
        return languageCode;
    }

    public void setLanguageCode(String languageCode) {
        this.languageCode = languageCode;
    }

    public String getNameTrans() {
        return nameTrans;
    }

    public void setNameTrans(String nameTrans) {
        this.nameTrans = nameTrans;
    }

    public String getLanguageType() {
        return languageType;
    }

    public void setLanguageType(String languageType) {
        this.languageType = languageType;
    }

    public String getTransLanguage() {
        return transLanguage;
    }

    public void setTransLanguage(String transLanguage) {
        this.transLanguage = transLanguage;
    }

    public String getNameSlanguage() {
        return nameSlanguage;
    }

    public void setNameSlanguage(String nameSlanguage) {
        this.nameSlanguage = nameSlanguage;
    }

    public String getAliasLanguage() {
        return aliasLanguage;
    }

    public void setAliasLanguage(String aliasLanguage) {
        this.aliasLanguage = aliasLanguage;
    }

    public String getExonym() {
        return exonym;
    }

    public void setExonym(String exonym) {
        this.exonym = exonym;
    }

    public String getExonymLanguage() {
        return exonymLanguage;
    }

    public void setExonymLanguage(String exonymLanguage) {
        this.exonymLanguage = exonymLanguage;
    }

    public String getStreetName() {
        return streetName;
    }

    public void setStreetName(String streetName) {
        this.streetName = streetName;
    }

    public String getHouseNumber() {
        return houseNumber;
    }

    public void setHouseNumber(String houseNumber) {
        this.houseNumber=houseNumber;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }



    public Double getLonGuide() {
        return lonGuide;
    }

    public void setLonGuide(Double lonGuide) {
        this.lonGuide = lonGuide;
    }

    public Double getLatGuide() {
        return latGuide;
    }

    public void setLatGuide(Double latGuide) {
        this.latGuide = latGuide;
    }

    private Double longitude;

    private Double latitude;

    private Double lonGuide;

    private Double latGuide;

    private String linkId;

    private String side;

    private String importance;

    private String vadminCode;

    private String zipCode;

    private String telephone;

    private String telType;

    private String poiClass;

    private String starRating;

    private String tgType;

    private String accessFlag;

    private String gate;

    private String aoiId;

    private String priorAuth;

    private String foodType;

    private String brand;

    private Integer qualityscore;

    private String stLangcd;

    private String stNameEng;

    public Integer getQualityscore() {
        return qualityscore;
    }

    public void setQualityscore(Integer qualityscore) {
        this.qualityscore = qualityscore;
    }

    public String getStLangcd() {
        return stLangcd;
    }

    public void setStLangcd(String stLangcd) {
        this.stLangcd = stLangcd;
    }

    public String getStNameEng() {
        return stNameEng;
    }

    public void setStNameEng(String stNameEng) {
        this.stNameEng = stNameEng;
    }

    public String getAdminlvl1Code() {
        return adminlvl1Code;
    }

    public void setAdminlvl1Code(String adminlvl1Code) {
        this.adminlvl1Code = adminlvl1Code;
    }

    public String getAdminlvl1Name() {
        return adminlvl1Name;
    }

    public void setAdminlvl1Name(String adminlvl1Name) {
        this.adminlvl1Name = adminlvl1Name;
    }

    public String getAdminlvl1NameEng() {
        return adminlvl1NameEng;
    }

    public void setAdminlvl1NameEng(String adminlvl1NameEng) {
        this.adminlvl1NameEng = adminlvl1NameEng;
    }

    public String getAdminlvl2Code() {
        return adminlvl2Code;
    }

    public void setAdminlvl2Code(String adminlvl2Code) {
        this.adminlvl2Code = adminlvl2Code;
    }

    public String getAdminlvl2Name() {
        return adminlvl2Name;
    }

    public void setAdminlvl2Name(String adminlvl2Name) {
        this.adminlvl2Name = adminlvl2Name;
    }

    public String getAdminlvl2NameEng() {
        return adminlvl2NameEng;
    }

    public void setAdminlvl2NameEng(String adminlvl2NameEng) {
        this.adminlvl2NameEng = adminlvl2NameEng;
    }

    private String adminlvl1Code;

    private String adminlvl1Name;

    private String adminlvl1NameEng;

    private String adminlvl2Code;

    private String adminlvl2Name;

    private String adminlvl2NameEng;

    private String adminlvl3Code;

    private String adminlvl3Name;

    private String adminlvl3NameEng;

    private String adminlvl4Code;

    private String adminlvl4Name;

    private String adminlvl4NameEng;

    private String adminlvl5Code;

    private String adminlvl5Name;

    private String adminlvl5NameEng;

    private String rank;

    private String parent;

    private String children;

    private String openStatus;

    private String accessNum;

    private String parkNum;

    private String memo;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    private Integer status;

    @TableField(typeHandler = MyGeometryTypeHandler.class)
    private String poiGeo;

    public String getNameS() {
        return nameS;
    }

    public void setNameS(String nameS) {
        this.nameS = nameS;
    }

    public String getNameSEng() {
        return nameSEng;
    }

    public void setNameSEng(String nameSEng) {
        this.nameSEng = nameSEng;
    }

    private String nameS;

    private String nameSEng;

    private String source;

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getPoiId() {
        return poiId;
    }

    public void setPoiId(String poiId) {
        this.poiId = poiId;
    }
    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }
    public String getKindCode() {
        return kindCode;
    }

    public void setKindCode(String kindCode) {
        this.kindCode = kindCode;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public String getNameEng() {
        return nameEng;
    }

    public void setNameEng(String nameEng) {
        this.nameEng = nameEng;
    }
    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }
    public String getAliasEng() {
        return aliasEng;
    }

    public void setAliasEng(String aliasEng) {
        this.aliasEng = aliasEng;
    }
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
    public String getAddressEng() {
        return addressEng;
    }

    public void setAddressEng(String addressEng) {
        this.addressEng = addressEng;
    }

    public String getLinkId() {
        return linkId;
    }

    public void setLinkId(String linkId) {
        this.linkId = linkId;
    }
    public String getSide() {
        return side;
    }

    public void setSide(String side) {
        this.side = side;
    }
    public String getImportance() {
        return importance;
    }

    public void setImportance(String importance) {
        this.importance = importance;
    }
    public String getVadminCode() {
        return vadminCode;
    }

    public void setVadminCode(String vadminCode) {
        this.vadminCode = vadminCode;
    }
    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }
    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }
    public String getTelType() {
        return telType;
    }

    public void setTelType(String telType) {
        this.telType = telType;
    }
    public String getPoiClass() {
        return poiClass;
    }

    public void setPoiClass(String poiClass) {
        this.poiClass = poiClass;
    }
    public String getStarRating() {
        return starRating;
    }

    public void setStarRating(String starRating) {
        this.starRating = starRating;
    }
    public String getTgType() {
        return tgType;
    }

    public void setTgType(String tgType) {
        this.tgType = tgType;
    }
    public String getAccessFlag() {
        return accessFlag;
    }

    public void setAccessFlag(String accessFlag) {
        this.accessFlag = accessFlag;
    }
    public String getGate() {
        return gate;
    }

    public void setGate(String gate) {
        this.gate = gate;
    }
    public String getAoiId() {
        return aoiId;
    }

    public void setAoiId(String aoiId) {
        this.aoiId = aoiId;
    }
    public String getPriorAuth() {
        return priorAuth;
    }

    public void setPriorAuth(String priorAuth) {
        this.priorAuth = priorAuth;
    }
    public String getFoodType() {
        return foodType;
    }

    public void setFoodType(String foodType) {
        this.foodType = foodType;
    }
    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }
    public String getAdminlvl3Code() {
        return adminlvl3Code;
    }

    public void setAdminlvl3Code(String adminlvl3Code) {
        this.adminlvl3Code = adminlvl3Code;
    }
    public String getAdminlvl3Name() {
        return adminlvl3Name;
    }

    public void setAdminlvl3Name(String adminlvl3Name) {
        this.adminlvl3Name = adminlvl3Name;
    }
    public String getAdminlvl3NameEng() {
        return adminlvl3NameEng;
    }

    public void setAdminlvl3NameEng(String adminlvl3NameEng) {
        this.adminlvl3NameEng = adminlvl3NameEng;
    }
    public String getAdminlvl4Code() {
        return adminlvl4Code;
    }

    public void setAdminlvl4Code(String adminlvl4Code) {
        this.adminlvl4Code = adminlvl4Code;
    }
    public String getAdminlvl4Name() {
        return adminlvl4Name;
    }

    public void setAdminlvl4Name(String adminlvl4Name) {
        this.adminlvl4Name = adminlvl4Name;
    }

    public String getAdminlvl4NameEng() {
        return adminlvl4NameEng;
    }

    public void setAdminlvl4NameEng(String adminlvl4NameEng) {
        this.adminlvl4NameEng = adminlvl4NameEng;
    }

    public String getAdminlvl5Code() {
        return adminlvl5Code;
    }

    public void setAdminlvl5Code(String adminlvl5Code) {
        this.adminlvl5Code = adminlvl5Code;
    }
    public String getAdminlvl5Name() {
        return adminlvl5Name;
    }

    public void setAdminlvl5Name(String adminlvl5Name) {
        this.adminlvl5Name = adminlvl5Name;
    }
    public String getAdminlvl5NameEng() {
        return adminlvl5NameEng;
    }

    public void setAdminlvl5NameEng(String adminlvl5NameEng) {
        this.adminlvl5NameEng = adminlvl5NameEng;
    }
    public String getRank() {
        return rank;
    }

    public void setRank(String rank) {
        this.rank = rank;
    }

    public String getParent() {
        return parent;
    }

    public void setParent(String parent) {
        this.parent = parent;
    }
    public String getChildren() {
        return children;
    }

    public void setChildren(String children) {
        this.children = children;
    }
    public String getOpenStatus() {
        return openStatus;
    }

    public void setOpenStatus(String openStatus) {
        this.openStatus = openStatus;
    }
    public String getAccessNum() {
        return accessNum;
    }

    public void setAccessNum(String accessNum) {
        this.accessNum = accessNum;
    }
    public String getParkNum() {
        return parkNum;
    }

    public void setParkNum(String parkNum) {
        this.parkNum = parkNum;
    }
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    public String getPoiGeo() {
        return poiGeo;
    }

    public void setPoiGeo(String poiGeo) {
        this.poiGeo = poiGeo;
    }

    @Override
    protected Serializable pkVal() {
        return this.poiId;
    }

    public String getPoiMiddleId() {
        return poiMiddleId;
    }

    public void setPoiMiddleId(String poiMiddleId) {
        this.poiMiddleId = poiMiddleId;
    }

}
