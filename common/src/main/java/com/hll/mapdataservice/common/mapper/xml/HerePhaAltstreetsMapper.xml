<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.HerePhaAltstreetsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.HerePhaAltstreets">
        <id column="gid" property="gid" />
        <result column="link_id" property="linkId" />
        <result column="st_name" property="stName" />
        <result column="feat_id" property="featId" />
        <result column="st_langcd" property="stLangcd" />
        <result column="st_nm_pref" property="stNmPref" />
        <result column="st_typ_bef" property="stTypBef" />
        <result column="st_nm_base" property="stNmBase" />
        <result column="st_nm_suff" property="stNmSuff" />
        <result column="st_typ_aft" property="stTypAft" />
        <result column="st_typ_att" property="stTypAtt" />
        <result column="addr_type" property="addrType" />
        <result column="l_refaddr" property="lRefaddr" />
        <result column="l_nrefaddr" property="lNrefaddr" />
        <result column="l_addrsch" property="lAddrsch" />
        <result column="l_addrform" property="lAddrform" />
        <result column="r_refaddr" property="rRefaddr" />
        <result column="r_nrefaddr" property="rNrefaddr" />
        <result column="r_addrsch" property="rAddrsch" />
        <result column="r_addrform" property="rAddrform" />
        <result column="num_ad_rng" property="numAdRng" />
        <result column="route_type" property="routeType" />
        <result column="dironsign" property="dironsign" />
        <result column="explicatbl" property="explicatbl" />
        <result column="nameonrdsn" property="nameonrdsn" />
        <result column="postalname" property="postalname" />
        <result column="stalename" property="stalename" />
        <result column="vanityname" property="vanityname" />
        <result column="junctionnm" property="junctionnm" />
        <result column="exitname" property="exitname" />
        <result column="scenic_nm" property="scenicNm" />
        <result column="geom" property="geom" />
    </resultMap>

</mapper>
