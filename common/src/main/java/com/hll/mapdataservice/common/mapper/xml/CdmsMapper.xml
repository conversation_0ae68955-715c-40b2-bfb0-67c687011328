<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.CdmsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.Cdms">
        <id column="link_id" property="linkId" />
        <result column="cond_id" property="condId" />
        <result column="cond_type" property="condType" />
        <result column="cond_val1" property="condVal1" />
        <result column="cond_val2" property="condVal2" />
        <result column="cond_val3" property="condVal3" />
        <result column="cond_val4" property="condVal4" />
        <result column="end_of_lk" property="endOfLk" />
        <result column="ar_auto" property="arAuto" />
        <result column="ar_bus" property="arBus" />
        <result column="ar_taxis" property="arTaxis" />
        <result column="ar_carpool" property="arCarpool" />
        <result column="ar_pedstrn" property="arPedstrn" />
        <result column="ar_trucks" property="arTrucks" />
        <result column="ar_thrutr" property="arThrutr" />
        <result column="ar_deliver" property="arDeliver" />
        <result column="ar_emerveh" property="arEmerveh" />
        <result column="ar_motor" property="arMotor" />
    </resultMap>

</mapper>
