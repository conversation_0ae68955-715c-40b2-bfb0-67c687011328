package com.hll.mapdataservice.common.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class StatusUtil {

    public static List<Integer> status(String status) {
        return Arrays.stream(status.split(","))
                .map(Integer::valueOf)
                .collect(Collectors.toCollection(ArrayList::new));
    }
}
