package com.hll.mapdataservice.common.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.hll.mapdataservice.common.entity.PoiSelect;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hll.mapdataservice.common.vo.RoadSelectVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-22
 */
public interface PoiSelectMapper extends BaseMapper<PoiSelect> {
    @Select("select order_id, '' as order_name from poi_select ${ew.customSqlSegment} group by order_id")
    List<RoadSelectVo> getRoads(@Param(Constants.WRAPPER) Wrapper wrapper);

    @Select("select true_id from poi_select ${ew.customSqlSegment} group by true_id")
    List<String> getTrueIds(@Param(Constants.WRAPPER) Wrapper wrapper);
}
