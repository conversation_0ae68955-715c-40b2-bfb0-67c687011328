package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/7/28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GatherResult extends Model<GatherResult> {
    private String year;
    private String quarter;
    private String market;
    private String result;
    private Date upDate;
}
