package com.hll.mapdataservice.common.entity;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-01
 */
//@DS("db4")
@ApiModel(value="Cndmod对象", description="")
public class Cndmod extends Model<Cndmod> {

    private static final long serialVersionUID = 1L;

    private Integer condId;

    private String langCode;

    private Integer modType;

    private String modVal;

    public Integer getCondId() {
        return condId;
    }

    public void setCondId(Integer condId) {
        this.condId = condId;
    }
    public String getLangCode() {
        return langCode;
    }

    public void setLangCode(String langCode) {
        this.langCode = langCode;
    }
    public Integer getModType() {
        return modType;
    }

    public void setModType(Integer modType) {
        this.modType = modType;
    }
    public String getModVal() {
        return modVal;
    }

    public void setModVal(String modVal) {
        this.modVal = modVal;
    }

    @Override
    protected Serializable pkVal() {
        return this.condId;
    }

    @Override
    public String toString() {
        return "Cndmod{" +
            "condId=" + condId +
            ", langCode=" + langCode +
            ", modType=" + modType +
            ", modVal=" + modVal +
        "}";
    }
}
