package com.hll.mapdataservice.common.entity;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-06
 */
@ApiModel(value="Toll对象", description="")
@DS("db20")
public class Toll extends Model<Toll> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "toll_id", type = IdType.INPUT)
    private Long tollId;

    private String tollNameEn;

    private String tollFee;

    private String tollNameMultiLang;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Integer status;

    private Long linkId;

    private String geom;

    public Long getTollId() {
        return tollId;
    }

    public void setTollId(Long tollId) {
        this.tollId = tollId;
    }
    public String getTollNameEn() {
        return tollNameEn;
    }

    public void setTollNameEn(String tollNameEn) {
        this.tollNameEn = tollNameEn;
    }
    public String getTollFee() {
        return tollFee;
    }

    public void setTollFee(String tollFee) {
        this.tollFee = tollFee;
    }
    public String getTollNameMultiLang() {
        return tollNameMultiLang;
    }

    public void setTollNameMultiLang(String tollNameMultiLang) {
        this.tollNameMultiLang = tollNameMultiLang;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public Long getLinkId() {
        return linkId;
    }

    public void setLinkId(Long linkId) {
        this.linkId = linkId;
    }
    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }

    @Override
    protected Serializable pkVal() {
        return this.tollId;
    }

    @Override
    public String toString() {
        return "Toll{" +
            "tollId=" + tollId +
            ", tollNameEn=" + tollNameEn +
            ", tollFee=" + tollFee +
            ", tollNameMultiLang=" + tollNameMultiLang +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", status=" + status +
            ", linkId=" + linkId +
            ", geom=" + geom +
        "}";
    }
}
