package com.hll.mapdataservice.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hll.mapdataservice.common.entity.LinkBreak;
import com.hll.mapdataservice.common.entity.LinkM;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * @Author: ares.chen
 * @Since: 2022/2/8
 */
public interface ILinkBreakService extends IService<LinkBreak> {
    void linkBreak(List<LinkM> batchList, CountDownLatch downLatch, Long version, String country, String area);
}
