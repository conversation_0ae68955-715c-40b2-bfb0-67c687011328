package com.hll.mapdataservice.common.mapper;

import org.apache.ibatis.annotations.Param;

/**
 * 用于做计算的mapper
 *
 * @Author: ares.chen
 * @Since: 2021/11/17
 */
public interface CalculationMapper {

    double calLineDistance(@Param("line1") String line1, @Param("line2") String line2);

    Double calAngle(Double long1, Double lat1, Double long2, Double lat2);
    Double calAngleLine(String line1,String line2);

    String isIntersection(String line1, String line2);

    String calClosetPoint(String targetLine,String sourceLine);

    boolean calContains(String geom, String linkGeom);
}
