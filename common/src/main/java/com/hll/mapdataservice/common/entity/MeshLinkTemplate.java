package com.hll.mapdataservice.common.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Data
@ToString
@EqualsAndHashCode(callSuper = false)
public class MeshLinkTemplate extends Model<MeshLinkTemplate> {

    @TableId(value = "hll_linkid", type = IdType.INPUT)
    private Long hllLinkid;
    private Long dLinkid;
    private Long hllSNid;
    private Long hllENid;
    private String kind;
    private String formway;
    private String direction;
    private Integer app;
    private Integer toll;
    @JSONField(serialize = false)
    private Integer adopt;
    private Integer spet;
    private Integer funct;
    private Integer urban;
    private Integer pave;
    private Integer laneN;
    private Integer laneL;
    private Integer laneR;
    private Integer laneC;
    private String width;
    private Integer viad;
    private String lAdmin;
    private String rAdmin;
    @TableField("ST_AsText(geom)")
    private String geom;
    private Double len;
    private String fSpeed;
    private String tSpeed;
    private String spClass;
    @JSONField(serialize = false)
    private Integer diciType;
    @JSONField(serialize = false)
    private Integer verifyflag;
    @JSONField(serialize = false)
    private String preLaunch;
    private String nameChO;
    private String nameChA;
    private String nameChF;
    @JSONField(serialize = false)
    private Integer codeType;
    @JSONField(serialize = false)
    private Integer srcFlag;
    private String meshId;
    private String memo;
    private String cp;
    private String datasource;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private LocalDateTime upDate;
    private Integer status;
    private String collectVersion;
    private String subId;
    private Integer workStatus;
    private String taskId;
    private String groupId;
    private Integer dataWay;
    private String compacity;
    private String signLength;
    private String citykind;
    private String nameType;
}
