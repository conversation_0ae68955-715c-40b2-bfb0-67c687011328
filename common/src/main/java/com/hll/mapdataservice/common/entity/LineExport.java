package com.hll.mapdataservice.common.entity;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * <AUTHOR>
 * @Date 2023/6/13
 */
public class LineExport {
    @ExcelProperty("inLinkId")
    private String inLinkId;
    @ExcelProperty("id")
    private String id;
    @ExcelProperty("outLinkId")
    private String outLinkId;
    @ExcelProperty("lineWkt")
    private String lineWkt;
    @ExcelProperty("direction")
    private String direction;

    public String getInLinkId() {
        return inLinkId;
    }

    public void setInLinkId(String inLinkId) {
        this.inLinkId = inLinkId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOutLinkId() {
        return outLinkId;
    }

    public void setOutLinkId(String outLinkId) {
        this.outLinkId = outLinkId;
    }

    public String getLineWkt() {
        return lineWkt;
    }

    public void setLineWkt(String lineWkt) {
        this.lineWkt = lineWkt;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }
}
