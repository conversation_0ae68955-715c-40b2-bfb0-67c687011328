package com.hll.mapdataservice.common.vo;

import com.hll.mapdataservice.common.entity.HerePhaAutosvc;
import com.hll.mapdataservice.common.entity.HerePhaBusiness;
import com.hll.mapdataservice.common.entity.HerePhaCommsvc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PoiBase {
    private Integer gid;

    private Integer linkId;

    private Integer poiId;

    private Integer seqNum;

    private Integer facType;

    private String poiName;

    private String poiLangcd;

    private String poiNmtype;

    private String poiStNum;

    private String stNumFul;

    private String stNfulLc;

    private String stName;

    private String stLangcd;

    private String poiStSd;

    private String accType;

    private String phNumber;

    private Integer chainId;

    private String natImport;

    public PoiBase(HerePhaAutosvc herePhaAutosvc) {
        this.accType=herePhaAutosvc.getAccType();
        this.actAddr=herePhaAutosvc.getActAddr();
        this.actAdmin=herePhaAutosvc.getActAdmin();
        this.actLangcd=herePhaAutosvc.getActLangcd();
        this.actPostal=herePhaAutosvc.getActPostal();
        this.actStNam=herePhaAutosvc.getActStNam();
        this.actStNum=herePhaAutosvc.getActStNum();
        this.chainId=herePhaAutosvc.getChainId();
        this.diesel=herePhaAutosvc.getDiesel();
        this.facType=herePhaAutosvc.getFacType();
        this.geom=herePhaAutosvc.getGeom();
        this.gid=herePhaAutosvc.getGid();
        this.inVicin=herePhaAutosvc.getInVicin();
        this.linkId=herePhaAutosvc.getLinkId();
        this.natImport=herePhaAutosvc.getNatImport();
        this.numChild=herePhaAutosvc.getNumChild();
        this.numParent=herePhaAutosvc.getNumParent();
        this.open24=herePhaAutosvc.getOpen24();
        this.percfrref=herePhaAutosvc.getPercfrref();
        this.phNumber=herePhaAutosvc.getPhNumber();
        this.poiId=herePhaAutosvc.getPoiId();
        this.poiLangcd=herePhaAutosvc.getPoiLangcd();
        this.poiName=herePhaAutosvc.getPoiName();
        this.poiNmtype=herePhaAutosvc.getPoiNmtype();
        this.poiStNum=herePhaAutosvc.getPoiStNum();
        this.poiStSd=herePhaAutosvc.getPoiStSd();
        this.privateInfo=herePhaAutosvc.getPrivateInfo();
        this.seqNum=herePhaAutosvc.getSeqNum();
        this.stLangcd=herePhaAutosvc.getStLangcd();
        this.stName=herePhaAutosvc.getStName();
        this.stNfulLc=herePhaAutosvc.getStNfulLc();
        this.stNumFul=herePhaAutosvc.getStNumFul();
        this.vancityId=herePhaAutosvc.getVancityId();
    }

    public PoiBase(HerePhaBusiness herePhaAutosvc) {
        this.accType=herePhaAutosvc.getAccType();
        this.actAddr=herePhaAutosvc.getActAddr();
        this.actAdmin=herePhaAutosvc.getActAdmin();
        this.actLangcd=herePhaAutosvc.getActLangcd();
        this.actPostal=herePhaAutosvc.getActPostal();
        this.actStNam=herePhaAutosvc.getActStNam();
        this.actStNum=herePhaAutosvc.getActStNum();
        this.chainId=herePhaAutosvc.getChainId();
        this.facType=herePhaAutosvc.getFacType();
        this.geom=herePhaAutosvc.getGeom();
        this.gid=herePhaAutosvc.getGid();
        this.inVicin=herePhaAutosvc.getInVicin();
        this.linkId=herePhaAutosvc.getLinkId();
        this.natImport=herePhaAutosvc.getNatImport();
        this.numChild=herePhaAutosvc.getNumChild();
        this.numParent=herePhaAutosvc.getNumParent();
        this.percfrref=herePhaAutosvc.getPercfrref();
        this.phNumber=herePhaAutosvc.getPhNumber();
        this.poiId=herePhaAutosvc.getPoiId();
        this.poiLangcd=herePhaAutosvc.getPoiLangcd();
        this.poiName=herePhaAutosvc.getPoiName();
        this.poiNmtype=herePhaAutosvc.getPoiNmtype();
        this.poiStNum=herePhaAutosvc.getPoiStNum();
        this.poiStSd=herePhaAutosvc.getPoiStSd();
        this.privateInfo=herePhaAutosvc.getPrivateInfo();
        this.seqNum=herePhaAutosvc.getSeqNum();
        this.stLangcd=herePhaAutosvc.getStLangcd();
        this.stName=herePhaAutosvc.getStName();
        this.stNfulLc=herePhaAutosvc.getStNfulLc();
        this.stNumFul=herePhaAutosvc.getStNumFul();
        this.vancityId=herePhaAutosvc.getVancityId();
    }

    public PoiBase(HerePhaCommsvc herePhaAutosvc) {
        this.accType=herePhaAutosvc.getAccType();
        this.actAddr=herePhaAutosvc.getActAddr();
        this.actAdmin=herePhaAutosvc.getActAdmin();
        this.actLangcd=herePhaAutosvc.getActLangcd();
        this.actPostal=herePhaAutosvc.getActPostal();
        this.actStNam=herePhaAutosvc.getActStNam();
        this.actStNum=herePhaAutosvc.getActStNum();
        this.chainId=herePhaAutosvc.getChainId();
        this.facType=herePhaAutosvc.getFacType();
        this.geom=herePhaAutosvc.getGeom();
        this.gid=herePhaAutosvc.getGid();
        this.inVicin=herePhaAutosvc.getInVicin();
        this.linkId=herePhaAutosvc.getLinkId();
        this.natImport=herePhaAutosvc.getNatImport();
        this.numChild=herePhaAutosvc.getNumChild();
        this.numParent=herePhaAutosvc.getNumParent();
        this.percfrref=herePhaAutosvc.getPercfrref();
        this.phNumber=herePhaAutosvc.getPhNumber();
        this.poiId=herePhaAutosvc.getPoiId();
        this.poiLangcd=herePhaAutosvc.getPoiLangcd();
        this.poiName=herePhaAutosvc.getPoiName();
        this.poiNmtype=herePhaAutosvc.getPoiNmtype();
        this.poiStNum=herePhaAutosvc.getPoiStNum();
        this.poiStSd=herePhaAutosvc.getPoiStSd();
        this.privateInfo=herePhaAutosvc.getPrivateInfo();
        this.seqNum=herePhaAutosvc.getSeqNum();
        this.stLangcd=herePhaAutosvc.getStLangcd();
        this.stName=herePhaAutosvc.getStName();
        this.stNfulLc=herePhaAutosvc.getStNfulLc();
        this.stNumFul=herePhaAutosvc.getStNumFul();
        this.vancityId=herePhaAutosvc.getVancityId();
    }


    public String getPrivateInfo() {
        return privateInfo;
    }

    public void setPrivateInfo(String privateInfo) {
        this.privateInfo = privateInfo;
    }

    private String privateInfo;

    private String inVicin;

    private Integer numParent;

    private Integer numChild;

    private Integer percfrref;

    private Integer vancityId;

    private String actAddr;

    private String actLangcd;

    private String actStNam;

    private String actStNum;

    private String actAdmin;

    private String actPostal;

    private String open24;

    private String diesel;

    private String geom;

    public Integer getGid() {
        return gid;
    }

    public void setGid(Integer gid) {
        this.gid = gid;
    }

    public Integer getLinkId() {
        return linkId;
    }

    public void setLinkId(Integer linkId) {
        this.linkId = linkId;
    }

    public Integer getPoiId() {
        return poiId;
    }

    public void setPoiId(Integer poiId) {
        this.poiId = poiId;
    }

    public Integer getSeqNum() {
        return seqNum;
    }

    public void setSeqNum(Integer seqNum) {
        this.seqNum = seqNum;
    }

    public Integer getFacType() {
        return facType;
    }

    public void setFacType(Integer facType) {
        this.facType = facType;
    }

    public String getPoiName() {
        return poiName;
    }

    public void setPoiName(String poiName) {
        this.poiName = poiName;
    }

    public String getPoiLangcd() {
        return poiLangcd;
    }

    public void setPoiLangcd(String poiLangcd) {
        this.poiLangcd = poiLangcd;
    }

    public String getPoiNmtype() {
        return poiNmtype;
    }

    public void setPoiNmtype(String poiNmtype) {
        this.poiNmtype = poiNmtype;
    }

    public String getPoiStNum() {
        return poiStNum;
    }

    public void setPoiStNum(String poiStNum) {
        this.poiStNum = poiStNum;
    }

    public String getStNumFul() {
        return stNumFul;
    }

    public void setStNumFul(String stNumFul) {
        this.stNumFul = stNumFul;
    }

    public String getStNfulLc() {
        return stNfulLc;
    }

    public void setStNfulLc(String stNfulLc) {
        this.stNfulLc = stNfulLc;
    }

    public String getStName() {
        return stName;
    }

    public void setStName(String stName) {
        this.stName = stName;
    }

    public String getStLangcd() {
        return stLangcd;
    }

    public void setStLangcd(String stLangcd) {
        this.stLangcd = stLangcd;
    }

    public String getPoiStSd() {
        return poiStSd;
    }

    public void setPoiStSd(String poiStSd) {
        this.poiStSd = poiStSd;
    }

    public String getAccType() {
        return accType;
    }

    public void setAccType(String accType) {
        this.accType = accType;
    }

    public String getPhNumber() {
        return phNumber;
    }

    public void setPhNumber(String phNumber) {
        this.phNumber = phNumber;
    }

    public Integer getChainId() {
        return chainId;
    }

    public void setChainId(Integer chainId) {
        this.chainId = chainId;
    }

    public String getNatImport() {
        return natImport;
    }

    public void setNatImport(String natImport) {
        this.natImport = natImport;
    }

    public String getInVicin() {
        return inVicin;
    }

    public void setInVicin(String inVicin) {
        this.inVicin = inVicin;
    }

    public Integer getNumParent() {
        return numParent;
    }

    public void setNumParent(Integer numParent) {
        this.numParent = numParent;
    }

    public Integer getNumChild() {
        return numChild;
    }

    public void setNumChild(Integer numChild) {
        this.numChild = numChild;
    }

    public Integer getPercfrref() {
        return percfrref;
    }

    public void setPercfrref(Integer percfrref) {
        this.percfrref = percfrref;
    }

    public Integer getVancityId() {
        return vancityId;
    }

    public void setVancityId(Integer vancityId) {
        this.vancityId = vancityId;
    }

    public String getActAddr() {
        return actAddr;
    }

    public void setActAddr(String actAddr) {
        this.actAddr = actAddr;
    }

    public String getActLangcd() {
        return actLangcd;
    }

    public void setActLangcd(String actLangcd) {
        this.actLangcd = actLangcd;
    }

    public String getActStNam() {
        return actStNam;
    }

    public void setActStNam(String actStNam) {
        this.actStNam = actStNam;
    }

    public String getActStNum() {
        return actStNum;
    }

    public void setActStNum(String actStNum) {
        this.actStNum = actStNum;
    }

    public String getActAdmin() {
        return actAdmin;
    }

    public void setActAdmin(String actAdmin) {
        this.actAdmin = actAdmin;
    }

    public String getActPostal() {
        return actPostal;
    }

    public void setActPostal(String actPostal) {
        this.actPostal = actPostal;
    }

    public String getOpen24() {
        return open24;
    }

    public void setOpen24(String open24) {
        this.open24 = open24;
    }

    public String getDiesel() {
        return diesel;
    }

    public void setDiesel(String diesel) {
        this.diesel = diesel;
    }

    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }
}
