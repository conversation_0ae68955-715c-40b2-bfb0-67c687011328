package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-17
 */
@ApiModel(value="HerePhaZlevels对象", description="")
public class HerePhaZlevels extends Model<HerePhaZlevels> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "gid", type = IdType.AUTO)
    private String gid;

    private String linkId;

    private String pointNum;

    private Integer nodeId;

    private String zLevel;

    private String intrsect;

    private String dotShape;

    private String aligned;

    private String geom;

    public String getGeomwkt() {
        return geomwkt;
    }

    public void setGeomwkt(String geomwkt) {
        this.geomwkt = geomwkt;
    }

    @TableField("st_astext(\"geom\")")
    private String geomwkt;

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }
    public String getLinkId() {
        return linkId;
    }

    public void setLinkId(String linkId) {
        this.linkId = linkId;
    }
    public String getPointNum() {
        return pointNum;
    }

    public void setPointNum(String pointNum) {
        this.pointNum = pointNum;
    }
    public Integer getNodeId() {
        return nodeId;
    }

    public void setNodeId(Integer nodeId) {
        this.nodeId = nodeId;
    }
    public String getzLevel() {
        return zLevel;
    }

    public void setzLevel(String zLevel) {
        this.zLevel = zLevel;
    }
    public String getIntrsect() {
        return intrsect;
    }

    public void setIntrsect(String intrsect) {
        this.intrsect = intrsect;
    }
    public String getDotShape() {
        return dotShape;
    }

    public void setDotShape(String dotShape) {
        this.dotShape = dotShape;
    }
    public String getAligned() {
        return aligned;
    }

    public void setAligned(String aligned) {
        this.aligned = aligned;
    }
    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }

    @Override
    protected Serializable pkVal() {
        return this.gid;
    }

    @Override
    public String toString() {
        return "HerePhaZlevels{" +
            "gid=" + gid +
            ", linkId=" + linkId +
            ", pointNum=" + pointNum +
            ", nodeId=" + nodeId +
            ", zLevel=" + zLevel +
            ", intrsect=" + intrsect +
            ", dotShape=" + dotShape +
            ", aligned=" + aligned +
            ", geom=" + geom +
        "}";
    }
}
