package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@ApiModel(value="ZlevelE对象", description="")
public class ZlevelE extends Model<ZlevelE> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "zlevel_id", type = IdType.AUTO)
    private String zlevelId;

    private String hllLinkid;

    private Integer pointNum;

    private String hllNodeid;

    private String geometry;

    private String z;

    private String linktype;

    private String intrsect;

    private Integer dotShape;

    private String aligned;

    private String levelId;

    private String tileId;

    private Integer tileType;

    private String datasource;

    private LocalDateTime upDate;

    private Integer status;

    private Integer area;

    public Integer getArea() {
        return area;
    }

    public void setArea(Integer area) {
        this.area = area;
    }

    public String getZlevelId() {
        return zlevelId;
    }

    public void setZlevelId(String zlevelId) {
        this.zlevelId = zlevelId;
    }
    public String getHllLinkid() {
        return hllLinkid;
    }

    public void setHllLinkid(String hllLinkid) {
        this.hllLinkid = hllLinkid;
    }
    public Integer getPointNum() {
        return pointNum;
    }

    public void setPointNum(Integer pointNum) {
        this.pointNum = pointNum;
    }
    public String getHllNodeid() {
        return hllNodeid;
    }

    public void setHllNodeid(String hllNodeid) {
        this.hllNodeid = hllNodeid;
    }
    public String getGeometry() {
        return geometry;
    }

    public void setGeometry(String geometry) {
        this.geometry = geometry;
    }
    public String getZ() {
        return z;
    }

    public void setZ(String z) {
        this.z = z;
    }
    public String getLinktype() {
        return linktype;
    }

    public void setLinktype(String linktype) {
        this.linktype = linktype;
    }
    public String getIntrsect() {
        return intrsect;
    }

    public void setIntrsect(String intrsect) {
        this.intrsect = intrsect;
    }
    public Integer getDotShape() {
        return dotShape;
    }

    public void setDotShape(Integer dotShape) {
        this.dotShape = dotShape;
    }
    public String getAligned() {
        return aligned;
    }

    public void setAligned(String aligned) {
        this.aligned = aligned;
    }
    public String getLevelId() {
        return levelId;
    }

    public void setLevelId(String levelId) {
        this.levelId = levelId;
    }
    public String getTileId() {
        return tileId;
    }

    public void setTileId(String tileId) {
        this.tileId = tileId;
    }
    public Integer getTileType() {
        return tileType;
    }

    public void setTileType(Integer tileType) {
        this.tileType = tileType;
    }
    public String getDatasource() {
        return datasource;
    }

    public void setDatasource(String datasource) {
        this.datasource = datasource;
    }
    public LocalDateTime getUpDate() {
        return upDate;
    }

    public void setUpDate(LocalDateTime upDate) {
        this.upDate = upDate;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    protected Serializable pkVal() {
        return this.zlevelId;
    }

    @Override
    public String toString() {
        return "Zlevel{" +
            "zlevelId=" + zlevelId +
            ", hllLinkid=" + hllLinkid +
            ", pointNum=" + pointNum +
            ", hllNodeid=" + hllNodeid +
            ", geometry=" + geometry +
            ", z=" + z +
            ", linktype=" + linktype +
            ", intrsect=" + intrsect +
            ", dotShape=" + dotShape +
            ", aligned=" + aligned +
            ", levelId=" + levelId +
            ", tileId=" + tileId +
            ", tileType=" + tileType +
            ", datasource=" + datasource +
            ", upDate=" + upDate +
            ", status=" + status +
        "}";
    }
}
