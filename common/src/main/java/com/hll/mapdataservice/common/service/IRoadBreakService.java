package com.hll.mapdataservice.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hll.mapdataservice.common.entity.Road;
import com.hll.mapdataservice.common.entity.RoadBreak;
import com.vividsolutions.jts.io.ParseException;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * @Author: ares.chen
 * @Since: 2021/11/26
 */
public interface IRoadBreakService extends IService<RoadBreak> {

    void batchInsertRoadBreak(List<RoadBreak> roadBreaks, CountDownLatch countDownLatch,String country,String area);

    void roadBreak(List<Road> roads, CountDownLatch downLatch, Long version,String country,String area) throws ParseException;
}
