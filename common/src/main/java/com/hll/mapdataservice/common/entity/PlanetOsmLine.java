package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Map;

import com.hll.mapdataservice.common.utils.HstoreTypeHandler;
import com.hll.mapdataservice.common.utils.MyGeometryTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
@ApiModel(value="PlanetOsmLine对象", description="")
@TableName(autoResultMap = true)
public class PlanetOsmLine extends Model<PlanetOsmLine> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "osm_id", type = IdType.INPUT)
    private Long osmId;

    private String access;

    @TableField("\"addr:housename\"")
    private String addrHousename;

    @TableField("\"addr:housenumber\"")
    private String addrHousenumber;

    @TableField("\"addr:interpolation\"")
    private String addrInterpolation;

    private String adminLevel;

    private String aerialway;

    private String aeroway;

    private String amenity;

    private String area;

    private String barrier;

    private String bicycle;

    private String brand;

    private String bridge;

    private String boundary;

    private String building;

    @TableField(exist = false)
    private String capital;

    private String club;

    private String construction;

    private String covered;

    private String craft;

    private String culvert;

    private String cutting;

    private String denomination;

    private String disused;

    @TableField(exist = false)
    private String ele;

    private String embankment;

    @TableField("\"emergency:amenity\"")
    private String emergencyAmenity;

    private String emergency;

    private String foot;

    @TableField("\"generator:source\"")
    private String generatorSource;

    private String geological;

    private String harbour;

    private String healthcare;

    private String highway;

    private String historic;

    private String horse;

    private String intermittent;

    private String junction;

    private String landuse;

    private String layer;

    private String leisure;

    private String lock;

    private String manMade;

    private String military;

    private String motorcar;

    private String mountainPass;

    private String name;

    @TableField("\"natural\"")
    private String natural;

    private String office;

    private String oneway;

    private String operator;

    private String place;

    private String population;

    private String power;

    private String powerSource;

    private String publicTransport;

    private String railway;

    private String ref;

    private String religion;

    private String route;

    private String service;

    private String shop;

    private String sport;

    private String surface;

    private String toll;

    private String tourism;

    @TableField("\"tower:type\"")
    private String towerType;

    public String getTracktype() {
        return tracktype;
    }

    public void setTracktype(String tracktype) {
        this.tracktype = tracktype;
    }

    private String tracktype;

    private String transport;

    private String tunnel;

    private String utility;

    private String water;

    private String waterway;

    private String wetland;

    private String width;

    private String wood;

    private Integer zOrder;

    public String getWayArea() {
        return wayArea;
    }

    public void setWayArea(String wayArea) {
        this.wayArea = wayArea;
    }

    private String wayArea;

    @TableField(value = "tags",typeHandler = HstoreTypeHandler.class)
    private Map<String, String> tags;

    @TableField(typeHandler = MyGeometryTypeHandler.class)
    private String way;

    public Long getOsmId() {
        return osmId;
    }

    public void setOsmId(Long osmId) {
        this.osmId = osmId;
    }
    public String getAccess() {
        return access;
    }

    public void setAccess(String access) {
        this.access = access;
    }
    public String getAddrHousename() {
        return addrHousename;
    }

    public void setAddrHousename(String housename) {
        this.addrHousename = housename;
    }
    public String getAddrHousenumber() {
        return addrHousenumber;
    }

    public void setAddrHousenumber(String housenumber) {
        this.addrHousenumber = housenumber;
    }
    public String getAddrInterpolation() {
        return addrInterpolation;
    }

    public void setAddrInterpolation(String interpolation) {
        this.addrInterpolation = interpolation;
    }
    public String getAdminLevel() {
        return adminLevel;
    }

    public void setAdminLevel(String adminLevel) {
        this.adminLevel = adminLevel;
    }
    public String getAerialway() {
        return aerialway;
    }

    public void setAerialway(String aerialway) {
        this.aerialway = aerialway;
    }
    public String getAeroway() {
        return aeroway;
    }

    public void setAeroway(String aeroway) {
        this.aeroway = aeroway;
    }
    public String getAmenity() {
        return amenity;
    }

    public void setAmenity(String amenity) {
        this.amenity = amenity;
    }
    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }
    public String getBarrier() {
        return barrier;
    }

    public void setBarrier(String barrier) {
        this.barrier = barrier;
    }
    public String getBicycle() {
        return bicycle;
    }

    public void setBicycle(String bicycle) {
        this.bicycle = bicycle;
    }
    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }
    public String getBridge() {
        return bridge;
    }

    public void setBridge(String bridge) {
        this.bridge = bridge;
    }
    public String getBoundary() {
        return boundary;
    }

    public void setBoundary(String boundary) {
        this.boundary = boundary;
    }
    public String getBuilding() {
        return building;
    }

    public void setBuilding(String building) {
        this.building = building;
    }
    public String getCapital() {
        return capital;
    }

    public void setCapital(String capital) {
        this.capital = capital;
    }
    public String getConstruction() {
        return construction;
    }

    public void setClub(String club) {
        this.club = club;
    }
    public String getClub() {
        return club;
    }

    public void setConstruction(String construction) {
        this.construction = construction;
    }
    public String getCovered() {
        return covered;
    }

    public void setCovered(String covered) {
        this.covered = covered;
    }
    public String getCraft() {
        return craft;
    }

    public void setCraft(String craft) {
        this.craft = craft;
    }
    public String getCulvert() {
        return culvert;
    }

    public void setCulvert(String culvert) {
        this.culvert = culvert;
    }
    public String getCutting() {
        return cutting;
    }

    public void setCutting(String cutting) {
        this.cutting = cutting;
    }
    public String getDenomination() {
        return denomination;
    }

    public void setDenomination(String denomination) {
        this.denomination = denomination;
    }
    public String getDisused() {
        return disused;
    }

    public void setDisused(String disused) {
        this.disused = disused;
    }
    public String getEle() {
        return ele;
    }

    public void setEle(String ele) {
        this.ele = ele;
    }
    public String getEmbankment() {
        return embankment;
    }

    public void setEmbankment(String embankment) {
        this.embankment = embankment;
    }
    public String getEmergencyAmenity() {
        return emergencyAmenity;
    }

    public void setEmergencyAmenity(String eme_amenity) {
        this.emergencyAmenity = eme_amenity;
    }
    public String getEmergency() {
        return emergency;
    }

    public void setEmergency(String emergency) {
        this.emergency = emergency;
    }
    public String getFoot() {
        return foot;
    }

    public void setFoot(String foot) {
        this.foot = foot;
    }
    public String getGeneratorSource() {
        return generatorSource;
    }

    public void setGeneratorSource(String gen_source) {
        this.generatorSource = gen_source;
    }
    public String getGeological() {
        return geological;
    }

    public void setGeological(String geological) {
        this.geological = geological;
    }
    public String getHarbour() {
        return harbour;
    }

    public void setHarbour(String harbour) {
        this.harbour = harbour;
    }
    public String getHealthcare() {
        return healthcare;
    }

    public void setHealthcare(String healthcare) {
        this.healthcare = healthcare;
    }
    public String getHighway() {
        return highway;
    }

    public void setHighway(String highway) {
        this.highway = highway;
    }
    public String getHistoric() {
        return historic;
    }

    public void setHistoric(String historic) {
        this.historic = historic;
    }
    public String getHorse() {
        return horse;
    }

    public void setHorse(String horse) {
        this.horse = horse;
    }
    public String getIntermittent() {
        return intermittent;
    }

    public void setIntermittent(String intermittent) {
        this.intermittent = intermittent;
    }
    public String getJunction() {
        return junction;
    }

    public void setJunction(String junction) {
        this.junction = junction;
    }
    public String getLanduse() {
        return landuse;
    }

    public void setLanduse(String landuse) {
        this.landuse = landuse;
    }
    public String getLayer() {
        return layer;
    }

    public void setLayer(String layer) {
        this.layer = layer;
    }
    public String getLeisure() {
        return leisure;
    }

    public void setLeisure(String leisure) {
        this.leisure = leisure;
    }
    public String getLock() {
        return lock;
    }

    public void setLock(String lock) {
        this.lock = lock;
    }
    public String getManMade() {
        return manMade;
    }

    public void setManMade(String manMade) {
        this.manMade = manMade;
    }
    public String getMilitary() {
        return military;
    }

    public void setMilitary(String military) {
        this.military = military;
    }
    public String getMotorcar() {
        return motorcar;
    }

    public void setMotorcar(String motorcar) {
        this.motorcar = motorcar;
    }
    public String getMountainPass() {
        return mountainPass;
    }

    public void setMountainPass(String mountainPass) {
        this.mountainPass = mountainPass;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public String getNatural() {
        return natural;
    }

    public void setNatural(String natural) {
        this.natural = natural;
    }
    public String getOffice() {
        return office;
    }

    public void setOffice(String office) {
        this.office = office;
    }
    public String getOneway() {
        return oneway;
    }

    public void setOneway(String oneway) {
        this.oneway = oneway;
    }
    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
    public String getPlace() {
        return place;
    }

    public void setPlace(String place) {
        this.place = place;
    }
    public String getPopulation() {
        return population;
    }

    public void setPopulation(String population) {
        this.population = population;
    }
    public String getPower() {
        return power;
    }

    public void setPower(String power) {
        this.power = power;
    }
    public String getPowerSource() {
        return powerSource;
    }

    public void setPowerSource(String powerSource) {
        this.powerSource = powerSource;
    }
    public String getPublicTransport() {
        return publicTransport;
    }

    public void setPublicTransport(String publicTransport) {
        this.publicTransport = publicTransport;
    }
    public String getRailway() {
        return railway;
    }

    public void setRailway(String railway) {
        this.railway = railway;
    }
    public String getRef() {
        return ref;
    }

    public void setRef(String ref) {
        this.ref = ref;
    }
    public String getReligion() {
        return religion;
    }

    public void setReligion(String religion) {
        this.religion = religion;
    }
    public String getRoute() {
        return route;
    }

    public void setRoute(String route) {
        this.route = route;
    }
    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }
    public String getShop() {
        return shop;
    }

    public void setShop(String shop) {
        this.shop = shop;
    }
    public String getSport() {
        return sport;
    }

    public void setSport(String sport) {
        this.sport = sport;
    }
    public String getSurface() {
        return surface;
    }

    public void setSurface(String surface) {
        this.surface = surface;
    }
    public String getToll() {
        return toll;
    }

    public void setToll(String toll) {
        this.toll = toll;
    }
    public String getTourism() {
        return tourism;
    }

    public void setTourism(String tourism) {
        this.tourism = tourism;
    }
    public String getTowerType() {
        return towerType;
    }

    public void setTowerType(String tow_type) {
        this.towerType = tow_type;
    }
    public String getTransport() {
        return transport;
    }

    public void setTransport(String transport) {
        this.transport = transport;
    }
    public String getTunnel() {
        return tunnel;
    }

    public void setTunnel(String tunnel) {
        this.tunnel = tunnel;
    }
    public String getUtility() {
        return utility;
    }

    public void setUtility(String utility) {
        this.utility = utility;
    }
    public String getWater() {
        return water;
    }

    public void setWater(String water) {
        this.water = water;
    }
    public String getWaterway() {
        return waterway;
    }

    public void setWaterway(String waterway) {
        this.waterway = waterway;
    }
    public String getWetland() {
        return wetland;
    }

    public void setWetland(String wetland) {
        this.wetland = wetland;
    }
    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width;
    }
    public String getWood() {
        return wood;
    }

    public void setWood(String wood) {
        this.wood = wood;
    }
    public Integer getzOrder() {
        return zOrder;
    }

    public void setzOrder(Integer zOrder) {
        this.zOrder = zOrder;
    }
    public Map<String, String> getTags() {
        return tags;
    }

    public void setTags(Map<String, String> tags) {
        this.tags = tags;
    }
    public String getWay() {
        return way;
    }

    public void setWay(String way) {
        this.way = way;
    }

    @Override
    protected Serializable pkVal() {
        return this.osmId;
    }

    @Override
    public String toString() {
        return "PlanetOsmLine{" +
                "osmId=" + osmId +
                ", access=" + access +
                ", addr:housename=" + addrHousename +
                ", addr:housenumber=" + addrHousenumber +
                ", addr:interpolation=" + addrInterpolation +
                ", adminLevel=" + adminLevel +
                ", aerialway=" + aerialway +
                ", aeroway=" + aeroway +
                ", amenity=" + amenity +
                ", area=" + area +
                ", barrier=" + barrier +
                ", bicycle=" + bicycle +
                ", brand=" + brand +
                ", bridge=" + bridge +
                ", boundary=" + boundary +
                ", building=" + building +
                ", capital=" + capital +
                ", construction=" + construction +
                ", covered=" + covered +
                ", craft=" + craft +
                ", culvert=" + culvert +
                ", cutting=" + cutting +
                ", denomination=" + denomination +
                ", disused=" + disused +
                ", ele=" + ele +
                ", embankment=" + embankment +
                ", emergency:amenity=" + emergencyAmenity +
                ", emergency=" + emergency +
                ", foot=" + foot +
                ", generator:source=" + generatorSource +
                ", geological=" + geological +
                ", harbour=" + harbour +
                ", healthcare=" + healthcare +
                ", highway=" + highway +
                ", historic=" + historic +
                ", horse=" + horse +
                ", intermittent=" + intermittent +
                ", junction=" + junction +
                ", landuse=" + landuse +
                ", layer=" + layer +
                ", leisure=" + leisure +
                ", lock=" + lock +
                ", manMade=" + manMade +
                ", military=" + military +
                ", motorcar=" + motorcar +
                ", mountainPass=" + mountainPass +
                ", name=" + name +
                ", natural=" + natural +
                ", office=" + office +
                ", oneway=" + oneway +
                ", operator=" + operator +
                ", place=" + place +
                ", population=" + population +
                ", power=" + power +
                ", powerSource=" + powerSource +
                ", publicTransport=" + publicTransport +
                ", railway=" + railway +
                ", ref=" + ref +
                ", religion=" + religion +
                ", route=" + route +
                ", service=" + service +
                ", shop=" + shop +
                ", sport=" + sport +
                ", surface=" + surface +
                ", toll=" + toll +
                ", tourism=" + tourism +
                ", tower:type=" + towerType +
                ", transport=" + transport +
                ", tunnel=" + tunnel +
                ", utility=" + utility +
                ", water=" + water +
                ", waterway=" + waterway +
                ", wetland=" + wetland +
                ", width=" + width +
                ", wood=" + wood +
                ", zOrder=" + zOrder +
                ", tags=" + tags +
                ", way=" + way +
                "}";
    }
}
