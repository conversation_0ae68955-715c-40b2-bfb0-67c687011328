<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.FoursquarePhaPoiMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.FoursquarePhaPoi">
        <id column="fsq_id" property="fsqId" />
        <result column="name" property="name" />
        <result column="name_translated" property="nameTranslated" />
        <result column="latitude" property="latitude" />
        <result column="longitude" property="longitude" />
        <result column="geocodes" property="geocodes" />
        <result column="address" property="address" />
        <result column="address_extended" property="addressExtended" />
        <result column="locality" property="locality" />
        <result column="dma" property="dma" />
        <result column="region" property="region" />
        <result column="postcode" property="postcode" />
        <result column="country" property="country" />
        <result column="admin_region" property="adminRegion" />
        <result column="post_town" property="postTown" />
        <result column="neighborhood" property="neighborhood" />
        <result column="po_box" property="poBox" />
        <result column="date_refreshed" property="dateRefreshed" />
        <result column="category_ids" property="categoryIds" />
        <result column="category_labels" property="categoryLabels" />
        <result column="fsq_chain_id" property="fsqChainId" />
        <result column="fsq_chain_name" property="fsqChainName" />
        <result column="parent_id" property="parentId" />
        <result column="subvenue_count" property="subvenueCount" />
        <result column="hours" property="hours" />
        <result column="hours_popular" property="hoursPopular" />
        <result column="hours_display" property="hoursDisplay" />
        <result column="tel" property="tel" />
        <result column="website" property="website" />
        <result column="fax" property="fax" />
        <result column="email" property="email" />
        <result column="facebook_id" property="facebookId" />
        <result column="instagram" property="instagram" />
        <result column="twitter" property="twitter" />
        <result column="description" property="description" />
        <result column="rating" property="rating" />
        <result column="price" property="price" />
        <result column="total_photos" property="totalPhotos" />
        <result column="photos" property="photos" />
        <result column="total_tips" property="totalTips" />
        <result column="tips" property="tips" />
        <result column="tastes" property="tastes" />
        <result column="popularity" property="popularity" />
        <result column="venue_reality_bucket" property="venueRealityBucket" />
        <result column="existence" property="existence" />
        <result column="provenance_rating" property="provenanceRating" />
        <result column="date_closed" property="dateClosed" />
        <result column="closed_bucket" property="closedBucket" />
        <result column="atm" property="atm" />
        <result column="barservice" property="barservice" />
        <result column="beer" property="beer" />
        <result column="businessmeeting" property="businessmeeting" />
        <result column="byo" property="byo" />
        <result column="clean" property="clean" />
        <result column="coatcheck" property="coatcheck" />
        <result column="cocktails" property="cocktails" />
        <result column="crowded" property="crowded" />
        <result column="datespopular" property="datespopular" />
        <result column="delivery" property="delivery" />
        <result column="dressy" property="dressy" />
        <result column="drivethrough" property="drivethrough" />
        <result column="essentialreservations" property="essentialreservations" />
        <result column="familiespopular" property="familiespopular" />
        <result column="fullbar" property="fullbar" />
        <result column="glutenfreediet" property="glutenfreediet" />
        <result column="goodfordogs" property="goodfordogs" />
        <result column="groupsonlyreservations" property="groupsonlyreservations" />
        <result column="groupspopular" property="groupspopular" />
        <result column="hasmusic" property="hasmusic" />
        <result column="hasparking" property="hasparking" />
        <result column="healthydiet" property="healthydiet" />
        <result column="jukeboxmusic" property="jukeboxmusic" />
        <result column="latenight" property="latenight" />
        <result column="livemusic" property="livemusic" />
        <result column="noisy" property="noisy" />
        <result column="onlinereservations" property="onlinereservations" />
        <result column="outdoorseating" property="outdoorseating" />
        <result column="privatelot" property="privatelot" />
        <result column="privateroom" property="privateroom" />
        <result column="publiclot" property="publiclot" />
        <result column="quickbite" property="quickbite" />
        <result column="reservations" property="reservations" />
        <result column="restroom" property="restroom" />
        <result column="romantic" property="romantic" />
        <result column="servesbarsnacks" property="servesbarsnacks" />
        <result column="servesbreakfast" property="servesbreakfast" />
        <result column="servesbrunch" property="servesbrunch" />
        <result column="servesdessert" property="servesdessert" />
        <result column="servesdinner" property="servesdinner" />
        <result column="serveshappyhour" property="serveshappyhour" />
        <result column="serveslunch" property="serveslunch" />
        <result column="servestastingmenu" property="servestastingmenu" />
        <result column="servicequality" property="servicequality" />
        <result column="singlespopular" property="singlespopular" />
        <result column="sitdowndining" property="sitdowndining" />
        <result column="smoking" property="smoking" />
        <result column="specialoccasion" property="specialoccasion" />
        <result column="streetparking" property="streetparking" />
        <result column="takeout" property="takeout" />
        <result column="takesamex" property="takesamex" />
        <result column="takescreditcards" property="takescreditcards" />
        <result column="takesdinersclub" property="takesdinersclub" />
        <result column="takesdiscover" property="takesdiscover" />
        <result column="takesmastercard" property="takesmastercard" />
        <result column="takesnfc" property="takesnfc" />
        <result column="takesunionpay" property="takesunionpay" />
        <result column="takesvisa" property="takesvisa" />
        <result column="trendy" property="trendy" />
        <result column="tvs" property="tvs" />
        <result column="valetparking" property="valetparking" />
        <result column="valueformoney" property="valueformoney" />
        <result column="vegandiet" property="vegandiet" />
        <result column="vegetariandiet" property="vegetariandiet" />
        <result column="wheelchairaccessible" property="wheelchairaccessible" />
        <result column="wifi" property="wifi" />
        <result column="wine" property="wine" />
    </resultMap>

</mapper>
