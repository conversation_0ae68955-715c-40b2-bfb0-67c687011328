package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.Map;

/**
  *<p>
  *
  *</p>
  *
  * @Author: ares.chen
  * @Since: 2021/8/16
  */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="link对象")
@Data
@ToString
public class Link extends Model<Link> {
    @ApiModelProperty(value="")
    private String id;

    @ApiModelProperty(value="")
    @TableId
    private String hllLinkid;

    @ApiModelProperty(value="")
    private String hllSNid;

    @ApiModelProperty(value="")
    private String hllENid;

    @ApiModelProperty(value="")
    private String kind;

    @ApiModelProperty(value="")
    private String formway;

    @ApiModelProperty(value="")
    private String direction;

    @ApiModelProperty(value="")
    private String constSt;

    @ApiModelProperty(value="")
    private String toll;

    @ApiModelProperty(value="")
    private String adopt;

    @ApiModelProperty(value="")
    private String md;

    @ApiModelProperty(value="")
    private String detailcity;

    @ApiModelProperty(value="")
    private String special;

    @ApiModelProperty(value="")
    private String funcclass;

    @ApiModelProperty(value="")
    private String uflag;

    @ApiModelProperty(value="")
    private String roadCond;

    @ApiModelProperty(value="")
    private Integer lanenumsum;

    @ApiModelProperty(value="")
    private Integer lanenums2e;

    @ApiModelProperty(value="")
    private Integer lanenume2s;

    @ApiModelProperty(value="")
    private String lanenumc;

    @ApiModelProperty(value="")
    private String width;

    @ApiModelProperty(value="")
    private String elevated;

    @ApiModelProperty(value="")
    private String admincodel;

    @ApiModelProperty(value="")
    private String admincoder;

    @ApiModelProperty(value="")
    private String geometry;

    @ApiModelProperty(value="")
    private Double len;

    @ApiModelProperty(value="")
    private String spdlmts2e;

    @ApiModelProperty(value="")
    private String spdlmte2s;

    @ApiModelProperty(value="")
    private String speedclass;

    @ApiModelProperty(value="")
    private String arVeh;

    @ApiModelProperty(value="")
    private String dcType;

    @ApiModelProperty(value="")
    private String verifyflag;

    @ApiModelProperty(value="")
    private String preLaunch;

    @ApiModelProperty(value="")
    private String nameChO;

    @ApiModelProperty(value="")
    private String nameChA;

    @ApiModelProperty(value="")
    private String nameChF;

    @ApiModelProperty(value="")
    private String namePhO;

    @ApiModelProperty(value="")
    private String namePhA;

    @ApiModelProperty(value="")
    private String namePhF;

    @ApiModelProperty(value="")
    private String nameEnO;

    @ApiModelProperty(value="")
    private String nameEnA;

    @ApiModelProperty(value="")
    private String nameEnF;

    @ApiModelProperty(value="")
    private String namePo;

    @ApiModelProperty(value="")
    private String nameCht;

    @ApiModelProperty(value="")
    private String codeType;

    @ApiModelProperty(value="")
    private String nameType;

    @ApiModelProperty(value="")
    private String srcFlag;

    @ApiModelProperty(value="")
    private String meshId;

    @ApiModelProperty(value="")
    private String memo;

    @ApiModelProperty(value="")
    private String cp;

    @ApiModelProperty(value="")
    private String datasource;

    @ApiModelProperty(value="")
    private Date upDate;

    @ApiModelProperty(value="")
    private Integer status;

    @ApiModelProperty(value="")
    private String geomwkt;

    @ApiModelProperty(value="")
    private String divider;

    @ApiModelProperty(value="")
    private String dividerLeg;

    @ApiModelProperty(value="")
    private String pubAccess;

    @ApiModelProperty(value="")
    private String tAdmin;

    @ApiModelProperty(value="")
    private String timeZone;

    @ApiModelProperty(value="")
    private Integer area;


    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> name;
    private String nmChoLangcd;
    private String nmChaLangcd;
    private String nmChfLangcd;
}