package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.sql.Timestamp;
import java.time.LocalDateTime;

public class PoiCoverageMpRes extends Model<PoiCoverageMpRes> implements Serializable {

    private String id;

    private String type;

    private String market;

    private String name;

    private Double longitude;

    private Double latitude;

    private String herePoiName;

    private String herePoiNameList;

    private Double herePoiNameSimilarity=0.0;

    private Integer herePoiNameMatch=0;

    private String googlePoiName;

    private String googlePoiNameList;

    private Double googlePoiNameSimilarity=0.0;

    private Integer googlePoiNameMatch=0;

    private String radius;

    private Double similarity;

    private LocalDateTime upTime;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public String getHerePoiName() {
        return herePoiName;
    }

    public void setHerePoiName(String herePoiName) {
        this.herePoiName = herePoiName;
    }

    public String getHerePoiNameList() {
        return herePoiNameList;
    }

    public void setHerePoiNameList(String herePoiNameList) {
        this.herePoiNameList = herePoiNameList;
    }

    public Double getHerePoiNameSimilarity() {
        return herePoiNameSimilarity;
    }

    public void setHerePoiNameSimilarity(Double herePoiNameSimilarity) {
        this.herePoiNameSimilarity = herePoiNameSimilarity;
    }

    public Integer getHerePoiNameMatch() {
        return herePoiNameMatch;
    }

    public void setHerePoiNameMatch(Integer herePoiNameMatch) {
        this.herePoiNameMatch = herePoiNameMatch;
    }

    public String getGooglePoiName() {
        return googlePoiName;
    }

    public void setGooglePoiName(String googlePoiName) {
        this.googlePoiName = googlePoiName;
    }

    public String getGooglePoiNameList() {
        return googlePoiNameList;
    }

    public void setGooglePoiNameList(String googlePoiNameList) {
        this.googlePoiNameList = googlePoiNameList;
    }

    public Double getGooglePoiNameSimilarity() {
        return googlePoiNameSimilarity;
    }

    public void setGooglePoiNameSimilarity(Double googlePoiNameSimilarity) {
        this.googlePoiNameSimilarity = googlePoiNameSimilarity;
    }

    public Integer getGooglePoiNameMatch() {
        return googlePoiNameMatch;
    }

    public void setGooglePoiNameMatch(Integer googlePoiNameMatch) {
        this.googlePoiNameMatch = googlePoiNameMatch;
    }

    public String getRadius() {
        return radius;
    }

    public void setRadius(String radius) {
        this.radius = radius;
    }

    public Double getSimilarity() {
        return similarity;
    }

    public void setSimilarity(Double similarity) {
        this.similarity = similarity;
    }

    public LocalDateTime getUpTime() {
        return upTime;
    }

    public void setUpTime(LocalDateTime upTime) {
        this.upTime = upTime;
    }
}
