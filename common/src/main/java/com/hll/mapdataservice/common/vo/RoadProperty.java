package com.hll.mapdataservice.common.vo;

import java.io.Serializable;
import java.util.List;

/*
 * <AUTHOR>
 * @date  3/3/21 10:36 AM
 * @Email:<EMAIL>
 * @remark:
 */
public class RoadProperty implements Serializable {
    /**
     * 线link
     */
    private String featId;

    /**
     * 显示等级
     */
    private String displayClass;

    /**
     * 导航等级
     */
    private String routingClass;

    /**
     * 形态2
     */
    private String formOfWay;

    private String noThroughTraffic;

    /**
     * 道路方向
     */
    private String simpleTrafficDirection;

    /**
     * 车道数
     */
    private String numOfLanes;
    /**
     * 正向最大限速
     */
    private String speedmaxPos;
    /**
     * 反向最大限速
     */
    private String speedmaxNeg;

    /**
     * 形态
     */
    private String partOfStructure;

    /**
     * 高速
     */
    private String freeway;

    /**
     * 匝道
     */
    private String ramp;
    /**
     * 所有者
     */
    private String ownership;
    /**
     * 道路名
     */
    private String name;
    /**
     * 铺设状态
     */
    private String roadCondition;

    /**
     * 里程
     */
    private Double centimeters;

    public Double getCentimeters() {
        return centimeters;
    }

    public void setCentimeters(Double centimeters) {
        this.centimeters = centimeters;
    }



    private String geom;

    private String jszt;

    private String ccxz;
    private String zdzzl;
    private String zdzz;
    private String zdyxcd;
    private String zdyxkd;
    private String zdyxgd;

    private String linkIdHere;

    private String funcClassHere;

    private String physLanesHere;

    private String travelDirectionHere;

    private String rampHere;

    private String frSpdLimHere;

    private String toSpdLimHere;

    private String stNameHere;

    private String pavedHere;

    public List<ManeuverVo> getHereManeuverVoList() {
        return hereManeuverVoList;
    }

    public void setHereManeuverVoList(List<ManeuverVo> hereManeuverVoList) {
        this.hereManeuverVoList = hereManeuverVoList;
    }

    private List<ManeuverVo> hereManeuverVoList;

    public String getJszt() {
        return jszt;
    }

    public void setJszt(String jszt) {
        this.jszt = jszt;
    }

    public String getCcxz() {
        return ccxz;
    }

    public void setCcxz(String ccxz) {
        this.ccxz = ccxz;
    }

    public String getZdzzl() {
        return zdzzl;
    }

    public void setZdzzl(String zdzzl) {
        this.zdzzl = zdzzl;
    }

    public String getZdzz() {
        return zdzz;
    }

    public void setZdzz(String zdzz) {
        this.zdzz = zdzz;
    }

    public String getZdyxcd() {
        return zdyxcd;
    }

    public void setZdyxcd(String zdyxcd) {
        this.zdyxcd = zdyxcd;
    }

    public String getZdyxkd() {
        return zdyxkd;
    }

    public void setZdyxkd(String zdyxkd) {
        this.zdyxkd = zdyxkd;
    }

    public String getZdyxgd() {
        return zdyxgd;
    }

    public void setZdyxgd(String zdyxgd) {
        this.zdyxgd = zdyxgd;
    }

    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }

    public String getFeatId() {
        return featId;
    }

    public void setFeatId(String featId) {
        this.featId = featId;
    }

    public String getDisplayClass() {
        return displayClass;
    }

    public void setDisplayClass(String displayClass) {
        this.displayClass = displayClass;
    }

    public String getRoutingClass() {
        return routingClass;
    }

    public void setRoutingClass(String routingClass) {
        this.routingClass = routingClass;
    }

    public String getFormOfWay() {
        return formOfWay;
    }

    public void setFormOfWay(String formOfWay) {
        this.formOfWay = formOfWay;
    }

    public String getNoThroughTraffic() {
        return noThroughTraffic;
    }

    public void setNoThroughTraffic(String noThroughTraffic) {
        this.noThroughTraffic = noThroughTraffic;
    }

    public String getSimpleTrafficDirection() {
        return simpleTrafficDirection;
    }

    public void setSimpleTrafficDirection(String simpleTrafficDirection) {
        this.simpleTrafficDirection = simpleTrafficDirection;
    }

    public String getNumOfLanes() {
        return numOfLanes;
    }

    public void setNumOfLanes(String numOfLanes) {
        this.numOfLanes = numOfLanes;
    }

    public String getSpeedmaxPos() {
        return speedmaxPos;
    }

    public void setSpeedmaxPos(String speedmaxPos) {
        this.speedmaxPos = speedmaxPos;
    }

    public String getSpeedmaxNeg() {
        return speedmaxNeg;
    }

    public void setSpeedmaxNeg(String speedmaxNeg) {
        this.speedmaxNeg = speedmaxNeg;
    }

    public String getPartOfStructure() {
        return partOfStructure;
    }

    public void setPartOfStructure(String partOfStructure) {
        this.partOfStructure = partOfStructure;
    }

    public String getFreeway() {
        return freeway;
    }

    public void setFreeway(String freeway) {
        this.freeway = freeway;
    }

    public String getRamp() {
        return ramp;
    }

    public void setRamp(String ramp) {
        this.ramp = ramp;
    }

    public String getOwnership() {
        return ownership;
    }

    public void setOwnership(String ownership) {
        this.ownership = ownership;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRoadCondition() {
        return roadCondition;
    }

    public void setRoadCondition(String roadCondition) {
        this.roadCondition = roadCondition;
    }
}
