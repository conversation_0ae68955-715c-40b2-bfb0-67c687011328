package com.hll.mapdataservice.common.entity;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-01
 */
//@DS("db4")
@ApiModel(value="Cdmsdtmod对象", description="")
public class Cdmsdtmod extends Model<Cdmsdtmod> {

    private static final long serialVersionUID = 1L;

    private Integer linkId;

    private Integer condId;

    private String dttmeType;

    private String exclDate;

    private String fromend;

    private String refDate;

    private String expDate;

    private String starttime;

    private String endtime;

    public Integer getLinkId() {
        return linkId;
    }

    public void setLinkId(Integer linkId) {
        this.linkId = linkId;
    }
    public Integer getCondId() {
        return condId;
    }

    public void setCondId(Integer condId) {
        this.condId = condId;
    }
    public String getDttmeType() {
        return dttmeType;
    }

    public void setDttmeType(String dttmeType) {
        this.dttmeType = dttmeType;
    }
    public String getExclDate() {
        return exclDate;
    }

    public void setExclDate(String exclDate) {
        this.exclDate = exclDate;
    }
    public String getFromend() {
        return fromend;
    }

    public void setFromend(String fromend) {
        this.fromend = fromend;
    }
    public String getRefDate() {
        return refDate;
    }

    public void setRefDate(String refDate) {
        this.refDate = refDate;
    }
    public String getExpDate() {
        return expDate;
    }

    public void setExpDate(String expDate) {
        this.expDate = expDate;
    }
    public String getStarttime() {
        return starttime;
    }

    public void setStarttime(String starttime) {
        this.starttime = starttime;
    }
    public String getEndtime() {
        return endtime;
    }

    public void setEndtime(String endtime) {
        this.endtime = endtime;
    }

    @Override
    protected Serializable pkVal() {
        return this.linkId;
    }

    @Override
    public String toString() {
        return "Cdmsdtmod{" +
            "linkId=" + linkId +
            ", condId=" + condId +
            ", dttmeType=" + dttmeType +
            ", exclDate=" + exclDate +
            ", fromend=" + fromend +
            ", refDate=" + refDate +
            ", expDate=" + expDate +
            ", starttime=" + starttime +
            ", endtime=" + endtime +
        "}";
    }
}
