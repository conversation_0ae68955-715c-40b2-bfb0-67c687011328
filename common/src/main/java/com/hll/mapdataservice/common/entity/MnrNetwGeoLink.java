package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-24
 */
@TableName(value = "mnr_netw_geo_link",schema = "core")
public class MnrNetwGeoLink extends Model<MnrNetwGeoLink> {

    private static final long serialVersionUID=1L;

    @TableId(value = "feat_id", type = IdType.AUTO)
    private String featId;

    private String ftRoadElement;

    private String ftFerryElement;

    private String ftAddressAreaBoundaryElement;

    private String ftRailwayElement;

    private String countryLeft;

    private String countryRight;

    private String centimeters;

    private String positionalAccuracy;

    private String adaCompliant;

    private String inCarImportance;

    private String geom;


    public String getFeatId() {
        return featId;
    }

    public void setFeatId(String featId) {
        this.featId = featId;
    }

    public String getFtRoadElement() {
        return ftRoadElement;
    }

    public void setFtRoadElement(String ftRoadElement) {
        this.ftRoadElement = ftRoadElement;
    }

    public String getFtFerryElement() {
        return ftFerryElement;
    }

    public void setFtFerryElement(String ftFerryElement) {
        this.ftFerryElement = ftFerryElement;
    }

    public String getFtAddressAreaBoundaryElement() {
        return ftAddressAreaBoundaryElement;
    }

    public void setFtAddressAreaBoundaryElement(String ftAddressAreaBoundaryElement) {
        this.ftAddressAreaBoundaryElement = ftAddressAreaBoundaryElement;
    }

    public String getFtRailwayElement() {
        return ftRailwayElement;
    }

    public void setFtRailwayElement(String ftRailwayElement) {
        this.ftRailwayElement = ftRailwayElement;
    }

    public String getCountryLeft() {
        return countryLeft;
    }

    public void setCountryLeft(String countryLeft) {
        this.countryLeft = countryLeft;
    }

    public String getCountryRight() {
        return countryRight;
    }

    public void setCountryRight(String countryRight) {
        this.countryRight = countryRight;
    }

    public String getCentimeters() {
        return centimeters;
    }

    public void setCentimeters(String centimeters) {
        this.centimeters = centimeters;
    }

    public String getPositionalAccuracy() {
        return positionalAccuracy;
    }

    public void setPositionalAccuracy(String positionalAccuracy) {
        this.positionalAccuracy = positionalAccuracy;
    }

    public String getAdaCompliant() {
        return adaCompliant;
    }

    public void setAdaCompliant(String adaCompliant) {
        this.adaCompliant = adaCompliant;
    }

    public String getInCarImportance() {
        return inCarImportance;
    }

    public void setInCarImportance(String inCarImportance) {
        this.inCarImportance = inCarImportance;
    }

    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }

    @Override
    protected Serializable pkVal() {
        return this.featId;
    }

    @Override
    public String toString() {
        return "MnrNetwGeoLink{" +
        "featId=" + featId +
        ", ftRoadElement=" + ftRoadElement +
        ", ftFerryElement=" + ftFerryElement +
        ", ftAddressAreaBoundaryElement=" + ftAddressAreaBoundaryElement +
        ", ftRailwayElement=" + ftRailwayElement +
        ", countryLeft=" + countryLeft +
        ", countryRight=" + countryRight +
        ", centimeters=" + centimeters +
        ", positionalAccuracy=" + positionalAccuracy +
        ", adaCompliant=" + adaCompliant +
        ", inCarImportance=" + inCarImportance +
        ", geom=" + geom +
        "}";
    }
}
