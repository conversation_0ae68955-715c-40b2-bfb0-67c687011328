package com.hll.mapdataservice.common.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

/*
 * <AUTHOR>
 * @date  3/24/21 5:43 PM
 * @Email:<EMAIL>
 */
public class hereManeuverVo {
    private String seqNumber;

    private String linkId;

    private String condId;

    private String manLinkid;

    private String geom;

    public String getSeqNumber() {
        return seqNumber;
    }

    public void setSeqNumber(String seqNumber) {
        this.seqNumber = seqNumber;
    }

    public String getLinkId() {
        return linkId;
    }

    public void setLinkId(String linkId) {
        this.linkId = linkId;
    }

    public String getCondId() {
        return condId;
    }

    public void setCondId(String condId) {
        this.condId = condId;
    }

    public String getManLinkid() {
        return manLinkid;
    }

    public void setManLinkid(String manLinkid) {
        this.manLinkid = manLinkid;
    }

    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private String name;
}
