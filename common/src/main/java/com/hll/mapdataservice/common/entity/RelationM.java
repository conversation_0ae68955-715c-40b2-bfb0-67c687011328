package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-28
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="Relation对象", description="")
@Data
@ToString
public class RelationM extends Model<RelationM> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "relationid", type = IdType.INPUT)
    private String relationid;

    private String inlinkId;

    private String nodeId;

    private String outlinkId;

    private String type;

    private String tollType;

    private String passNum;

    private String tollForm;

    private String cardType;

    private String veh;

    private String nameCh;

    private String nameFo;

    private String namePh;

    private String nameCht;

    private String gateType;

    private String gateFee;

    private String tlLocat;

    private String tlFlag;

    private String slopetype;

    private String slopeangle;

    private String memo;

    private String meshId;

    private String cp;

    private String datasource;

    private LocalDateTime upDate;

    private Integer status;

    private String tileId;

    private Integer tileType;

    private Integer area = 0;

    private String taskId;

    private Long olv = 0L;
}
