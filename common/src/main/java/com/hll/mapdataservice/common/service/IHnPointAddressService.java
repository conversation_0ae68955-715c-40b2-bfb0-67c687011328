package com.hll.mapdataservice.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hll.mapdataservice.common.entity.HnPointAddress;
import com.hll.mapdataservice.common.entity.Poi;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * @Classname IHnPointAddressService
 * @Description TODO
 * @Date 2021/12/9 4:16 下午
 * @Created by qunfu
 */
public interface IHnPointAddressService extends IService<HnPointAddress> {
    void handleId(String area, String country, CountDownLatch countDownLatch, List<HnPointAddress> hnPointAddressList);
}
