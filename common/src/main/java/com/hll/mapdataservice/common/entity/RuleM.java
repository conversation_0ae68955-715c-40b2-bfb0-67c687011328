package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-28
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="Rule对象", description="")
@Data
@ToString
public class RuleM extends Model<RuleM> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "rule_id", type = IdType.INPUT)
    private String ruleId;

    private String inlinkId;

    private String nodeId;

    private String outlinkId;

    private String pass;

    private String pass2;

    private Integer flag;

    private String vperiod;

    private String vehclType;

    private String vpdir;

    private String meshList;

    private String memo;

    private String cp;

    private String datasource;

    private LocalDateTime upDate;

    private Integer status;

    private String linkAngle;

    private String tileId;

    private Integer tileType;

    private Integer area = 0;

    private String taskId;

    private Long olv = 0L;
}
