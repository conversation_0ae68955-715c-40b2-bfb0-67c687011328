<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.RoadMatchResMapper">
    <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.RoadMatchRes">
        <!--@mbg.generated-->
        <!--@Table road_match_res-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="link_id" jdbcType="VARCHAR" property="linkId"/>
        <result column="link_geom" jdbcType="OTHER" property="linkGeom"/>
        <result column="link_dir" jdbcType="VARCHAR" property="linkDir"/>
        <result column="road_geom" jdbcType="OTHER" property="roadGeom"/>
        <result column="road_dir" jdbcType="VARCHAR" property="roadDir"/>
        <result column="match_res" jdbcType="NUMERIC" property="matchRes"/>
    </resultMap>

    <select id="getAloneMergeRoadNum" resultType="int">
        select count(1)
        from road_match_res
        where match_res = 0
          and link_id is not null
          and osm_id not in
              (select osm_id from road_match_res where match_res = 0 group by osm_id having count(osm_id) > 1)
    </select>

    <select id="getAloneMergeRoad" resultMap="BaseResultMap">
        select * from road_match_res
        where match_res = 0
          and link_id is not null
          and osm_id not in
              (select osm_id from road_match_res where match_res = 0 group by osm_id having count(osm_id) > 1) order by id
        limit #{param1} offset #{param2}
    </select>
</mapper>