package com.hll.mapdataservice.common.entity;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 读取提供excel中的限行数据
 *
 * <AUTHOR>
 * @Date 2022/10/9
 */
public class RestrictionVO {
    @ExcelProperty("market")
    private String market;
    @ExcelProperty("city")
    private String city;
    @ExcelProperty("city_id")
    private String cityId;
    @ExcelProperty("here_link_id")
    private String hereLinkId;
    @ExcelProperty("road_name_en")
    private String restrictedRoadName;
    @ExcelProperty("road_name_multi_lang")
    private String roadNameMultiLang;
    @ExcelProperty("road_line")
    private String roadLine;
    @ExcelProperty("restricted_order_vehicle_id")
    private String restrictedOrderVehicleId;
    @ExcelProperty("restricted_time")
    private String restrictedTime;
    @ExcelProperty("restricted_service_type")
    private String restrictedServiceType;
    @ExcelProperty("Params(departure&weight)")
    private String params;
    @ExcelProperty("white_list_weekday")
    private String whiteListWeekday;
    @ExcelProperty("white_list_day")
    private String whiteListDay;

    @ExcelProperty("direction")
    private String direction;

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getRestrictedRoadName() {
        return restrictedRoadName;
    }

    public void setRestrictedRoadName(String restrictedRoadName) {
        this.restrictedRoadName = restrictedRoadName;
    }

    public String getRestrictedTime() {
        return restrictedTime;
    }

    public void setRestrictedTime(String restrictedTime) {
        this.restrictedTime = restrictedTime;
    }

    public String getRestrictedServiceType() {
        return restrictedServiceType;
    }

    public void setRestrictedServiceType(String restrictedServiceType) {
        this.restrictedServiceType = restrictedServiceType;
    }

    public String getHereLinkId() {
        return hereLinkId;
    }

    public void setHereLinkId(String hereLinkId) {
        this.hereLinkId = hereLinkId;
    }

    public String getRoadNameMultiLang() {
        return roadNameMultiLang;
    }

    public void setRoadNameMultiLang(String roadNameMultiLang) {
        this.roadNameMultiLang = roadNameMultiLang;
    }

    public String getRoadLine() {
        return roadLine;
    }

    public void setRoadLine(String roadLine) {
        this.roadLine = roadLine;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public String getWhiteListWeekday() {
        return whiteListWeekday;
    }

    public void setWhiteListWeekday(String whiteListWeekday) {
        this.whiteListWeekday = whiteListWeekday;
    }

    public String getWhiteListDay() {
        return whiteListDay;
    }

    public void setWhiteListDay(String whiteListDay) {
        this.whiteListDay = whiteListDay;
    }

    public String getRestrictedOrderVehicleId() {
        return restrictedOrderVehicleId;
    }

    public void setRestrictedOrderVehicleId(String restrictedOrderVehicleId) {
        this.restrictedOrderVehicleId = restrictedOrderVehicleId;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }
}
