package com.hll.mapdataservice.common.entity;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-25
 */
@ApiModel(value="HerePhaPointaddress对象", description="")
@DS("db5")
public class HerePhaPointaddress extends Model<HerePhaPointaddress> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private String gid;

    private String linkId;

    private String ptAddrId;

    private String side;

    private String featureId;

    private String paLangcd;

    private String address;

    private String addrType;

    private String dispLon;

    private String dispLat;

    private String bldgNm;

    private String arLinkId;

    private String arSide;

    private String enhanced;
    @TableField("ST_AsText(geom)")
    private String geom;

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }
    public String getLinkId() {
        return linkId;
    }

    public void setLinkId(String linkId) {
        this.linkId = linkId;
    }
    public String getPtAddrId() {
        return ptAddrId;
    }

    public void setPtAddrId(String ptAddrId) {
        this.ptAddrId = ptAddrId;
    }
    public String getSide() {
        return side;
    }

    public void setSide(String side) {
        this.side = side;
    }
    public String getFeatureId() {
        return featureId;
    }

    public void setFeatureId(String featureId) {
        this.featureId = featureId;
    }
    public String getPaLangcd() {
        return paLangcd;
    }

    public void setPaLangcd(String paLangcd) {
        this.paLangcd = paLangcd;
    }
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
    public String getAddrType() {
        return addrType;
    }

    public void setAddrType(String addrType) {
        this.addrType = addrType;
    }
    public String getDispLon() {
        return dispLon;
    }

    public void setDispLon(String dispLon) {
        this.dispLon = dispLon;
    }
    public String getDispLat() {
        return dispLat;
    }

    public void setDispLat(String dispLat) {
        this.dispLat = dispLat;
    }
    public String getBldgNm() {
        return bldgNm;
    }

    public void setBldgNm(String bldgNm) {
        this.bldgNm = bldgNm;
    }
    public String getArLinkId() {
        return arLinkId;
    }

    public void setArLinkId(String arLinkId) {
        this.arLinkId = arLinkId;
    }
    public String getArSide() {
        return arSide;
    }

    public void setArSide(String arSide) {
        this.arSide = arSide;
    }
    public String getEnhanced() {
        return enhanced;
    }

    public void setEnhanced(String enhanced) {
        this.enhanced = enhanced;
    }
    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }

    @Override
    protected Serializable pkVal() {
        return this.gid;
    }

    @Override
    public String toString() {
        return "HerePhaPointaddress{" +
            "gid=" + gid +
            ", linkId=" + linkId +
            ", ptAddrId=" + ptAddrId +
            ", side=" + side +
            ", featureId=" + featureId +
            ", paLangcd=" + paLangcd +
            ", address=" + address +
            ", addrType=" + addrType +
            ", dispLon=" + dispLon +
            ", dispLat=" + dispLat +
            ", bldgNm=" + bldgNm +
            ", arLinkId=" + arLinkId +
            ", arSide=" + arSide +
            ", enhanced=" + enhanced +
            ", geom=" + geom +
        "}";
    }
}
