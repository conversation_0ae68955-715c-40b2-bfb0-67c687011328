/**
  * Copyright 2021 bejson.com 
  */
package com.hll.mapdataservice.common.utils.po.hereplacenamepo;

/**
 * Auto-generated: 2021-04-02 11:27:34
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class JsonRootBean {

    private String xmlns;
    private Content Content;
    private Identity Identity;
    private LocationList LocationList;
    public void setXmlns(String xmlns) {
         this.xmlns = xmlns;
     }
     public String getXmlns() {
         return xmlns;
     }

    public void setContent(Content Content) {
         this.Content = Content;
     }
     public Content getContent() {
         return Content;
     }

    public void setIdentity(Identity Identity) {
         this.Identity = Identity;
     }
     public Identity getIdentity() {
         return Identity;
     }

    public void setLocationList(LocationList LocationList) {
         this.LocationList = LocationList;
     }
     public LocationList getLocationList() {
         return LocationList;
     }

}