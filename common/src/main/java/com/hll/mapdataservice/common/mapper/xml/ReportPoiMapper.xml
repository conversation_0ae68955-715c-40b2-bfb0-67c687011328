<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.ReportPoiMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.ReportPoi">
        <id column="id" property="id" />
        <result column="audit_poi_id" property="auditPoiId" />
        <result column="name" property="name" />
        <result column="address" property="address" />
        <result column="lon" property="lon" />
        <result column="lat" property="lat" />
        <result column="city" property="city" />
        <result column="adname" property="adname" />
        <result column="adcode" property="adcode" />
        <result column="userid" property="userid" />
        <result column="audit_userid" property="auditUserid" />
        <result column="tag" property="tag" />
        <result column="tel" property="tel" />
        <result column="remarks" property="remarks" />
        <result column="photos_info" property="photosInfo" />
        <result column="create_time" property="createTime" />
        <result column="accpet_time" property="accpetTime" />
        <result column="send_time" property="sendTime" />
        <result column="online_time" property="onlineTime" />
        <result column="update_time" property="updateTime" />
        <result column="audit_time" property="auditTime" />
        <result column="status" property="status" />
        <result column="ustatus" property="ustatus" />
        <result column="cause" property="cause" />
    </resultMap>

</mapper>
