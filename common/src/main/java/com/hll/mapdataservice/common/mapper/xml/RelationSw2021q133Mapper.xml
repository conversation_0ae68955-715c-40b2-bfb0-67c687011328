<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.RelationSw2021q133Mapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.RelationSw2021q133">
        <id column="relationid" property="relationid" />
        <result column="inlink_id" property="inlinkId" />
        <result column="node_id" property="nodeId" />
        <result column="outlink_id" property="outlinkId" />
        <result column="type" property="type" />
        <result column="toll_type" property="tollType" />
        <result column="pass_num" property="passNum" />
        <result column="toll_form" property="tollForm" />
        <result column="card_type" property="cardType" />
        <result column="veh" property="veh" />
        <result column="name_ch" property="nameCh" />
        <result column="name_fo" property="nameFo" />
        <result column="name_ph" property="namePh" />
        <result column="name_cht" property="nameCht" />
        <result column="gate_type" property="gateType" />
        <result column="gate_fee" property="gateFee" />
        <result column="tl_locat" property="tlLocat" />
        <result column="tl_flag" property="tlFlag" />
        <result column="slopetype" property="slopetype" />
        <result column="slopeangle" property="slopeangle" />
        <result column="memo" property="memo" />
        <result column="link_angle" property="linkAngle" />
        <result column="mesh_id" property="meshId" />
        <result column="cp" property="cp" />
        <result column="datasource" property="datasource" />
        <result column="up_date" property="upDate" />
        <result column="status" property="status" />
    </resultMap>

</mapper>
