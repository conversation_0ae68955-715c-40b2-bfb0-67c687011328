package com.hll.mapdataservice.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hll.mapdataservice.common.entity.NodeE;
import com.hll.mapdataservice.common.entity.NodeM;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-28
 */
public interface INodeEService extends IService<NodeE> {

}
