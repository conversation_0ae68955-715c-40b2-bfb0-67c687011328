package com.hll.mapdataservice.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hll.mapdataservice.common.entity.LinkM;
import com.hll.mapdataservice.common.entity.RoadMatchRes;
import com.vividsolutions.jts.io.ParseException;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-28
 */
public interface ILinkMService extends IService<LinkM> {

    /**
     * 孤立新增道路融合入库
     * @param batchList
     * @param downLatch
     * @param country
     * @param area
     */
    void aloneRoadMerge(List<RoadMatchRes> batchList, CountDownLatch downLatch,String country,String area) throws ParseException;

    /**
     * 支路长度有变化道路融合
     *
     * @param resList
     * @param country
     * @param area
     * @param downLatch
     */
    void branchRoadMerge(List<String> resList, String country, String area, CountDownLatch downLatch) throws ParseException;

    /**
     * 支路长度有变化道路融合(link)
     *
     * @param resList
     * @param country
     * @param area
     * @param countryOsm
     * @param downLatch
     */
    void branchLinkMergeOfMove(List<String> resList, String country, String area, String countryOsm, CountDownLatch downLatch);

    /**
     * 孤立新增道路融合入库(link)
     *
     * @param matchResList
     * @param downLatch
     * @param countryHere
     * @param area
     * @param countryOsm
     */
    void aloneLinkMerge(List<RoadMatchRes> matchResList, CountDownLatch downLatch, String countryHere, String area,String countryOsm);
}
