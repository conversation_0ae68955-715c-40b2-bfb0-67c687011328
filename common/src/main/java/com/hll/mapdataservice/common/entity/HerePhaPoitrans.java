package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-25
 */
@ApiModel(value="HerePhaPoitrans对象", description="")
public class HerePhaPoitrans extends Model<HerePhaPoitrans> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "gid", type = IdType.AUTO)
    private String gid;

    private Integer poiId;

    private String seqNum;

    private String facType;

    private String transType;

    private String poiNmTr;

    private String stNmTr;

    private String actaddrTr;

    private String stNfulTr;

    private String actSnmTr;

    private String actHouTr;

    private String actAdmTr;

    private String actPstTr;

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }
    public Integer getPoiId() {
        return poiId;
    }

    public void setPoiId(Integer poiId) {
        this.poiId = poiId;
    }
    public String getSeqNum() {
        return seqNum;
    }

    public void setSeqNum(String seqNum) {
        this.seqNum = seqNum;
    }
    public String getFacType() {
        return facType;
    }

    public void setFacType(String facType) {
        this.facType = facType;
    }
    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }
    public String getPoiNmTr() {
        return poiNmTr;
    }

    public void setPoiNmTr(String poiNmTr) {
        this.poiNmTr = poiNmTr;
    }
    public String getStNmTr() {
        return stNmTr;
    }

    public void setStNmTr(String stNmTr) {
        this.stNmTr = stNmTr;
    }
    public String getActaddrTr() {
        return actaddrTr;
    }

    public void setActaddrTr(String actaddrTr) {
        this.actaddrTr = actaddrTr;
    }
    public String getStNfulTr() {
        return stNfulTr;
    }

    public void setStNfulTr(String stNfulTr) {
        this.stNfulTr = stNfulTr;
    }
    public String getActSnmTr() {
        return actSnmTr;
    }

    public void setActSnmTr(String actSnmTr) {
        this.actSnmTr = actSnmTr;
    }
    public String getActHouTr() {
        return actHouTr;
    }

    public void setActHouTr(String actHouTr) {
        this.actHouTr = actHouTr;
    }
    public String getActAdmTr() {
        return actAdmTr;
    }

    public void setActAdmTr(String actAdmTr) {
        this.actAdmTr = actAdmTr;
    }
    public String getActPstTr() {
        return actPstTr;
    }

    public void setActPstTr(String actPstTr) {
        this.actPstTr = actPstTr;
    }

    @Override
    protected Serializable pkVal() {
        return this.gid;
    }

    @Override
    public String toString() {
        return "HerePhaPoitrans{" +
            "gid=" + gid +
            ", poiId=" + poiId +
            ", seqNum=" + seqNum +
            ", facType=" + facType +
            ", transType=" + transType +
            ", poiNmTr=" + poiNmTr +
            ", stNmTr=" + stNmTr +
            ", actaddrTr=" + actaddrTr +
            ", stNfulTr=" + stNfulTr +
            ", actSnmTr=" + actSnmTr +
            ", actHouTr=" + actHouTr +
            ", actAdmTr=" + actAdmTr +
            ", actPstTr=" + actPstTr +
        "}";
    }
}
