<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.LandusebMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.Landuseb">
        <id column="gid" property="gid" />
        <result column="polygon_id" property="polygonId" />
        <result column="polygon_nm" property="polygonNm" />
        <result column="nm_langcd" property="nmLangcd" />
        <result column="poly_nm_tr" property="polyNmTr" />
        <result column="trans_type" property="transType" />
        <result column="feat_type" property="featType" />
        <result column="detail_cty" property="detailCty" />
        <result column="feat_cod" property="featCod" />
        <result column="coverind" property="coverind" />
        <result column="polyres" property="polyres" />
        <result column="expand_inc" property="expandInc" />
        <result column="geom" property="geom" />
    </resultMap>

</mapper>
