package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-19
 */
@ApiModel(value="FoursquarePhaPoi对象", description="")
public class FoursquarePhaPoi extends Model<FoursquarePhaPoi> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "fsq_id", type = IdType.AUTO)
    private String fsqId;

    private String name;

    private String nameTranslated;

    private String latitude;

    private String longitude;

    private String geocodes;

    private String address;

    private String addressExtended;

    private String locality;

    private String dma;

    private String region;

    private String postcode;

    private String country;

    private String adminRegion;

    private String postTown;

    private String neighborhood;

    private String poBox;

    private String dateRefreshed;

    private String categoryIds;

    private String categoryLabels;

    private String fsqChainId;

    private String fsqChainName;

    private String parentId;

    private String subvenueCount;

    private String hours;

    private String hoursPopular;

    private String hoursDisplay;

    private String tel;

    private String website;

    private String fax;

    private String email;

    private String facebookId;

    private String instagram;

    private String twitter;

    private String description;

    private String rating;

    private String price;

    private String totalPhotos;

    private String photos;

    private String totalTips;

    private String tips;

    private String tastes;

    private String popularity;

    private String venueRealityBucket;

    private String existence;

    private String provenanceRating;

    private String dateClosed;

    private String closedBucket;

    private String atm;

    private String barservice;

    private String beer;

    private String businessmeeting;

    private String byo;

    private String clean;

    private String coatcheck;

    private String cocktails;

    private String crowded;

    private String datespopular;

    private String delivery;

    private String dressy;

    private String drivethrough;

    private String essentialreservations;

    private String familiespopular;

    private String fullbar;

    private String glutenfreediet;

    private String goodfordogs;

    private String groupsonlyreservations;

    private String groupspopular;

    private String hasmusic;

    private String hasparking;

    private String healthydiet;

    private String jukeboxmusic;

    private String latenight;

    private String livemusic;

    private String noisy;

    private String onlinereservations;

    private String outdoorseating;

    private String privatelot;

    private String privateroom;

    private String publiclot;

    private String quickbite;

    private String reservations;

    private String restroom;

    private String romantic;

    private String servesbarsnacks;

    private String servesbreakfast;

    private String servesbrunch;

    private String servesdessert;

    private String servesdinner;

    private String serveshappyhour;

    private String serveslunch;

    private String servestastingmenu;

    private String servicequality;

    private String singlespopular;

    private String sitdowndining;

    private String smoking;

    private String specialoccasion;

    private String streetparking;

    private String takeout;

    private String takesamex;

    private String takescreditcards;

    private String takesdinersclub;

    private String takesdiscover;

    private String takesmastercard;

    private String takesnfc;

    private String takesunionpay;

    private String takesvisa;

    private String trendy;

    private String tvs;

    private String valetparking;

    private String valueformoney;

    private String vegandiet;

    private String vegetariandiet;

    private String wheelchairaccessible;

    private String wifi;

    private String wine;

    public String getFsqId() {
        return fsqId;
    }

    public void setFsqId(String fsqId) {
        this.fsqId = fsqId;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public String getNameTranslated() {
        return nameTranslated;
    }

    public void setNameTranslated(String nameTranslated) {
        this.nameTranslated = nameTranslated;
    }
    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }
    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }
    public String getGeocodes() {
        return geocodes;
    }

    public void setGeocodes(String geocodes) {
        this.geocodes = geocodes;
    }
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
    public String getAddressExtended() {
        return addressExtended;
    }

    public void setAddressExtended(String addressExtended) {
        this.addressExtended = addressExtended;
    }
    public String getLocality() {
        return locality;
    }

    public void setLocality(String locality) {
        this.locality = locality;
    }
    public String getDma() {
        return dma;
    }

    public void setDma(String dma) {
        this.dma = dma;
    }
    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }
    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }
    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }
    public String getAdminRegion() {
        return adminRegion;
    }

    public void setAdminRegion(String adminRegion) {
        this.adminRegion = adminRegion;
    }
    public String getPostTown() {
        return postTown;
    }

    public void setPostTown(String postTown) {
        this.postTown = postTown;
    }
    public String getNeighborhood() {
        return neighborhood;
    }

    public void setNeighborhood(String neighborhood) {
        this.neighborhood = neighborhood;
    }
    public String getPoBox() {
        return poBox;
    }

    public void setPoBox(String poBox) {
        this.poBox = poBox;
    }
    public String getDateRefreshed() {
        return dateRefreshed;
    }

    public void setDateRefreshed(String dateRefreshed) {
        this.dateRefreshed = dateRefreshed;
    }
    public String getCategoryIds() {
        return categoryIds;
    }

    public void setCategoryIds(String categoryIds) {
        this.categoryIds = categoryIds;
    }
    public String getCategoryLabels() {
        return categoryLabels;
    }

    public void setCategoryLabels(String categoryLabels) {
        this.categoryLabels = categoryLabels;
    }
    public String getFsqChainId() {
        return fsqChainId;
    }

    public void setFsqChainId(String fsqChainId) {
        this.fsqChainId = fsqChainId;
    }
    public String getFsqChainName() {
        return fsqChainName;
    }

    public void setFsqChainName(String fsqChainName) {
        this.fsqChainName = fsqChainName;
    }
    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }
    public String getSubvenueCount() {
        return subvenueCount;
    }

    public void setSubvenueCount(String subvenueCount) {
        this.subvenueCount = subvenueCount;
    }
    public String getHours() {
        return hours;
    }

    public void setHours(String hours) {
        this.hours = hours;
    }
    public String getHoursPopular() {
        return hoursPopular;
    }

    public void setHoursPopular(String hoursPopular) {
        this.hoursPopular = hoursPopular;
    }
    public String getHoursDisplay() {
        return hoursDisplay;
    }

    public void setHoursDisplay(String hoursDisplay) {
        this.hoursDisplay = hoursDisplay;
    }
    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }
    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }
    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
    public String getFacebookId() {
        return facebookId;
    }

    public void setFacebookId(String facebookId) {
        this.facebookId = facebookId;
    }
    public String getInstagram() {
        return instagram;
    }

    public void setInstagram(String instagram) {
        this.instagram = instagram;
    }
    public String getTwitter() {
        return twitter;
    }

    public void setTwitter(String twitter) {
        this.twitter = twitter;
    }
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
    public String getRating() {
        return rating;
    }

    public void setRating(String rating) {
        this.rating = rating;
    }
    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }
    public String getTotalPhotos() {
        return totalPhotos;
    }

    public void setTotalPhotos(String totalPhotos) {
        this.totalPhotos = totalPhotos;
    }
    public String getPhotos() {
        return photos;
    }

    public void setPhotos(String photos) {
        this.photos = photos;
    }
    public String getTotalTips() {
        return totalTips;
    }

    public void setTotalTips(String totalTips) {
        this.totalTips = totalTips;
    }
    public String getTips() {
        return tips;
    }

    public void setTips(String tips) {
        this.tips = tips;
    }
    public String getTastes() {
        return tastes;
    }

    public void setTastes(String tastes) {
        this.tastes = tastes;
    }
    public String getPopularity() {
        return popularity;
    }

    public void setPopularity(String popularity) {
        this.popularity = popularity;
    }
    public String getVenueRealityBucket() {
        return venueRealityBucket;
    }

    public void setVenueRealityBucket(String venueRealityBucket) {
        this.venueRealityBucket = venueRealityBucket;
    }
    public String getExistence() {
        return existence;
    }

    public void setExistence(String existence) {
        this.existence = existence;
    }
    public String getProvenanceRating() {
        return provenanceRating;
    }

    public void setProvenanceRating(String provenanceRating) {
        this.provenanceRating = provenanceRating;
    }
    public String getDateClosed() {
        return dateClosed;
    }

    public void setDateClosed(String dateClosed) {
        this.dateClosed = dateClosed;
    }
    public String getClosedBucket() {
        return closedBucket;
    }

    public void setClosedBucket(String closedBucket) {
        this.closedBucket = closedBucket;
    }
    public String getAtm() {
        return atm;
    }

    public void setAtm(String atm) {
        this.atm = atm;
    }
    public String getBarservice() {
        return barservice;
    }

    public void setBarservice(String barservice) {
        this.barservice = barservice;
    }
    public String getBeer() {
        return beer;
    }

    public void setBeer(String beer) {
        this.beer = beer;
    }
    public String getBusinessmeeting() {
        return businessmeeting;
    }

    public void setBusinessmeeting(String businessmeeting) {
        this.businessmeeting = businessmeeting;
    }
    public String getByo() {
        return byo;
    }

    public void setByo(String byo) {
        this.byo = byo;
    }
    public String getClean() {
        return clean;
    }

    public void setClean(String clean) {
        this.clean = clean;
    }
    public String getCoatcheck() {
        return coatcheck;
    }

    public void setCoatcheck(String coatcheck) {
        this.coatcheck = coatcheck;
    }
    public String getCocktails() {
        return cocktails;
    }

    public void setCocktails(String cocktails) {
        this.cocktails = cocktails;
    }
    public String getCrowded() {
        return crowded;
    }

    public void setCrowded(String crowded) {
        this.crowded = crowded;
    }
    public String getDatespopular() {
        return datespopular;
    }

    public void setDatespopular(String datespopular) {
        this.datespopular = datespopular;
    }
    public String getDelivery() {
        return delivery;
    }

    public void setDelivery(String delivery) {
        this.delivery = delivery;
    }
    public String getDressy() {
        return dressy;
    }

    public void setDressy(String dressy) {
        this.dressy = dressy;
    }
    public String getDrivethrough() {
        return drivethrough;
    }

    public void setDrivethrough(String drivethrough) {
        this.drivethrough = drivethrough;
    }
    public String getEssentialreservations() {
        return essentialreservations;
    }

    public void setEssentialreservations(String essentialreservations) {
        this.essentialreservations = essentialreservations;
    }
    public String getFamiliespopular() {
        return familiespopular;
    }

    public void setFamiliespopular(String familiespopular) {
        this.familiespopular = familiespopular;
    }
    public String getFullbar() {
        return fullbar;
    }

    public void setFullbar(String fullbar) {
        this.fullbar = fullbar;
    }
    public String getGlutenfreediet() {
        return glutenfreediet;
    }

    public void setGlutenfreediet(String glutenfreediet) {
        this.glutenfreediet = glutenfreediet;
    }
    public String getGoodfordogs() {
        return goodfordogs;
    }

    public void setGoodfordogs(String goodfordogs) {
        this.goodfordogs = goodfordogs;
    }
    public String getGroupsonlyreservations() {
        return groupsonlyreservations;
    }

    public void setGroupsonlyreservations(String groupsonlyreservations) {
        this.groupsonlyreservations = groupsonlyreservations;
    }
    public String getGroupspopular() {
        return groupspopular;
    }

    public void setGroupspopular(String groupspopular) {
        this.groupspopular = groupspopular;
    }
    public String getHasmusic() {
        return hasmusic;
    }

    public void setHasmusic(String hasmusic) {
        this.hasmusic = hasmusic;
    }
    public String getHasparking() {
        return hasparking;
    }

    public void setHasparking(String hasparking) {
        this.hasparking = hasparking;
    }
    public String getHealthydiet() {
        return healthydiet;
    }

    public void setHealthydiet(String healthydiet) {
        this.healthydiet = healthydiet;
    }
    public String getJukeboxmusic() {
        return jukeboxmusic;
    }

    public void setJukeboxmusic(String jukeboxmusic) {
        this.jukeboxmusic = jukeboxmusic;
    }
    public String getLatenight() {
        return latenight;
    }

    public void setLatenight(String latenight) {
        this.latenight = latenight;
    }
    public String getLivemusic() {
        return livemusic;
    }

    public void setLivemusic(String livemusic) {
        this.livemusic = livemusic;
    }
    public String getNoisy() {
        return noisy;
    }

    public void setNoisy(String noisy) {
        this.noisy = noisy;
    }
    public String getOnlinereservations() {
        return onlinereservations;
    }

    public void setOnlinereservations(String onlinereservations) {
        this.onlinereservations = onlinereservations;
    }
    public String getOutdoorseating() {
        return outdoorseating;
    }

    public void setOutdoorseating(String outdoorseating) {
        this.outdoorseating = outdoorseating;
    }
    public String getPrivatelot() {
        return privatelot;
    }

    public void setPrivatelot(String privatelot) {
        this.privatelot = privatelot;
    }
    public String getPrivateroom() {
        return privateroom;
    }

    public void setPrivateroom(String privateroom) {
        this.privateroom = privateroom;
    }
    public String getPubliclot() {
        return publiclot;
    }

    public void setPubliclot(String publiclot) {
        this.publiclot = publiclot;
    }
    public String getQuickbite() {
        return quickbite;
    }

    public void setQuickbite(String quickbite) {
        this.quickbite = quickbite;
    }
    public String getReservations() {
        return reservations;
    }

    public void setReservations(String reservations) {
        this.reservations = reservations;
    }
    public String getRestroom() {
        return restroom;
    }

    public void setRestroom(String restroom) {
        this.restroom = restroom;
    }
    public String getRomantic() {
        return romantic;
    }

    public void setRomantic(String romantic) {
        this.romantic = romantic;
    }
    public String getServesbarsnacks() {
        return servesbarsnacks;
    }

    public void setServesbarsnacks(String servesbarsnacks) {
        this.servesbarsnacks = servesbarsnacks;
    }
    public String getServesbreakfast() {
        return servesbreakfast;
    }

    public void setServesbreakfast(String servesbreakfast) {
        this.servesbreakfast = servesbreakfast;
    }
    public String getServesbrunch() {
        return servesbrunch;
    }

    public void setServesbrunch(String servesbrunch) {
        this.servesbrunch = servesbrunch;
    }
    public String getServesdessert() {
        return servesdessert;
    }

    public void setServesdessert(String servesdessert) {
        this.servesdessert = servesdessert;
    }
    public String getServesdinner() {
        return servesdinner;
    }

    public void setServesdinner(String servesdinner) {
        this.servesdinner = servesdinner;
    }
    public String getServeshappyhour() {
        return serveshappyhour;
    }

    public void setServeshappyhour(String serveshappyhour) {
        this.serveshappyhour = serveshappyhour;
    }
    public String getServeslunch() {
        return serveslunch;
    }

    public void setServeslunch(String serveslunch) {
        this.serveslunch = serveslunch;
    }
    public String getServestastingmenu() {
        return servestastingmenu;
    }

    public void setServestastingmenu(String servestastingmenu) {
        this.servestastingmenu = servestastingmenu;
    }
    public String getServicequality() {
        return servicequality;
    }

    public void setServicequality(String servicequality) {
        this.servicequality = servicequality;
    }
    public String getSinglespopular() {
        return singlespopular;
    }

    public void setSinglespopular(String singlespopular) {
        this.singlespopular = singlespopular;
    }
    public String getSitdowndining() {
        return sitdowndining;
    }

    public void setSitdowndining(String sitdowndining) {
        this.sitdowndining = sitdowndining;
    }
    public String getSmoking() {
        return smoking;
    }

    public void setSmoking(String smoking) {
        this.smoking = smoking;
    }
    public String getSpecialoccasion() {
        return specialoccasion;
    }

    public void setSpecialoccasion(String specialoccasion) {
        this.specialoccasion = specialoccasion;
    }
    public String getStreetparking() {
        return streetparking;
    }

    public void setStreetparking(String streetparking) {
        this.streetparking = streetparking;
    }
    public String getTakeout() {
        return takeout;
    }

    public void setTakeout(String takeout) {
        this.takeout = takeout;
    }
    public String getTakesamex() {
        return takesamex;
    }

    public void setTakesamex(String takesamex) {
        this.takesamex = takesamex;
    }
    public String getTakescreditcards() {
        return takescreditcards;
    }

    public void setTakescreditcards(String takescreditcards) {
        this.takescreditcards = takescreditcards;
    }
    public String getTakesdinersclub() {
        return takesdinersclub;
    }

    public void setTakesdinersclub(String takesdinersclub) {
        this.takesdinersclub = takesdinersclub;
    }
    public String getTakesdiscover() {
        return takesdiscover;
    }

    public void setTakesdiscover(String takesdiscover) {
        this.takesdiscover = takesdiscover;
    }
    public String getTakesmastercard() {
        return takesmastercard;
    }

    public void setTakesmastercard(String takesmastercard) {
        this.takesmastercard = takesmastercard;
    }
    public String getTakesnfc() {
        return takesnfc;
    }

    public void setTakesnfc(String takesnfc) {
        this.takesnfc = takesnfc;
    }
    public String getTakesunionpay() {
        return takesunionpay;
    }

    public void setTakesunionpay(String takesunionpay) {
        this.takesunionpay = takesunionpay;
    }
    public String getTakesvisa() {
        return takesvisa;
    }

    public void setTakesvisa(String takesvisa) {
        this.takesvisa = takesvisa;
    }
    public String getTrendy() {
        return trendy;
    }

    public void setTrendy(String trendy) {
        this.trendy = trendy;
    }
    public String getTvs() {
        return tvs;
    }

    public void setTvs(String tvs) {
        this.tvs = tvs;
    }
    public String getValetparking() {
        return valetparking;
    }

    public void setValetparking(String valetparking) {
        this.valetparking = valetparking;
    }
    public String getValueformoney() {
        return valueformoney;
    }

    public void setValueformoney(String valueformoney) {
        this.valueformoney = valueformoney;
    }
    public String getVegandiet() {
        return vegandiet;
    }

    public void setVegandiet(String vegandiet) {
        this.vegandiet = vegandiet;
    }
    public String getVegetariandiet() {
        return vegetariandiet;
    }

    public void setVegetariandiet(String vegetariandiet) {
        this.vegetariandiet = vegetariandiet;
    }
    public String getWheelchairaccessible() {
        return wheelchairaccessible;
    }

    public void setWheelchairaccessible(String wheelchairaccessible) {
        this.wheelchairaccessible = wheelchairaccessible;
    }
    public String getWifi() {
        return wifi;
    }

    public void setWifi(String wifi) {
        this.wifi = wifi;
    }
    public String getWine() {
        return wine;
    }

    public void setWine(String wine) {
        this.wine = wine;
    }

    @Override
    protected Serializable pkVal() {
        return this.fsqId;
    }

    @Override
    public String toString() {
        return "FoursquarePhaPoi{" +
            "fsqId=" + fsqId +
            ", name=" + name +
            ", nameTranslated=" + nameTranslated +
            ", latitude=" + latitude +
            ", longitude=" + longitude +
            ", geocodes=" + geocodes +
            ", address=" + address +
            ", addressExtended=" + addressExtended +
            ", locality=" + locality +
            ", dma=" + dma +
            ", region=" + region +
            ", postcode=" + postcode +
            ", country=" + country +
            ", adminRegion=" + adminRegion +
            ", postTown=" + postTown +
            ", neighborhood=" + neighborhood +
            ", poBox=" + poBox +
            ", dateRefreshed=" + dateRefreshed +
            ", categoryIds=" + categoryIds +
            ", categoryLabels=" + categoryLabels +
            ", fsqChainId=" + fsqChainId +
            ", fsqChainName=" + fsqChainName +
            ", parentId=" + parentId +
            ", subvenueCount=" + subvenueCount +
            ", hours=" + hours +
            ", hoursPopular=" + hoursPopular +
            ", hoursDisplay=" + hoursDisplay +
            ", tel=" + tel +
            ", website=" + website +
            ", fax=" + fax +
            ", email=" + email +
            ", facebookId=" + facebookId +
            ", instagram=" + instagram +
            ", twitter=" + twitter +
            ", description=" + description +
            ", rating=" + rating +
            ", price=" + price +
            ", totalPhotos=" + totalPhotos +
            ", photos=" + photos +
            ", totalTips=" + totalTips +
            ", tips=" + tips +
            ", tastes=" + tastes +
            ", popularity=" + popularity +
            ", venueRealityBucket=" + venueRealityBucket +
            ", existence=" + existence +
            ", provenanceRating=" + provenanceRating +
            ", dateClosed=" + dateClosed +
            ", closedBucket=" + closedBucket +
            ", atm=" + atm +
            ", barservice=" + barservice +
            ", beer=" + beer +
            ", businessmeeting=" + businessmeeting +
            ", byo=" + byo +
            ", clean=" + clean +
            ", coatcheck=" + coatcheck +
            ", cocktails=" + cocktails +
            ", crowded=" + crowded +
            ", datespopular=" + datespopular +
            ", delivery=" + delivery +
            ", dressy=" + dressy +
            ", drivethrough=" + drivethrough +
            ", essentialreservations=" + essentialreservations +
            ", familiespopular=" + familiespopular +
            ", fullbar=" + fullbar +
            ", glutenfreediet=" + glutenfreediet +
            ", goodfordogs=" + goodfordogs +
            ", groupsonlyreservations=" + groupsonlyreservations +
            ", groupspopular=" + groupspopular +
            ", hasmusic=" + hasmusic +
            ", hasparking=" + hasparking +
            ", healthydiet=" + healthydiet +
            ", jukeboxmusic=" + jukeboxmusic +
            ", latenight=" + latenight +
            ", livemusic=" + livemusic +
            ", noisy=" + noisy +
            ", onlinereservations=" + onlinereservations +
            ", outdoorseating=" + outdoorseating +
            ", privatelot=" + privatelot +
            ", privateroom=" + privateroom +
            ", publiclot=" + publiclot +
            ", quickbite=" + quickbite +
            ", reservations=" + reservations +
            ", restroom=" + restroom +
            ", romantic=" + romantic +
            ", servesbarsnacks=" + servesbarsnacks +
            ", servesbreakfast=" + servesbreakfast +
            ", servesbrunch=" + servesbrunch +
            ", servesdessert=" + servesdessert +
            ", servesdinner=" + servesdinner +
            ", serveshappyhour=" + serveshappyhour +
            ", serveslunch=" + serveslunch +
            ", servestastingmenu=" + servestastingmenu +
            ", servicequality=" + servicequality +
            ", singlespopular=" + singlespopular +
            ", sitdowndining=" + sitdowndining +
            ", smoking=" + smoking +
            ", specialoccasion=" + specialoccasion +
            ", streetparking=" + streetparking +
            ", takeout=" + takeout +
            ", takesamex=" + takesamex +
            ", takescreditcards=" + takescreditcards +
            ", takesdinersclub=" + takesdinersclub +
            ", takesdiscover=" + takesdiscover +
            ", takesmastercard=" + takesmastercard +
            ", takesnfc=" + takesnfc +
            ", takesunionpay=" + takesunionpay +
            ", takesvisa=" + takesvisa +
            ", trendy=" + trendy +
            ", tvs=" + tvs +
            ", valetparking=" + valetparking +
            ", valueformoney=" + valueformoney +
            ", vegandiet=" + vegandiet +
            ", vegetariandiet=" + vegetariandiet +
            ", wheelchairaccessible=" + wheelchairaccessible +
            ", wifi=" + wifi +
            ", wine=" + wine +
        "}";
    }
}
