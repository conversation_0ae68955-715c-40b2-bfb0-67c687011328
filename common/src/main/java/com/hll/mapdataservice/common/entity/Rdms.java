package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-01
 */
@ApiModel(value="Rdms对象", description="")
public class Rdms extends Model<Rdms> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "link_id", type = IdType.INPUT)
    private Integer linkId;

    private Integer condId;

    private Integer manLinkid;

    private Integer seqNumber;

    public Integer getLinkId() {
        return linkId;
    }

    public void setLinkId(Integer linkId) {
        this.linkId = linkId;
    }
    public Integer getCondId() {
        return condId;
    }

    public void setCondId(Integer condId) {
        this.condId = condId;
    }
    public Integer getManLinkid() {
        return manLinkid;
    }

    public void setManLinkid(Integer manLinkid) {
        this.manLinkid = manLinkid;
    }
    public Integer getSeqNumber() {
        return seqNumber;
    }

    public void setSeqNumber(Integer seqNumber) {
        this.seqNumber = seqNumber;
    }

    @Override
    protected Serializable pkVal() {
        return this.linkId;
    }

    @Override
    public String toString() {
        return "Rdms{" +
            "linkId=" + linkId +
            ", condId=" + condId +
            ", manLinkid=" + manLinkid +
            ", seqNumber=" + seqNumber +
        "}";
    }
}
