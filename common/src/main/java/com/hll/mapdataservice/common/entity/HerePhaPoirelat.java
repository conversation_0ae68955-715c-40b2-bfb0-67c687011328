package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-25
 */
@ApiModel(value="HerePhaPoirelat对象", description="")
public class HerePhaPoirelat extends Model<HerePhaPoirelat> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "gid", type = IdType.AUTO)
    private String gid;

    private String assocId;

    private Integer poiId;

    private String primarPoi;

    private String assoType;

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }
    public String getAssocId() {
        return assocId;
    }

    public void setAssocId(String assocId) {
        this.assocId = assocId;
    }
    public Integer getPoiId() {
        return poiId;
    }

    public void setPoiId(Integer poiId) {
        this.poiId = poiId;
    }
    public String getPrimarPoi() {
        return primarPoi;
    }

    public void setPrimarPoi(String primarPoi) {
        this.primarPoi = primarPoi;
    }
    public String getAssoType() {
        return assoType;
    }

    public void setAssoType(String assoType) {
        this.assoType = assoType;
    }

    @Override
    protected Serializable pkVal() {
        return this.gid;
    }

    @Override
    public String toString() {
        return "HerePhaPoirelat{" +
            "gid=" + gid +
            ", assocId=" + assocId +
            ", poiId=" + poiId +
            ", primarPoi=" + primarPoi +
            ", assoType=" + assoType +
        "}";
    }
}
