package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Classname PntAddrTrans
 * @Description here 门址
 * @Date 2021/12/13 10:39 上午
 * @Created by qunfu
 */
@ApiModel(value = "PntAddrTrans")
@TableName("pntaddrtrans")
public class PntAddrTrans extends Model<PntAddrTrans> {
    @ApiModelProperty(value = "")
    @TableId(value = "gid")
    private Integer gid;

    @ApiModelProperty(value = "")
    private Integer ptAddrId;

    @ApiModelProperty(value = "")
    private String  transType;

    @ApiModelProperty(value = "")
    private String addressTr;

    @ApiModelProperty(value = "")
    private String  bldgNmTr;

    public Integer getGid() {
        return gid;
    }

    public void setGid(Integer gid) {
        this.gid = gid;
    }

    public Integer getPtAddrId() {
        return ptAddrId;
    }

    public void setPtAddrId(Integer ptAddrId) {
        this.ptAddrId = ptAddrId;
    }

    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }

    public String getAddressTr() {
        return addressTr;
    }

    public void setAddressTr(String addressTr) {
        this.addressTr = addressTr;
    }

    public String getBldgNmTr() {
        return bldgNmTr;
    }

    public void setBldgNmTr(String bldgNmTr) {
        this.bldgNmTr = bldgNmTr;
    }

    @Override
    public String toString() {
        return "PntAddrTrans{" +
                "gid=" + gid +
                ", ptAddrId=" + ptAddrId +
                ", transType='" + transType + '\'' +
                ", addressTr='" + addressTr + '\'' +
                ", bldgNmTr='" + bldgNmTr + '\'' +
                '}';
    }
}
