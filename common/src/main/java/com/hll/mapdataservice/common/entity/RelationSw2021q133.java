package com.hll.mapdataservice.common.entity;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-01
 */
@ApiModel(value="RelationSw2021q133对象", description="")
//@DS("db8")
@TableName(value = "relation")
public class RelationSw2021q133 extends Model<RelationSw2021q133> {

    private static final long serialVersionUID = 1L;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    @Override
    public String toString() {
        return "RelationSw2021q133{" +
                "relationid='" + relationid + '\'' +
                ", inlinkId='" + inlinkId + '\'' +
                ", nodeId='" + nodeId + '\'' +
                ", outlinkId='" + outlinkId + '\'' +
                ", type='" + type + '\'' +
                ", tollType='" + tollType + '\'' +
                ", passNum='" + passNum + '\'' +
                ", tollForm='" + tollForm + '\'' +
                ", cardType='" + cardType + '\'' +
                ", veh='" + veh + '\'' +
                ", nameCh='" + nameCh + '\'' +
                ", nameFo='" + nameFo + '\'' +
                ", namePh='" + namePh + '\'' +
                ", nameCht='" + nameCht + '\'' +
                ", gateType='" + gateType + '\'' +
                ", gateFee='" + gateFee + '\'' +
                ", tlLocat='" + tlLocat + '\'' +
                ", tlFlag='" + tlFlag + '\'' +
                ", slopetype='" + slopetype + '\'' +
                ", slopeangle='" + slopeangle + '\'' +
                ", memo='" + memo + '\'' +
                ", meshId='" + meshId + '\'' +
                ", cp='" + cp + '\'' +
                ", datasource='" + datasource + '\'' +
                ", upDate=" + upDate +
                ", status=" + status +
                '}';
    }

    public String getRelationid() {
        return relationid;
    }

    public void setRelationid(String relationid) {
        this.relationid = relationid;
    }

    public String getInlinkId() {
        return inlinkId;
    }

    public void setInlinkId(String inlinkId) {
        this.inlinkId = inlinkId;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public String getOutlinkId() {
        return outlinkId;
    }

    public void setOutlinkId(String outlinkId) {
        this.outlinkId = outlinkId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTollType() {
        return tollType;
    }

    public void setTollType(String tollType) {
        this.tollType = tollType;
    }

    public String getPassNum() {
        return passNum;
    }

    public void setPassNum(String passNum) {
        this.passNum = passNum;
    }

    public String getTollForm() {
        return tollForm;
    }

    public void setTollForm(String tollForm) {
        this.tollForm = tollForm;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getVeh() {
        return veh;
    }

    public void setVeh(String veh) {
        this.veh = veh;
    }

    public String getNameCh() {
        return nameCh;
    }

    public void setNameCh(String nameCh) {
        this.nameCh = nameCh;
    }

    public String getNameFo() {
        return nameFo;
    }

    public void setNameFo(String nameFo) {
        this.nameFo = nameFo;
    }

    public String getNamePh() {
        return namePh;
    }

    public void setNamePh(String namePh) {
        this.namePh = namePh;
    }

    public String getNameCht() {
        return nameCht;
    }

    public void setNameCht(String nameCht) {
        this.nameCht = nameCht;
    }

    public String getGateType() {
        return gateType;
    }

    public void setGateType(String gateType) {
        this.gateType = gateType;
    }

    public String getGateFee() {
        return gateFee;
    }

    public void setGateFee(String gateFee) {
        this.gateFee = gateFee;
    }

    public String getTlLocat() {
        return tlLocat;
    }

    public void setTlLocat(String tlLocat) {
        this.tlLocat = tlLocat;
    }

    public String getTlFlag() {
        return tlFlag;
    }

    public void setTlFlag(String tlFlag) {
        this.tlFlag = tlFlag;
    }

    public String getSlopetype() {
        return slopetype;
    }

    public void setSlopetype(String slopetype) {
        this.slopetype = slopetype;
    }

    public String getSlopeangle() {
        return slopeangle;
    }

    public void setSlopeangle(String slopeangle) {
        this.slopeangle = slopeangle;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getMeshId() {
        return meshId;
    }

    public void setMeshId(String meshId) {
        this.meshId = meshId;
    }

    public String getCp() {
        return cp;
    }

    public void setCp(String cp) {
        this.cp = cp;
    }

    public String getDatasource() {
        return datasource;
    }

    public void setDatasource(String datasource) {
        this.datasource = datasource;
    }

    public LocalDateTime getUpDate() {
        return upDate;
    }

    public void setUpDate(LocalDateTime upDate) {
        this.upDate = upDate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @TableId(value = "relationid", type = IdType.INPUT)
    private String relationid;

    private String inlinkId;

    private String nodeId;

    private String outlinkId;

    private String type;

    private String tollType;

    private String passNum;

    private String tollForm;

    private String cardType;

    private String veh;

    private String nameCh;

    private String nameFo;

    private String namePh;

    private String nameCht;

    private String gateType;

    private String gateFee;

    private String tlLocat;

    private String tlFlag;

    private String slopetype;

    private String slopeangle;

    private String memo;

    private String meshId;

    private String cp;

    private String datasource;

    private LocalDateTime upDate;

    private Integer status;

}
