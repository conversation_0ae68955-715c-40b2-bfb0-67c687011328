package com.hll.mapdataservice.common.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.hll.mapdataservice.common.entity.MnrPoi;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hll.mapdataservice.common.vo.PoiAddress;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-19
 */
@DS("db2")
public interface MnrPoiMapper extends BaseMapper<MnrPoi> {
    @Select(" SELECT p.poi_address_id,p.hsn, t.\"name\" as place_name, s.\"name\" as street_name FROM poi.mnr_poi_address p \n" +
            "LEFT JOIN (SELECT ns.nameset_id, ns.name_id,n.\"name\" FROM poi.mnr_nameset2name ns\n" +
            "\t\t\t\t\t\tLEFT JOIN poi.mnr_name n ON ns.name_id= n.name_id) t on p.place_nameset_id = t.nameset_id\n" +
            "LEFT JOIN (SELECT ns.nameset_id, ns.name_id,n.\"name\" FROM poi.mnr_nameset2name ns\n" +
            "\t\t\t\t\t\tLEFT JOIN poi.mnr_name n ON ns.name_id= n.name_id) s on p.street_nameset_id = s.nameset_id" +
            " ${ew.customSqlSegment}")
    PoiAddress getAddressInfoById(@Param(Constants.WRAPPER) Wrapper wrapper);

    @Select("SELECT a.feat_id as id,ma.addressset_id as poi_address_id, ST_AsText(a.geom) as geom, n1.\"name\" AS place_name, " +
            "n2.\"name\" as street_name, n3.hsn as hsn from apt.mnr_apt a \n" +
            "\tLEFT JOIN apt.mnr_apt2addressset ad on a.feat_id = ad.apt_id\n" +
            "\tLEFT JOIN apt.mnr_address ma on ad.addressset_id=ma.addressset_id\n" +
            "\tLEFT JOIN apt.mnr_name n1 on ma.place_name_id = n1.name_id\n" +
            "\tLEFT JOIN apt.mnr_name n2 on ma.street_name_id = n2.name_id \n" +
            "\tLEFT JOIN apt.mnr_hsn n3 on ma.house_number_id = n3.hsn_id ${ew.customSqlSegment}")
    List<PoiAddress> getMzAddressInfoById(@Param(Constants.WRAPPER) Wrapper wrapper);
}