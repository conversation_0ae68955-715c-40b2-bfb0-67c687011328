package com.hll.mapdataservice.common.utils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;

public class ExcelUtils {

    public static Object[][] readExcel(String filePath, String sheetName) {
        try (InputStream inputStream = new FileInputStream(filePath)) {
            Workbook workbook = new XSSFWorkbook(inputStream);
            Sheet sheet = workbook.getSheet(sheetName);

            int rows = sheet.getPhysicalNumberOfRows();
            int cols = sheet.getRow(0).getLastCellNum();

            Object[][] data = new Object[rows][cols];

            for (int i = 0; i < rows; i++) {
                Row row = sheet.getRow(i);
                for (int j = 0; j < cols; j++) {
                    Cell cell = row.getCell(j);
                    data[i][j] = getCellValue(cell);
                }
            }

            workbook.close();
            return data;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void writeExcel(String filePath, String sheetName, Object[][] data) {
        try (OutputStream outputStream = new FileOutputStream(filePath)) {
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet(sheetName);

            int rows = data.length;
            int cols = data[0].length;

            for (int i = 0; i < rows; i++) {
                Row row = sheet.createRow(i);
                for (int j = 0; j < cols; j++) {
                    Cell cell = row.createCell(j);
                    setCellValue(cell, data[i][j]);
                }
            }

            workbook.write(outputStream);
            workbook.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static Object getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue();
                } else {
                    return cell.getNumericCellValue();
                }
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case FORMULA:
                return cell.getCellFormula();
            default:
                return null;
        }
    }

    private static void setCellValue(Cell cell, Object value) {
        if (value == null) {
            cell.setCellValue("");
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Number) {
            cell.setCellValue(((Number) value).doubleValue());
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else if (value instanceof java.util.Date) {
            cell.setCellValue((java.util.Date) value);
        } else {
            cell.setCellValue(value.toString());
        }
    }
    public static void main(String[] args) {

        Object[][] data = readExcel("/Users/<USER>/Downloads/result_1687257182473.xlsx", "0");
        int[][] data2 = new int[data.length][data[0].length];
        for (int i = 1; i < data.length; i++) {
            data2[i][0] = Integer.parseInt((String) data[i][1]);
            data2[i][1] = Integer.parseInt((String) data[i][2]);
        }
        System.out.println("data = " + data.length);
//        writeExcel("src/main/java/com/wangkang/gts4vect/DFSLinkConnection.java", "Sheet2", data);
    }
}
