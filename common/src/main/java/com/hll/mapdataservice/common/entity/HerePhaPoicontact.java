package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-25
 */
@ApiModel(value="HerePhaPoicontact对象", description="")
public class HerePhaPoicontact extends Model<HerePhaPoicontact> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "gid", type = IdType.AUTO)
    private String gid;

    private Integer poiId;

    private String facType;

    private String contType;

    private String preferred;

    private String contact;

    private String areaCode;

    private String localNbr;

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }
    public Integer getPoiId() {
        return poiId;
    }

    public void setPoiId(Integer poiId) {
        this.poiId = poiId;
    }
    public String getFacType() {
        return facType;
    }

    public void setFacType(String facType) {
        this.facType = facType;
    }
    public String getContType() {
        return contType;
    }

    public void setContType(String contType) {
        this.contType = contType;
    }
    public String getPreferred() {
        return preferred;
    }

    public void setPreferred(String preferred) {
        this.preferred = preferred;
    }
    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }
    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }
    public String getLocalNbr() {
        return localNbr;
    }

    public void setLocalNbr(String localNbr) {
        this.localNbr = localNbr;
    }

    @Override
    protected Serializable pkVal() {
        return this.gid;
    }

    @Override
    public String toString() {
        return "HerePhaPoicontact{" +
            "gid=" + gid +
            ", poiId=" + poiId +
            ", facType=" + facType +
            ", contType=" + contType +
            ", preferred=" + preferred +
            ", contact=" + contact +
            ", areaCode=" + areaCode +
            ", localNbr=" + localNbr +
        "}";
    }
}
