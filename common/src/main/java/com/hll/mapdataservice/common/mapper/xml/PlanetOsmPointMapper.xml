<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.PlanetOsmPointMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.PlanetOsmPoint">
        <id column="osm_id" property="osmId" />
        <result column="access" property="access" />
        <result column="addr:housename" property="addrHousename" />
        <result column="addr:housenumber" property="addrHousenumber" />
        <result column="addr:interpolation" property="addrInterpolation" />
        <result column="admin_level" property="adminLevel" />
        <result column="aerialway" property="aerialway" />
        <result column="aeroway" property="aeroway" />
        <result column="amenity" property="amenity" />
        <result column="area" property="area" />
        <result column="barrier" property="barrier" />
        <result column="bicycle" property="bicycle" />
        <result column="brand" property="brand" />
        <result column="bridge" property="bridge" />
        <result column="boundary" property="boundary" />
        <result column="building" property="building" />
        <result column="capital" property="capital" />
        <result column="construction" property="construction" />
        <result column="covered" property="covered" />
        <result column="craft" property="craft" />
        <result column="culvert" property="culvert" />
        <result column="cutting" property="cutting" />
        <result column="denomination" property="denomination" />
        <result column="disused" property="disused" />
        <result column="ele" property="ele" />
        <result column="embankment" property="embankment" />
        <result column="emergency:amenity" property="emergencyAmenity" />
        <result column="emergency" property="emergency" />
        <result column="foot" property="foot" />
        <result column="generator:source" property="generatorSource" />
        <result column="geological" property="geological" />
        <result column="harbour" property="harbour" />
        <result column="healthcare" property="healthcare" />
        <result column="highway" property="highway" />
        <result column="historic" property="historic" />
        <result column="horse" property="horse" />
        <result column="intermittent" property="intermittent" />
        <result column="junction" property="junction" />
        <result column="landuse" property="landuse" />
        <result column="layer" property="layer" />
        <result column="leisure" property="leisure" />
        <result column="lock" property="lock" />
        <result column="man_made" property="manMade" />
        <result column="military" property="military" />
        <result column="motorcar" property="motorcar" />
        <result column="mountain_pass" property="mountainPass" />
        <result column="name" property="name" />
        <result column="natural" property="natural" />
        <result column="office" property="office" />
        <result column="oneway" property="oneway" />
        <result column="operator" property="operator" />
        <result column="place" property="place" />
        <result column="population" property="population" />
        <result column="power" property="power" />
        <result column="power_source" property="powerSource" />
        <result column="public_transport" property="publicTransport" />
        <result column="railway" property="railway" />
        <result column="ref" property="ref" />
        <result column="religion" property="religion" />
        <result column="route" property="route" />
        <result column="service" property="service" />
        <result column="shop" property="shop" />
        <result column="sport" property="sport" />
        <result column="surface" property="surface" />
        <result column="toll" property="toll" />
        <result column="tourism" property="tourism" />
        <result column="tower:type" property="towerType" />
        <result column="transport" property="transport" />
        <result column="tunnel" property="tunnel" />
        <result column="utility" property="utility" />
        <result column="water" property="water" />
        <result column="waterway" property="waterway" />
        <result column="wetland" property="wetland" />
        <result column="width" property="width" />
        <result column="wood" property="wood" />
        <result column="z_order" property="zOrder" />
        <result column="tags" property="tags" />
        <result column="way" property="way" />
    </resultMap>

</mapper>
