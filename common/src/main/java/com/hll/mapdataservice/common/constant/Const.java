package com.hll.mapdataservice.common.constant;

import io.swagger.models.auth.In;

public interface Const {
    Integer H3 = 1;
    Integer TILE_NDS = 2;

    int STATUS_NORMAL = 0;
    int STATUS_DELETED = 1;
    int STATUS_UPDATED = 2;
    int STATUS_NEW = 3;


    Integer RULE_INFO_UNKNOWN = 0;
    Integer RULE_INFO_FORWARD = 1;
    Integer RULE_INFO_LEFT = 2;
    Integer RULE_INFO_RIGHT = 3;
    Integer RULE_INFO_TURN = 4;
    Integer RULE_INFO_ALLOW_TURN = 5;


    String LINK_DIR_UNKNOWN = "0";
    String LINK_DIR_DOUBLE = "1";
    String LINK_DIR_FORWARD = "2";
    String LINK_DIR_BACKWARD = "3";
    String LINK_DIR_DOUBLE_NO = "4";
}
