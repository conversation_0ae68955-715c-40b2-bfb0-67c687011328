/**
  * Copyright 2021 bejson.com 
  */
package com.hll.mapdataservice.common.utils.po.hereplacenamepo;
import java.util.Date;

/**
 * Auto-generated: 2021-04-02 11:27:34
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class Identity {

    private int QualityLevel;
    private String PlaceId;
    private boolean isDeleted;
    private Date lastUpdatedTimeStamp;
    public void setQualityLevel(int QualityLevel) {
         this.QualityLevel = QualityLevel;
     }
     public int getQualityLevel() {
         return QualityLevel;
     }

    public void setPlaceId(String PlaceId) {
         this.PlaceId = PlaceId;
     }
     public String getPlaceId() {
         return PlaceId;
     }

    public void setIsDeleted(boolean isDeleted) {
         this.isDeleted = isDeleted;
     }
     public boolean getIsDeleted() {
         return isDeleted;
     }

    public void setLastUpdatedTimeStamp(Date lastUpdatedTimeStamp) {
         this.lastUpdatedTimeStamp = lastUpdatedTimeStamp;
     }
     public Date getLastUpdatedTimeStamp() {
         return lastUpdatedTimeStamp;
     }

}