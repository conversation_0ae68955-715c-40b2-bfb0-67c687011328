<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.AdminMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.Admin">
        <id column="id" property="id" />
        <result column="area_id" property="areaId" />
        <result column="nm_area_id" property="nmAreaId" />
        <result column="polygon_name" property="polygonName" />
        <result column="nm_langcd" property="nmLangcd" />
        <result column="nm_tr" property="nmTr" />
        <result column="trans_type" property="transType" />
        <result column="feat_type" property="featType" />
        <result column="detail_cty" property="detailCty" />
        <result column="feat_code" property="featCode" />
        <result column="coverind" property="coverind" />
        <result column="claimed_by" property="claimedBy" />
        <result column="control_by" property="controlBy" />
        <result column="admin_lev" property="adminLev" />
    </resultMap>

</mapper>
