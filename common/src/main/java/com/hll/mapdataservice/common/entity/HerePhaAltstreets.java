package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-04
 */
@ApiModel(value="HerePhaAltstreets对象", description="")
@TableName(value = "altstreets")
public class HerePhaAltstreets extends Model<HerePhaAltstreets> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "gid", type = IdType.AUTO)
    private Integer gid;

    private Long linkId;

    private String stName;

    private Long featId;

    private String stLangcd;

    private String stNmPref;

    private String stTypBef;

    private String stNmBase;

    private String stNmSuff;

    private String stTypAft;

    private String stTypAtt;

    private String addrType;

    private String lRefaddr;

    private String lNrefaddr;

    private String lAddrsch;

    private String lAddrform;

    private String rRefaddr;

    private String rNrefaddr;

    private String rAddrsch;

    private String rAddrform;

    private Integer numAdRng;

    private String routeType;

    private String dironsign;

    private String explicatbl;

    private String nameonrdsn;

    private String postalname;

    private String stalename;

    private String vanityname;

    private String junctionnm;

    private String exitname;

    private String scenicNm;

    private String geom;

    public Integer getGid() {
        return gid;
    }

    public void setGid(Integer gid) {
        this.gid = gid;
    }
    public Long getLinkId() {
        return linkId;
    }

    public void setLinkId(Long linkId) {
        this.linkId = linkId;
    }
    public String getStName() {
        return stName;
    }

    public void setStName(String stName) {
        this.stName = stName;
    }
    public Long getFeatId() {
        return featId;
    }

    public void setFeatId(Long featId) {
        this.featId = featId;
    }
    public String getStLangcd() {
        return stLangcd;
    }

    public void setStLangcd(String stLangcd) {
        this.stLangcd = stLangcd;
    }
    public String getStNmPref() {
        return stNmPref;
    }

    public void setStNmPref(String stNmPref) {
        this.stNmPref = stNmPref;
    }
    public String getStTypBef() {
        return stTypBef;
    }

    public void setStTypBef(String stTypBef) {
        this.stTypBef = stTypBef;
    }
    public String getStNmBase() {
        return stNmBase;
    }

    public void setStNmBase(String stNmBase) {
        this.stNmBase = stNmBase;
    }
    public String getStNmSuff() {
        return stNmSuff;
    }

    public void setStNmSuff(String stNmSuff) {
        this.stNmSuff = stNmSuff;
    }
    public String getStTypAft() {
        return stTypAft;
    }

    public void setStTypAft(String stTypAft) {
        this.stTypAft = stTypAft;
    }
    public String getStTypAtt() {
        return stTypAtt;
    }

    public void setStTypAtt(String stTypAtt) {
        this.stTypAtt = stTypAtt;
    }
    public String getAddrType() {
        return addrType;
    }

    public void setAddrType(String addrType) {
        this.addrType = addrType;
    }
    public String getlRefaddr() {
        return lRefaddr;
    }

    public void setlRefaddr(String lRefaddr) {
        this.lRefaddr = lRefaddr;
    }
    public String getlNrefaddr() {
        return lNrefaddr;
    }

    public void setlNrefaddr(String lNrefaddr) {
        this.lNrefaddr = lNrefaddr;
    }
    public String getlAddrsch() {
        return lAddrsch;
    }

    public void setlAddrsch(String lAddrsch) {
        this.lAddrsch = lAddrsch;
    }
    public String getlAddrform() {
        return lAddrform;
    }

    public void setlAddrform(String lAddrform) {
        this.lAddrform = lAddrform;
    }
    public String getrRefaddr() {
        return rRefaddr;
    }

    public void setrRefaddr(String rRefaddr) {
        this.rRefaddr = rRefaddr;
    }
    public String getrNrefaddr() {
        return rNrefaddr;
    }

    public void setrNrefaddr(String rNrefaddr) {
        this.rNrefaddr = rNrefaddr;
    }
    public String getrAddrsch() {
        return rAddrsch;
    }

    public void setrAddrsch(String rAddrsch) {
        this.rAddrsch = rAddrsch;
    }
    public String getrAddrform() {
        return rAddrform;
    }

    public void setrAddrform(String rAddrform) {
        this.rAddrform = rAddrform;
    }
    public Integer getNumAdRng() {
        return numAdRng;
    }

    public void setNumAdRng(Integer numAdRng) {
        this.numAdRng = numAdRng;
    }
    public String getRouteType() {
        return routeType;
    }

    public void setRouteType(String routeType) {
        this.routeType = routeType;
    }
    public String getDironsign() {
        return dironsign;
    }

    public void setDironsign(String dironsign) {
        this.dironsign = dironsign;
    }
    public String getExplicatbl() {
        return explicatbl;
    }

    public void setExplicatbl(String explicatbl) {
        this.explicatbl = explicatbl;
    }
    public String getNameonrdsn() {
        return nameonrdsn;
    }

    public void setNameonrdsn(String nameonrdsn) {
        this.nameonrdsn = nameonrdsn;
    }
    public String getPostalname() {
        return postalname;
    }

    public void setPostalname(String postalname) {
        this.postalname = postalname;
    }
    public String getStalename() {
        return stalename;
    }

    public void setStalename(String stalename) {
        this.stalename = stalename;
    }
    public String getVanityname() {
        return vanityname;
    }

    public void setVanityname(String vanityname) {
        this.vanityname = vanityname;
    }
    public String getJunctionnm() {
        return junctionnm;
    }

    public void setJunctionnm(String junctionnm) {
        this.junctionnm = junctionnm;
    }
    public String getExitname() {
        return exitname;
    }

    public void setExitname(String exitname) {
        this.exitname = exitname;
    }
    public String getScenicNm() {
        return scenicNm;
    }

    public void setScenicNm(String scenicNm) {
        this.scenicNm = scenicNm;
    }
    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }

    @Override
    protected Serializable pkVal() {
        return this.gid;
    }

    @Override
    public String toString() {
        return "HerePhaAltstreets{" +
            "gid=" + gid +
            ", linkId=" + linkId +
            ", stName=" + stName +
            ", featId=" + featId +
            ", stLangcd=" + stLangcd +
            ", stNmPref=" + stNmPref +
            ", stTypBef=" + stTypBef +
            ", stNmBase=" + stNmBase +
            ", stNmSuff=" + stNmSuff +
            ", stTypAft=" + stTypAft +
            ", stTypAtt=" + stTypAtt +
            ", addrType=" + addrType +
            ", lRefaddr=" + lRefaddr +
            ", lNrefaddr=" + lNrefaddr +
            ", lAddrsch=" + lAddrsch +
            ", lAddrform=" + lAddrform +
            ", rRefaddr=" + rRefaddr +
            ", rNrefaddr=" + rNrefaddr +
            ", rAddrsch=" + rAddrsch +
            ", rAddrform=" + rAddrform +
            ", numAdRng=" + numAdRng +
            ", routeType=" + routeType +
            ", dironsign=" + dironsign +
            ", explicatbl=" + explicatbl +
            ", nameonrdsn=" + nameonrdsn +
            ", postalname=" + postalname +
            ", stalename=" + stalename +
            ", vanityname=" + vanityname +
            ", junctionnm=" + junctionnm +
            ", exitname=" + exitname +
            ", scenicNm=" + scenicNm +
            ", geom=" + geom +
        "}";
    }
}
