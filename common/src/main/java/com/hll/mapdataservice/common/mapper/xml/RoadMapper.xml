<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.RoadMapper">
  <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.Road">
    <!--@mbg.generated-->
    <!--@Table roads-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="geom" jdbcType="OTHER" property="geom" />
    <result column="osm_id" jdbcType="BIGINT" property="osmId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="ref" jdbcType="VARCHAR" property="ref" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="oneway" jdbcType="INTEGER" property="oneway" />
    <result column="bridge" jdbcType="INTEGER" property="bridge" />
    <result column="maxspeed" jdbcType="INTEGER" property="maxspeed" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, geom, osm_id, "name", "ref", "type", oneway, bridge, maxspeed
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from roads
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from roads
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.hll.mapdataservice.common.entity.Road">
    <!--@mbg.generated-->
    insert into roads (id, geom, osm_id, 
      "name", "ref", "type", oneway, 
      bridge, maxspeed)
    values (#{id,jdbcType=INTEGER}, #{geom,jdbcType=OTHER}, #{osmId,jdbcType=BIGINT}, 
      #{name,jdbcType=VARCHAR}, #{ref,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{oneway,jdbcType=INTEGER}, 
      #{bridge,jdbcType=INTEGER}, #{maxspeed,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.hll.mapdataservice.common.entity.Road">
    <!--@mbg.generated-->
    insert into roads
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="geom != null">
        geom,
      </if>
      <if test="osmId != null">
        osm_id,
      </if>
      <if test="name != null">
        "name",
      </if>
      <if test="ref != null">
        "ref",
      </if>
      <if test="type != null">
        "type",
      </if>
      <if test="oneway != null">
        oneway,
      </if>
      <if test="bridge != null">
        bridge,
      </if>
      <if test="maxspeed != null">
        maxspeed,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="geom != null">
        #{geom,jdbcType=OTHER},
      </if>
      <if test="osmId != null">
        #{osmId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="ref != null">
        #{ref,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="oneway != null">
        #{oneway,jdbcType=INTEGER},
      </if>
      <if test="bridge != null">
        #{bridge,jdbcType=INTEGER},
      </if>
      <if test="maxspeed != null">
        #{maxspeed,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.hll.mapdataservice.common.entity.Road">
    <!--@mbg.generated-->
    update roads
    <set>
      <if test="geom != null">
        geom = #{geom,jdbcType=OTHER},
      </if>
      <if test="osmId != null">
        osm_id = #{osmId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        "name" = #{name,jdbcType=VARCHAR},
      </if>
      <if test="ref != null">
        "ref" = #{ref,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        "type" = #{type,jdbcType=VARCHAR},
      </if>
      <if test="oneway != null">
        oneway = #{oneway,jdbcType=INTEGER},
      </if>
      <if test="bridge != null">
        bridge = #{bridge,jdbcType=INTEGER},
      </if>
      <if test="maxspeed != null">
        maxspeed = #{maxspeed,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hll.mapdataservice.common.entity.Road">
    <!--@mbg.generated-->
    update roads
    set geom = #{geom,jdbcType=OTHER},
      osm_id = #{osmId,jdbcType=BIGINT},
      "name" = #{name,jdbcType=VARCHAR},
      "ref" = #{ref,jdbcType=VARCHAR},
      "type" = #{type,jdbcType=VARCHAR},
      oneway = #{oneway,jdbcType=INTEGER},
      bridge = #{bridge,jdbcType=INTEGER},
      maxspeed = #{maxspeed,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>