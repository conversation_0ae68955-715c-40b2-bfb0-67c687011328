package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-26
 */
@ApiModel(value="Adminline2对象", description="")
public class Adminline2 extends Model<Adminline2> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "gid", type = IdType.AUTO)
    private Integer gid;

    private Long linkId;

    private Long areaId;

    private String adminNm;

    private String nmLangcd;

    private String featType;

    private Long featCod;

    private String detailCty;

    private String covInd;

    private String lineCntrl;

    private String claimedBy;

    private String controlBy;

    private String geom;

    public Integer getGid() {
        return gid;
    }

    public void setGid(Integer gid) {
        this.gid = gid;
    }
    public Long getLinkId() {
        return linkId;
    }

    public void setLinkId(Long linkId) {
        this.linkId = linkId;
    }
    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }
    public String getAdminNm() {
        return adminNm;
    }

    public void setAdminNm(String adminNm) {
        this.adminNm = adminNm;
    }
    public String getNmLangcd() {
        return nmLangcd;
    }

    public void setNmLangcd(String nmLangcd) {
        this.nmLangcd = nmLangcd;
    }
    public String getFeatType() {
        return featType;
    }

    public void setFeatType(String featType) {
        this.featType = featType;
    }
    public Long getFeatCod() {
        return featCod;
    }

    public void setFeatCod(Long featCod) {
        this.featCod = featCod;
    }
    public String getDetailCty() {
        return detailCty;
    }

    public void setDetailCty(String detailCty) {
        this.detailCty = detailCty;
    }
    public String getCovInd() {
        return covInd;
    }

    public void setCovInd(String covInd) {
        this.covInd = covInd;
    }
    public String getLineCntrl() {
        return lineCntrl;
    }

    public void setLineCntrl(String lineCntrl) {
        this.lineCntrl = lineCntrl;
    }
    public String getClaimedBy() {
        return claimedBy;
    }

    public void setClaimedBy(String claimedBy) {
        this.claimedBy = claimedBy;
    }
    public String getControlBy() {
        return controlBy;
    }

    public void setControlBy(String controlBy) {
        this.controlBy = controlBy;
    }
    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }

    @Override
    protected Serializable pkVal() {
        return this.gid;
    }

    @Override
    public String toString() {
        return "Adminline2{" +
            "gid=" + gid +
            ", linkId=" + linkId +
            ", areaId=" + areaId +
            ", adminNm=" + adminNm +
            ", nmLangcd=" + nmLangcd +
            ", featType=" + featType +
            ", featCod=" + featCod +
            ", detailCty=" + detailCty +
            ", covInd=" + covInd +
            ", lineCntrl=" + lineCntrl +
            ", claimedBy=" + claimedBy +
            ", controlBy=" + controlBy +
            ", geom=" + geom +
        "}";
    }
}
