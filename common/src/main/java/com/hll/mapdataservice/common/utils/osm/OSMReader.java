/*
 *  Licensed to GraphHopper GmbH under one or more contributor
 *  license agreements. See the NOTICE file distributed with this work for
 *  additional information regarding copyright ownership.
 *
 *  GraphHopper GmbH licenses this file to you under the Apache License,
 *  Version 2.0 (the "License"); you may not use this file except in
 *  compliance with the License. You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.hll.mapdataservice.common.utils.osm;

import com.carrotsearch.hppc.*;
import com.graphhopper.util.Helper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.stream.XMLStreamException;
import java.io.File;
import java.io.IOException;
import java.util.*;

import static com.graphhopper.util.Helper.nf;


public class OSMReader {
    protected static final int EMPTY_NODE = -1;
    // pillar node is >= 3
    protected static final int PILLAR_NODE = 1;
    // tower node is <= -3
    protected static final int TOWER_NODE = -2;
    private static final Logger LOGGER = LoggerFactory.getLogger(OSMReader.class);
    private int workerThreads = 2;

    private Date osmDataDate;

    //private LongIntMap osmNodeIdToInternalNodeMap= new GHLongIntBTree(200);
    private LongIntMap osmNodeIdToInternalNodeMap;
    private File osmFile;

    public OSMReader() {

    }

    /**
     * Preprocessing of OSM file to select nodes which are used for highways. This allows a more
     * compact graph data structure.
     */
    void preProcess(File osmFile) {
        LOGGER.info("Starting to process OSM file: '" + osmFile + "'");
        try (OSMInput in = openOsmInputFile(osmFile)) {
            long tmpWayCounter = 1;
            long tmpRelationCounter = 1;
            ReaderElement item;
            while ((item = in.getNext()) != null) {
                if (item.isType(ReaderElement.NODE)) {
                    final ReaderNode node = (ReaderNode) item;
                    if(node.hasTag("amenity","adult_entertainment") || node.hasTag("amenity","stripclub")
                            || node.hasTag("amenity","brothel") || node.hasTag("amenity","swingerclub")
                            || node.hasTag("amenity","love_hotel")) {
                        LOGGER.info("node id:{}, point-adult_entertainment", node.getId());
                    }
                } else if (item.isType(ReaderElement.WAY)) {
                    final ReaderWay way = (ReaderWay) item;
                        LongIndexedContainer wayNodes = way.getNodes();
                        int s = wayNodes.size();
                        if(wayNodes.get(0) == wayNodes.get(s-1)) {
                            // remove circular way, which are used e.g. for round-abouts
//                            LOGGER.info("way id:{},circular way: area-Simple Polygon", way.getId());
                            if(way.hasTag("amenity","adult_entertainment") || way.hasTag("amenity","stripclub")
                                    || way.hasTag("amenity","brothel") || way.hasTag("amenity","swingerclub")
                                    || way.hasTag("amenity","love_hotel")) {
                                LOGGER.info("way id:{}, area-Simple-adult_entertainment", way.getId());
                            }
                        }
                        for (int index = 0; index < s; index++) {
//                            prepareHighwayNode(wayNodes.get(index));
                        }

                        if (++tmpWayCounter % 10_000_000 == 0) {
//                            LOGGER.info(nf(tmpWayCounter) + " (preprocess), osmIdMap:" + nf(getNodeMap().getSize()) + " ("
//                                    + getNodeMap().getMemoryUsage() + "MB) " + Helper.getMemInfo());
                        }
                } else if (item.isType(ReaderElement.RELATION)) {
                    final ReaderRelation relation = (ReaderRelation) item;
                    if(relation.hasTag("type","multipolygon")) {
//                        LOGGER.info("relation id:{},area-multipolygon way", relation.getId());
                        if(relation.hasTag("amenity","adult_entertainment") || relation.hasTag("amenity","stripclub")
                                || relation.hasTag("amenity","brothel") || relation.hasTag("amenity","swingerclub")
                                || relation.hasTag("amenity","love_hotel")) {
                            LOGGER.info("relation id:{}, area-multi-adult_entertainment", relation.getId());
                        }
                    }
                    if(relation.hasTag("type","boundary")) {
//                        LOGGER.info("relation id:{}, boundary polygon", relation.getId());
                    }
                    if (!relation.isMetaRelation() && relation.hasTag("type", "route"))
                        //prepareWaysWithRelationInfo(relation);

                    if (relation.hasTag("type", "restriction")) {
                        //prepareRestrictionRelation(relation);
                    }

                    if (++tmpRelationCounter % 100_000 == 0) {
//                        LOGGER.info(nf(tmpRelationCounter) + " (preprocess), osmWayMap:" + nf(getRelFlagsMapSize())
//                                + ", " + Helper.getMemInfo());
                    }
                } else if (item.isType(ReaderElement.FILEHEADER)) {
                    final OSMFileHeader fileHeader = (OSMFileHeader) item;
                    osmDataDate = Helper.createFormatter().parse(fileHeader.getTag("timestamp"));
                }

            }
        } catch (Exception ex) {
            throw new RuntimeException("Problem while parsing file", ex);
        }
    }

    protected OSMInput openOsmInputFile(File osmFile) throws XMLStreamException, IOException {
        return new OSMInputFile(osmFile).setWorkerThreads(workerThreads).open();
    }

    void prepareHighwayNode(long osmId) {
        int tmpGHNodeId = getNodeMap().get(osmId);
        if (tmpGHNodeId == EMPTY_NODE) {
            // this is the first time we see this osmId
            getNodeMap().put(osmId, PILLAR_NODE);
        } else if (tmpGHNodeId > EMPTY_NODE) {
            // mark node as tower node as it now occurred for at least the second time
            getNodeMap().put(osmId, TOWER_NODE);
        } else {
            // tmpIndex is already negative (already tower node)
        }
    }


    /**
     * Maps OSM IDs (long) to internal node IDs (int)
     */
    protected LongIntMap getNodeMap() {
        return osmNodeIdToInternalNodeMap;
    }

    public OSMReader setWorkerThreads(int numOfWorkers) {
        this.workerThreads = numOfWorkers;
        return this;
    }

    public OSMReader setFile(File osmFile) {
        this.osmFile = osmFile;
        return this;
    }

    private void printInfo(String str) {
//        LOGGER.info("finished " + str + " processing." + " nodes: " + graph.getNodes()
//                + ", osmIdMap.size:" + getNodeMap().getSize() + ", osmIdMap:" + getNodeMap().getMemoryUsage() + "MB"
//                + ", nodeFlagsMap.size:" + getNodeFlagsMap().size() + ", relFlagsMap.size:" + getRelFlagsMapSize()
//                + ", zeroCounter:" + zeroCounter
//                + " " + Helper.getMemInfo());
    }

    /**
     * @return the timestamp given in the OSM file header or null if not found
     */
    public Date getDataDate() {
        return osmDataDate;
    }

    @Override
    public String toString() {
        return getClass().getSimpleName();
    }

    public static void main(String[] args) {
//        String file1 = "/Users/<USER>/Downloads/vietnam-latest.osm.pbf";
        String file1 = "/Users/<USER>/Documents/work/国际图/tomtom/orbis_enterprise_24420_000_global_mys.osm.pbf";
        File osmFile = new File(file1);
        new OSMReader().setFile(osmFile).preProcess(osmFile);
    }
}
