package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2024/12/5
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class PoiDiff extends Model<PoiDiff> {
    // @TableId
    // 使用联合主键
    private String dataid;
    private String source;
    private String name;
    private String address;
    private String kind;
    private String diffType;
    private Double lon;
    private Double lat;
    private String currentQuarter;
    private String preQuarter;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}
