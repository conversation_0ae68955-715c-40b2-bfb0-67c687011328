package com.hll.mapdataservice.common.utils;

import com.linuxense.javadbf.DBFDataType;
import com.linuxense.javadbf.DBFField;
import com.linuxense.javadbf.DBFReader;
import com.linuxense.javadbf.DBFWriter;
import org.postgresql.copy.CopyManager;
import org.postgresql.core.BaseConnection;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.Charset;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DbfFileUtils {
    /**
     * 创建dbf
     * @param path:文件路径
     * @param fieldList:字段
     * @param charsetName:编码字符集
     * @throws IOException
     */
    public static void createDbf(String path, List<Map<String, String>> fieldList, String charsetName)
            throws IOException {
        DBFField[] fields = new DBFField[fieldList.size()];
        int index = 0;
        for (Map<String, String> fieldMap : fieldList) {
            DBFField field = new DBFField();
            field.setName(fieldMap.get("name"));//字段名称
            field.setType(DBFDataType.CHARACTER);//指定字段类型为字符串
            field.setLength(Integer.valueOf(fieldMap.get("length")));//指定长度
            fields[index] = field;
            index++;
        }
        //定义DBFWriter实例用来写DBF文件
        DBFWriter dbfWriter = new DBFWriter(new FileOutputStream(path), Charset.forName(charsetName));
        //设置字段
        dbfWriter.setFields(fields);
        //写入dbf文件并关闭
        dbfWriter.close();
    }
    /**
     * 获取字段名
     * @param path
     * @param charsetName
     * @return
     * @throws IOException
     */
    public static String[] getFieldName(String path, String charsetName) throws IOException {
//		InputStream fis = new FileInputStream(path);
        DBFReader dbfReader = new DBFReader(new FileInputStream(path), Charset.forName(charsetName));
        int fieldCount = dbfReader.getFieldCount();//获取字段数量
        String[] fieldName = new String[fieldCount];
        for (int i = 0; i < fieldCount; i++) {
            fieldName[i] = dbfReader.getField(i).getName();
        }
        dbfReader.close();
//		fis.close();
        return fieldName;
    }
    /**
     * 写dbf文件
     * @param path:dbf文件路径
     * @param rowList:要写入的记录行
     * @param charsetName：字符集
     * @throws IOException
     */
    public static void writeDbf(String path, List<Map<String, String>> rowList, String charsetName)
            throws IOException {
        DBFWriter dbfWriter = new DBFWriter(new File(path));
        //获取字段
        String[] fieldName = getFieldName(path, charsetName);
        for (Map<String, String> rowMap : rowList) {
            Object[] rowData = new Object[fieldName.length];
            for (int i = 0; i < rowData.length; i++) {
                //根据字段来排列指，不然可能出现错位情况
                rowData[i] = rowMap.get(fieldName[i]);
            }
//			rowMap.values().toArray(rowData);
            //添加记录（此时并没有写入文件）
            dbfWriter.addRecord(rowData);
        }
        //写入dbf文件并保存关闭
        dbfWriter.close();
    }
    /**
     * 读dbf记录
     * @param path
     * @return
     * @throws IOException
     */
    public static List<Map<String, String>> readDbf(String path, String charsetName) throws IOException {
        List<Map<String, String>> rowList = new ArrayList<>();
//		InputStream fis = new FileInputStream(path);
        DBFReader dbfReader = new DBFReader(new FileInputStream(path), Charset.forName(charsetName));
        Object[] rowValues;
        while ((rowValues = dbfReader.nextRecord()) != null) {
            Map<String, String> rowMap = new HashMap<String, String>();
            for (int i = 0; i < rowValues.length; i++) {
                rowMap.put(dbfReader.getField(i).getName(), String.valueOf(rowValues[i]).trim());
            }
//			System.out.println(rowMap);
            rowList.add(rowMap);
        }
        dbfReader.close();
//		fis.close();
        return rowList;
    }
    /**
     * 获取字段名
     * @param tableName:表名
     * @return
     * @throws IOException
     */
    public static DBFField[] getFieldNameByTableName(String tableName)
    {
        switch (tableName){
            case "rule":
                DBFField[] dbfFields = new DBFField[17];
                dbfFields[0] = new DBFField();
                dbfFields[0].setName("rule_id".toUpperCase());
                dbfFields[0].setType(DBFDataType.CHARACTER);
                dbfFields[0].setLength(128);

                dbfFields[1] = new DBFField();
                dbfFields[1].setName("inlink_id".toUpperCase());
                dbfFields[1].setType(DBFDataType.CHARACTER);
                dbfFields[1].setLength(128);

                dbfFields[2] = new DBFField();
                dbfFields[2].setName("node_id".toUpperCase());
                dbfFields[2].setType(DBFDataType.CHARACTER);
                dbfFields[2].setLength(128);

                dbfFields[3] = new DBFField();
                dbfFields[3].setName("outlink_id".toUpperCase());
                dbfFields[3].setType(DBFDataType.CHARACTER);
                dbfFields[3].setLength(128);

                dbfFields[4] = new DBFField();
                dbfFields[4].setName("pass".toUpperCase());
                dbfFields[4].setType(DBFDataType.CHARACTER);
                dbfFields[4].setLength(254);

                dbfFields[5] = new DBFField();
                dbfFields[5].setName("pass2".toUpperCase());
                dbfFields[5].setType(DBFDataType.CHARACTER);
                dbfFields[5].setLength(254);

                dbfFields[6] = new DBFField();
                dbfFields[6].setName("flag".toUpperCase());
                dbfFields[6].setType(DBFDataType.NUMERIC);
                dbfFields[6].setLength(2);

                dbfFields[7] = new DBFField();
                dbfFields[7].setName("vperiod".toUpperCase());
                dbfFields[7].setType(DBFDataType.CHARACTER);
                dbfFields[7].setLength(254);

                dbfFields[8] = new DBFField();
                dbfFields[8].setName("vehcl_type".toUpperCase());
                dbfFields[8].setType(DBFDataType.CHARACTER);
                dbfFields[8].setLength(35);

                dbfFields[9] = new DBFField();
                dbfFields[9].setName("vpdir".toUpperCase());
                dbfFields[9].setType(DBFDataType.CHARACTER);
                dbfFields[9].setLength(1);

                dbfFields[10] = new DBFField();
                dbfFields[10].setName("mesh_list".toUpperCase());
                dbfFields[10].setType(DBFDataType.CHARACTER);
                dbfFields[10].setLength(128);

                dbfFields[11] = new DBFField();
                dbfFields[11].setName("memo".toUpperCase());
                dbfFields[11].setType(DBFDataType.CHARACTER);
                dbfFields[11].setLength(254);

                dbfFields[12] = new DBFField();
                dbfFields[12].setName("cp".toUpperCase());
                dbfFields[12].setType(DBFDataType.CHARACTER);
                dbfFields[12].setLength(32);

                dbfFields[13] = new DBFField();
                dbfFields[13].setName("datasource".toUpperCase());
                dbfFields[13].setType(DBFDataType.CHARACTER);
                dbfFields[13].setLength(254);

                dbfFields[14] = new DBFField();
                dbfFields[14].setName("up_date".toUpperCase());
                dbfFields[14].setType(DBFDataType.CHARACTER);
                dbfFields[14].setLength(30);

                dbfFields[15] = new DBFField();
                dbfFields[15].setName("status".toUpperCase());
                dbfFields[15].setType(DBFDataType.NUMERIC);
                dbfFields[15].setLength(1);

                dbfFields[16] = new DBFField();
                dbfFields[16].setName("link_angle".toUpperCase());
                dbfFields[16].setType(DBFDataType.CHARACTER);
                dbfFields[16].setLength(6);
                return dbfFields;
            case "releation":
                DBFField[] dbfReleationFields = new DBFField[26];
                dbfReleationFields[0] = new DBFField();
                dbfReleationFields[0].setName("relationid".toUpperCase());
                dbfReleationFields[0].setType(DBFDataType.CHARACTER);
                dbfReleationFields[0].setLength(128);

                dbfReleationFields[1] = new DBFField();
                dbfReleationFields[1].setName("inlink_id".toUpperCase());
                dbfReleationFields[1].setType(DBFDataType.CHARACTER);
                dbfReleationFields[1].setLength(128);

                dbfReleationFields[2] = new DBFField();
                dbfReleationFields[2].setName("node_id".toUpperCase());
                dbfReleationFields[2].setType(DBFDataType.CHARACTER);
                dbfReleationFields[2].setLength(128);

                dbfReleationFields[3] = new DBFField();
                dbfReleationFields[3].setName("outlink_id".toUpperCase());
                dbfReleationFields[3].setType(DBFDataType.CHARACTER);
                dbfReleationFields[3].setLength(128);

                dbfReleationFields[4] = new DBFField();
                dbfReleationFields[4].setName("type".toUpperCase());
                dbfReleationFields[4].setType(DBFDataType.CHARACTER);
                dbfReleationFields[4].setLength(2);

                dbfReleationFields[5] = new DBFField();
                dbfReleationFields[5].setName("toll_type".toUpperCase());
                dbfReleationFields[5].setType(DBFDataType.CHARACTER);
                dbfReleationFields[5].setLength(128);

                dbfReleationFields[6] = new DBFField();
                dbfReleationFields[6].setName("pass_num".toUpperCase());
                dbfReleationFields[6].setType(DBFDataType.CHARACTER);
                dbfReleationFields[6].setLength(2);

                dbfReleationFields[7] = new DBFField();
                dbfReleationFields[7].setName("toll_form".toUpperCase());
                dbfReleationFields[7].setType(DBFDataType.CHARACTER);
                dbfReleationFields[7].setLength(8);

                dbfReleationFields[8] = new DBFField();
                dbfReleationFields[8].setName("card_type".toUpperCase());
                dbfReleationFields[8].setType(DBFDataType.CHARACTER);
                dbfReleationFields[8].setLength(1);

                dbfReleationFields[9] = new DBFField();
                dbfReleationFields[9].setName("veh".toUpperCase());
                dbfReleationFields[9].setType(DBFDataType.CHARACTER);
                dbfReleationFields[9].setLength(35);

                dbfReleationFields[10] = new DBFField();
                dbfReleationFields[10].setName("name_ch".toUpperCase());
                dbfReleationFields[10].setType(DBFDataType.CHARACTER);
                dbfReleationFields[10].setLength(120);

                dbfReleationFields[11] = new DBFField();
                dbfReleationFields[11].setName("name_fo".toUpperCase());
                dbfReleationFields[11].setType(DBFDataType.CHARACTER);
                dbfReleationFields[11].setLength(254);

                dbfReleationFields[12] = new DBFField();
                dbfReleationFields[12].setName("name_ph".toUpperCase());
                dbfReleationFields[12].setType(DBFDataType.CHARACTER);
                dbfReleationFields[12].setLength(254);

                dbfReleationFields[13] = new DBFField();
                dbfReleationFields[13].setName("name_cht".toUpperCase());
                dbfReleationFields[13].setType(DBFDataType.CHARACTER);
                dbfReleationFields[13].setLength(254);

                dbfReleationFields[14] = new DBFField();
                dbfReleationFields[14].setName("gate_type".toUpperCase());
                dbfReleationFields[14].setType(DBFDataType.CHARACTER);
                dbfReleationFields[14].setLength(1);

                dbfReleationFields[15] = new DBFField();
                dbfReleationFields[15].setName("gate_fee".toUpperCase());
                dbfReleationFields[15].setType(DBFDataType.CHARACTER);
                dbfReleationFields[15].setLength(1);

                dbfReleationFields[16] = new DBFField();
                dbfReleationFields[16].setName("tl_locat".toUpperCase());
                dbfReleationFields[16].setType(DBFDataType.CHARACTER);
                dbfReleationFields[16].setLength(3);

                dbfReleationFields[17] = new DBFField();
                dbfReleationFields[17].setName("tl_flag".toUpperCase());
                dbfReleationFields[17].setType(DBFDataType.CHARACTER);
                dbfReleationFields[17].setLength(1);

                dbfReleationFields[18] = new DBFField();
                dbfReleationFields[18].setName("slopetype".toUpperCase());
                dbfReleationFields[18].setType(DBFDataType.CHARACTER);
                dbfReleationFields[18].setLength(1);

                dbfReleationFields[19] = new DBFField();
                dbfReleationFields[19].setName("slopeangle".toUpperCase());
                dbfReleationFields[19].setType(DBFDataType.CHARACTER);
                dbfReleationFields[19].setLength(6);

                dbfReleationFields[20] = new DBFField();
                dbfReleationFields[20].setName("memo".toUpperCase());
                dbfReleationFields[20].setType(DBFDataType.CHARACTER);
                dbfReleationFields[20].setLength(254);

                dbfReleationFields[21] = new DBFField();
                dbfReleationFields[21].setName("mesh_id".toUpperCase());
                dbfReleationFields[21].setType(DBFDataType.CHARACTER);
                dbfReleationFields[21].setLength(20);

                dbfReleationFields[22] = new DBFField();
                dbfReleationFields[22].setName("cp".toUpperCase());
                dbfReleationFields[22].setType(DBFDataType.CHARACTER);
                dbfReleationFields[22].setLength(32);

                dbfReleationFields[23] = new DBFField();
                dbfReleationFields[23].setName("datasource".toUpperCase());
                dbfReleationFields[23].setType(DBFDataType.CHARACTER);
                dbfReleationFields[23].setLength(254);

                dbfReleationFields[24] = new DBFField();
                dbfReleationFields[24].setName("up_date".toUpperCase());
                dbfReleationFields[24].setType(DBFDataType.CHARACTER);
                dbfReleationFields[24].setLength(30);

                dbfReleationFields[25] = new DBFField();
                dbfReleationFields[25].setName("status".toUpperCase());
                dbfReleationFields[25].setType(DBFDataType.NUMERIC);
                dbfReleationFields[25].setLength(1);

                return dbfReleationFields;
            default:
                return null;
        }
    }

    public void copyFromFile(Connection connection, String filePath, String tableName)
            throws SQLException, IOException {

        FileInputStream fileInputStream = null;

        try {
            CopyManager copyManager = new CopyManager((BaseConnection)connection);
            fileInputStream = new FileInputStream(filePath);
            copyManager.copyIn("COPY " + tableName + " FROM STDIN", fileInputStream);
        } finally {
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
    public void copyToFile(Connection connection, String filePath, String tableOrQuery)
            throws SQLException, IOException {

        FileOutputStream fileOutputStream = null;

        try {
            CopyManager copyManager = new CopyManager((BaseConnection)connection);
            fileOutputStream = new FileOutputStream(filePath);
            copyManager.copyOut("COPY " + tableOrQuery + " TO STDOUT", fileOutputStream);
        } finally {
            if (fileOutputStream != null) {
                try {
                    fileOutputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
