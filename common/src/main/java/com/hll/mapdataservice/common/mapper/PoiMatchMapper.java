package com.hll.mapdataservice.common.mapper;

import com.hll.mapdataservice.common.entity.PoiMatch;

/**
  *
  *
  * @Author: ares.chen
  * @Since: 2021/12/8
  */
public interface PoiMatchMapper extends RootMapper<PoiMatch> {
    int deleteByPrimaryKey(String id);

    int insert(PoiMatch record);

    int insertSelective(PoiMatch record);

    PoiMatch selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(PoiMatch record);

    int updateByPrimaryKey(PoiMatch record);
}