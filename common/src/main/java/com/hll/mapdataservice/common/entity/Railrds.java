package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-26
 */
@ApiModel(value="Railrds对象", description="")
public class Railrds extends Model<Railrds> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "gid", type = IdType.AUTO)
    private Integer gid;

    private Long linkId;

    private String railwayNm;

    private String langCode;

    private String railNmTr;

    private String transType;

    private String bridge;

    private String tunnel;

    private String coverind;

    private String featType;

    private String geom;

    public Integer getGid() {
        return gid;
    }

    public void setGid(Integer gid) {
        this.gid = gid;
    }
    public Long getLinkId() {
        return linkId;
    }

    public void setLinkId(Long linkId) {
        this.linkId = linkId;
    }
    public String getRailwayNm() {
        return railwayNm;
    }

    public void setRailwayNm(String railwayNm) {
        this.railwayNm = railwayNm;
    }
    public String getLangCode() {
        return langCode;
    }

    public void setLangCode(String langCode) {
        this.langCode = langCode;
    }
    public String getRailNmTr() {
        return railNmTr;
    }

    public void setRailNmTr(String railNmTr) {
        this.railNmTr = railNmTr;
    }
    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }
    public String getBridge() {
        return bridge;
    }

    public void setBridge(String bridge) {
        this.bridge = bridge;
    }
    public String getTunnel() {
        return tunnel;
    }

    public void setTunnel(String tunnel) {
        this.tunnel = tunnel;
    }
    public String getCoverind() {
        return coverind;
    }

    public void setCoverind(String coverind) {
        this.coverind = coverind;
    }
    public String getFeatType() {
        return featType;
    }

    public void setFeatType(String featType) {
        this.featType = featType;
    }
    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }

    @Override
    protected Serializable pkVal() {
        return this.gid;
    }

    @Override
    public String toString() {
        return "Railrds{" +
            "gid=" + gid +
            ", linkId=" + linkId +
            ", railwayNm=" + railwayNm +
            ", langCode=" + langCode +
            ", railNmTr=" + railNmTr +
            ", transType=" + transType +
            ", bridge=" + bridge +
            ", tunnel=" + tunnel +
            ", coverind=" + coverind +
            ", featType=" + featType +
            ", geom=" + geom +
        "}";
    }
}
