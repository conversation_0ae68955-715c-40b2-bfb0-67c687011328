package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/10/9
 */
public class RestrictedInfo {
    // private List<RestrictTime> restrict_time;
    private String restrict_time;
    private String white_list_weekday;
    private String white_list_day;
    private String restricted_service_type;
    private Param param;
    private String direction;

    // public List<RestrictTime> getRestrict_time() {
    //     return restrict_time;
    // }
    //
    // public void setRestrict_time(List<RestrictTime> restrict_time) {
    //     this.restrict_time = restrict_time;
    // }


    public String getRestrict_time() {
        return restrict_time;
    }

    public void setRestrict_time(String restrict_time) {
        this.restrict_time = restrict_time;
    }

    public String getWhite_list_weekday() {
        return white_list_weekday;
    }

    public void setWhite_list_weekday(String white_list_weekday) {
        this.white_list_weekday = white_list_weekday;
    }

    public String getWhite_list_day() {
        return white_list_day;
    }

    public void setWhite_list_day(String white_list_day) {
        this.white_list_day = white_list_day;
    }

    public String getRestricted_service_type() {
        return restricted_service_type;
    }

    public void setRestricted_service_type(String restricted_service_type) {
        this.restricted_service_type = restricted_service_type;
    }

    public Param getParam() {
        return param;
    }

    public void setParam(Param param) {
        this.param = param;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }
}
