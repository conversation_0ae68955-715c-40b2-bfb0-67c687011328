create index altstreets_area11_link_id_index
    on altstreets_area11 (link_id);

create index streets_area11_link_id_index
    on streets_area11 (link_id);

create index zlevels_area11_node_id_index
    on zlevels_area11 (node_id);

create index altstreets_area12_link_id_index
    on altstreets_area12 (link_id);

create index streets_area12_link_id_index
    on streets_area12 (link_id);

create index zlevels_area12_node_id_index
    on zlevels_area12 (node_id);

create index altstreets_area13_link_id_index
    on altstreets_area13 (link_id);

create index streets_area13_link_id_index
    on streets_area13 (link_id);

create index zlevels_area13_node_id_index
    on zlevels_area13 (node_id);

create index altstreets_area14_link_id_index
    on altstreets_area14 (link_id);

create index streets_area14_link_id_index
    on streets_area14 (link_id);

create index zlevels_area14_node_id_index
    on zlevels_area14 (node_id);

create index altstreets_area15_link_id_index
    on altstreets_area15 (link_id);

create index streets_area15_link_id_index
    on streets_area15 (link_id);

create index zlevels_area15_node_id_index
    on zlevels_area15 (node_id);

create index altstreets_area16_link_id_index
    on altstreets_area16 (link_id);

create index streets_area16_link_id_index
    on streets_area16 (link_id);

create index zlevels_area16_node_id_index
    on zlevels_area16 (node_id);

create index altstreets_area17_link_id_index
    on altstreets_area17 (link_id);

create index streets_area17_link_id_index
    on streets_area17 (link_id);

create index zlevels_area17_node_id_index
    on zlevels_area17 (node_id);

create index altstreets_area18_link_id_index
    on altstreets_area18 (link_id);

create index streets_area18_link_id_index
    on streets_area18 (link_id);

create index zlevels_area18_node_id_index
    on zlevels_area18 (node_id);

create index altstreets_area19_link_id_index
    on altstreets_area19 (link_id);

create index streets_area19_link_id_index
    on streets_area19 (link_id);

create index zlevels_area19_node_id_index
    on zlevels_area19 (node_id);

create index altstreets_area20_link_id_index
    on altstreets_area20 (link_id);

create index streets_area20_link_id_index
    on streets_area20 (link_id);

create index zlevels_area20_node_id_index
    on zlevels_area20 (node_id);

create index altstreets_area21_link_id_index
    on altstreets_area21 (link_id);

create index streets_area21_link_id_index
    on streets_area21 (link_id);

create index zlevels_area21_node_id_index
    on zlevels_area21 (node_id);

create index rdms_area11_cond_id_index
    on public.rdms_area11 (cond_id);

create index rdms_area12_cond_id_index
    on public.rdms_area12 (cond_id);

create index rdms_area13_cond_id_index
    on public.rdms_area13 (cond_id);

create index rdms_area14_cond_id_index
    on public.rdms_area14 (cond_id);

create index rdms_area15_cond_id_index
    on public.rdms_area15 (cond_id);

create index rdms_area16_cond_id_index
    on public.rdms_area16 (cond_id);

create index rdms_area17_cond_id_index
    on public.rdms_area17 (cond_id);
create index rdms_area18_cond_id_index
    on public.rdms_area18 (cond_id);
create index rdms_area19_cond_id_index
    on public.rdms_area19 (cond_id);
create index rdms_area20_cond_id_index
    on public.rdms_area20 (cond_id);
create index rdms_area21_cond_id_index
    on public.rdms_area21 (cond_id);

create index cndmod_area11_cond_id_index
    on public.cndmod_area11 (cond_id);

create index cndmod_area12_cond_id_index
    on public.cndmod_area12 (cond_id);

create index cndmod_area13_cond_id_index
    on public.cndmod_area13 (cond_id);

create index cndmod_area14_cond_id_index
    on public.cndmod_area14 (cond_id);

create index cndmod_area15_cond_id_index
    on public.cndmod_area15 (cond_id);

create index cndmod_area16_cond_id_index
    on public.cndmod_area16 (cond_id);

create index cndmod_area17_cond_id_index
    on public.cndmod_area17 (cond_id);
create index cndmod_area18_cond_id_index
    on public.cndmod_area18 (cond_id);
create index cndmod_area19_cond_id_index
    on public.cndmod_area19 (cond_id);
create index cndmod_area20_cond_id_index
    on public.cndmod_area20 (cond_id);
create index cndmod_area21_cond_id_index
    on public.cndmod_area21 (cond_id);

create index cdms_area11_cond_type_index
    on cdms_area11 (cond_type);

create index cdms_area12_cond_type_index
    on cdms_area12 (cond_type);

create index cdms_area13_cond_type_index
    on cdms_area13 (cond_type);

create index cdms_area14_cond_type_index
    on cdms_area14 (cond_type);

create index cdms_area15_cond_type_index
    on cdms_area15 (cond_type);
create index cdms_area16_cond_type_index
    on cdms_area16 (cond_type);
create index cdms_area17_cond_type_index
    on cdms_area17 (cond_type);
create index cdms_area18_cond_type_index
    on cdms_area18 (cond_type);
create index cdms_area19_cond_type_index
    on cdms_area19 (cond_type);
create index cdms_area20_cond_type_index
    on cdms_area20 (cond_type);
create index cdms_area21_cond_type_index
    on cdms_area21 (cond_type);

create index pointaddress_area11_link_id_index
    on pointaddress_area11 (link_id);
create index pointaddress_area12_link_id_index
    on pointaddress_area12 (link_id);
create index pointaddress_area13_link_id_index
    on pointaddress_area13 (link_id);
create index pointaddress_area14_link_id_index
    on pointaddress_area14 (link_id);
create index pointaddress_area15_link_id_index
    on pointaddress_area15 (link_id);
create index pointaddress_area16_link_id_index
    on pointaddress_area16 (link_id);
create index pointaddress_area17_link_id_index
    on pointaddress_area17 (link_id);
create index pointaddress_area18_link_id_index
    on pointaddress_area18 (link_id);
create index pointaddress_area19_link_id_index
    on pointaddress_area19 (link_id);
create index pointaddress_area20_link_id_index
    on pointaddress_area20 (link_id);
create index pointaddress_area21_link_id_index
    on pointaddress_area21 (link_id);
