create index altstreets_area1_link_id_index
    on altstreets_area1 (link_id);

create index streets_area1_link_id_index
    on streets_area1 (link_id);

create index zlevels_area1_node_id_index
    on zlevels_area1 (node_id);

create index altstreets_area2_link_id_index
    on altstreets_area2 (link_id);

create index streets_area2_link_id_index
    on streets_area2 (link_id);

create index zlevels_area2_node_id_index
    on zlevels_area2 (node_id);

create index altstreets_area3_link_id_index
    on altstreets_area3 (link_id);

create index streets_area3_link_id_index
    on streets_area3 (link_id);

create index zlevels_area3_node_id_index
    on zlevels_area3 (node_id);

create index altstreets_area4_link_id_index
    on altstreets_area4 (link_id);

create index streets_area4_link_id_index
    on streets_area4 (link_id);

create index zlevels_area4_node_id_index
    on zlevels_area4 (node_id);

create index altstreets_area5_link_id_index
    on altstreets_area5 (link_id);

create index streets_area5_link_id_index
    on streets_area5 (link_id);

create index zlevels_area5_node_id_index
    on zlevels_area5 (node_id);

create index altstreets_area6_link_id_index
    on altstreets_area6 (link_id);

create index streets_area6_link_id_index
    on streets_area6 (link_id);

create index zlevels_area6_node_id_index
    on zlevels_area6 (node_id);

create index altstreets_area7_link_id_index
    on altstreets_area7 (link_id);

create index streets_area7_link_id_index
    on streets_area7 (link_id);

create index zlevels_area7_node_id_index
    on zlevels_area7 (node_id);

create index altstreets_area8_link_id_index
    on altstreets_area8 (link_id);

create index streets_area8_link_id_index
    on streets_area8 (link_id);

create index zlevels_area8_node_id_index
    on zlevels_area8 (node_id);

create index altstreets_area9_link_id_index
    on altstreets_area9 (link_id);

create index streets_area9_link_id_index
    on streets_area9 (link_id);

create index zlevels_area9_node_id_index
    on zlevels_area9 (node_id);

create index altstreets_area10_link_id_index
    on altstreets_area10 (link_id);

create index streets_area10_link_id_index
    on streets_area10 (link_id);

create index zlevels_area10_node_id_index
    on zlevels_area10 (node_id);
create index rdms_area1_cond_id_index
    on public.rdms_area1 (cond_id);

create index rdms_area2_cond_id_index
    on public.rdms_area2 (cond_id);

create index rdms_area3_cond_id_index
    on public.rdms_area3 (cond_id);

create index rdms_area4_cond_id_index
    on public.rdms_area4 (cond_id);

create index rdms_area5_cond_id_index
    on public.rdms_area5 (cond_id);

create index rdms_area6_cond_id_index
    on public.rdms_area6 (cond_id);

create index rdms_area7_cond_id_index
    on public.rdms_area7 (cond_id);
create index rdms_area8_cond_id_index
    on public.rdms_area8 (cond_id);
create index rdms_area9_cond_id_index
    on public.rdms_area9 (cond_id);
create index rdms_area10_cond_id_index
    on public.rdms_area10 (cond_id);

create index cndmod_area1_cond_id_index
    on public.cndmod_area1 (cond_id);

create index cndmod_area2_cond_id_index
    on public.cndmod_area2 (cond_id);

create index cndmod_area3_cond_id_index
    on public.cndmod_area3 (cond_id);

create index cndmod_area4_cond_id_index
    on public.cndmod_area4 (cond_id);

create index cndmod_area5_cond_id_index
    on public.cndmod_area5 (cond_id);

create index cndmod_area6_cond_id_index
    on public.cndmod_area6 (cond_id);

create index cndmod_area7_cond_id_index
    on public.cndmod_area7 (cond_id);
create index cndmod_area8_cond_id_index
    on public.cndmod_area8 (cond_id);
create index cndmod_area9_cond_id_index
    on public.cndmod_area9 (cond_id);
create index cndmod_area10_cond_id_index
    on public.cndmod_area10 (cond_id);
create index cdms_area1_cond_type_index
    on cdms_area1 (cond_type);

create index cdms_area2_cond_type_index
    on cdms_area2 (cond_type);

create index cdms_area3_cond_type_index
    on cdms_area3 (cond_type);

create index cdms_area4_cond_type_index
    on cdms_area4 (cond_type);

create index cdms_area5_cond_type_index
    on cdms_area5 (cond_type);
create index cdms_area6_cond_type_index
    on cdms_area6 (cond_type);
create index cdms_area7_cond_type_index
    on cdms_area7 (cond_type);
create index cdms_area8_cond_type_index
    on cdms_area8 (cond_type);
create index cdms_area9_cond_type_index
    on cdms_area9 (cond_type);
create index cdms_area10_cond_type_index
    on cdms_area10 (cond_type);
create index pointaddress_area1_link_id_index
    on pointaddress_area1 (link_id);
create index pointaddress_area2_link_id_index
    on pointaddress_area2 (link_id);
create index pointaddress_area3_link_id_index
    on pointaddress_area3 (link_id);
create index pointaddress_area4_link_id_index
    on pointaddress_area4 (link_id);
create index pointaddress_area5_link_id_index
    on pointaddress_area5 (link_id);
create index pointaddress_area6_link_id_index
    on pointaddress_area6 (link_id);
create index pointaddress_area7_link_id_index
    on pointaddress_area7 (link_id);
create index pointaddress_area8_link_id_index
    on pointaddress_area8 (link_id);
create index pointaddress_area9_link_id_index
    on pointaddress_area9 (link_id);
create index pointaddress_area10_link_id_index
    on pointaddress_area10 (link_id);
