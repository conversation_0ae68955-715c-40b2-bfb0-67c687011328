create index altstreets_area1_link_id_index
    on altstreets_area1 (link_id);

create index streets_area1_link_id_index
    on streets_area1 (link_id);

create index zlevels_area1_node_id_index
    on zlevels_area1 (node_id);

create index pntaddrtrans_area1_pt_addr_id_index
    on pntaddrtrans_area1 (pt_addr_id);

create index altstreets_area2_link_id_index
    on altstreets_area2 (link_id);

create index streets_area2_link_id_index
    on streets_area2 (link_id);

create index zlevels_area2_node_id_index
    on zlevels_area2 (node_id);

create index pntaddrtrans_area2_pt_addr_id_index
    on pntaddrtrans_area2 (pt_addr_id);

create index altstreets_area3_link_id_index
    on altstreets_area3 (link_id);

create index streets_area3_link_id_index
    on streets_area3 (link_id);

create index zlevels_area3_node_id_index
    on zlevels_area3 (node_id);

create index pntaddrtrans_area3_pt_addr_id_index
    on pntaddrtrans_area3 (pt_addr_id);

create index altstreets_area4_link_id_index
    on altstreets_area4 (link_id);

create index streets_area4_link_id_index
    on streets_area4 (link_id);

create index zlevels_area4_node_id_index
    on zlevels_area4 (node_id);

create index pntaddrtrans_area4_pt_addr_id_index
    on pntaddrtrans_area4 (pt_addr_id);

create index altstreets_area5_link_id_index
    on altstreets_area5 (link_id);

create index streets_area5_link_id_index
    on streets_area5 (link_id);

create index zlevels_area5_node_id_index
    on zlevels_area5 (node_id);

create index pntaddrtrans_area5_pt_addr_id_index
    on pntaddrtrans_area5 (pt_addr_id);

create index rdms_area1_cond_id_index
    on public.rdms_area1 (cond_id);

create index rdms_area2_cond_id_index
    on public.rdms_area2 (cond_id);

create index rdms_area3_cond_id_index
    on public.rdms_area3 (cond_id);

create index rdms_area4_cond_id_index
    on public.rdms_area4 (cond_id);

create index rdms_area5_cond_id_index
    on public.rdms_area5 (cond_id);

create index cndmod_area1_cond_id_index
    on public.cndmod_area1 (cond_id);

create index cndmod_area2_cond_id_index
    on public.cndmod_area2 (cond_id);

create index cndmod_area3_cond_id_index
    on public.cndmod_area3 (cond_id);

create index cndmod_area4_cond_id_index
    on public.cndmod_area4 (cond_id);

create index cndmod_area5_cond_id_index
    on public.cndmod_area5 (cond_id);

create index cdms_area1_cond_type_index
    on cdms_area1 (cond_type);

create index cdms_area2_cond_type_index
    on cdms_area2 (cond_type);

create index cdms_area3_cond_type_index
    on cdms_area3 (cond_type);

create index cdms_area4_cond_type_index
    on cdms_area4 (cond_type);

create index cdms_area5_cond_type_index
    on cdms_area5 (cond_type);
create index pointaddress_area1_link_id_index
    on pointaddress_area1 (link_id);
create index pointaddress_area2_link_id_index
    on pointaddress_area2 (link_id);
create index pointaddress_area3_link_id_index
    on pointaddress_area3 (link_id);
create index pointaddress_area4_link_id_index
    on pointaddress_area4 (link_id);
create index pointaddress_area5_link_id_index
    on pointaddress_area5 (link_id);
