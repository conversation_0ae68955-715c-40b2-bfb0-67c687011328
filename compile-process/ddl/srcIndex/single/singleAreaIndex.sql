create index altstreets_link_id_index
    on altstreets (link_id);

create index streets_link_id_index
    on streets (link_id);

create index zlevels_node_id_index
    on zlevels (node_id);

create index streettrans_feature_id_index
    on streettrans (feature_id);

create index pntaddrtrans_pt_addr_id_index
    on pntaddrtrans (pt_addr_id);

create index rdms_cond_id_index
    on public.rdms (cond_id);

create index cndmod_cond_id_index
    on public.cndmod (cond_id);


create index cdms_cond_type_index
    on cdms (cond_type);

create index pointaddress_link_id_index
    on pointaddress (link_id);

create index railrds_link_id_index
    on public.railrds (link_id);
create index adminline1_link_id_index
    on public.adminline1 (link_id);
create index adminline2_link_id_index
    on public.adminline2 (link_id);
create index waterseg_link_id_index
    on public.waterseg (link_id);

create index waterpoly_polygon_id_index
    on public.waterpoly (polygon_id);
create index landusea_polygon_id_index
    on public.landusea (polygon_id);
create index landuseb_polygon_id_index
    on public.landuseb (polygon_id);
create index oceans_polygon_id_index
    on public.oceans (polygon_id);
create index islands_polygon_id_index
    on public.islands (polygon_id);

create index landmark_polygon_id_index
    on public.landmark (polygon_id);

create index namedplc_h_polygon_id_index
    on public.namedplc_h (poi_id);
