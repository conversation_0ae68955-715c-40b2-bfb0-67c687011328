create table public.link_m_area1
(
    hll_linkid  varchar(256) not null
        constraint link_m_area1_pk
            primary key,
    link_id     varchar(128) not null,
    hll_s_nid   varchar(256) not null,
    hll_e_nid   varchar(256) not null,
    kind        varchar(2)       default NULL::character varying,
    formway     varchar(128)     default 1,
    dir         varchar(2)       default NULL::character varying,
    app         varchar(2)       default NULL::character varying,
    toll        varchar(2)       default NULL::character varying,
    adopt       varchar(2)       default NULL::character varying,
    md          varchar(1)       default NULL::character varying,
    divider     varchar(1)       default NULL::character varying,
    divider_leg varchar(1)       default NULL::character varying,
    devs        varchar(1)       default NULL::character varying,
    spet        varchar(1)       default NULL::character varying,
    funct       varchar(2)       default NULL::character varying,
    urban       varchar(1)       default NULL::character varying,
    pave        varchar(1)       default NULL::character varying,
    lane_n      integer          default 0,
    lane_l      integer          default 0,
    lane_r      integer          default 0,
    lane_c      varchar(2)       default NULL::character varying,
    width       varchar(8)       default NULL::character varying,
    viad        varchar(1)       default NULL::character varying,
    l_admin     varchar(10)  not null,
    r_admin     varchar(10)  not null,
    geom        geometry(LineString, 4326),
    len         double precision default 0.0,
    f_speed     varchar(100),
    t_speed     varchar(100),
    sp_class    varchar(100),
    ar_veh      varchar(35),
    dici_type   varchar(1)       default NULL::character varying,
    verifyflag  varchar(1)       default NULL::character varying,
    pre_launch  varchar(256),
    name_ch_o   varchar(1000),
    name_ch_a   varchar(1000),
    name_ch_f   varchar(1000),
    name_ph_o   varchar(1000),
    name_ph_a   varchar(1000),
    name_ph_f   varchar(1000),
    name_en_o   varchar(1000),
    name_en_a   varchar(1000),
    name_en_f   varchar(1000),
    name_po     varchar(1000),
    name_cht    varchar(1000),
    code_type   varchar(2)       default NULL::character varying,
    name_type   varchar(2)       default 0,
    src_flag    varchar(2)       default NULL::character varying,
    mesh_id     varchar(8),
    memo        varchar(1000),
    cp          varchar(32),
    datasource  varchar(256),
    up_date     timestamp    not null,
    status      integer          default 0,
    geomwkt     varchar,
    pub_access  char,
    t_admin     varchar,
    time_zone   varchar
);

alter table public.link_m_area1
    owner to postgres;

create index link_m_area1_kind_index
    on public.link_m_area1 (kind);

create index link_m_area1_geom_index
    on public.link_m_area1 using gist (geom);

create index link_m_area1_link_id_index
    on public.link_m_area1 (link_id);

create table public.link_m_area2
(
    hll_linkid  varchar(256) not null
        constraint link_m_area2_pk
            primary key,
    link_id     varchar(128) not null,
    hll_s_nid   varchar(256) not null,
    hll_e_nid   varchar(256) not null,
    kind        varchar(2)       default NULL::character varying,
    formway     varchar(128)     default 1,
    dir         varchar(2)       default NULL::character varying,
    app         varchar(2)       default NULL::character varying,
    toll        varchar(2)       default NULL::character varying,
    adopt       varchar(2)       default NULL::character varying,
    md          varchar(1)       default NULL::character varying,
    divider     varchar(1)       default NULL::character varying,
    divider_leg varchar(1)       default NULL::character varying,
    devs        varchar(1)       default NULL::character varying,
    spet        varchar(1)       default NULL::character varying,
    funct       varchar(2)       default NULL::character varying,
    urban       varchar(1)       default NULL::character varying,
    pave        varchar(1)       default NULL::character varying,
    lane_n      integer          default 0,
    lane_l      integer          default 0,
    lane_r      integer          default 0,
    lane_c      varchar(2)       default NULL::character varying,
    width       varchar(8)       default NULL::character varying,
    viad        varchar(1)       default NULL::character varying,
    l_admin     varchar(10)  not null,
    r_admin     varchar(10)  not null,
    geom        geometry(LineString, 4326),
    len         double precision default 0.0,
    f_speed     varchar(100),
    t_speed     varchar(100),
    sp_class    varchar(100),
    ar_veh      varchar(35),
    dici_type   varchar(1)       default NULL::character varying,
    verifyflag  varchar(1)       default NULL::character varying,
    pre_launch  varchar(256),
    name_ch_o   varchar(1000),
    name_ch_a   varchar(1000),
    name_ch_f   varchar(1000),
    name_ph_o   varchar(1000),
    name_ph_a   varchar(1000),
    name_ph_f   varchar(1000),
    name_en_o   varchar(1000),
    name_en_a   varchar(1000),
    name_en_f   varchar(1000),
    name_po     varchar(1000),
    name_cht    varchar(1000),
    code_type   varchar(2)       default NULL::character varying,
    name_type   varchar(2)       default 0,
    src_flag    varchar(2)       default NULL::character varying,
    mesh_id     varchar(8),
    memo        varchar(1000),
    cp          varchar(32),
    datasource  varchar(256),
    up_date     timestamp    not null,
    status      integer          default 0,
    geomwkt     varchar,
    pub_access  char,
    t_admin     varchar,
    time_zone   varchar
);

alter table public.link_m_area2
    owner to postgres;

create index link_m_area2_kind_index
    on public.link_m_area2 (kind);

create index link_m_area2_geom_index
    on public.link_m_area2 using gist (geom);

create index link_m_area2_link_id_index
    on public.link_m_area2 (link_id);

create table public.link_m_area3
(
    hll_linkid  varchar(256) not null
        constraint link_m_area3_pk
            primary key,
    link_id     varchar(128) not null,
    hll_s_nid   varchar(256) not null,
    hll_e_nid   varchar(256) not null,
    kind        varchar(2)       default NULL::character varying,
    formway     varchar(128)     default 1,
    dir         varchar(2)       default NULL::character varying,
    app         varchar(2)       default NULL::character varying,
    toll        varchar(2)       default NULL::character varying,
    adopt       varchar(2)       default NULL::character varying,
    md          varchar(1)       default NULL::character varying,
    divider     varchar(1)       default NULL::character varying,
    divider_leg varchar(1)       default NULL::character varying,
    devs        varchar(1)       default NULL::character varying,
    spet        varchar(1)       default NULL::character varying,
    funct       varchar(2)       default NULL::character varying,
    urban       varchar(1)       default NULL::character varying,
    pave        varchar(1)       default NULL::character varying,
    lane_n      integer          default 0,
    lane_l      integer          default 0,
    lane_r      integer          default 0,
    lane_c      varchar(2)       default NULL::character varying,
    width       varchar(8)       default NULL::character varying,
    viad        varchar(1)       default NULL::character varying,
    l_admin     varchar(10)  not null,
    r_admin     varchar(10)  not null,
    geom        geometry(LineString, 4326),
    len         double precision default 0.0,
    f_speed     varchar(100),
    t_speed     varchar(100),
    sp_class    varchar(100),
    ar_veh      varchar(35),
    dici_type   varchar(1)       default NULL::character varying,
    verifyflag  varchar(1)       default NULL::character varying,
    pre_launch  varchar(256),
    name_ch_o   varchar(1000),
    name_ch_a   varchar(1000),
    name_ch_f   varchar(1000),
    name_ph_o   varchar(1000),
    name_ph_a   varchar(1000),
    name_ph_f   varchar(1000),
    name_en_o   varchar(1000),
    name_en_a   varchar(1000),
    name_en_f   varchar(1000),
    name_po     varchar(1000),
    name_cht    varchar(1000),
    code_type   varchar(2)       default NULL::character varying,
    name_type   varchar(2)       default 0,
    src_flag    varchar(2)       default NULL::character varying,
    mesh_id     varchar(8),
    memo        varchar(1000),
    cp          varchar(32),
    datasource  varchar(256),
    up_date     timestamp    not null,
    status      integer          default 0,
    geomwkt     varchar,
    pub_access  char,
    t_admin     varchar,
    time_zone   varchar
);

alter table public.link_m_area3
    owner to postgres;

create index link_m_area3_kind_index
    on public.link_m_area3 (kind);

create index link_m_area3_geom_index
    on public.link_m_area3 using gist (geom);

create index link_m_area3_link_id_index
    on public.link_m_area3 (link_id);

create table public.link_m_area4
(
    hll_linkid  varchar(256) not null
        constraint link_m_area4_pk
            primary key,
    link_id     varchar(128) not null,
    hll_s_nid   varchar(256) not null,
    hll_e_nid   varchar(256) not null,
    kind        varchar(2)       default NULL::character varying,
    formway     varchar(128)     default 1,
    dir         varchar(2)       default NULL::character varying,
    app         varchar(2)       default NULL::character varying,
    toll        varchar(2)       default NULL::character varying,
    adopt       varchar(2)       default NULL::character varying,
    md          varchar(1)       default NULL::character varying,
    divider     varchar(1)       default NULL::character varying,
    divider_leg varchar(1)       default NULL::character varying,
    devs        varchar(1)       default NULL::character varying,
    spet        varchar(1)       default NULL::character varying,
    funct       varchar(2)       default NULL::character varying,
    urban       varchar(1)       default NULL::character varying,
    pave        varchar(1)       default NULL::character varying,
    lane_n      integer          default 0,
    lane_l      integer          default 0,
    lane_r      integer          default 0,
    lane_c      varchar(2)       default NULL::character varying,
    width       varchar(8)       default NULL::character varying,
    viad        varchar(1)       default NULL::character varying,
    l_admin     varchar(10)  not null,
    r_admin     varchar(10)  not null,
    geom        geometry(LineString, 4326),
    len         double precision default 0.0,
    f_speed     varchar(100),
    t_speed     varchar(100),
    sp_class    varchar(100),
    ar_veh      varchar(35),
    dici_type   varchar(1)       default NULL::character varying,
    verifyflag  varchar(1)       default NULL::character varying,
    pre_launch  varchar(256),
    name_ch_o   varchar(1000),
    name_ch_a   varchar(1000),
    name_ch_f   varchar(1000),
    name_ph_o   varchar(1000),
    name_ph_a   varchar(1000),
    name_ph_f   varchar(1000),
    name_en_o   varchar(1000),
    name_en_a   varchar(1000),
    name_en_f   varchar(1000),
    name_po     varchar(1000),
    name_cht    varchar(1000),
    code_type   varchar(2)       default NULL::character varying,
    name_type   varchar(2)       default 0,
    src_flag    varchar(2)       default NULL::character varying,
    mesh_id     varchar(8),
    memo        varchar(1000),
    cp          varchar(32),
    datasource  varchar(256),
    up_date     timestamp    not null,
    status      integer          default 0,
    geomwkt     varchar,
    pub_access  char,
    t_admin     varchar,
    time_zone   varchar
);

alter table public.link_m_area4
    owner to postgres;

create index link_m_area4_kind_index
    on public.link_m_area4 (kind);

create index link_m_area4_geom_index
    on public.link_m_area4 using gist (geom);

create index link_m_area4_link_id_index
    on public.link_m_area4 (link_id);

create table public.link_m_area5
(
    hll_linkid  varchar(256) not null
        constraint link_m_area5_pk
            primary key,
    link_id     varchar(128) not null,
    hll_s_nid   varchar(256) not null,
    hll_e_nid   varchar(256) not null,
    kind        varchar(2)       default NULL::character varying,
    formway     varchar(128)     default 1,
    dir         varchar(2)       default NULL::character varying,
    app         varchar(2)       default NULL::character varying,
    toll        varchar(2)       default NULL::character varying,
    adopt       varchar(2)       default NULL::character varying,
    md          varchar(1)       default NULL::character varying,
    divider     varchar(1)       default NULL::character varying,
    divider_leg varchar(1)       default NULL::character varying,
    devs        varchar(1)       default NULL::character varying,
    spet        varchar(1)       default NULL::character varying,
    funct       varchar(2)       default NULL::character varying,
    urban       varchar(1)       default NULL::character varying,
    pave        varchar(1)       default NULL::character varying,
    lane_n      integer          default 0,
    lane_l      integer          default 0,
    lane_r      integer          default 0,
    lane_c      varchar(2)       default NULL::character varying,
    width       varchar(8)       default NULL::character varying,
    viad        varchar(1)       default NULL::character varying,
    l_admin     varchar(10)  not null,
    r_admin     varchar(10)  not null,
    geom        geometry(LineString, 4326),
    len         double precision default 0.0,
    f_speed     varchar(100),
    t_speed     varchar(100),
    sp_class    varchar(100),
    ar_veh      varchar(35),
    dici_type   varchar(1)       default NULL::character varying,
    verifyflag  varchar(1)       default NULL::character varying,
    pre_launch  varchar(256),
    name_ch_o   varchar(1000),
    name_ch_a   varchar(1000),
    name_ch_f   varchar(1000),
    name_ph_o   varchar(1000),
    name_ph_a   varchar(1000),
    name_ph_f   varchar(1000),
    name_en_o   varchar(1000),
    name_en_a   varchar(1000),
    name_en_f   varchar(1000),
    name_po     varchar(1000),
    name_cht    varchar(1000),
    code_type   varchar(2)       default NULL::character varying,
    name_type   varchar(2)       default 0,
    src_flag    varchar(2)       default NULL::character varying,
    mesh_id     varchar(8),
    memo        varchar(1000),
    cp          varchar(32),
    datasource  varchar(256),
    up_date     timestamp    not null,
    status      integer          default 0,
    geomwkt     varchar,
    pub_access  char,
    t_admin     varchar,
    time_zone   varchar
);

alter table public.link_m_area5
    owner to postgres;

create index link_m_area5_kind_index
    on public.link_m_area5 (kind);

create index link_m_area5_geom_index
    on public.link_m_area5 using gist (geom);

create index link_m_area5_link_id_index
    on public.link_m_area5 (link_id);

create table public.link_m_area6
(
    hll_linkid  varchar(256) not null
        constraint link_m_area6_pk
            primary key,
    link_id     varchar(128) not null,
    hll_s_nid   varchar(256) not null,
    hll_e_nid   varchar(256) not null,
    kind        varchar(2)       default NULL::character varying,
    formway     varchar(128)     default 1,
    dir         varchar(2)       default NULL::character varying,
    app         varchar(2)       default NULL::character varying,
    toll        varchar(2)       default NULL::character varying,
    adopt       varchar(2)       default NULL::character varying,
    md          varchar(1)       default NULL::character varying,
    divider     varchar(1)       default NULL::character varying,
    divider_leg varchar(1)       default NULL::character varying,
    devs        varchar(1)       default NULL::character varying,
    spet        varchar(1)       default NULL::character varying,
    funct       varchar(2)       default NULL::character varying,
    urban       varchar(1)       default NULL::character varying,
    pave        varchar(1)       default NULL::character varying,
    lane_n      integer          default 0,
    lane_l      integer          default 0,
    lane_r      integer          default 0,
    lane_c      varchar(2)       default NULL::character varying,
    width       varchar(8)       default NULL::character varying,
    viad        varchar(1)       default NULL::character varying,
    l_admin     varchar(10)  not null,
    r_admin     varchar(10)  not null,
    geom        geometry(LineString, 4326),
    len         double precision default 0.0,
    f_speed     varchar(100),
    t_speed     varchar(100),
    sp_class    varchar(100),
    ar_veh      varchar(35),
    dici_type   varchar(1)       default NULL::character varying,
    verifyflag  varchar(1)       default NULL::character varying,
    pre_launch  varchar(256),
    name_ch_o   varchar(1000),
    name_ch_a   varchar(1000),
    name_ch_f   varchar(1000),
    name_ph_o   varchar(1000),
    name_ph_a   varchar(1000),
    name_ph_f   varchar(1000),
    name_en_o   varchar(1000),
    name_en_a   varchar(1000),
    name_en_f   varchar(1000),
    name_po     varchar(1000),
    name_cht    varchar(1000),
    code_type   varchar(2)       default NULL::character varying,
    name_type   varchar(2)       default 0,
    src_flag    varchar(2)       default NULL::character varying,
    mesh_id     varchar(8),
    memo        varchar(1000),
    cp          varchar(32),
    datasource  varchar(256),
    up_date     timestamp    not null,
    status      integer          default 0,
    geomwkt     varchar,
    pub_access  char,
    t_admin     varchar,
    time_zone   varchar
);

alter table public.link_m_area6
    owner to postgres;

create index link_m_area6_kind_index
    on public.link_m_area6 (kind);

create index link_m_area6_geom_index
    on public.link_m_area6 using gist (geom);

create index link_m_area6_link_id_index
    on public.link_m_area6 (link_id);

create table public.link_area1
(
    hll_linkid  varchar(256) not null
        primary key,
    id          varchar(128) not null,
    hll_s_nid   varchar(256) not null,
    hll_e_nid   varchar(256) not null,
    kind        varchar(2)       default NULL::character varying,
    formway     varchar(128)     default 1,
    direction   varchar(2)       default NULL::character varying,
    const_st    varchar(2)       default NULL::character varying,
    toll        varchar(2)       default NULL::character varying,
    adopt       varchar(2)       default NULL::character varying,
    md          varchar(1)       default NULL::character varying,
    divider     varchar(1)       default NULL::character varying,
    divider_leg varchar(1)       default NULL::character varying,
    detailcity  varchar(1)       default NULL::character varying,
    special     varchar(1)       default NULL::character varying,
    funcclass   varchar(2)       default NULL::character varying,
    uflag       varchar(1)       default NULL::character varying,
    road_cond   varchar(1)       default NULL::character varying,
    lanenumsum  integer          default 0,
    lanenums2e  integer          default 0,
    lanenume2s  integer          default 0,
    lanenumc    varchar(2)       default NULL::character varying,
    width       varchar(8)       default NULL::character varying,
    elevated    varchar(1)       default NULL::character varying,
    admincodel  varchar(10)  not null,
    admincoder  varchar(10)  not null,
    geom        geometry,
    len         double precision default 0.0,
    spdlmts2e   varchar(100),
    spdlmte2s   varchar(100),
    speedclass  varchar(100),
    dc_type     varchar(1)       default NULL::character varying,
    verifyflag  varchar(1)       default NULL::character varying,
    pre_launch  varchar(256),
    name_ch_o   varchar(1000),
    name_ch_a   varchar(1000),
    name_ch_f   varchar(1000),
    name_ph_o   varchar(1000),
    name_ph_a   varchar(1000),
    name_ph_f   varchar(1000),
    name_en_o   varchar(1000),
    name_en_a   varchar(1000),
    name_en_f   varchar(1000),
    name_po     varchar(1000),
    name_cht    varchar(1000),
    code_type   varchar(2)       default NULL::character varying,
    name_type   varchar(2)       default 0,
    src_flag    varchar(2)       default NULL::character varying,
    mesh_id     varchar(8),
    memo        varchar(1000),
    cp          varchar(32),
    datasource  varchar(256),
    up_date     timestamp(6) not null,
    status      integer          default 0,
    geomwkt     varchar,
    ar_veh      varchar(35),
    pub_access  char,
    t_admin     varchar(128),
    time_zone   varchar(128)
);

alter table public.link_area1
    owner to postgres;

create index kind_area1_index
    on public.link_area1 (kind);

create index link_area1_geom_index
    on public.link_area1 using gist (geom);

create index idx_link_hll_e_nid_area1
    on public.link_area1 (hll_e_nid);

create index idx_link_hll_s_nid_area1
    on public.link_area1 (hll_s_nid);

create table public.link_area2
(
    hll_linkid  varchar(256) not null
        primary key,
    id          varchar(128) not null,
    hll_s_nid   varchar(256) not null,
    hll_e_nid   varchar(256) not null,
    kind        varchar(2)       default NULL::character varying,
    formway     varchar(128)     default 1,
    direction   varchar(2)       default NULL::character varying,
    const_st    varchar(2)       default NULL::character varying,
    toll        varchar(2)       default NULL::character varying,
    adopt       varchar(2)       default NULL::character varying,
    md          varchar(1)       default NULL::character varying,
    divider     varchar(1)       default NULL::character varying,
    divider_leg varchar(1)       default NULL::character varying,
    detailcity  varchar(1)       default NULL::character varying,
    special     varchar(1)       default NULL::character varying,
    funcclass   varchar(2)       default NULL::character varying,
    uflag       varchar(1)       default NULL::character varying,
    road_cond   varchar(1)       default NULL::character varying,
    lanenumsum  integer          default 0,
    lanenums2e  integer          default 0,
    lanenume2s  integer          default 0,
    lanenumc    varchar(2)       default NULL::character varying,
    width       varchar(8)       default NULL::character varying,
    elevated    varchar(1)       default NULL::character varying,
    admincodel  varchar(10)  not null,
    admincoder  varchar(10)  not null,
    geom        geometry,
    len         double precision default 0.0,
    spdlmts2e   varchar(100),
    spdlmte2s   varchar(100),
    speedclass  varchar(100),
    dc_type     varchar(1)       default NULL::character varying,
    verifyflag  varchar(1)       default NULL::character varying,
    pre_launch  varchar(256),
    name_ch_o   varchar(1000),
    name_ch_a   varchar(1000),
    name_ch_f   varchar(1000),
    name_ph_o   varchar(1000),
    name_ph_a   varchar(1000),
    name_ph_f   varchar(1000),
    name_en_o   varchar(1000),
    name_en_a   varchar(1000),
    name_en_f   varchar(1000),
    name_po     varchar(1000),
    name_cht    varchar(1000),
    code_type   varchar(2)       default NULL::character varying,
    name_type   varchar(2)       default 0,
    src_flag    varchar(2)       default NULL::character varying,
    mesh_id     varchar(8),
    memo        varchar(1000),
    cp          varchar(32),
    datasource  varchar(256),
    up_date     timestamp(6) not null,
    status      integer          default 0,
    geomwkt     varchar,
    ar_veh      varchar(35),
    pub_access  char,
    t_admin     varchar(128),
    time_zone   varchar(128)
);

alter table public.link_area2
    owner to postgres;

create index kind_area2_index
    on public.link_area2 (kind);

create index link_area2_geom_index
    on public.link_area2 using gist (geom);

create index idx_link_hll_e_nid_area2
    on public.link_area2 (hll_e_nid);

create index idx_link_hll_s_nid_area2
    on public.link_area2 (hll_s_nid);

create table public.link_area3
(
    hll_linkid  varchar(256) not null
        primary key,
    id          varchar(128) not null,
    hll_s_nid   varchar(256) not null,
    hll_e_nid   varchar(256) not null,
    kind        varchar(2)       default NULL::character varying,
    formway     varchar(128)     default 1,
    direction   varchar(2)       default NULL::character varying,
    const_st    varchar(2)       default NULL::character varying,
    toll        varchar(2)       default NULL::character varying,
    adopt       varchar(2)       default NULL::character varying,
    md          varchar(1)       default NULL::character varying,
    divider     varchar(1)       default NULL::character varying,
    divider_leg varchar(1)       default NULL::character varying,
    detailcity  varchar(1)       default NULL::character varying,
    special     varchar(1)       default NULL::character varying,
    funcclass   varchar(2)       default NULL::character varying,
    uflag       varchar(1)       default NULL::character varying,
    road_cond   varchar(1)       default NULL::character varying,
    lanenumsum  integer          default 0,
    lanenums2e  integer          default 0,
    lanenume2s  integer          default 0,
    lanenumc    varchar(2)       default NULL::character varying,
    width       varchar(8)       default NULL::character varying,
    elevated    varchar(1)       default NULL::character varying,
    admincodel  varchar(10)  not null,
    admincoder  varchar(10)  not null,
    geom        geometry,
    len         double precision default 0.0,
    spdlmts2e   varchar(100),
    spdlmte2s   varchar(100),
    speedclass  varchar(100),
    dc_type     varchar(1)       default NULL::character varying,
    verifyflag  varchar(1)       default NULL::character varying,
    pre_launch  varchar(256),
    name_ch_o   varchar(1000),
    name_ch_a   varchar(1000),
    name_ch_f   varchar(1000),
    name_ph_o   varchar(1000),
    name_ph_a   varchar(1000),
    name_ph_f   varchar(1000),
    name_en_o   varchar(1000),
    name_en_a   varchar(1000),
    name_en_f   varchar(1000),
    name_po     varchar(1000),
    name_cht    varchar(1000),
    code_type   varchar(2)       default NULL::character varying,
    name_type   varchar(2)       default 0,
    src_flag    varchar(2)       default NULL::character varying,
    mesh_id     varchar(8),
    memo        varchar(1000),
    cp          varchar(32),
    datasource  varchar(256),
    up_date     timestamp(6) not null,
    status      integer          default 0,
    geomwkt     varchar,
    ar_veh      varchar(35),
    pub_access  char,
    t_admin     varchar(128),
    time_zone   varchar(128)
);

alter table public.link_area3
    owner to postgres;

create index kind_area3_index
    on public.link_area3 (kind);

create index link_area3_geom_index
    on public.link_area3 using gist (geom);

create index idx_link_hll_e_nid_area3
    on public.link_area3 (hll_e_nid);

create index idx_link_hll_s_nid_area3
    on public.link_area3 (hll_s_nid);

create table public.link_area4
(
    hll_linkid  varchar(256) not null
        primary key,
    id          varchar(128) not null,
    hll_s_nid   varchar(256) not null,
    hll_e_nid   varchar(256) not null,
    kind        varchar(2)       default NULL::character varying,
    formway     varchar(128)     default 1,
    direction   varchar(2)       default NULL::character varying,
    const_st    varchar(2)       default NULL::character varying,
    toll        varchar(2)       default NULL::character varying,
    adopt       varchar(2)       default NULL::character varying,
    md          varchar(1)       default NULL::character varying,
    divider     varchar(1)       default NULL::character varying,
    divider_leg varchar(1)       default NULL::character varying,
    detailcity  varchar(1)       default NULL::character varying,
    special     varchar(1)       default NULL::character varying,
    funcclass   varchar(2)       default NULL::character varying,
    uflag       varchar(1)       default NULL::character varying,
    road_cond   varchar(1)       default NULL::character varying,
    lanenumsum  integer          default 0,
    lanenums2e  integer          default 0,
    lanenume2s  integer          default 0,
    lanenumc    varchar(2)       default NULL::character varying,
    width       varchar(8)       default NULL::character varying,
    elevated    varchar(1)       default NULL::character varying,
    admincodel  varchar(10)  not null,
    admincoder  varchar(10)  not null,
    geom        geometry,
    len         double precision default 0.0,
    spdlmts2e   varchar(100),
    spdlmte2s   varchar(100),
    speedclass  varchar(100),
    dc_type     varchar(1)       default NULL::character varying,
    verifyflag  varchar(1)       default NULL::character varying,
    pre_launch  varchar(256),
    name_ch_o   varchar(1000),
    name_ch_a   varchar(1000),
    name_ch_f   varchar(1000),
    name_ph_o   varchar(1000),
    name_ph_a   varchar(1000),
    name_ph_f   varchar(1000),
    name_en_o   varchar(1000),
    name_en_a   varchar(1000),
    name_en_f   varchar(1000),
    name_po     varchar(1000),
    name_cht    varchar(1000),
    code_type   varchar(2)       default NULL::character varying,
    name_type   varchar(2)       default 0,
    src_flag    varchar(2)       default NULL::character varying,
    mesh_id     varchar(8),
    memo        varchar(1000),
    cp          varchar(32),
    datasource  varchar(256),
    up_date     timestamp(6) not null,
    status      integer          default 0,
    geomwkt     varchar,
    ar_veh      varchar(35),
    pub_access  char,
    t_admin     varchar(128),
    time_zone   varchar(128)
);

alter table public.link_area4
    owner to postgres;

create index kind_area4_index
    on public.link_area4 (kind);

create index link_area4_geom_index
    on public.link_area4 using gist (geom);

create index idx_link_hll_e_nid_area4
    on public.link_area4 (hll_e_nid);

create index idx_link_hll_s_nid_area4
    on public.link_area4 (hll_s_nid);

create table public.link_area5
(
    hll_linkid  varchar(256) not null
        primary key,
    id          varchar(128) not null,
    hll_s_nid   varchar(256) not null,
    hll_e_nid   varchar(256) not null,
    kind        varchar(2)       default NULL::character varying,
    formway     varchar(128)     default 1,
    direction   varchar(2)       default NULL::character varying,
    const_st    varchar(2)       default NULL::character varying,
    toll        varchar(2)       default NULL::character varying,
    adopt       varchar(2)       default NULL::character varying,
    md          varchar(1)       default NULL::character varying,
    divider     varchar(1)       default NULL::character varying,
    divider_leg varchar(1)       default NULL::character varying,
    detailcity  varchar(1)       default NULL::character varying,
    special     varchar(1)       default NULL::character varying,
    funcclass   varchar(2)       default NULL::character varying,
    uflag       varchar(1)       default NULL::character varying,
    road_cond   varchar(1)       default NULL::character varying,
    lanenumsum  integer          default 0,
    lanenums2e  integer          default 0,
    lanenume2s  integer          default 0,
    lanenumc    varchar(2)       default NULL::character varying,
    width       varchar(8)       default NULL::character varying,
    elevated    varchar(1)       default NULL::character varying,
    admincodel  varchar(10)  not null,
    admincoder  varchar(10)  not null,
    geom        geometry,
    len         double precision default 0.0,
    spdlmts2e   varchar(100),
    spdlmte2s   varchar(100),
    speedclass  varchar(100),
    dc_type     varchar(1)       default NULL::character varying,
    verifyflag  varchar(1)       default NULL::character varying,
    pre_launch  varchar(256),
    name_ch_o   varchar(1000),
    name_ch_a   varchar(1000),
    name_ch_f   varchar(1000),
    name_ph_o   varchar(1000),
    name_ph_a   varchar(1000),
    name_ph_f   varchar(1000),
    name_en_o   varchar(1000),
    name_en_a   varchar(1000),
    name_en_f   varchar(1000),
    name_po     varchar(1000),
    name_cht    varchar(1000),
    code_type   varchar(2)       default NULL::character varying,
    name_type   varchar(2)       default 0,
    src_flag    varchar(2)       default NULL::character varying,
    mesh_id     varchar(8),
    memo        varchar(1000),
    cp          varchar(32),
    datasource  varchar(256),
    up_date     timestamp(6) not null,
    status      integer          default 0,
    geomwkt     varchar,
    ar_veh      varchar(35),
    pub_access  char,
    t_admin     varchar(128),
    time_zone   varchar(128)
);

alter table public.link_area5
    owner to postgres;

create index kind_area5_index
    on public.link_area5 (kind);

create index link_area5_geom_index
    on public.link_area5 using gist (geom);

create index idx_link_hll_e_nid_area5
    on public.link_area5 (hll_e_nid);

create index idx_link_hll_s_nid_area5
    on public.link_area5 (hll_s_nid);

create table public.link_area6
(
    hll_linkid  varchar(256) not null
        primary key,
    id          varchar(128) not null,
    hll_s_nid   varchar(256) not null,
    hll_e_nid   varchar(256) not null,
    kind        varchar(2)       default NULL::character varying,
    formway     varchar(128)     default 1,
    direction   varchar(2)       default NULL::character varying,
    const_st    varchar(2)       default NULL::character varying,
    toll        varchar(2)       default NULL::character varying,
    adopt       varchar(2)       default NULL::character varying,
    md          varchar(1)       default NULL::character varying,
    divider     varchar(1)       default NULL::character varying,
    divider_leg varchar(1)       default NULL::character varying,
    detailcity  varchar(1)       default NULL::character varying,
    special     varchar(1)       default NULL::character varying,
    funcclass   varchar(2)       default NULL::character varying,
    uflag       varchar(1)       default NULL::character varying,
    road_cond   varchar(1)       default NULL::character varying,
    lanenumsum  integer          default 0,
    lanenums2e  integer          default 0,
    lanenume2s  integer          default 0,
    lanenumc    varchar(2)       default NULL::character varying,
    width       varchar(8)       default NULL::character varying,
    elevated    varchar(1)       default NULL::character varying,
    admincodel  varchar(10)  not null,
    admincoder  varchar(10)  not null,
    geom        geometry,
    len         double precision default 0.0,
    spdlmts2e   varchar(100),
    spdlmte2s   varchar(100),
    speedclass  varchar(100),
    dc_type     varchar(1)       default NULL::character varying,
    verifyflag  varchar(1)       default NULL::character varying,
    pre_launch  varchar(256),
    name_ch_o   varchar(1000),
    name_ch_a   varchar(1000),
    name_ch_f   varchar(1000),
    name_ph_o   varchar(1000),
    name_ph_a   varchar(1000),
    name_ph_f   varchar(1000),
    name_en_o   varchar(1000),
    name_en_a   varchar(1000),
    name_en_f   varchar(1000),
    name_po     varchar(1000),
    name_cht    varchar(1000),
    code_type   varchar(2)       default NULL::character varying,
    name_type   varchar(2)       default 0,
    src_flag    varchar(2)       default NULL::character varying,
    mesh_id     varchar(8),
    memo        varchar(1000),
    cp          varchar(32),
    datasource  varchar(256),
    up_date     timestamp(6) not null,
    status      integer          default 0,
    geomwkt     varchar,
    ar_veh      varchar(35),
    pub_access  char,
    t_admin     varchar(128),
    time_zone   varchar(128)
);

alter table public.link_area6
    owner to postgres;

create index kind_area6_index
    on public.link_area6 (kind);

create index link_area6_geom_index
    on public.link_area6 using gist (geom);

create index idx_link_hll_e_nid_area6
    on public.link_area6 (hll_e_nid);

create index idx_link_hll_s_nid_area6
    on public.link_area6 (hll_s_nid);

create table public.node_m_area1
(
    hll_nodeid varchar(256) not null
        constraint node_m_area1_pk
            primary key,
    kind       varchar(128),
    geom       geometry     not null,
    name_ch    varchar(128),
    name_fo    varchar(256),
    name_cht   varchar(1000),
    name_ph    varchar(1000),
    adjoin_mid varchar(6),
    adjoin_nid varchar(128),
    type       varchar(1)    default NULL::character varying,
    mainnodeid varchar(256)  default NULL::character varying,
    subnodeid  varchar(1000) default NULL::character varying,
    subnodeid2 varchar(512),
    light      varchar(1)    default NULL::character varying,
    is_pbnode  varchar(1)    default NULL::character varying,
    cp         varchar(32),
    datasource varchar(256),
    node_id    varchar(128) not null,
    up_date    timestamp(6) not null,
    memo       varchar(1000),
    status     integer       default 0,
    geom_wkt   varchar
);

alter table public.node_m_area1
    owner to postgres;

create index node_m_area1_node_id
    on public.node_m_area1 (node_id);

create table public.node_m_area2
(
    hll_nodeid varchar(256) not null
        constraint node_m_area2_pk
            primary key,
    kind       varchar(128),
    geom       geometry     not null,
    name_ch    varchar(128),
    name_fo    varchar(256),
    name_cht   varchar(1000),
    name_ph    varchar(1000),
    adjoin_mid varchar(6),
    adjoin_nid varchar(128),
    type       varchar(1)    default NULL::character varying,
    mainnodeid varchar(256)  default NULL::character varying,
    subnodeid  varchar(1000) default NULL::character varying,
    subnodeid2 varchar(512),
    light      varchar(1)    default NULL::character varying,
    is_pbnode  varchar(1)    default NULL::character varying,
    cp         varchar(32),
    datasource varchar(256),
    node_id    varchar(128) not null,
    up_date    timestamp(6) not null,
    memo       varchar(1000),
    status     integer       default 0,
    geom_wkt   varchar
);

alter table public.node_m_area2
    owner to postgres;

create index node_m_area2_node_id
    on public.node_m_area2 (node_id);

create table public.node_m_area3
(
    hll_nodeid varchar(256) not null
        constraint node_m_area3_pk
            primary key,
    kind       varchar(128),
    geom       geometry     not null,
    name_ch    varchar(128),
    name_fo    varchar(256),
    name_cht   varchar(1000),
    name_ph    varchar(1000),
    adjoin_mid varchar(6),
    adjoin_nid varchar(128),
    type       varchar(1)    default NULL::character varying,
    mainnodeid varchar(256)  default NULL::character varying,
    subnodeid  varchar(1000) default NULL::character varying,
    subnodeid2 varchar(512),
    light      varchar(1)    default NULL::character varying,
    is_pbnode  varchar(1)    default NULL::character varying,
    cp         varchar(32),
    datasource varchar(256),
    node_id    varchar(128) not null,
    up_date    timestamp(6) not null,
    memo       varchar(1000),
    status     integer       default 0,
    geom_wkt   varchar
);

alter table public.node_m_area3
    owner to postgres;

create index node_m_area3_node_id
    on public.node_m_area3 (node_id);

create table public.node_m_area4
(
    hll_nodeid varchar(256) not null
        constraint node_m_area4_pk
            primary key,
    kind       varchar(128),
    geom       geometry     not null,
    name_ch    varchar(128),
    name_fo    varchar(256),
    name_cht   varchar(1000),
    name_ph    varchar(1000),
    adjoin_mid varchar(6),
    adjoin_nid varchar(128),
    type       varchar(1)    default NULL::character varying,
    mainnodeid varchar(256)  default NULL::character varying,
    subnodeid  varchar(1000) default NULL::character varying,
    subnodeid2 varchar(512),
    light      varchar(1)    default NULL::character varying,
    is_pbnode  varchar(1)    default NULL::character varying,
    cp         varchar(32),
    datasource varchar(256),
    node_id    varchar(128) not null,
    up_date    timestamp(6) not null,
    memo       varchar(1000),
    status     integer       default 0,
    geom_wkt   varchar
);

alter table public.node_m_area4
    owner to postgres;

create index node_m_area4_node_id
    on public.node_m_area4 (node_id);

create table public.node_m_area5
(
    hll_nodeid varchar(256) not null
        constraint node_m_area5_pk
            primary key,
    kind       varchar(128),
    geom       geometry     not null,
    name_ch    varchar(128),
    name_fo    varchar(256),
    name_cht   varchar(1000),
    name_ph    varchar(1000),
    adjoin_mid varchar(6),
    adjoin_nid varchar(128),
    type       varchar(1)    default NULL::character varying,
    mainnodeid varchar(256)  default NULL::character varying,
    subnodeid  varchar(1000) default NULL::character varying,
    subnodeid2 varchar(512),
    light      varchar(1)    default NULL::character varying,
    is_pbnode  varchar(1)    default NULL::character varying,
    cp         varchar(32),
    datasource varchar(256),
    node_id    varchar(128) not null,
    up_date    timestamp(6) not null,
    memo       varchar(1000),
    status     integer       default 0,
    geom_wkt   varchar
);

alter table public.node_m_area5
    owner to postgres;

create index node_m_area5_node_id
    on public.node_m_area5 (node_id);

create table public.node_m_area6
(
    hll_nodeid varchar(256) not null
        constraint node_m_area6_pk
            primary key,
    kind       varchar(128),
    geom       geometry     not null,
    name_ch    varchar(128),
    name_fo    varchar(256),
    name_cht   varchar(1000),
    name_ph    varchar(1000),
    adjoin_mid varchar(6),
    adjoin_nid varchar(128),
    type       varchar(1)    default NULL::character varying,
    mainnodeid varchar(256)  default NULL::character varying,
    subnodeid  varchar(1000) default NULL::character varying,
    subnodeid2 varchar(512),
    light      varchar(1)    default NULL::character varying,
    is_pbnode  varchar(1)    default NULL::character varying,
    cp         varchar(32),
    datasource varchar(256),
    node_id    varchar(128) not null,
    up_date    timestamp(6) not null,
    memo       varchar(1000),
    status     integer       default 0,
    geom_wkt   varchar
);

alter table public.node_m_area6
    owner to postgres;

create index node_m_area6_node_id
    on public.node_m_area6 (node_id);

create table public.node_area1
(
    hll_nodeid varchar(256) not null
        constraint node_area1_pkey1
            primary key,
    kind       varchar(128),
    geom       geometry     not null,
    name_ch    varchar(128),
    name_fo    varchar(256),
    name_cht   varchar(1000),
    name_ph    varchar(1000),
    adjoin_mid varchar(6),
    adjoin_nid varchar(128),
    type       varchar(1)    default NULL::character varying,
    mainnodeid varchar(256)  default NULL::character varying,
    subnodeid  varchar(1000) default NULL::character varying,
    subnodeid2 varchar(512),
    light_flag varchar(1)    default NULL::character varying,
    is_pbnode  varchar(1)    default NULL::character varying,
    cp         varchar(32),
    datasource varchar(256),
    id         varchar(128) not null,
    up_date    timestamp(6) not null,
    memo       varchar(1000),
    status     integer       default 0,
    geom_wkt   varchar
);

alter table public.node_area1
    owner to postgres;

create index idx_node_adjoin_nid_area1
    on public.node_area1 (adjoin_nid);

create index idx_node_adjoin_mid_area1
    on public.node_area1 (adjoin_mid);

create index idx_node_mainnodeid_area1
    on public.node_area1 (mainnodeid);

create index idx_node_geom_area1
    on public.node_area1 using gist (geom);

create table public.node_area2
(
    hll_nodeid varchar(256) not null
        constraint node_area2_pkey1
            primary key,
    kind       varchar(128),
    geom       geometry     not null,
    name_ch    varchar(128),
    name_fo    varchar(256),
    name_cht   varchar(1000),
    name_ph    varchar(1000),
    adjoin_mid varchar(6),
    adjoin_nid varchar(128),
    type       varchar(1)    default NULL::character varying,
    mainnodeid varchar(256)  default NULL::character varying,
    subnodeid  varchar(1000) default NULL::character varying,
    subnodeid2 varchar(512),
    light_flag varchar(1)    default NULL::character varying,
    is_pbnode  varchar(1)    default NULL::character varying,
    cp         varchar(32),
    datasource varchar(256),
    id         varchar(128) not null,
    up_date    timestamp(6) not null,
    memo       varchar(1000),
    status     integer       default 0,
    geom_wkt   varchar
);

alter table public.node_area2
    owner to postgres;

create index idx_node_adjoin_nid_area2
    on public.node_area2 (adjoin_nid);

create index idx_node_adjoin_mid_area2
    on public.node_area2 (adjoin_mid);

create index idx_node_mainnodeid_area2
    on public.node_area2 (mainnodeid);

create index idx_node_geom_area2
    on public.node_area2 using gist (geom);

create table public.node_area3
(
    hll_nodeid varchar(256) not null
        constraint node_area3_pkey1
            primary key,
    kind       varchar(128),
    geom       geometry     not null,
    name_ch    varchar(128),
    name_fo    varchar(256),
    name_cht   varchar(1000),
    name_ph    varchar(1000),
    adjoin_mid varchar(6),
    adjoin_nid varchar(128),
    type       varchar(1)    default NULL::character varying,
    mainnodeid varchar(256)  default NULL::character varying,
    subnodeid  varchar(1000) default NULL::character varying,
    subnodeid2 varchar(512),
    light_flag varchar(1)    default NULL::character varying,
    is_pbnode  varchar(1)    default NULL::character varying,
    cp         varchar(32),
    datasource varchar(256),
    id         varchar(128) not null,
    up_date    timestamp(6) not null,
    memo       varchar(1000),
    status     integer       default 0,
    geom_wkt   varchar
);

alter table public.node_area3
    owner to postgres;

create index idx_node_adjoin_nid_area3
    on public.node_area3 (adjoin_nid);

create index idx_node_adjoin_mid_area3
    on public.node_area3 (adjoin_mid);

create index idx_node_mainnodeid_area3
    on public.node_area3 (mainnodeid);

create index idx_node_geom_area3
    on public.node_area3 using gist (geom);

create table public.node_area4
(
    hll_nodeid varchar(256) not null
        constraint node_area4_pkey1
            primary key,
    kind       varchar(128),
    geom       geometry     not null,
    name_ch    varchar(128),
    name_fo    varchar(256),
    name_cht   varchar(1000),
    name_ph    varchar(1000),
    adjoin_mid varchar(6),
    adjoin_nid varchar(128),
    type       varchar(1)    default NULL::character varying,
    mainnodeid varchar(256)  default NULL::character varying,
    subnodeid  varchar(1000) default NULL::character varying,
    subnodeid2 varchar(512),
    light_flag varchar(1)    default NULL::character varying,
    is_pbnode  varchar(1)    default NULL::character varying,
    cp         varchar(32),
    datasource varchar(256),
    id         varchar(128) not null,
    up_date    timestamp(6) not null,
    memo       varchar(1000),
    status     integer       default 0,
    geom_wkt   varchar
);

alter table public.node_area4
    owner to postgres;

create index idx_node_adjoin_nid_area4
    on public.node_area4 (adjoin_nid);

create index idx_node_adjoin_mid_area4
    on public.node_area4 (adjoin_mid);

create index idx_node_mainnodeid_area4
    on public.node_area4 (mainnodeid);

create index idx_node_geom_area4
    on public.node_area4 using gist (geom);

create table public.node_area5
(
    hll_nodeid varchar(256) not null
        constraint node_area5_pkey1
            primary key,
    kind       varchar(128),
    geom       geometry     not null,
    name_ch    varchar(128),
    name_fo    varchar(256),
    name_cht   varchar(1000),
    name_ph    varchar(1000),
    adjoin_mid varchar(6),
    adjoin_nid varchar(128),
    type       varchar(1)    default NULL::character varying,
    mainnodeid varchar(256)  default NULL::character varying,
    subnodeid  varchar(1000) default NULL::character varying,
    subnodeid2 varchar(512),
    light_flag varchar(1)    default NULL::character varying,
    is_pbnode  varchar(1)    default NULL::character varying,
    cp         varchar(32),
    datasource varchar(256),
    id         varchar(128) not null,
    up_date    timestamp(6) not null,
    memo       varchar(1000),
    status     integer       default 0,
    geom_wkt   varchar
);

alter table public.node_area5
    owner to postgres;

create index idx_node_adjoin_nid_area5
    on public.node_area5 (adjoin_nid);

create index idx_node_adjoin_mid_area5
    on public.node_area5 (adjoin_mid);

create index idx_node_mainnodeid_area5
    on public.node_area5 (mainnodeid);

create index idx_node_geom_area5
    on public.node_area5 using gist (geom);

create table public.node_area6
(
    hll_nodeid varchar(256) not null
        constraint node_area6_pkey1
            primary key,
    kind       varchar(128),
    geom       geometry     not null,
    name_ch    varchar(128),
    name_fo    varchar(256),
    name_cht   varchar(1000),
    name_ph    varchar(1000),
    adjoin_mid varchar(6),
    adjoin_nid varchar(128),
    type       varchar(1)    default NULL::character varying,
    mainnodeid varchar(256)  default NULL::character varying,
    subnodeid  varchar(1000) default NULL::character varying,
    subnodeid2 varchar(512),
    light_flag varchar(1)    default NULL::character varying,
    is_pbnode  varchar(1)    default NULL::character varying,
    cp         varchar(32),
    datasource varchar(256),
    id         varchar(128) not null,
    up_date    timestamp(6) not null,
    memo       varchar(1000),
    status     integer       default 0,
    geom_wkt   varchar
);

alter table public.node_area6
    owner to postgres;

create index idx_node_adjoin_nid_area6
    on public.node_area6 (adjoin_nid);

create index idx_node_adjoin_mid_area6
    on public.node_area6 (adjoin_mid);

create index idx_node_mainnodeid_area6
    on public.node_area6 (mainnodeid);

create index idx_node_geom_area6
    on public.node_area6 using gist (geom);

create table public.poi
(
    poi_id             varchar(64),
    source_id          text not null
        primary key,
    kind_code          varchar,
    name               text,
    name_eng           text,
    alias              text,
    alias_eng          text,
    address            text,
    address_eng        text,
    longitude          numeric(15, 12),
    latitude           numeric(15, 12),
    lon_guide          numeric(15, 12),
    lat_guide          numeric(15, 12),
    link_id            varchar(32),
    side               varchar(12),
    importance         varchar(3),
    vadmin_code        varchar,
    zip_code           varchar(12),
    telephone          varchar(255),
    tel_type           varchar(50),
    poi_class          varchar(3),
    star_rating        varchar(3),
    tg_type            varchar(3),
    access_flag        varchar(3),
    gate               varchar(3),
    aoi_id             varchar(32),
    prior_auth         varchar(32),
    food_type          varchar(50),
    brand              text,
    adminlvl3_code     varchar,
    adminlvl3_name     varchar(50),
    adminlvl3_name_eng varchar(255),
    adminlvl4_code     varchar,
    adminlvl4_name     varchar(50),
    adminlvl4_name_eng text,
    adminlvl5_code     varchar,
    adminlvl5_name     varchar(50),
    adminlvl5_name_eng text,
    rank               varchar(3),
    parent             varchar,
    children           varchar,
    open_status        varchar(3),
    access_num         varchar,
    park_num           varchar,
    memo               text,
    create_time        text,
    update_time        timestamp(6),
    poi_geo            geometry(Point, 4326),
    kind               text,
    name_s             text,
    name_s_eng         varchar(255),
    adminlvl1_code     varchar(255),
    adminlvl1_name     varchar(255),
    adminlvl1_name_eng varchar(255),
    adminlvl2_code     varchar(255),
    adminlvl2_name     varchar(255),
    adminlvl2_name_eng varchar(255),
    status             integer default 0,
    language_code      varchar(255),
    name_trans         text,
    language_type      text,
    trans_language     text,
    street_name        varchar(255),
    house_number       varchar(255),
    name_s_language    text,
    alias_language     text,
    exonym             text,
    exonym_language    text,
    poi_middle_id      varchar(128),
    qualityscore       integer,
    st_langcd          varchar(256),
    st_name_eng        varchar(512)
);

alter table public.poi
    owner to postgres;

create index poi_index
    on public.poi using gist (poi_geo);

create index poi_geom_index
    on public.poi using gist (poi_geo);

create index poi_geom_check_index
    on public.poi using gist (poi_geo);

create table public.relation_area1
(
    relationid varchar(128) not null
        primary key,
    inlink_id  varchar(128),
    node_id    varchar(128) not null,
    outlink_id varchar(128),
    type       varchar(2)   not null,
    toll_type  varchar(128) default NULL::character varying,
    pass_num   varchar(2)   default NULL::character varying,
    toll_form  varchar(8)   default NULL::character varying,
    card_type  varchar(1)   default NULL::character varying,
    veh        varchar(35)  default NULL::character varying,
    name_ch    varchar(120),
    name_fo    varchar(1000),
    name_ph    varchar(1000),
    name_cht   varchar(1000),
    gate_type  varchar(1)   default NULL::character varying,
    gate_fee   varchar(1)   default NULL::character varying,
    tl_locat   varchar(3)   default NULL::character varying,
    tl_flag    varchar(1)   default NULL::character varying,
    slopetype  varchar(1)   default NULL::character varying,
    slopeangle varchar(6)   default NULL::character varying,
    memo       varchar(1000),
    mesh_id    varchar(20),
    cp         varchar(32),
    datasource varchar(256),
    up_date    timestamp(6) not null,
    status     integer      default 0
);

alter table public.relation_area1
    owner to postgres;

create index idx_relation_inlink_id_area1
    on public.relation_area1 (inlink_id);

create index idx_relation_node_id_area1
    on public.relation_area1 (node_id);

create index idx_relation_outlink_id_area1
    on public.relation_area1 (outlink_id);

create table public.relation_area2
(
    relationid varchar(128) not null
        primary key,
    inlink_id  varchar(128),
    node_id    varchar(128) not null,
    outlink_id varchar(128),
    type       varchar(2)   not null,
    toll_type  varchar(128) default NULL::character varying,
    pass_num   varchar(2)   default NULL::character varying,
    toll_form  varchar(8)   default NULL::character varying,
    card_type  varchar(1)   default NULL::character varying,
    veh        varchar(35)  default NULL::character varying,
    name_ch    varchar(120),
    name_fo    varchar(1000),
    name_ph    varchar(1000),
    name_cht   varchar(1000),
    gate_type  varchar(1)   default NULL::character varying,
    gate_fee   varchar(1)   default NULL::character varying,
    tl_locat   varchar(3)   default NULL::character varying,
    tl_flag    varchar(1)   default NULL::character varying,
    slopetype  varchar(1)   default NULL::character varying,
    slopeangle varchar(6)   default NULL::character varying,
    memo       varchar(1000),
    mesh_id    varchar(20),
    cp         varchar(32),
    datasource varchar(256),
    up_date    timestamp(6) not null,
    status     integer      default 0
);

alter table public.relation_area2
    owner to postgres;

create index idx_relation_inlink_id_area2
    on public.relation_area2 (inlink_id);

create index idx_relation_node_id_area2
    on public.relation_area2 (node_id);

create index idx_relation_outlink_id_area2
    on public.relation_area2 (outlink_id);

create table public.relation_area3
(
    relationid varchar(128) not null
        primary key,
    inlink_id  varchar(128),
    node_id    varchar(128) not null,
    outlink_id varchar(128),
    type       varchar(2)   not null,
    toll_type  varchar(128) default NULL::character varying,
    pass_num   varchar(2)   default NULL::character varying,
    toll_form  varchar(8)   default NULL::character varying,
    card_type  varchar(1)   default NULL::character varying,
    veh        varchar(35)  default NULL::character varying,
    name_ch    varchar(120),
    name_fo    varchar(1000),
    name_ph    varchar(1000),
    name_cht   varchar(1000),
    gate_type  varchar(1)   default NULL::character varying,
    gate_fee   varchar(1)   default NULL::character varying,
    tl_locat   varchar(3)   default NULL::character varying,
    tl_flag    varchar(1)   default NULL::character varying,
    slopetype  varchar(1)   default NULL::character varying,
    slopeangle varchar(6)   default NULL::character varying,
    memo       varchar(1000),
    mesh_id    varchar(20),
    cp         varchar(32),
    datasource varchar(256),
    up_date    timestamp(6) not null,
    status     integer      default 0
);

alter table public.relation_area3
    owner to postgres;

create index idx_relation_inlink_id_area3
    on public.relation_area3 (inlink_id);

create index idx_relation_node_id_area3
    on public.relation_area3 (node_id);

create index idx_relation_outlink_id_area3
    on public.relation_area3 (outlink_id);

create table public.relation_area4
(
    relationid varchar(128) not null
        primary key,
    inlink_id  varchar(128),
    node_id    varchar(128) not null,
    outlink_id varchar(128),
    type       varchar(2)   not null,
    toll_type  varchar(128) default NULL::character varying,
    pass_num   varchar(2)   default NULL::character varying,
    toll_form  varchar(8)   default NULL::character varying,
    card_type  varchar(1)   default NULL::character varying,
    veh        varchar(35)  default NULL::character varying,
    name_ch    varchar(120),
    name_fo    varchar(1000),
    name_ph    varchar(1000),
    name_cht   varchar(1000),
    gate_type  varchar(1)   default NULL::character varying,
    gate_fee   varchar(1)   default NULL::character varying,
    tl_locat   varchar(3)   default NULL::character varying,
    tl_flag    varchar(1)   default NULL::character varying,
    slopetype  varchar(1)   default NULL::character varying,
    slopeangle varchar(6)   default NULL::character varying,
    memo       varchar(1000),
    mesh_id    varchar(20),
    cp         varchar(32),
    datasource varchar(256),
    up_date    timestamp(6) not null,
    status     integer      default 0
);

alter table public.relation_area4
    owner to postgres;

create index idx_relation_inlink_id_area4
    on public.relation_area4 (inlink_id);

create index idx_relation_node_id_area4
    on public.relation_area4 (node_id);

create index idx_relation_outlink_id_area4
    on public.relation_area4 (outlink_id);

create table public.relation_area5
(
    relationid varchar(128) not null
        primary key,
    inlink_id  varchar(128),
    node_id    varchar(128) not null,
    outlink_id varchar(128),
    type       varchar(2)   not null,
    toll_type  varchar(128) default NULL::character varying,
    pass_num   varchar(2)   default NULL::character varying,
    toll_form  varchar(8)   default NULL::character varying,
    card_type  varchar(1)   default NULL::character varying,
    veh        varchar(35)  default NULL::character varying,
    name_ch    varchar(120),
    name_fo    varchar(1000),
    name_ph    varchar(1000),
    name_cht   varchar(1000),
    gate_type  varchar(1)   default NULL::character varying,
    gate_fee   varchar(1)   default NULL::character varying,
    tl_locat   varchar(3)   default NULL::character varying,
    tl_flag    varchar(1)   default NULL::character varying,
    slopetype  varchar(1)   default NULL::character varying,
    slopeangle varchar(6)   default NULL::character varying,
    memo       varchar(1000),
    mesh_id    varchar(20),
    cp         varchar(32),
    datasource varchar(256),
    up_date    timestamp(6) not null,
    status     integer      default 0
);

alter table public.relation_area5
    owner to postgres;

create index idx_relation_inlink_id_area5
    on public.relation_area5 (inlink_id);

create index idx_relation_node_id_area5
    on public.relation_area5 (node_id);

create index idx_relation_outlink_id_area5
    on public.relation_area5 (outlink_id);

create table public.relation_area6
(
    relationid varchar(128) not null
        primary key,
    inlink_id  varchar(128),
    node_id    varchar(128) not null,
    outlink_id varchar(128),
    type       varchar(2)   not null,
    toll_type  varchar(128) default NULL::character varying,
    pass_num   varchar(2)   default NULL::character varying,
    toll_form  varchar(8)   default NULL::character varying,
    card_type  varchar(1)   default NULL::character varying,
    veh        varchar(35)  default NULL::character varying,
    name_ch    varchar(120),
    name_fo    varchar(1000),
    name_ph    varchar(1000),
    name_cht   varchar(1000),
    gate_type  varchar(1)   default NULL::character varying,
    gate_fee   varchar(1)   default NULL::character varying,
    tl_locat   varchar(3)   default NULL::character varying,
    tl_flag    varchar(1)   default NULL::character varying,
    slopetype  varchar(1)   default NULL::character varying,
    slopeangle varchar(6)   default NULL::character varying,
    memo       varchar(1000),
    mesh_id    varchar(20),
    cp         varchar(32),
    datasource varchar(256),
    up_date    timestamp(6) not null,
    status     integer      default 0
);

alter table public.relation_area6
    owner to postgres;

create index idx_relation_inlink_id_area6
    on public.relation_area6 (inlink_id);

create index idx_relation_node_id_area6
    on public.relation_area6 (node_id);

create index idx_relation_outlink_id_area6
    on public.relation_area6 (outlink_id);

create table public.rule_area1
(
    rule_id    varchar      not null
        primary key,
    inlink_id  varchar(128) not null,
    node_id    varchar(128) not null,
    outlink_id varchar(128) not null,
    pass       varchar(512)  default NULL::character varying,
    pass2      varchar(512),
    flag       integer       default 2,
    vperiod    varchar(1000) default NULL::character varying,
    vehcl_type varchar(35)   default NULL::character varying,
    vpdir      varchar(1)    default NULL::character varying,
    mesh_list  varchar(128),
    memo       varchar(1000),
    cp         varchar(32),
    datasource varchar(256),
    up_date    timestamp    not null,
    status     integer       default 0,
    link_angle varchar(6)    default NULL::character varying
);

alter table public.rule_area1
    owner to postgres;

create index idx_rule_inlink_id_area1
    on public.rule_area1 (inlink_id);

create index idx_rule_node_id_area1
    on public.rule_area1 (node_id);

create index idx_rule_outlink_id_area1
    on public.rule_area1 (outlink_id);

create table public.rule_area2
(
    rule_id    varchar      not null
        primary key,
    inlink_id  varchar(128) not null,
    node_id    varchar(128) not null,
    outlink_id varchar(128) not null,
    pass       varchar(512)  default NULL::character varying,
    pass2      varchar(512),
    flag       integer       default 2,
    vperiod    varchar(1000) default NULL::character varying,
    vehcl_type varchar(35)   default NULL::character varying,
    vpdir      varchar(1)    default NULL::character varying,
    mesh_list  varchar(128),
    memo       varchar(1000),
    cp         varchar(32),
    datasource varchar(256),
    up_date    timestamp    not null,
    status     integer       default 0,
    link_angle varchar(6)    default NULL::character varying
);

alter table public.rule_area2
    owner to postgres;

create index idx_rule_inlink_id_area2
    on public.rule_area2 (inlink_id);

create index idx_rule_node_id_area2
    on public.rule_area2 (node_id);

create index idx_rule_outlink_id_area2
    on public.rule_area2 (outlink_id);

create table public.rule_area3
(
    rule_id    varchar      not null
        primary key,
    inlink_id  varchar(128) not null,
    node_id    varchar(128) not null,
    outlink_id varchar(128) not null,
    pass       varchar(512)  default NULL::character varying,
    pass2      varchar(512),
    flag       integer       default 2,
    vperiod    varchar(1000) default NULL::character varying,
    vehcl_type varchar(35)   default NULL::character varying,
    vpdir      varchar(1)    default NULL::character varying,
    mesh_list  varchar(128),
    memo       varchar(1000),
    cp         varchar(32),
    datasource varchar(256),
    up_date    timestamp    not null,
    status     integer       default 0,
    link_angle varchar(6)    default NULL::character varying
);

alter table public.rule_area3
    owner to postgres;

create index idx_rule_inlink_id_area3
    on public.rule_area3 (inlink_id);

create index idx_rule_node_id_area3
    on public.rule_area3 (node_id);

create index idx_rule_outlink_id_area3
    on public.rule_area3 (outlink_id);

create table public.rule_area4
(
    rule_id    varchar      not null
        primary key,
    inlink_id  varchar(128) not null,
    node_id    varchar(128) not null,
    outlink_id varchar(128) not null,
    pass       varchar(512)  default NULL::character varying,
    pass2      varchar(512),
    flag       integer       default 2,
    vperiod    varchar(1000) default NULL::character varying,
    vehcl_type varchar(35)   default NULL::character varying,
    vpdir      varchar(1)    default NULL::character varying,
    mesh_list  varchar(128),
    memo       varchar(1000),
    cp         varchar(32),
    datasource varchar(256),
    up_date    timestamp    not null,
    status     integer       default 0,
    link_angle varchar(6)    default NULL::character varying
);

alter table public.rule_area4
    owner to postgres;

create index idx_rule_inlink_id_area4
    on public.rule_area4 (inlink_id);

create index idx_rule_node_id_area4
    on public.rule_area4 (node_id);

create index idx_rule_outlink_id_area4
    on public.rule_area4 (outlink_id);

create table public.rule_area5
(
    rule_id    varchar      not null
        primary key,
    inlink_id  varchar(128) not null,
    node_id    varchar(128) not null,
    outlink_id varchar(128) not null,
    pass       varchar(512)  default NULL::character varying,
    pass2      varchar(512),
    flag       integer       default 2,
    vperiod    varchar(1000) default NULL::character varying,
    vehcl_type varchar(35)   default NULL::character varying,
    vpdir      varchar(1)    default NULL::character varying,
    mesh_list  varchar(128),
    memo       varchar(1000),
    cp         varchar(32),
    datasource varchar(256),
    up_date    timestamp    not null,
    status     integer       default 0,
    link_angle varchar(6)    default NULL::character varying
);

alter table public.rule_area5
    owner to postgres;

create index idx_rule_inlink_id_area5
    on public.rule_area5 (inlink_id);

create index idx_rule_node_id_area5
    on public.rule_area5 (node_id);

create index idx_rule_outlink_id_area5
    on public.rule_area5 (outlink_id);

create table public.rule_area6
(
    rule_id    varchar      not null
        primary key,
    inlink_id  varchar(128) not null,
    node_id    varchar(128) not null,
    outlink_id varchar(128) not null,
    pass       varchar(512)  default NULL::character varying,
    pass2      varchar(512),
    flag       integer       default 2,
    vperiod    varchar(1000) default NULL::character varying,
    vehcl_type varchar(35)   default NULL::character varying,
    vpdir      varchar(1)    default NULL::character varying,
    mesh_list  varchar(128),
    memo       varchar(1000),
    cp         varchar(32),
    datasource varchar(256),
    up_date    timestamp    not null,
    status     integer       default 0,
    link_angle varchar(6)    default NULL::character varying
);

alter table public.rule_area6
    owner to postgres;

create index idx_rule_inlink_id_area6
    on public.rule_area6 (inlink_id);

create index idx_rule_node_id_area6
    on public.rule_area6 (node_id);

create index idx_rule_outlink_id_area6
    on public.rule_area6 (outlink_id);

create table public.hn_point_address_area1
(
    hn_id                 varchar(255) not null
        primary key,
    source_id             varchar(255),
    link_id               varchar(255),
    side                  varchar(255),
    street_name           text,
    st_name_trans         text,
    st_name_alias         text,
    st_name_alias_trans   text,
    st_name_stale         text,
    st_name_stale_trans   text,
    address               varchar(255),
    bldg_nm               varchar(255),
    bldg_nm_trans         varchar(255),
    trans_type            varchar(255),
    address_type          varchar(255),
    language_code         varchar(255),
    disp_lon              varchar(255),
    disp_lat              varchar(255),
    admin_level1_name     varchar(255),
    admin_level1_govncode varchar(255),
    admin_level2_name     varchar(255),
    admin_level2_govncode varchar(255),
    admin_level3_name     varchar(255),
    admin_level3_govncode varchar(255),
    admin_level4_name     varchar(255),
    admin_level4_govncode varchar(255),
    admin_level5_name     varchar(255),
    admin_level5_govncode varchar(255),
    ar_link_id            varchar(255),
    ar_side               varchar(255),
    version               varchar(255),
    geom                  geometry
);

alter table public.hn_point_address_area1
    owner to postgres;

create table public.hn_point_address_area2
(
    hn_id                 varchar(255) not null
        primary key,
    source_id             varchar(255),
    link_id               varchar(255),
    side                  varchar(255),
    street_name           text,
    st_name_trans         text,
    st_name_alias         text,
    st_name_alias_trans   text,
    st_name_stale         text,
    st_name_stale_trans   text,
    address               varchar(255),
    bldg_nm               varchar(255),
    bldg_nm_trans         varchar(255),
    trans_type            varchar(255),
    address_type          varchar(255),
    language_code         varchar(255),
    disp_lon              varchar(255),
    disp_lat              varchar(255),
    admin_level1_name     varchar(255),
    admin_level1_govncode varchar(255),
    admin_level2_name     varchar(255),
    admin_level2_govncode varchar(255),
    admin_level3_name     varchar(255),
    admin_level3_govncode varchar(255),
    admin_level4_name     varchar(255),
    admin_level4_govncode varchar(255),
    admin_level5_name     varchar(255),
    admin_level5_govncode varchar(255),
    ar_link_id            varchar(255),
    ar_side               varchar(255),
    version               varchar(255),
    geom                  geometry
);

alter table public.hn_point_address_area2
    owner to postgres;

create table public.hn_point_address_area3
(
    hn_id                 varchar(255) not null
        primary key,
    source_id             varchar(255),
    link_id               varchar(255),
    side                  varchar(255),
    street_name           text,
    st_name_trans         text,
    st_name_alias         text,
    st_name_alias_trans   text,
    st_name_stale         text,
    st_name_stale_trans   text,
    address               varchar(255),
    bldg_nm               varchar(255),
    bldg_nm_trans         varchar(255),
    trans_type            varchar(255),
    address_type          varchar(255),
    language_code         varchar(255),
    disp_lon              varchar(255),
    disp_lat              varchar(255),
    admin_level1_name     varchar(255),
    admin_level1_govncode varchar(255),
    admin_level2_name     varchar(255),
    admin_level2_govncode varchar(255),
    admin_level3_name     varchar(255),
    admin_level3_govncode varchar(255),
    admin_level4_name     varchar(255),
    admin_level4_govncode varchar(255),
    admin_level5_name     varchar(255),
    admin_level5_govncode varchar(255),
    ar_link_id            varchar(255),
    ar_side               varchar(255),
    version               varchar(255),
    geom                  geometry
);

alter table public.hn_point_address_area3
    owner to postgres;

create table public.hn_point_address_area4
(
    hn_id                 varchar(255) not null
        primary key,
    source_id             varchar(255),
    link_id               varchar(255),
    side                  varchar(255),
    street_name           text,
    st_name_trans         text,
    st_name_alias         text,
    st_name_alias_trans   text,
    st_name_stale         text,
    st_name_stale_trans   text,
    address               varchar(255),
    bldg_nm               varchar(255),
    bldg_nm_trans         varchar(255),
    trans_type            varchar(255),
    address_type          varchar(255),
    language_code         varchar(255),
    disp_lon              varchar(255),
    disp_lat              varchar(255),
    admin_level1_name     varchar(255),
    admin_level1_govncode varchar(255),
    admin_level2_name     varchar(255),
    admin_level2_govncode varchar(255),
    admin_level3_name     varchar(255),
    admin_level3_govncode varchar(255),
    admin_level4_name     varchar(255),
    admin_level4_govncode varchar(255),
    admin_level5_name     varchar(255),
    admin_level5_govncode varchar(255),
    ar_link_id            varchar(255),
    ar_side               varchar(255),
    version               varchar(255),
    geom                  geometry
);

alter table public.hn_point_address_area4
    owner to postgres;

create table public.hn_point_address_area5
(
    hn_id                 varchar(255) not null
        primary key,
    source_id             varchar(255),
    link_id               varchar(255),
    side                  varchar(255),
    street_name           text,
    st_name_trans         text,
    st_name_alias         text,
    st_name_alias_trans   text,
    st_name_stale         text,
    st_name_stale_trans   text,
    address               varchar(255),
    bldg_nm               varchar(255),
    bldg_nm_trans         varchar(255),
    trans_type            varchar(255),
    address_type          varchar(255),
    language_code         varchar(255),
    disp_lon              varchar(255),
    disp_lat              varchar(255),
    admin_level1_name     varchar(255),
    admin_level1_govncode varchar(255),
    admin_level2_name     varchar(255),
    admin_level2_govncode varchar(255),
    admin_level3_name     varchar(255),
    admin_level3_govncode varchar(255),
    admin_level4_name     varchar(255),
    admin_level4_govncode varchar(255),
    admin_level5_name     varchar(255),
    admin_level5_govncode varchar(255),
    ar_link_id            varchar(255),
    ar_side               varchar(255),
    version               varchar(255),
    geom                  geometry
);

alter table public.hn_point_address_area5
    owner to postgres;

create table public.hn_point_address_area6
(
    hn_id                 varchar(255) not null
        primary key,
    source_id             varchar(255),
    link_id               varchar(255),
    side                  varchar(255),
    street_name           text,
    st_name_trans         text,
    st_name_alias         text,
    st_name_alias_trans   text,
    st_name_stale         text,
    st_name_stale_trans   text,
    address               varchar(255),
    bldg_nm               varchar(255),
    bldg_nm_trans         varchar(255),
    trans_type            varchar(255),
    address_type          varchar(255),
    language_code         varchar(255),
    disp_lon              varchar(255),
    disp_lat              varchar(255),
    admin_level1_name     varchar(255),
    admin_level1_govncode varchar(255),
    admin_level2_name     varchar(255),
    admin_level2_govncode varchar(255),
    admin_level3_name     varchar(255),
    admin_level3_govncode varchar(255),
    admin_level4_name     varchar(255),
    admin_level4_govncode varchar(255),
    admin_level5_name     varchar(255),
    admin_level5_govncode varchar(255),
    ar_link_id            varchar(255),
    ar_side               varchar(255),
    version               varchar(255),
    geom                  geometry
);

alter table public.hn_point_address_area6
    owner to postgres;

create table public.link
(
    hll_linkid  varchar(256) not null
        constraint link_copy1_pkey
            primary key,
    id          varchar(128) not null,
    hll_s_nid   varchar(256) not null,
    hll_e_nid   varchar(256) not null,
    kind        varchar(2)       default NULL::character varying,
    formway     varchar(128)     default 1,
    direction   varchar(2)       default NULL::character varying,
    const_st    varchar(2)       default NULL::character varying,
    toll        varchar(2)       default NULL::character varying,
    adopt       varchar(2)       default NULL::character varying,
    md          varchar(1)       default NULL::character varying,
    divider     varchar(1)       default NULL::character varying,
    divider_leg varchar(1)       default NULL::character varying,
    detailcity  varchar(1)       default NULL::character varying,
    special     varchar(1)       default NULL::character varying,
    funcclass   varchar(2)       default NULL::character varying,
    uflag       varchar(1)       default NULL::character varying,
    road_cond   varchar(1)       default NULL::character varying,
    lanenumsum  integer          default 0,
    lanenums2e  integer          default 0,
    lanenume2s  integer          default 0,
    lanenumc    varchar(2)       default NULL::character varying,
    width       varchar(8)       default NULL::character varying,
    elevated    varchar(1)       default NULL::character varying,
    admincodel  varchar(10)  not null,
    admincoder  varchar(10)  not null,
    geom        geometry(LineString, 4326),
    len         double precision default 0.0,
    spdlmts2e   varchar(100),
    spdlmte2s   varchar(100),
    speedclass  varchar(100),
    dc_type     varchar(1)       default NULL::character varying,
    verifyflag  varchar(1)       default NULL::character varying,
    pre_launch  varchar(256),
    name_ch_o   varchar(1000),
    name_ch_a   varchar(1000),
    name_ch_f   varchar(1000),
    name_ph_o   varchar(1000),
    name_ph_a   varchar(1000),
    name_ph_f   varchar(1000),
    name_en_o   varchar(1000),
    name_en_a   varchar(1000),
    name_en_f   varchar(1000),
    name_po     varchar(1000),
    name_cht    varchar(1000),
    code_type   varchar(2)       default NULL::character varying,
    name_type   varchar(2)       default 0,
    src_flag    varchar(2)       default NULL::character varying,
    mesh_id     varchar(8),
    memo        varchar(1000),
    cp          varchar(32),
    datasource  varchar(256),
    up_date     timestamp(6) not null,
    status      integer          default 0,
    geomwkt     varchar,
    ar_veh      varchar(35),
    pub_access  char,
    t_admin     varchar(128),
    time_zone   varchar(128)
);

alter table public.link
    owner to postgres;

create index kind_index_copy1
    on public.link (kind);

create index link_geom_index_copy1
    on public.link using gist (geom);

create index idx_link_hll_e_nid
    on public.link (hll_e_nid);

create index idx_link_hll_s_nid
    on public.link (hll_s_nid);

create table public.relation
(
    relationid varchar(128) not null
        primary key,
    inlink_id  varchar(128),
    node_id    varchar(128) not null,
    outlink_id varchar(128),
    type       varchar(2)   not null,
    toll_type  varchar(128) default NULL::character varying,
    pass_num   varchar(2)   default NULL::character varying,
    toll_form  varchar(8)   default NULL::character varying,
    card_type  varchar(1)   default NULL::character varying,
    veh        varchar(35)  default NULL::character varying,
    name_ch    varchar(120),
    name_fo    varchar(1000),
    name_ph    varchar(1000),
    name_cht   varchar(1000),
    gate_type  varchar(1)   default NULL::character varying,
    gate_fee   varchar(1)   default NULL::character varying,
    tl_locat   varchar(3)   default NULL::character varying,
    tl_flag    varchar(1)   default NULL::character varying,
    slopetype  varchar(1)   default NULL::character varying,
    slopeangle varchar(6)   default NULL::character varying,
    memo       varchar(1000),
    mesh_id    varchar(20),
    cp         varchar(32),
    datasource varchar(256),
    up_date    timestamp(6) not null,
    status     integer      default 0
);

alter table public.relation
    owner to postgres;

create index idx_relation_inlink_id
    on public.relation (inlink_id);

create index idx_relation_node_id
    on public.relation (node_id);

create index idx_relation_outlink_id
    on public.relation (outlink_id);

create table public.rule
(
    rule_id    varchar      not null
        primary key,
    inlink_id  varchar(128) not null,
    node_id    varchar(128) not null,
    outlink_id varchar(128) not null,
    pass       varchar(512)  default NULL::character varying,
    pass2      varchar(512),
    flag       integer       default 2,
    vperiod    varchar(1000) default NULL::character varying,
    vehcl_type varchar(35)   default NULL::character varying,
    vpdir      varchar(1)    default NULL::character varying,
    mesh_list  varchar(128),
    memo       varchar(1000),
    cp         varchar(32),
    datasource varchar(256),
    up_date    timestamp    not null,
    status     integer       default 0,
    link_angle varchar(6)    default NULL::character varying
);

alter table public.rule
    owner to postgres;

create index idx_rule_inlink_id
    on public.rule (inlink_id);

create index idx_rule_node_id
    on public.rule (node_id);

create index idx_rule_outlink_id
    on public.rule (outlink_id);

create table public.hn_point_address
(
    hn_id                 varchar(255) not null
        primary key,
    source_id             varchar(255),
    link_id               varchar(255),
    side                  varchar(255),
    street_name           text,
    st_name_trans         text,
    st_name_alias         text,
    st_name_alias_trans   text,
    st_name_stale         text,
    st_name_stale_trans   text,
    address               varchar(255),
    bldg_nm               varchar(255),
    bldg_nm_trans         varchar(255),
    trans_type            varchar(255),
    address_type          varchar(255),
    language_code         varchar(255),
    disp_lon              varchar(255),
    disp_lat              varchar(255),
    admin_level1_name     varchar(255),
    admin_level1_govncode varchar(255),
    admin_level2_name     varchar(255),
    admin_level2_govncode varchar(255),
    admin_level3_name     varchar(255),
    admin_level3_govncode varchar(255),
    admin_level4_name     varchar(255),
    admin_level4_govncode varchar(255),
    admin_level5_name     varchar(255),
    admin_level5_govncode varchar(255),
    ar_link_id            varchar(255),
    ar_side               varchar(255),
    version               varchar(255),
    geom                  geometry
);

alter table public.hn_point_address
    owner to postgres;

create table public.node
(
    hll_nodeid varchar(256) not null
        constraint node_copy1_pkey1
            primary key,
    kind       varchar(128),
    geom       geometry     not null,
    name_ch    varchar(128),
    name_fo    varchar(256),
    name_cht   varchar(1000),
    name_ph    varchar(1000),
    adjoin_mid varchar(6),
    adjoin_nid varchar(128),
    type       varchar(1)    default NULL::character varying,
    mainnodeid varchar(256)  default NULL::character varying,
    subnodeid  varchar(1000) default NULL::character varying,
    subnodeid2 varchar(512),
    light_flag varchar(1)    default NULL::character varying,
    is_pbnode  varchar(1)    default NULL::character varying,
    cp         varchar(32),
    datasource varchar(256),
    id         varchar(128) not null,
    up_date    timestamp(6) not null,
    memo       varchar(1000),
    status     integer       default 0,
    geom_wkt   varchar
);

alter table public.node
    owner to postgres;

create index idx_node_adjoin_nid
    on public.node (adjoin_nid);

create index idx_node_adjoin_mid
    on public.node (adjoin_mid);

create index idx_node_mainnodeid
    on public.node (mainnodeid);

create index idx_node_geom
    on public.node using gist (geom);

