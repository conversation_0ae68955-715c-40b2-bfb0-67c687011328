--1.convert to link node
curl -X "POST" "http://localhost:10092/api/road/herelink/convert" -d 'area=area1&rdfnavlinkfilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_nav_link.txt.001,/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_nav_link.txt.002&rdfcflinkfilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_link.txt&country=tha&rdfcfnodefilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_node.txt&rdfcffilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf.txt&iscompiletranseng=true&step=2000&iscompilenode=true&version='
curl -X "POST" "http://localhost:10095/api/road/herelink/convert" -d 'area=area2&rdfnavlinkfilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_nav_link.txt.001,/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_nav_link.txt.002&rdfcflinkfilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_link.txt&country=tha&rdfcfnodefilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_node.txt&rdfcffilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf.txt&iscompiletranseng=true&step=2000&iscompilenode=true&version='
curl -X "POST" "http://localhost:10096/api/road/herelink/convert" -d 'area=area3&rdfnavlinkfilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_nav_link.txt.001,/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_nav_link.txt.002&rdfcflinkfilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_link.txt&country=tha&rdfcfnodefilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_node.txt&rdfcffilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf.txt&iscompiletranseng=true&step=2000&iscompilenode=true&version='
curl -X "POST" "http://localhost:10097/api/road/herelink/convert" -d 'area=area4&rdfnavlinkfilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_nav_link.txt.001,/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_nav_link.txt.002&rdfcflinkfilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_link.txt&country=tha&rdfcfnodefilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_node.txt&rdfcffilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf.txt&iscompiletranseng=true&step=2000&iscompilenode=true&version='
curl -X "POST" "http://localhost:10098/api/road/herelink/convert" -d 'area=area5&rdfnavlinkfilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_nav_link.txt.001,/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_nav_link.txt.002&rdfcflinkfilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_link.txt&country=tha&rdfcfnodefilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_node.txt&rdfcffilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf.txt&iscompiletranseng=true&step=2000&iscompilenode=true&version='

curl -X "POST" "http://localhost:10092/api/road/herelink/updatemainsubnode" -d 'area=area1&rdfcflinkfilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_link.txt&country=tha&rdfcfnodefilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_node.txt&rdfcffilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf.txt'
curl -X "POST" "http://localhost:10095/api/road/herelink/updatemainsubnode" -d 'area=area2&rdfcflinkfilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_link.txt&country=tha&rdfcfnodefilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_node.txt&rdfcffilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf.txt'
curl -X "POST" "http://localhost:10096/api/road/herelink/updatemainsubnode" -d 'area=area3&rdfcflinkfilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_link.txt&country=tha&rdfcfnodefilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_node.txt&rdfcffilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf.txt'
curl -X "POST" "http://localhost:10097/api/road/herelink/updatemainsubnode" -d 'area=area4&rdfcflinkfilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_link.txt&country=tha&rdfcfnodefilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_node.txt&rdfcffilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf.txt'
curl -X "POST" "http://localhost:10098/api/road/herelink/updatemainsubnode" -d 'area=area5&rdfcflinkfilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_link.txt&country=tha&rdfcfnodefilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_node.txt&rdfcffilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf.txt'

curl -X "GET" "http://localhost:10092/common/nodeSw2021q133/handleNodeId?step=1400&area=area1&country=tha"
curl -X "GET" "http://localhost:10095/common/nodeSw2021q133/handleNodeId?step=1400&area=area2&country=tha"
curl -X "GET" "http://localhost:10096/common/nodeSw2021q133/handleNodeId?step=1400&area=area3&country=tha"
curl -X "GET" "http://localhost:10097/common/nodeSw2021q133/handleNodeId?step=1400&area=area4&country=tha"
curl -X "GET" "http://localhost:10098/common/nodeSw2021q133/handleNodeId?step=1400&area=area5&country=tha"
--2.convert to relation rule
curl -X "POST" "http://localhost:10092/api/road/herelinkreleation/convert2" -d 'area=area1&country=tha'
curl -X "POST" "http://localhost:10092/api/road/herelinkrule/convert" -d 'area=area1&country=tha&step=1900'

curl -X "POST" "http://localhost:10095/api/road/herelinkreleation/convert2" -d 'area=area2&country=tha'
curl -X "POST" "http://localhost:10095/api/road/herelinkrule/convert" -d 'area=area2&country=tha&step=1900'

curl -X "POST" "http://localhost:10096/api/road/herelinkreleation/convert2" -d 'area=area3&country=tha'
curl -X "POST" "http://localhost:10096/api/road/herelinkrule/convert" -d 'area=area3&country=tha&step=1900'

curl -X "POST" "http://localhost:10097/api/road/herelinkreleation/convert2" -d 'area=area4&country=tha'
curl -X "POST" "http://localhost:10097/api/road/herelinkrule/convert" -d 'area=area4&country=tha&step=1900'

curl -X "POST" "http://localhost:10098/api/road/herelinkreleation/convert2" -d 'area=area5&country=tha'
curl -X "POST" "http://localhost:10098/api/road/herelinkrule/convert" -d 'area=area5&country=tha&step=1900'

curl -X "POST" "http://localhost:10092/api/road/herelinkreleation/convert2" -d 'area=area3&country=tha'
curl -X "POST" "http://localhost:10095/api/road/herelinkreleation/convert2" -d 'area=area4&country=tha'
curl -X "POST" "http://localhost:10096/api/road/herelinkreleation/convert2" -d 'area=area5&country=tha'
--3.left rule
curl -X "POST" "http://localhost:10092/api/road/herelink/leftruleconvert" -d 'area=area1&country=tha&step=1900&filepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_condition_divider.txt'
curl -X "POST" "http://localhost:10095/api/road/herelink/leftruleconvert" -d 'area=area2&country=tha&step=1900&filepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_condition_divider.txt'
curl -X "POST" "http://localhost:10096/api/road/herelink/leftruleconvert" -d 'area=area3&country=tha&step=1900&filepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_condition_divider.txt'
curl -X "POST" "http://localhost:10097/api/road/herelink/leftruleconvert" -d 'area=area4&country=tha&step=1900&filepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_condition_divider.txt'
curl -X "POST" "http://localhost:10098/api/road/herelink/leftruleconvert" -d 'area=area5&country=tha&step=1900&filepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_condition_divider.txt'
--3.1 left rule vehicle type
curl -X "GET" "http://localhost:10092/api/road/herelink/updateVehclType?area=area1&country=tha"
curl -X "GET" "http://localhost:10095/api/road/herelink/updateVehclType?area=area2&country=tha"
curl -X "GET" "http://localhost:10096/api/road/herelink/updateVehclType?area=area3&country=tha"
curl -X "GET" "http://localhost:10097/api/road/herelink/updateVehclType?area=area4&country=tha"
curl -X "GET" "http://localhost:10098/api/road/herelink/updateVehclType?area=area5&country=tha"

--4.convert to rp
curl -X "GET" "http://localhost:10092/api/road/herelink/convert2rp?area=area1&country=tha&step=2000"
curl -X "GET" "http://localhost:10092/common/nodeSw2021q133/convert2rp?area=area1&country=tha&step=2000"

curl -X "GET" "http://localhost:10095/api/road/herelink/convert2rp?area=area2&country=tha&step=2000"
curl -X "GET" "http://localhost:10095/common/nodeSw2021q133/convert2rp?area=area2&country=tha&step=2000"

curl -X "GET" "http://localhost:10096/api/road/herelink/convert2rp?area=area3&country=tha&step=2000"
curl -X "GET" "http://localhost:10096/common/nodeSw2021q133/convert2rp?area=area3&country=tha&step=2000"

curl -X "GET" "http://localhost:10097/api/road/herelink/convert2rp?area=area4&country=tha&step=2000"
curl -X "GET" "http://localhost:10097/common/nodeSw2021q133/convert2rp?area=area4&country=tha&step=2000"

curl -X "GET" "http://localhost:10098/api/road/herelink/convert2rp?area=area5&country=tha&step=2000"
curl -X "GET" "http://localhost:10098/common/nodeSw2021q133/convert2rp?area=area5&country=tha&step=2000"

--5.convert to point address
curl -X "POST" "http://localhost:10098/api/road/pointaddress/convert" -d 'area=area1&country=tha&step=1000&isCompileTrans=true&version=24q1'
curl -X "POST" "http://localhost:10098/api/road/pointaddress/convert" -d 'area=area2&country=tha&step=1000&isCompileTrans=true&version=24q1'
curl -X "POST" "http://localhost:10098/api/road/pointaddress/convert" -d 'area=area3&country=tha&step=1000&isCompileTrans=true&version=24q1'
curl -X "POST" "http://localhost:10098/api/road/pointaddress/convert" -d 'area=area4&country=tha&step=1000&isCompileTrans=true&version=24q1'
curl -X "POST" "http://localhost:10098/api/road/pointaddress/convert" -d 'area=area5&country=tha&step=1000&isCompileTrans=true&version=24q1'

--6.convert to poi
curl -X "POST" "http://localhost:10092/api/poi/herepoi/import" -d 'area=area1&country=tha&create=true&step=340&filePath=/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS2/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS3/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS4/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS5/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS2/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS3/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS4/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS5/THA'
curl -X "POST" "http://localhost:10095/api/poi/herepoi/import" -d 'area=area2&country=tha&create=false&step=340&filePath=/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS2/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS3/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS4/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS5/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS2/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS3/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS4/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS5/THA'
curl -X "POST" "http://localhost:10096/api/poi/herepoi/import" -d 'area=area3&country=tha&create=false&step=340&filePath=/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS2/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS3/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS4/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS5/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS2/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS3/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS4/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS5/THA'
curl -X "POST" "http://localhost:10097/api/poi/herepoi/import" -d 'area=area4&country=tha&create=false&step=340&filePath=/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS2/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS3/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS4/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS5/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS2/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS3/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS4/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS5/THA'
curl -X "POST" "http://localhost:10098/api/poi/herepoi/import" -d 'area=area5&country=tha&create=false&step=340&filePath=/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS2/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS3/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS4/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS5/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS2/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS3/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS4/THA,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS5/THA'