here streets convert to link node
relation convert finished
rule convert finished

update main sub node finished
left rule convert

here pointA<PERSON><PERSON> convert to HnPointAddress
importHerePoi finished


grep 'here streets convert to link node' system.log

grep 'relation convert finished' system.log
grep 'rule convert finished' system.log
grep 'left rule convert,cost time' system.log

grep 'here pointAddress convert to HnPointAddress' system.log

grep 'update main sub node finished' system.log

grep 'map link diff column to link_rp ' system.log
grep 'map node diff column to node_rp ' system.log
grep 'importHerePoi finished,country is' system.log

grep 'map link diff column to link_rp' system.log
grep 'map node diff column to node_rp' system.log


curl -X "GET" "http://localhost:10092/api/road/herelinkrule/checkRuleResult?area=&country=hkg&filePath=/data/oversea/here/2024/q1/hkg/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10095/api/road/herelinkrule/checkRuleResult?area=&country=mys&filePath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10096/api/road/herelinkrule/checkRuleResult?area=&country=phl&filePath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10097/api/road/herelinkrule/checkRuleResult?area=&country=sgp&filePath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10098/api/road/herelinkrule/checkRuleResult?area=&country=ban&filePath=/data/oversea/here/2024/q1/ind/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10099/api/road/herelinkrule/checkRuleResult?area=area1&country=vnm&filePath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10099/api/road/herelinkrule/checkRuleResult?area=area2&country=vnm&filePath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10099/api/road/herelinkrule/checkRuleResult?area=area3&country=vnm&filePath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_condition_divider.txt"

curl -X "GET" "http://localhost:10095/api/road/herelinkrule/checkRuleResult?area=area1&country=twn&filePath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10095/api/road/herelinkrule/checkRuleResult?area=area2&country=twn&filePath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10095/api/road/herelinkrule/checkRuleResult?area=area3&country=twn&filePath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_condition_divider.txt"

curl -X "GET" "http://localhost:10092/api/road/herelinkrule/checkRuleResult?area=area1&country=tha&filePath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10092/api/road/herelinkrule/checkRuleResult?area=area2&country=tha&filePath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10092/api/road/herelinkrule/checkRuleResult?area=area3&country=tha&filePath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10092/api/road/herelinkrule/checkRuleResult?area=area4&country=tha&filePath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10092/api/road/herelinkrule/checkRuleResult?area=area5&country=tha&filePath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_condition_divider.txt"

curl -X "GET" "http://localhost:10096/api/road/herelinkrule/checkRuleResult?area=area1&country=bra&filePath=/data/oversea/here/2024/q1/SouthAmerica/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10096/api/road/herelinkrule/checkRuleResult?area=area2&country=bra&filePath=/data/oversea/here/2024/q1/SouthAmerica/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10096/api/road/herelinkrule/checkRuleResult?area=area3&country=bra&filePath=/data/oversea/here/2024/q1/SouthAmerica/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10096/api/road/herelinkrule/checkRuleResult?area=area4&country=bra&filePath=/data/oversea/here/2024/q1/SouthAmerica/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10096/api/road/herelinkrule/checkRuleResult?area=area5&country=bra&filePath=/data/oversea/here/2024/q1/SouthAmerica/rdf/txtDir/rdf_condition_divider.txt"


curl -X "GET" "http://localhost:10097/api/road/herelinkrule/checkRuleResult?area=area1&country=idn&filePath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10097/api/road/herelinkrule/checkRuleResult?area=area2&country=idn&filePath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10097/api/road/herelinkrule/checkRuleResult?area=area3&country=idn&filePath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10097/api/road/herelinkrule/checkRuleResult?area=area4&country=idn&filePath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10097/api/road/herelinkrule/checkRuleResult?area=area5&country=idn&filePath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10097/api/road/herelinkrule/checkRuleResult?area=area6&country=idn&filePath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_condition_divider.txt"


curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area1&country=mex&filePath=/data/oversea/here/2024/q1/NorthAmerica/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area2&country=mex&filePath=/data/oversea/here/2024/q1/NorthAmerica/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area3&country=mex&filePath=/data/oversea/here/2024/q1/NorthAmerica/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area4&country=mex&filePath=/data/oversea/here/2024/q1/NorthAmerica/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area5&country=mex&filePath=/data/oversea/here/2024/q1/NorthAmerica/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area6&country=mex&filePath=/data/oversea/here/2024/q1/NorthAmerica/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area7&country=mex&filePath=/data/oversea/here/2024/q1/NorthAmerica/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area8&country=mex&filePath=/data/oversea/here/2024/q1/NorthAmerica/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area9&country=mex&filePath=/data/oversea/here/2024/q1/NorthAmerica/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area10&country=mex&filePath=/data/oversea/here/2024/q1/NorthAmerica/rdf/txtDir/rdf_condition_divider.txt"

curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area1&country=ind&filePath=/data/oversea/here/2024/q1/ind/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area2&country=ind&filePath=/data/oversea/here/2024/q1/ind/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area3&country=ind&filePath=/data/oversea/here/2024/q1/ind/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area4&country=ind&filePath=/data/oversea/here/2024/q1/ind/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area5&country=ind&filePath=/data/oversea/here/2024/q1/ind/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area6&country=ind&filePath=/data/oversea/here/2024/q1/ind/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area7&country=ind&filePath=/data/oversea/here/2024/q1/ind/rdf/txtDir/rdf_condition_divider.txt"

curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area8&country=ind&filePath=/data/oversea/here/2024/q1/ind/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area9&country=ind&filePath=/data/oversea/here/2024/q1/ind/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area10&country=ind&filePath=/data/oversea/here/2024/q1/ind/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area11&country=ind&filePath=/data/oversea/here/2024/q1/ind/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area12&country=ind&filePath=/data/oversea/here/2024/q1/ind/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area13&country=ind&filePath=/data/oversea/here/2024/q1/ind/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area14&country=ind&filePath=/data/oversea/here/2024/q1/ind/rdf/txtDir/rdf_condition_divider.txt"

curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area15&country=ind&filePath=/data/oversea/here/2024/q1/ind/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area16&country=ind&filePath=/data/oversea/here/2024/q1/ind/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area17&country=ind&filePath=/data/oversea/here/2024/q1/ind/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area18&country=ind&filePath=/data/oversea/here/2024/q1/ind/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area19&country=ind&filePath=/data/oversea/here/2024/q1/ind/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area20&country=ind&filePath=/data/oversea/here/2024/q1/ind/rdf/txtDir/rdf_condition_divider.txt"
curl -X "GET" "http://localhost:10011/api/road/herelinkrule/checkRuleResult?area=area21&country=ind&filePath=/data/oversea/here/2024/q1/ind/rdf/txtDir/rdf_condition_divider.txt"
-- 1.检查street link_m link数量
select count(1) from streets;

select count(1) from link_m;
select count(1) from link;
select count(1) from link_area1 union all select count(1) from link_m_area1;

select count(1) from node_m;
select count(1) from node;
select count(1) from node_area1 union all select count(1) from node_m_area1;

-- 2.检查relation rule 数量
select count(1) from cdms where cond_type in ('1','4','9','11','16');
select count(1) from cdms_area1 where cond_type in ('1','4','9','11','16');
select count(1) from relation;

-- 3.检查rule的数量
call /api/road/herelinkrule/checkRuleResult




-- 4.basemap检查
-- bline
-- 2311
select count(1) from railrds;
-- select link_id,count(1) from railrds group by link_id having count(1)>1;
-- select * from railrds where link_id = '984608108';
-- select st_astext(geom) from railrds where link_id = '984608108';
-- 20707
select count(1) from adminline1;
-- 30191
select count(1) from adminline2;
-- 1870
select count(1) from waterseg;

-- bpolygon
-- 1505
select count(1) from waterpoly;
-- 1632
select count(1) from landusea;
-- 149
select count(1) from landuseb;
-- 19
select count(1) from oceans;
-- 2837
select count(1) from islands;

-- building
-- 640667
select count(1) from landmark;

-- namedplc
-- 41740
select count(1) from namedplc_h;
select * from namedplc_h where poi_id = 1;
-- unique
select count(1) from namedplc_h where poi_id not in(
select poi_id from namedplc_h group by poi_id having count(1)>1);
select * from namedplc_h;
-- unique size : 37948
select poi_id from namedplc_h group by poi_id having count(1)=1;
-- repeat size
select poi_id from namedplc_h group by poi_id having count(1)>1;

select * from namedplc_h where poi_id = '1038969606';

select a.poi_id
from (select poi_id, count(1) num from namedplc_h group by poi_id having count(1) > 1) a where a.num > 5;
-- 2 1733 3466
-- 3 48 144
-- 4 32 128
-- 5 4 20
-- 2+16+24+867=909
select count(1) from namedplc_h;