--1.convert to link node
curl -X "POST" "http://localhost:10099/api/road/herelink/convert" -d 'area=area1&rdfnavlinkfilepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_nav_link.txt&rdfcflinkfilepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_cf_link.txt&country=twn&rdfcfnodefilepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_cf_node.txt&rdfcffilepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_cf.txt&iscompiletranseng=true&step=2000&iscompilenode=true&version='
curl -X "POST" "http://localhost:10010/api/road/herelink/convert" -d 'area=area2&rdfnavlinkfilepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_nav_link.txt&rdfcflinkfilepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_cf_link.txt&country=twn&rdfcfnodefilepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_cf_node.txt&rdfcffilepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_cf.txt&iscompiletranseng=true&step=2000&iscompilenode=true&version='
curl -X "POST" "http://localhost:10011/api/road/herelink/convert" -d 'area=area3&rdfnavlinkfilepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_nav_link.txt&rdfcflinkfilepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_cf_link.txt&country=twn&rdfcfnodefilepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_cf_node.txt&rdfcffilepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_cf.txt&iscompiletranseng=true&step=2000&iscompilenode=true&version='

curl -X "POST" "http://localhost:10092/api/road/herelink/updatemainsubnode" -d 'area=area1&rdfcflinkfilepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_cf_link.txt&country=twn&rdfcfnodefilepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_cf_node.txt&rdfcffilepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_cf.txt'
curl -X "POST" "http://localhost:10095/api/road/herelink/updatemainsubnode" -d 'area=area2&rdfcflinkfilepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_cf_link.txt&country=twn&rdfcfnodefilepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_cf_node.txt&rdfcffilepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_cf.txt'
curl -X "POST" "http://localhost:10096/api/road/herelink/updatemainsubnode" -d 'area=area3&rdfcflinkfilepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_cf_link.txt&country=twn&rdfcfnodefilepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_cf_node.txt&rdfcffilepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_cf.txt'

curl -X "GET" "http://localhost:10092/common/nodeSw2021q133/handleNodeId?step=1400&area=area1&country=twn"
curl -X "GET" "http://localhost:10095/common/nodeSw2021q133/handleNodeId?step=1400&area=area2&country=twn"
curl -X "GET" "http://localhost:10096/common/nodeSw2021q133/handleNodeId?step=1400&area=area3&country=twn"

--2.convert to relation rule
curl -X "POST" "http://localhost:10092/api/road/herelinkreleation/convert2" -d 'area=area1&country=twn'
curl -X "POST" "http://localhost:10092/api/road/herelinkrule/convert" -d 'area=area1&country=twn&step=1900'

curl -X "POST" "http://localhost:10095/api/road/herelinkreleation/convert2" -d 'area=area2&country=twn'
curl -X "POST" "http://localhost:10095/api/road/herelinkrule/convert" -d 'area=area2&country=twn&step=1900'

curl -X "POST" "http://localhost:10096/api/road/herelinkreleation/convert2" -d 'area=area3&country=twn'
curl -X "POST" "http://localhost:10096/api/road/herelinkrule/convert" -d 'area=area3&country=twn&step=1900'

--3.left rule
curl -X "POST" "http://localhost:10092/api/road/herelink/leftruleconvert" -d 'area=area1&country=twn&step=1900&filepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_condition_divider.txt'

curl -X "POST" "http://localhost:10095/api/road/herelink/leftruleconvert" -d 'area=area2&country=twn&step=1900&filepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_condition_divider.txt'

curl -X "POST" "http://localhost:10096/api/road/herelink/leftruleconvert" -d 'area=area3&country=twn&step=1900&filepath=/data/oversea/here/2024/q1/twn/rdf/txtDir/rdf_condition_divider.txt'
--3.1 left rule vehicle type
curl -X "GET" "http://localhost:10092/api/road/herelink/updateVehclType?area=area1&country=twn"
curl -X "GET" "http://localhost:10095/api/road/herelink/updateVehclType?area=area2&country=twn"
curl -X "GET" "http://localhost:10096/api/road/herelink/updateVehclType?area=area3&country=twn"

--4.convert to rp
curl -X "GET" "http://localhost:10092/api/road/herelink/convert2rp?area=area1&country=twn&step=2000"
curl -X "GET" "http://localhost:10092/common/nodeSw2021q133/convert2rp?area=area1&country=twn&step=2000"

curl -X "GET" "http://localhost:10095/api/road/herelink/convert2rp?area=area2&country=twn&step=2000"
curl -X "GET" "http://localhost:10095/common/nodeSw2021q133/convert2rp?area=area2&country=twn&step=2000"

curl -X "GET" "http://localhost:10096/api/road/herelink/convert2rp?area=area3&country=twn&step=2000"
curl -X "GET" "http://localhost:10096/common/nodeSw2021q133/convert2rp?area=area3&country=twn&step=2000"

--5.convert to point address
curl -X "POST" "http://localhost:10097/api/road/pointaddress/convert" -d 'area=area1&country=twn&step=1000&isCompileTrans=true&version=24q1'
curl -X "POST" "http://localhost:10097/api/road/pointaddress/convert" -d 'area=area2&country=twn&step=1000&isCompileTrans=true&version=24q1'
curl -X "POST" "http://localhost:10097/api/road/pointaddress/convert" -d 'area=area3&country=twn&step=1000&isCompileTrans=true&version=24q1'

--6.convert to poi
curl -X "POST" "http://localhost:10092/api/poi/herepoi/import" -d 'area=area1&country=twn&create=true&step=340&filePath=/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0/TQS2/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0/TQS3/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0/TQS4/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0/TQS5/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0_FAILED_MAP_BIND/TQS2/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0_FAILED_MAP_BIND/TQS3/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0_FAILED_MAP_BIND/TQS4/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0_FAILED_MAP_BIND/TQS5/TWN'
curl -X "POST" "http://localhost:10095/api/poi/herepoi/import" -d 'area=area2&country=twn&create=false&step=340&filePath=/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0/TQS2/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0/TQS3/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0/TQS4/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0/TQS5/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0_FAILED_MAP_BIND/TQS2/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0_FAILED_MAP_BIND/TQS3/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0_FAILED_MAP_BIND/TQS4/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0_FAILED_MAP_BIND/TQS5/TWN'
curl -X "POST" "http://localhost:10096/api/poi/herepoi/import" -d 'area=area3&country=twn&create=false&step=340&filePath=/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0/TQS2/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0/TQS3/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0/TQS4/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0/TQS5/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0_FAILED_MAP_BIND/TQS2/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0_FAILED_MAP_BIND/TQS3/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0_FAILED_MAP_BIND/TQS4/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0_FAILED_MAP_BIND/TQS5/TWN'


/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0_FAILED_MAP_BIND/TQS2/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0_FAILED_MAP_BIND/TQS3/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0_FAILED_MAP_BIND/TQS4/TWN,/data/oversea/here/2024/q1/twn/poi/PLACE_XML_231H0_WVD_DPLAC/TWN_231H0_FAILED_MAP_BIND/TQS5/TWN'