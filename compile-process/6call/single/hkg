--1.convert to link node
curl -X "POST" "http://localhost:10092/api/road/herelink/convert" -d 'area=&rdfnavlinkfilepath=/data/oversea/here/2024/q1/hkg/rdf/txtDir/rdf_nav_link.txt&rdfcflinkfilepath=/data/oversea/here/2024/q1/hkg/rdf/txtDir/rdf_cf_link.txt&country=hkg&rdfcfnodefilepath=/data/oversea/here/2024/q1/hkg/rdf/txtDir/rdf_cf_node.txt&rdfcffilepath=/data/oversea/here/2024/q1/hkg/rdf/txtDir/rdf_cf.txt&iscompiletranseng=true&step=2000&iscompilenode=true&version='
curl -X "POST" "http://localhost:10092/api/road/herelink/updatemainsubnode" -d 'area=&rdfcflinkfilepath=/data/oversea/here/2024/q1/hkg/rdf/txtDir/rdf_cf_link.txt&country=hkg&rdfcfnodefilepath=/data/oversea/here/2024/q1/hkg/rdf/txtDir/rdf_cf_node.txt&rdfcffilepath=/data/oversea/here/2024/q1/hkg/rdf/txtDir/rdf_cf.txt'
curl -X "GET" "http://localhost:10092/common/nodeSw2021q133/handleNodeId?step=1400&area=&country=hkg"

--2.convert to relation rule
curl -X "POST" "http://localhost:10092/api/road/herelinkreleation/convert" -d 'area=&country=hkg&step=1260'
curl -X "POST" "http://localhost:10092/api/road/herelinkreleation/convert2" -d 'area=&country=hkg'
curl -X "POST" "http://localhost:10092/api/road/herelinkrule/convert" -d 'area=&country=hkg&step=1900'

--3.left rule
curl -X "POST" "http://localhost:10092/api/road/herelink/leftruleconvert" -d 'area=&country=hkg&step=1900&filepath=/data/oversea/here/2024/q1/hkg/rdf/txtDir/rdf_condition_divider.txt'

--3.1 left rule vehicle type
curl -X "GET" "http://localhost:10092/api/road/herelink/updateVehclType?area=&country=hkg"

--4.convert to rp
curl -X "GET" "http://localhost:10092/api/road/herelink/convert2rp?area=&country=hkg&step=2000"
curl -X "GET" "http://localhost:10092/common/nodeSw2021q133/convert2rp?area=&country=hkg&step=2000"

--5.convert to point address
curl -X "POST" "http://localhost:10092/api/road/pointaddress/convert" -d 'area=&country=hkg&step=1000&isCompileTrans=true&version=24q1'

--6.convert to poi
curl -X "POST" "http://localhost:10092/api/poi/herepoi/import" -d 'area=&country=hkg&create=true&step=340&filePath=/data/oversea/here/2024/q1/hkg/poi/PLACE_XML_231S0_HK_DPLAC/HKG_231S0/TQS2/HKG,/data/oversea/here/2024/q1/hkg/poi/PLACE_XML_231S0_HK_DPLAC/HKG_231S0/TQS3/HKG,/data/oversea/here/2024/q1/hkg/poi/PLACE_XML_231S0_HK_DPLAC/HKG_231S0/TQS4/HKG,/data/oversea/here/2024/q1/hkg/poi/PLACE_XML_231S0_HK_DPLAC/HKG_231S0/TQS5/HKG,/data/oversea/here/2024/q1/hkg/poi/PLACE_XML_231S0_HK_DPLAC/HKG_231S0_FAILED_MAP_BIND/TQS2/HKG,/data/oversea/here/2024/q1/hkg/poi/PLACE_XML_231S0_HK_DPLAC/HKG_231S0_FAILED_MAP_BIND/TQS3/HKG,/data/oversea/here/2024/q1/hkg/poi/PLACE_XML_231S0_HK_DPLAC/HKG_231S0_FAILED_MAP_BIND/TQS4/HKG,/data/oversea/here/2024/q1/hkg/poi/PLACE_XML_231S0_HK_DPLAC/HKG_231S0_FAILED_MAP_BIND/TQS5/HKG'

--base map
--bline
curl -X "POST" "http://localhost:10092/api/basemap/bline/convert" -d 'area=&country=hkg&step=2000'
curl -X "POST" "http://localhost:10092/api/basemap/building/convert" -d 'area=&country=hkg&step=2000'
curl -X "POST" "http://localhost:10092/api/basemap/bpolygon/convert" -d 'area=&country=hkg&step=2000'
curl -X "POST" "http://localhost:10092/api/basemap/namedplc/convert" -d 'area=&country=hkg&step=2000'
