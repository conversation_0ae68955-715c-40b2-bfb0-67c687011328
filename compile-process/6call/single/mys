--1.convert to link node
curl -X "POST" "http://localhost:10095/api/road/herelink/convert" -d 'area=&rdfnavlinkfilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_nav_link.txt.001,/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_nav_link.txt.002&rdfcflinkfilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_link.txt&country=mys&rdfcfnodefilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_node.txt&rdfcffilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf.txt&iscompiletranseng=false&step=2000&iscompilenode=true&version='
curl -X "POST" "http://localhost:10095/api/road/herelink/updatemainsubnode" -d 'area=&rdfcflinkfilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_link.txt&country=mys&rdfcfnodefilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf_node.txt&rdfcffilepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_cf.txt'
curl -X "GET" "http://localhost:10095/common/nodeSw2021q133/handleNodeId?step=1400&area=&country=mys"

--2.convert to relation rule
curl -X "POST" "http://localhost:10095/api/road/herelinkreleation/convert" -d 'area=&country=mys&step=1260'
curl -X "POST" "http://localhost:10095/api/road/herelinkrule/convert" -d 'area=&country=mys&step=1900'

curl -X "POST" "http://localhost:10095/api/road/herelinkreleation/convert2" -d 'area=&country=mys&step=1260'
--3.left rule
curl -X "POST" "http://localhost:10095/api/road/herelink/leftruleconvert" -d 'area=&country=mys&step=1900&filepath=/data/oversea/here/2024/q1/AsiaPacific/rdf/txtDir/rdf_condition_divider.txt'
--3.1 left rule vehicle type
curl -X "GET" "http://localhost:10095/api/road/herelink/updateVehclType?area=&country=mys"

--4.convert to rp
curl -X "GET" "http://localhost:10095/api/road/herelink/convert2rp?area=&country=mys&step=2000"
curl -X "GET" "http://localhost:10095/common/nodeSw2021q133/convert2rp?area=&country=mys&step=2000"

--5.convert to point address
curl -X "POST" "http://localhost:10010/api/road/pointaddress/convert" -d 'area=&country=mys&step=10000&isCompileTrans=false&version=24q1'

--6.convert to poi
curl -X "POST" "http://localhost:10097/api/poi/herepoi/import" -d 'area=&country=mys&create=true&step=340&filePath=/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS2/MYS,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS3/MYS,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS4/MYS,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0/TQS5/MYS,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS2/MYS,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS3/MYS,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS4/MYS,/data/oversea/here/2024/q1/AsiaPacific/poi/PLACE_XML_231H0_WPZ_DPLAC/APAC_231H0_FAILED_MAP_BIND/TQS5/MYS'