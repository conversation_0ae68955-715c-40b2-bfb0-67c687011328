shp2pgsql -s 4326 -W uft-8 Streets.shp public streets_area1 | psql -h localhost -U postgres -d here_tha_2024_q2 -p 15999
shp2pgsql -s 4326 -W uft-8 Streets.shp public streets_area1 | psql -h localhost -U postgres -d here_tha_2024_q2 -p 15999
shp2pgsql -s 4326 -W uft-8 Streets.shp public streets_area1 | psql -h localhost -U postgres -d here_tha_2024_q2 -p 15999
shp2pgsql -s 4326 -W uft-8 Streets.shp public streets_area1 | psql -h localhost -U postgres -d here_tha_2024_q2 -p 15999
shp2pgsql -s 4326 -W uft-8 Streets.shp public streets_area1 | psql -h localhost -U postgres -d here_tha_2024_q2 -p 15999
shp2pgsql -s 4326 -W uft-8 Streets.shp public streets_area20 | psql -h localhost -U postgres -d here_india_2024_q2 -p 15999
shp2pgsql -s 4326 -W uft-8 Streets.shp public streets_area21 | psql -h localhost -U postgres -d here_india_2024_q2 -p 15999
shp2pgsql -s 4326 -W uft-8 Lane.shp public lane | psql -h localhost -U postgres -d here_phl_2024_q2 -p 15999
-- mtddst
-- tha
shp2pgsql -s 4326 -W LATIN1 MtdDST.shp public.mtddst_area1 | psql -h localhost -U postgres -d here_tha_2024_q2 -p 15999

-------------------------------------------------------------------
ban streets
tha streets area1~5
india streets area9,13,20,21
curl -X "POST" "http://localhost:10089/shp2db" -d 'url=127.0.0.1&port=15999&dbName=here_ban_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&shpFilePath=/data/oversea/here/2024/q2/ban/road/Streets.shp&pgTableName=streets'

curl -X "POST" "http://localhost:10089/shp2db" -d 'url=127.0.0.1&port=15999&dbName=here_tha_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&shpFilePath=/data/oversea/here/2024/q2/tha/road/J1AM241E0WJ1000AACVH/Streets.shp&pgTableName=streets_area1'
curl -X "POST" "http://localhost:10089/shp2db" -d 'url=127.0.0.1&port=15999&dbName=here_tha_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&shpFilePath=/data/oversea/here/2024/q2/tha/road/J2AM241E0WJ2000AACVH/Streets.shp&pgTableName=streets_area2'
curl -X "POST" "http://localhost:10089/shp2db" -d 'url=127.0.0.1&port=15999&dbName=here_tha_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&shpFilePath=/data/oversea/here/2024/q2/tha/road/J3AM241E0WJ3000AACVH/Streets.shp&pgTableName=streets_area3'
curl -X "POST" "http://localhost:10089/shp2db" -d 'url=127.0.0.1&port=15999&dbName=here_tha_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&shpFilePath=/data/oversea/here/2024/q2/tha/road/J4AM241E0WJ4000AACVH/Streets.shp&pgTableName=streets_area4'
curl -X "POST" "http://localhost:10089/shp2db" -d 'url=127.0.0.1&port=15999&dbName=here_tha_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&shpFilePath=/data/oversea/here/2024/q2/tha/road/J5AM241E0WJ5000AACVH/Streets.shp&pgTableName=streets_area5'

curl -X "POST" "http://localhost:10089/shp2db" -d 'url=127.0.0.1&port=15999&dbName=here_india_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&shpFilePath=/data/oversea/here/2024/q2/ind/road/5LAM241E0W5L000AACV5/Streets.shp&pgTableName=streets_area9'
curl -X "POST" "http://localhost:10089/shp2db" -d 'url=127.0.0.1&port=15999&dbName=here_india_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&shpFilePath=/data/oversea/here/2024/q2/ind/road/5QAM241E0W5Q000AACV5/Streets.shp&pgTableName=streets_area13'
curl -X "POST" "http://localhost:10089/shp2db" -d 'url=127.0.0.1&port=15999&dbName=here_india_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&shpFilePath=/data/oversea/here/2024/q2/ind/road/6QAM241E0W6Q000AACV5/Streets.shp&pgTableName=streets_area20'
curl -X "POST" "http://localhost:10089/shp2db" -d 'url=127.0.0.1&port=15999&dbName=here_india_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&shpFilePath=/data/oversea/here/2024/q2/ind/road/6SAM241E0W6S000AACV5/Streets.shp&pgTableName=streets_area21'

tha point_address1
curl -X "POST" "http://localhost:10089/shp2db" -d 'url=127.0.0.1&port=15999&dbName=here_tha_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&shpFilePath=/data/oversea/here/2024/q2/tha/road/J1AM241E0WJ1000AACVH/PointAddress.shp&pgTableName=pointaddress_area1'

--需更改表的主键fid -> gid

alter table public.streets
    rename column fid to gid;

alter table public.streets_area1
    rename column fid to gid;
alter table public.streets_area2
    rename column fid to gid;
alter table public.streets_area3
    rename column fid to gid;
alter table public.streets_area4
    rename column fid to gid;
alter table public.streets_area5
    rename column fid to gid;


alter table public.streets_area9
    rename column fid to gid;
alter table public.streets_area13
    rename column fid to gid;
alter table public.streets_area20
    rename column fid to gid;
alter table public.streets_area21
    rename column fid to gid;
