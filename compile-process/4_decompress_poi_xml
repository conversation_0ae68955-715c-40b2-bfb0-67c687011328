# handle poi
# -----------------------------------------------------------------
#!/bin/bash

# 验证参数数量
if [ "$#" -ne 1 ]; then
    echo "使用方法: $0 <国家代码>"
    exit 1
fi

# 要解压的国家代码
COUNTRY_CODE=$1

# 定义包含所有文件夹名称的数组
FOLDERS=(TQS2 TQS3 TQS4 TQS5)

# 遍历数组中的每个文件夹
for folder in "${FOLDERS[@]}"; do
    # 检查对应的国家文件夹是否存在
    if [ -d "$folder/$COUNTRY_CODE" ]; then
        # 查找.gz文件并逐个解压
        find "$folder/$COUNTRY_CODE" -name '*.gz' -exec gunzip '{}' \;
        echo "已解压 $folder/$COUNTRY_CODE 中的所有.gz文件。
        "
    else
        echo "文件夹 $folder/$COUNTRY_CODE 不存在。"
    fi
done

echo "解压完成。"
# -----------------------------------------------------------------

# asia hkg india(ban) twn northamerica southamerica

#cp poi_decompress.sh {target_directory}

#cd /data/oversea/here/2024/q2/AsiaPacific/poi/PLACE_XML_241E0_WPZ_DPLAC/APAC_241E0
#cd /data/oversea/here/2024/q2/AsiaPacific/poi/PLACE_XML_241E0_WPZ_DPLAC/APAC_241E0_FAILED_MAP_BIND
sh poi_decompress.sh IDN
sh poi_decompress.sh MYS
sh poi_decompress.sh PHL
sh poi_decompress.sh SGP
sh poi_decompress.sh THA
sh poi_decompress.sh VNM

#cd /data/oversea/here/2024/q2/hkg/poi/PLACE_XML_241P0_HK_DPLAC/HKG_241P0
#cd /data/oversea/here/2024/q2/hkg/poi/PLACE_XML_241P0_HK_DPLAC/HKG_241P0_FAILED_MAP_BIND
sh poi_decompress.sh HKG

#cd /data/oversea/here/2024/q2/ind/poi/PLACE_XML_241E0_WRR_DPLAC/INDIA_241E0
#cd /data/oversea/here/2024/q2/ind/poi/PLACE_XML_241E0_WRR_DPLAC/INDIA_241E0_FAILED_MAP_BIND
sh poi_decompress.sh BGD
sh poi_decompress.sh IND

#cd /data/oversea/here/2024/q2/twn/poi/PLACE_XML_241E0_WVD_DPLAC/TWN_241E0
#cd /data/oversea/here/2024/q2/twn/poi/PLACE_XML_241E0_WVD_DPLAC/TWN_241E0_FAILED_MAP_BIND
sh poi_decompress.sh TWN

#cd /data/oversea/here/2024/q2/NorthAmerica/poi/PLACE_XML_241E0_NNA_DPLAC/NA_241E0
#cd /data/oversea/here/2024/q2/NorthAmerica/poi/PLACE_XML_241E0_NNA_DPLAC/NA_241E0_FAILED_MAP_BIND
sh poi_decompress.sh MEX

#cd /data/oversea/here/2024/q2/SouthAmerica/poi/PLACE_XML_241E0_WSZ_DPLAC/SAM_241E0
#cd /data/oversea/here/2024/q2/SouthAmerica/poi/PLACE_XML_241E0_WSZ_DPLAC/SAM_241E0_FAILED_MAP_BIND
sh poi_decompress.sh BRA
