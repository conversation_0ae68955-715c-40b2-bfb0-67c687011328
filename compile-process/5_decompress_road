# distribute.sh
# -----------------------------------------------------------------
#!/bin/bash
file_to_copy="import.py"

for folder in */; do
  cp "$file_to_copy" "$folder"
done
# -----------------------------------------------------------------

# 1.single area
# import.py
# -----------------------------------------------------------------
import sys
import os
import time

areaappend =''
database =''
layerList=[]
if len(sys.argv) > 4:
    area = sys.argv[1]
    if area == '0':
        areaappend = ''
    else:
        areaappend = '_area'+area
    database = sys.argv[2]
    lancode = sys.argv[3]
    layers = sys.argv[4]
    if layers == 'all':
        layerList = ['Streets','Zlevels','AltStreets','StreetTrans','Cdms','CdmsDtmod','CndMod','Rdms','MtdArea','MtdRef','PointAddress','PntAddrTrans','Adminbndy1','Adminbndy2','Adminbndy3','Adminbndy4','Adminbndy5','RailRds','WaterSeg','Islands','LandUseA','LandUseB','WaterPoly','Oceans','NamedPlc','Hamlet','Landmark','AdminLine1','AdminLine2','MtdDST']
    elif layers == 'line':
        layerList = ['Streets','Zlevels','AltStreets','StreetTrans','Cdms','CdmsDtmod','CndMod','Rdms']
    elif layers == 'poionly':
        layerList = ['Streets','MtdArea','MtdRef']
    elif layers == 'poiadd':
        layerList = ['MtdArea','MtdRef']
    elif layers == 'basemap':
        layerList = ['RailRds','WaterSeg','Islands','LandUseA','LandUseB','WaterPoly','Oceans','NamedPlc','Hamlet','Landmark','AdminLine1','AdminLine2']
    elif layers == 'tool':
        layerList = ['MtdArea','Adminbndy1','Adminbndy2','Adminbndy3','Adminbndy4','Adminbndy5']
    elif layers == 'task':
        layerList = ['MtdDST']
    else:
        layerList = layers.split(',')
    for layer in layerList:
        print ('start import '+ layer + '.shp')
        begin_time = time.time()
        os.system('shp2pgsql -s 4326 -W \"'+lancode+'\" '+layer+'.shp public.'+layer.lower()+areaappend+' | psql -h localhost -U postgres -d ' + database + ' -p 15999')
        end_time = time.time()
        print ('finish import '+ layer + '.shp,time is(s):' ,end_time-begin_time)
else:
    print ('args are less than 5,please run as python import.py areanum(0-9) database lancode(LATIN1/utf-8) layers(\'Streets,Zlevels,AltStreets,StreetTrans,Cdms,CdmsDtmod,CndMod,Rdms,MtdArea,MtdRef\')')
# -----------------------------------------------------------------
cd /data/oversea/here
cp import.py /data/oversea/here/2024/q2/hkg/road
cp import.py /data/oversea/here/2024/q2/mys/road
cp import.py /data/oversea/here/2024/q2/phl/road
cp import.py /data/oversea/here/2024/q2/sgp/road
cp import.py /data/oversea/here/2024/q2/ban/road
cd /data/oversea/here/2024/q2/{}/road
nohup python3 import.py 0 here_hkg_2024_q2 utf-8 all 15999 &
nohup python3 import.py 0 here_mys_2024_q2 utf-8 all 15999 &
nohup python3 import.py 0 here_phl_2024_q2 utf-8 all 15999 &
nohup python3 import.py 0 here_sgp_2024_q2 utf-8 all 15999 &
nohup python3 import.py 0 here_ban_2024_q2 utf-8 all 15999 &

# 2. multi area
# run_import.sh
# -----------------------------------------------------------------
#!/bin/bash
# 如果没有提供参数，则退出脚本并显示错误消息
if [ -z "$1" ]; then
    echo "请提供必要的参数。使用方法: ./run_import.sh [参数]"
    exit 1
fi

# 定义计数器
counter=1

# 获取并排序当前目录下的所有文件夹
for folder in $(ls -d */ | sort); do
    # 进入文件夹
    cd $folder
    # 执行命令，其中$1是传入的参数
    nohup python3 import.py ${counter} $1 utf-8 all 15999 &
    # 返回到外层目录
    cd ..
    # 计数器增加
    counter=$((counter + 1))
done
# -----------------------------------------------------------------
cd /data/oversea/here
cp import.py run_import.sh distribute.sh /data/oversea/here/2024/q2/bra/road
cp import.py run_import.sh distribute.sh /data/oversea/here/2024/q2/idn/road
cp import.py run_import.sh distribute.sh /data/oversea/here/2024/q2/ind/road
cp import.py run_import.sh distribute.sh /data/oversea/here/2024/q2/mex/road
cp import.py run_import.sh distribute.sh /data/oversea/here/2024/q2/tha/road
cp import.py run_import.sh distribute.sh /data/oversea/here/2024/q2/twn/road
cp import.py run_import.sh distribute.sh /data/oversea/here/2024/q2/vnm/road
sh distribute.sh
~~~
nohup sh run_import.sh here_bra_2024_q2 &
nohup sh run_import.sh here_ind_2024_q2 &
nohup sh run_import.sh here_india_2024_q2 &
nohup sh run_import.sh here_mex_2024_q2 &
nohup sh run_import.sh here_tha_2024_q2 &
nohup sh run_import.sh here_twn_2024_q2 &
nohup sh run_import.sh here_vnm_2024_q2 &
