curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_hkg_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_linkid&pgTableName=link&geojsonpath=/data/oversea/here/output/2024/geojson/q2/hkg/link.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_hkg_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_nodeid&pgTableName=node&geojsonpath=/data/oversea/here/output/2024/geojson/q2/hkg/node.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_hkg_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=relationid&pgTableName=relation&geojsonpath=/data/oversea/here/output/2024/geojson/q2/hkg/relation.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_hkg_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=rule_id&pgTableName=rule&geojsonpath=/data/oversea/here/output/2024/geojson/q2/hkg/rule.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_hkg_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=source_id&pgTableName=poi&geojsonpath=/data/oversea/here/output/2024/geojson/q2/hkg/poi.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_hkg_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hn_id&pgTableName=hn_point_address&geojsonpath=/data/oversea/here/output/2024/geojson/q2/hkg/point_address.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_mys_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_linkid&pgTableName=link&geojsonpath=/data/oversea/here/output/2024/geojson/q2/mys/link.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_mys_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_nodeid&pgTableName=node&geojsonpath=/data/oversea/here/output/2024/geojson/q2/mys/node.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_mys_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=relationid&pgTableName=relation&geojsonpath=/data/oversea/here/output/2024/geojson/q2/mys/relation.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_mys_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=rule_id&pgTableName=rule&geojsonpath=/data/oversea/here/output/2024/geojson/q2/mys/rule.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_mys_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=source_id&pgTableName=poi&geojsonpath=/data/oversea/here/output/2024/geojson/q2/mys/poi.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_mys_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hn_id&pgTableName=hn_point_address&geojsonpath=/data/oversea/here/output/2024/geojson/q2/mys/point_address.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_phl_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_linkid&pgTableName=link&geojsonpath=/data/oversea/here/output/2024/geojson/q2/phl/link.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_phl_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_nodeid&pgTableName=node&geojsonpath=/data/oversea/here/output/2024/geojson/q2/phl/node.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_phl_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=relationid&pgTableName=relation&geojsonpath=/data/oversea/here/output/2024/geojson/q2/phl/relation.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_phl_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=rule_id&pgTableName=rule&geojsonpath=/data/oversea/here/output/2024/geojson/q2/phl/rule.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_phl_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=source_id&pgTableName=poi&geojsonpath=/data/oversea/here/output/2024/geojson/q2/phl/poi.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_phl_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hn_id&pgTableName=hn_point_address&geojsonpath=/data/oversea/here/output/2024/geojson/q2/phl/point_address.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_sgp_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_linkid&pgTableName=link&geojsonpath=/data/oversea/here/output/2024/geojson/q2/sgp/link.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_sgp_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_nodeid&pgTableName=node&geojsonpath=/data/oversea/here/output/2024/geojson/q2/sgp/node.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_sgp_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=relationid&pgTableName=relation&geojsonpath=/data/oversea/here/output/2024/geojson/q2/sgp/relation.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_sgp_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=rule_id&pgTableName=rule&geojsonpath=/data/oversea/here/output/2024/geojson/q2/sgp/rule.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_sgp_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=source_id&pgTableName=poi&geojsonpath=/data/oversea/here/output/2024/geojson/q2/sgp/poi.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_sgp_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hn_id&pgTableName=hn_point_address&geojsonpath=/data/oversea/here/output/2024/geojson/q2/sgp/point_address.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_vnm_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_linkid&pgTableName=link&geojsonpath=/data/oversea/here/output/2024/geojson/q2/vnm/link.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_vnm_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_nodeid&pgTableName=node&geojsonpath=/data/oversea/here/output/2024/geojson/q2/vnm/node.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_vnm_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=relationid&pgTableName=relation&geojsonpath=/data/oversea/here/output/2024/geojson/q2/vnm/relation.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_vnm_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=rule_id&pgTableName=rule&geojsonpath=/data/oversea/here/output/2024/geojson/q2/vnm/rule.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_vnm_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=source_id&pgTableName=poi&geojsonpath=/data/oversea/here/output/2024/geojson/q2/vnm/poi.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_vnm_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hn_id&pgTableName=hn_point_address&geojsonpath=/data/oversea/here/output/2024/geojson/q2/vnm/point_address.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_bra_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_linkid&pgTableName=link&geojsonpath=/data/oversea/here/output/2024/geojson/q2/bra/link.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_bra_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_nodeid&pgTableName=node&geojsonpath=/data/oversea/here/output/2024/geojson/q2/bra/node.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_bra_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=relationid&pgTableName=relation&geojsonpath=/data/oversea/here/output/2024/geojson/q2/bra/relation.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_bra_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=rule_id&pgTableName=rule&geojsonpath=/data/oversea/here/output/2024/geojson/q2/bra/rule.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_bra_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=source_id&pgTableName=poi&geojsonpath=/data/oversea/here/output/2024/geojson/q2/bra/poi.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_bra_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hn_id&pgTableName=hn_point_address&geojsonpath=/data/oversea/here/output/2024/geojson/q2/bra/point_address.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_ind_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_linkid&pgTableName=link&geojsonpath=/data/oversea/here/output/2024/geojson/q2/ind/link.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_ind_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_nodeid&pgTableName=node&geojsonpath=/data/oversea/here/output/2024/geojson/q2/ind/node.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_ind_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=relationid&pgTableName=relation&geojsonpath=/data/oversea/here/output/2024/geojson/q2/ind/relation.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_ind_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=rule_id&pgTableName=rule&geojsonpath=/data/oversea/here/output/2024/geojson/q2/ind/rule.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_ind_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=source_id&pgTableName=poi&geojsonpath=/data/oversea/here/output/2024/geojson/q2/ind/poi.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_ind_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hn_id&pgTableName=hn_point_address&geojsonpath=/data/oversea/here/output/2024/geojson/q2/ind/point_address.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_mex_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_linkid&pgTableName=link&geojsonpath=/data/oversea/here/output/2024/geojson/q2/mex/link.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_mex_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_nodeid&pgTableName=node&geojsonpath=/data/oversea/here/output/2024/geojson/q2/mex/node.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_mex_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=relationid&pgTableName=relation&geojsonpath=/data/oversea/here/output/2024/geojson/q2/mex/relation.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_mex_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=rule_id&pgTableName=rule&geojsonpath=/data/oversea/here/output/2024/geojson/q2/mex/rule.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_mex_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=source_id&pgTableName=poi&geojsonpath=/data/oversea/here/output/2024/geojson/q2/mex/poi.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_mex_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hn_id&pgTableName=hn_point_address&geojsonpath=/data/oversea/here/output/2024/geojson/q2/mex/point_address.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_tha_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_linkid&pgTableName=link&geojsonpath=/data/oversea/here/output/2024/geojson/q2/tha/link.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_tha_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_nodeid&pgTableName=node&geojsonpath=/data/oversea/here/output/2024/geojson/q2/tha/node.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_tha_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=relationid&pgTableName=relation&geojsonpath=/data/oversea/here/output/2024/geojson/q2/tha/relation.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_tha_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=rule_id&pgTableName=rule&geojsonpath=/data/oversea/here/output/2024/geojson/q2/tha/rule.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_tha_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=source_id&pgTableName=poi&geojsonpath=/data/oversea/here/output/2024/geojson/q2/tha/poi.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_tha_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hn_id&pgTableName=hn_point_address&geojsonpath=/data/oversea/here/output/2024/geojson/q2/tha/point_address.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_twn_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_linkid&pgTableName=link&geojsonpath=/data/oversea/here/output/2024/geojson/q2/twn/link.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_twn_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_nodeid&pgTableName=node&geojsonpath=/data/oversea/here/output/2024/geojson/q2/twn/node.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_twn_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=relationid&pgTableName=relation&geojsonpath=/data/oversea/here/output/2024/geojson/q2/twn/relation.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_twn_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=rule_id&pgTableName=rule&geojsonpath=/data/oversea/here/output/2024/geojson/q2/twn/rule.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_twn_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=source_id&pgTableName=poi&geojsonpath=/data/oversea/here/output/2024/geojson/q2/twn/poi.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_twn_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hn_id&pgTableName=hn_point_address&geojsonpath=/data/oversea/here/output/2024/geojson/q2/twn/point_address.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_india_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_linkid&pgTableName=link&geojsonpath=/data/oversea/here/output/2024/geojson/q2/india/link.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_india_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_nodeid&pgTableName=node&geojsonpath=/data/oversea/here/output/2024/geojson/q2/india/node.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_india_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=relationid&pgTableName=relation&geojsonpath=/data/oversea/here/output/2024/geojson/q2/india/relation.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_india_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=rule_id&pgTableName=rule&geojsonpath=/data/oversea/here/output/2024/geojson/q2/india/rule.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_india_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=source_id&pgTableName=poi&geojsonpath=/data/oversea/here/output/2024/geojson/q2/india/poi.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_india_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hn_id&pgTableName=hn_point_address&geojsonpath=/data/oversea/here/output/2024/geojson/q2/india/point_address.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_ban_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_linkid&pgTableName=link&geojsonpath=/data/oversea/here/output/2024/geojson/q2/ban/link.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_ban_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hll_nodeid&pgTableName=node&geojsonpath=/data/oversea/here/output/2024/geojson/q2/ban/node.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_ban_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=relationid&pgTableName=relation&geojsonpath=/data/oversea/here/output/2024/geojson/q2/ban/relation.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_ban_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=rule_id&pgTableName=rule&geojsonpath=/data/oversea/here/output/2024/geojson/q2/ban/rule.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_ban_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=source_id&pgTableName=poi&geojsonpath=/data/oversea/here/output/2024/geojson/q2/ban/poi.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=**************&port=15999&dbName=hll_oversea_h_ban_2024_q2&dbUser=postgres&dbPwd=Huolala@2021&pIdName=hn_id&pgTableName=hn_point_address&geojsonpath=/data/oversea/here/output/2024/geojson/q2/ban/point_address.geojson'












--test
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=***************&port=5432&dbName=hll_oversea_h_phl_2023_q4&dbUser=postgres&dbPwd=1q2w3e&pIdName=id&pgTableName=bline&geojsonpath=/data/oversea/here/output/2023/geojson/q4/phl/bline.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=***************&port=5432&dbName=hll_oversea_h_phl_2023_q4&dbUser=postgres&dbPwd=1q2w3e&pIdName=id&pgTableName=bpolygon&geojsonpath=/data/oversea/here/output/2023/geojson/q4/phl/bpolygon.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=***************&port=5432&dbName=hll_oversea_h_phl_2023_q4&dbUser=postgres&dbPwd=1q2w3e&pIdName=id&pgTableName=building&geojsonpath=/data/oversea/here/output/2023/geojson/q4/phl/building.geojson'
curl -X "POST" "http://localhost:10089/pgtable2geojson" -d 'url=***************&port=5432&dbName=hll_oversea_h_phl_2023_q4&dbUser=postgres&dbPwd=1q2w3e&pIdName=id&pgTableName=namedplc&geojsonpath=/data/oversea/here/output/2023/geojson/q4/phl/namedplc.geojson'

curl -X "POST" "http://localhost:10091/pgtable2geojson" -d 'url=***************&port=5432&dbName=hll_oversea_h_phl_2023_q4&dbUser=postgres&dbPwd=1q2w3e&pIdName=id&pgTableName=bline&geojsonpath=/data/oversea/here/output/2023/geojson/q4/phl/bline.geojson'
curl -X "POST" "http://localhost:10091/pgtable2geojson" -d 'url=***************&port=5432&dbName=hll_oversea_h_phl_2023_q4&dbUser=postgres&dbPwd=1q2w3e&pIdName=id&pgTableName=bpolygon&geojsonpath=/Users/<USER>/bpolygon.geojson'
