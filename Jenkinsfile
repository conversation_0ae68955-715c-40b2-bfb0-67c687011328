pipeline {
    agent any
    options {
       timeout(time: 5, unit: 'MINUTES')
    }
    environment {
       serviceVersion = 'v1.3.6'
       deployName = 'oversea'
       serviceName = 'oversea-compile-service'
       registryDev = '**************:5000'
       registryPrd = '**************:5000'
    }
    stages {
        stage('Clear dir') {
            steps {
                sh 'pwd'
//                 deleteDir()
            }
        }
        stage('Compile') {
            agent {
                docker {
                    image 'gradle:6.7.1-jdk8-hotspot'
                    args '-v /data/gradle:/home/<USER>/.gradle'
                }
            }
            steps {
                sh 'gradle :business:clean :business:bootJar'
                stash includes: 'business/Dockerfile,business/build/libs/*jar', name: 'app'
            }
        }
        stage('Build') {
            steps {
                unstash name: 'app'
                sh "cp -f business/Dockerfile business/build/libs && cd business/build/libs/ && docker build -t ${serviceName}:${serviceVersion} ."
                sh "docker tag ${serviceName}:${serviceVersion} ${registryDev}/${serviceName}:latest"
                sh "docker tag ${serviceName}:${serviceVersion} ${registryDev}/${serviceName}:${serviceVersion}"
                sh "docker tag ${serviceName}:${serviceVersion} ${registryPrd}/${serviceName}:latest"
                sh "docker tag ${serviceName}:${serviceVersion} ${registryPrd}/${serviceName}:${serviceVersion}"
                sh "docker push ${registryDev}/${serviceName}:latest"
                sh "docker push ${registryDev}/${serviceName}:${serviceVersion}"
                sh "docker push ${registryPrd}/${serviceName}:latest"
                sh "docker push ${registryPrd}/${serviceName}:${serviceVersion}"
            }
        }
        stage('Deploy') {
            steps {
                sh "kubectl set image deployment/${deployName} --context=hll-dev -n work ${deployName}=${registryDev}/${serviceName}:${serviceVersion}"
            }

            post {
                  success {
                    echo 'deploy success'
                    echo 'swagger:http://192.168.106.45:8083/swagger-ui.html'
                  }

                  unstable {
                    echo 'I am unstable :/'
                  }

                  failure {
                    echo 'I failed :('
                  }

                  changed {
                    echo 'Things were different before...'
                  }
            }
        }
    }
}