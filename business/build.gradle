plugins {
    id 'java'
}


group 'com.hll'
version '0.0.1-SNAPSHOT'
archivesBaseName = 'mapdataservice'

repositories {
    mavenCentral()
}

dependencies {

    implementation 'junit:junit:4.12'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    apply plugin: "org.springframework.boot"
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.6.0'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-mail'

    implementation 'com.baomidou:mybatis-plus-boot-starter'
    implementation group: 'com.baomidou', name: 'mybatis-plus', version: '3.4.2'
    implementation group: 'com.baomidou', name: 'mybatis-plus-boot-starter', version: '3.4.2'
    implementation group: 'com.baomidou', name: 'mybatis-plus-extension', version: '3.4.2'
    compile project(":common")

//    compile 'com.alibaba:fastjson:2.0.15'
    compile 'com.alibaba:druid:1.2.5'
    implementation group: 'com.spring4all', name: 'swagger-spring-boot-starter', version: '1.9.1.RELEASE'
    //cloud-starters
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    //dynamic-datasource
    implementation group: 'com.baomidou', name: 'dynamic-datasource-spring-boot-starter', version: '3.3.1'
    //excel
    implementation group: 'org.jxls', name: 'jxls-poi', version: '2.8.1'
    implementation group: 'org.jxls', name: 'jxls', version: '2.8.1'
    implementation group: 'org.postgis', name: 'postgis-jdbc', version: '1.3.3'
    // https://mvnrepository.com/artifact/net.postgis/postgis-jdbc
    implementation group: 'net.postgis', name: 'postgis-jdbc', version: '2.5.0'
// https://mvnrepository.com/artifact/com.alibaba/easyexcel
//    implementation group: 'com.alibaba', name: 'easyexcel', version: '3.1.5'

    implementation group: 'cn.hutool', name: 'hutool-all', version: '5.5.9'
    // https://mvnrepository.com/artifact/org.postgresql/postgresql
    implementation group: 'org.postgresql', name: 'postgresql', version: '42.7.3'
    // https://mvnrepository.com/artifact/mysql/mysql-connector-java
    implementation group: 'mysql', name: 'mysql-connector-java', version: '8.0.30'
    // https://mvnrepository.com/artifact/org.projectlombok/lombok
    compileOnly group: 'org.projectlombok', name: 'lombok', version: '1.18.18'
    annotationProcessor 'org.projectlombok:lombok:1.18.8'
    // https://mvnrepository.com/artifact/org.jxls/jxls
    implementation group: 'org.jxls', name: 'jxls', version: '2.10.0'
    // https://mvnrepository.com/artifact/org.jxls/jxls-poi
    implementation group: 'org.jxls', name: 'jxls-poi', version: '2.10.0'
    // https://mvnrepository.com/artifact/com.vividsolutions/jts
    implementation group: 'com.vividsolutions', name: 'jts', version: '1.13'
    //implementation group: 'org.apache.poi', name: 'poi', version: '5.0.0'
    // https://mvnrepository.com/artifact/p6spy/p6spy
    implementation group: 'p6spy', name: 'p6spy', version: '3.9.1'
    // https://mvnrepository.com/artifact/com.github.albfernandez/javadbf
    implementation group: 'com.github.albfernandez', name: 'javadbf', version: '1.13.1'
    // https://mvnrepository.com/artifact/org.apache.commons/commons-text
    implementation group: 'org.apache.commons', name: 'commons-text', version: '1.9'
    // https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-configuration-processor
    //implementation group: 'org.springframework.boot', name: 'spring-boot-configuration-processor', version: '2.5.3'

    // https://mvnrepository.com/artifact/com.carrotsearch/hppc
    implementation group: 'com.carrotsearch', name: 'hppc', version: '0.9.1'
    implementation group: 'com.graphhopper', name: 'graphhopper-map-matching-core', version: '2.0'

    // https://mvnrepository.com/artifact/com.alibaba/fastjson
    implementation group: 'com.alibaba', name: 'fastjson', version: '1.2.83'

    // https://mvnrepository.com/artifact/org.json/json
    implementation group: 'org.json', name: 'json', version: '20210307'
    dependencies {
        annotationProcessor "org.springframework.boot:spring-boot-configuration-processor"
    }
    // https://mvnrepository.com/artifact/com.graphhopper/graphhopper-map-matching-core
    implementation group: 'com.graphhopper', name: 'graphhopper-map-matching-core', version: '2.0'
// https://mvnrepository.com/artifact/com.xuxueli/xxl-job-core
    implementation group: 'com.xuxueli', name: 'xxl-job-core', version: '2.3.0'
    // https://mvnrepository.com/artifact/io.minio/minio
    implementation group: 'io.minio', name: 'minio', version: '8.3.9'

    // https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-data-redis
    implementation group: 'org.springframework.boot', name: 'spring-boot-starter-data-redis', version: '2.3.3.RELEASE'

    // https://mvnrepository.com/artifact/com.alibaba.fastjson2/fastjson2
    implementation group: 'com.alibaba.fastjson2', name: 'fastjson2', version: '2.0.43'
    // https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-databind
//    implementation group: 'com.fasterxml.jackson.core', name: 'jackson-databind', version: '2.18.2'

}

test {
    useJUnitPlatform()
}