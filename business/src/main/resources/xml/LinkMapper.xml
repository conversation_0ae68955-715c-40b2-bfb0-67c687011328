<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.LinkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.Link">
        <id column="hll_linkid" property="hllLinkid" />
        <result column="link_id" property="linkId" />
        <result column="hll_s_nid" property="hllSNid" />
        <result column="hll_e_nid" property="hllENid" />
        <result column="kind" property="kind" />
        <result column="formway" property="formway" />
        <result column="dir" property="dir" />
        <result column="app" property="app" />
        <result column="toll" property="toll" />
        <result column="adopt" property="adopt" />
        <result column="md" property="md" />
        <result column="devs" property="devs" />
        <result column="spet" property="spet" />
        <result column="funct" property="funct" />
        <result column="urban" property="urban" />
        <result column="pave" property="pave" />
        <result column="lane_n" property="laneN" />
        <result column="lane_l" property="laneL" />
        <result column="lane_r" property="laneR" />
        <result column="lane_c" property="laneC" />
        <result column="width" property="width" />
        <result column="viad" property="viad" />
        <result column="l_admin" property="lAdmin" />
        <result column="r_admin" property="rAdmin" />
        <result column="geom" property="geom" />
        <result column="len" property="len" />
        <result column="f_speed" property="fSpeed" />
        <result column="t_speed" property="tSpeed" />
        <result column="sp_class" property="spClass" />
        <result column="dici_type" property="diciType" />
        <result column="verifyflag" property="verifyflag" />
        <result column="pre_launch" property="preLaunch" />
        <result column="name_ch_o" property="nameChO" />
        <result column="name_ch_a" property="nameChA" />
        <result column="name_ch_f" property="nameChF" />
        <result column="name_ph_o" property="namePhO" />
        <result column="name_ph_a" property="namePhA" />
        <result column="name_ph_f" property="namePhF" />
        <result column="name_en_o" property="nameEnO" />
        <result column="name_en_a" property="nameEnA" />
        <result column="name_en_f" property="nameEnF" />
        <result column="name_po" property="namePo" />
        <result column="name_cht" property="nameCht" />
        <result column="code_type" property="codeType" />
        <result column="name_type" property="nameType" />
        <result column="src_flag" property="srcFlag" />
        <result column="mesh_id" property="meshId" />
        <result column="memo" property="memo" />
        <result column="cp" property="cp" />
        <result column="datasource" property="datasource" />
        <result column="up_date" property="upDate" />
        <result column="status" property="status" />
        <result column="geomwkt" property="geomwkt" />
    </resultMap>

    <sql id="Base_Column_List">
        hll_linkid, link_id, hll_s_nid, hll_e_nid, kind, formway, dir, app, toll, adopt,
        md, devs, spet, funct, urban, pave, lane_n, lane_l, lane_r, lane_c, width, viad,
        l_admin, r_admin, geom, len, f_speed, t_speed, sp_class, dici_type, verifyflag,
        pre_launch, name_ch_o, name_ch_a, name_ch_f, name_ph_o, name_ph_a, name_ph_f, name_en_o,
        name_en_a, name_en_f, name_po, name_cht, code_type, name_type, src_flag, mesh_id, memo, cp,
        datasource, up_date, status, geomwkt, ar_veh
    </sql>

    <select id="streamQueryLink" resultMap="BaseResultMap" fetchSize="1000">
        select * from link
    </select>

    <insert id="saveBatch">
        insert into link (<include refid="Base_Column_List"/>)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.hllLinkid}, #{item.linkId}, #{item.hllSNid}, #{item.hllENid}, #{item.kind}, #{item.formway}, #{item.dir}, #{item.app}, #{item.toll}, #{item.adopt},
            #{item.md}, #{item.devs}, #{item.spet}, #{item.funct}, #{item.urban}, #{item.pave}, #{item.laneN}, #{item.laneL}, #{item.laneR}, #{item.laneC}, #{item.width}, #{item.viad},
            #{item.lAdmin}, #{item.rAdmin}, st_geomfromtext(#{item.geomwkt},4326), #{item.len}, #{item.fSpeed}, #{item.tSpeed}, #{item.spClass}, #{item.diciType}, #{item.verifyflag},
            #{item.preLaunch}, #{item.nameChO}, #{item.nameChA}, #{item.nameChF}, #{item.namePhO}, #{item.namePhA}, #{item.namePhF}, #{item.nameEnO},
            #{item.nameEnA}, #{item.nameEnF}, #{item.namePo}, #{item.nameCht}, #{item.codeType}, #{item.nameType}, #{item.srcFlag}, #{item.meshId}, #{item.memo}, #{item.cp},
            #{item.datasource}, #{item.upDate}, #{item.status}, #{item.geomwkt}, #{item.arVeh})
        </foreach>
    </insert>
</mapper>
