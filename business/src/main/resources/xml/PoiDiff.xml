<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.PoiDiffMapper">
    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO poi_diff (dataid,source,name,address,kind,diff_type,lon,lat,pre_quarter,current_quarter,create_time,update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.dataid}, #{item.source}, #{item.name}, #{item.address}, #{item.kind}, #{item.diffType}, #{item.lon}, #{item.lat}, #{item.preQuarter}, #{item.currentQuarter}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>
</mapper>
