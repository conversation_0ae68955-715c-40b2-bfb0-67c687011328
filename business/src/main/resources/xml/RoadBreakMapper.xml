<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.RoadBreakMapper">
    <!--  <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.RoadBreak">-->
    <!--    &lt;!&ndash;@mbg.generated&ndash;&gt;-->
    <!--    &lt;!&ndash;@Table road_break&ndash;&gt;-->
    <!--    <id column="road_id" jdbcType="INTEGER" property="roadId" />-->
    <!--    <result column="osm_id" jdbcType="VARCHAR" property="osmId" />-->
    <!--    <result column="road_geom" jdbcType="VARCHAR" property="roadGeom" />-->
    <!--    <result column="osm_geom" jdbcType="VARCHAR" property="osmGeom" />-->
    <!--    <result column="code" jdbcType="INTEGER" property="code" />-->
    <!--    <result column="fclass" jdbcType="VARCHAR" property="fclass" />-->
    <!--    <result column="name" jdbcType="VARCHAR" property="name" />-->
    <!--    <result column="ref" jdbcType="VARCHAR" property="ref" />-->
    <!--    <result column="oneway" jdbcType="VARCHAR" property="oneway" />-->
    <!--    <result column="maxspeed" jdbcType="INTEGER" property="maxspeed" />-->
    <!--    <result column="layer" jdbcType="BIGINT" property="layer" />-->
    <!--    <result column="bridge" jdbcType="VARCHAR" property="bridge" />-->
    <!--    <result column="tunnel" jdbcType="VARCHAR" property="tunnel" />-->
    <!--    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />-->
    <!--    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />-->
    <!--    <result column="version" jdbcType="BIGINT" property="version" />-->
    <!--  </resultMap>-->
      <sql id="Base_Column_List">
        <!--@mbg.generated-->
        road_id, osm_id, road_geom, osm_geom, code, fclass, "name", "ref", oneway, maxspeed,
        layer, bridge, tunnel, create_time, update_time, version
      </sql>


    <insert id="batchInsertRoadBreak">
        insert into road_break (<include refid="Base_Column_List"/>) values
        <foreach collection="list" separator="," item="item">
            (#{item.roadId},#{item.osmId},st_geomfromtext(#{item.roadGeom},4326),#{item.osmGeom},#{item.code},#{item.fclass},#{item.name},#{item.ref},#{item.oneway}
            ,#{item.maxspeed},#{item.layer},#{item.bridge},#{item.tunnel},#{item.createTime},#{item.updateTime},#{item.version})
        </foreach>

    </insert>
</mapper>