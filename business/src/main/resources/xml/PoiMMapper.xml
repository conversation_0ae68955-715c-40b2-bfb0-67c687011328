<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.PoiMMapper">

    <!-- 通用查询映射结果 -->
    <select id="selectUnhandleIds" resultType="com.hll.mapdataservice.common.entity.PoiM">
        select * from poi_m where length(link_id)!= 18 limit #{param1} offset #{param2}
    </select>
</mapper>
