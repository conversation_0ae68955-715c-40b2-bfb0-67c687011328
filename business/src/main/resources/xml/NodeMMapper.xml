<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.NodeMMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.NodeM">
        <id column="hll_nodeid" property="hllNodeid" />
        <result column="kind" property="kind" />
        <result column="geom" property="geom" />
        <result column="name_ch" property="nameCh" />
        <result column="name_fo" property="nameFo" />
        <result column="name_cht" property="nameCht" />
        <result column="name_ph" property="namePh" />
        <result column="adjoin_mid" property="adjoinMid" />
        <result column="adjoin_nid" property="adjoinNid" />
        <result column="type" property="type" />
        <result column="mainnodeid" property="mainnodeid" />
        <result column="subnodeid" property="subnodeid" />
        <result column="subnodeid2" property="subnodeid2" />
        <result column="light" property="light" />
        <result column="is_pbnode" property="isPbnode" />
        <result column="cp" property="cp" />
        <result column="datasource" property="datasource" />
        <result column="node_id" property="nodeId" />
        <result column="up_date" property="upDate" />
        <result column="memo" property="memo" />
        <result column="status" property="status" />
        <result column="geom_wkt" property="geomWkt" />
    </resultMap>

    <select id="streamQueryNode" resultMap="BaseResultMap" fetchSize="1000">
        select * from node limit 600
    </select>

    <sql id="Base_Column_List">
        hll_nodeid, kind, geom, name_ch, name_fo, name_cht, name_ph, adjoin_mid, adjoin_nid, type,
        mainnodeid, subnodeid, subnodeid2, light, is_pbnode, cp, datasource, node_id, up_date, memo, status, geom_wkt

    </sql>

    <insert id="saveBatch">
        insert into node_m (<include refid="Base_Column_List"/>) values
        <foreach collection="list" separator="," item="item">
            (#{item.hllNodeid}, #{item.kind}, st_geomfromtext(#{item.geomWkt},4326), #{item.nameCh}, #{item.nameFo}, #{item.nameCht}, #{item.namePh}, #{item.adjoinMid}, #{item.adjoinNid}, #{item.type},
            #{item.mainnodeid}, #{item.subnodeid}, #{item.subnodeid2}, #{item.light}, #{item.isPbnode}, #{item.cp}, #{item.datasource}, #{item.nodeId}, #{item.upDate}, #{item.memo}, #{item.status}, #{item.geomWkt})
        </foreach>
    </insert>

    <select id="selectUnhandleIds" resultType="com.hll.mapdataservice.common.entity.NodeM">
        select * from node_m where mainnodeid is not null or subnodeid is not null or subnodeid2 is not null limit #{param1}  offset #{param2}
    </select>
</mapper>
