<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.RestrictionMapper">
  <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.Restriction">
    <!--@mbg.generated-->
    <!--@Table restriction-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="market" jdbcType="VARCHAR" property="market" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="city_id" jdbcType="VARCHAR" property="cityId" />
    <result column="link_id" jdbcType="VARCHAR" property="linkId" />
    <result column="road_name_en" jdbcType="VARCHAR" property="roadNameEn" />
    <result column="road_name_multi_lang" jdbcType="VARCHAR" property="roadNameMultiLang" />
    <result column="geom" jdbcType="OTHER" property="geom" />
    <result column="restricted_order_vehicle_id" jdbcType="VARCHAR" property="restrictedOrderVehicleId" />
    <result column="restricted_info" jdbcType="VARCHAR" property="restrictedInfo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, group_id, market, city, city_id, link_id, road_name_en, road_name_multi_lang,
    geom, restricted_order_vehicle_id, restricted_info
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from restriction
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from restriction
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.hll.mapdataservice.common.entity.Restriction">
    <!--@mbg.generated-->
    insert into restriction (id, group_id, market,
      city, city_id, link_id,
      road_name_en, road_name_multi_lang, geom,
      restricted_order_vehicle_id, restricted_info
      )
    values (#{id,jdbcType=VARCHAR}, #{groupId,jdbcType=VARCHAR}, #{market,jdbcType=VARCHAR},
      #{city,jdbcType=VARCHAR}, #{cityId,jdbcType=VARCHAR}, #{linkId,jdbcType=VARCHAR},
      #{roadNameEn,jdbcType=VARCHAR}, #{roadNameMultiLang,jdbcType=VARCHAR}, #{geom,jdbcType=OTHER},
      #{restrictedOrderVehicleId,jdbcType=VARCHAR}, #{restrictedInfo,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.hll.mapdataservice.common.entity.Restriction">
    <!--@mbg.generated-->
    insert into restriction
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="market != null">
        market,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
      <if test="linkId != null">
        link_id,
      </if>
      <if test="roadNameEn != null">
        road_name_en,
      </if>
      <if test="roadNameMultiLang != null">
        road_name_multi_lang,
      </if>
      <if test="geom != null">
        geom,
      </if>
      <if test="restrictedOrderVehicleId != null">
        restricted_order_vehicle_id,
      </if>
      <if test="restrictedInfo != null">
        restricted_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        #{market,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=VARCHAR},
      </if>
      <if test="linkId != null">
        #{linkId,jdbcType=VARCHAR},
      </if>
      <if test="roadNameEn != null">
        #{roadNameEn,jdbcType=VARCHAR},
      </if>
      <if test="roadNameMultiLang != null">
        #{roadNameMultiLang,jdbcType=VARCHAR},
      </if>
      <if test="geom != null">
        #{geom,jdbcType=OTHER},
      </if>
      <if test="restrictedOrderVehicleId != null">
        #{restrictedOrderVehicleId,jdbcType=VARCHAR},
      </if>
      <if test="restrictedInfo != null">
        #{restrictedInfo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.hll.mapdataservice.common.entity.Restriction">
    <!--@mbg.generated-->
    update restriction
    <set>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="market != null">
        market = #{market,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        city_id = #{cityId,jdbcType=VARCHAR},
      </if>
      <if test="linkId != null">
        link_id = #{linkId,jdbcType=VARCHAR},
      </if>
      <if test="roadNameEn != null">
        road_name_en = #{roadNameEn,jdbcType=VARCHAR},
      </if>
      <if test="roadNameMultiLang != null">
        road_name_multi_lang = #{roadNameMultiLang,jdbcType=VARCHAR},
      </if>
      <if test="geom != null">
        geom = #{geom,jdbcType=OTHER},
      </if>
      <if test="restrictedOrderVehicleId != null">
        restricted_order_vehicle_id = #{restrictedOrderVehicleId,jdbcType=VARCHAR},
      </if>
      <if test="restrictedInfo != null">
        restricted_info = #{restrictedInfo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hll.mapdataservice.common.entity.Restriction">
    <!--@mbg.generated-->
    update restriction
    set group_id = #{groupId,jdbcType=VARCHAR},
      market = #{market,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      city_id = #{cityId,jdbcType=VARCHAR},
      link_id = #{linkId,jdbcType=VARCHAR},
      road_name_en = #{roadNameEn,jdbcType=VARCHAR},
      road_name_multi_lang = #{roadNameMultiLang,jdbcType=VARCHAR},
      geom = #{geom,jdbcType=OTHER},
      restricted_order_vehicle_id = #{restrictedOrderVehicleId,jdbcType=VARCHAR},
      restricted_info = #{restrictedInfo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>