<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.PoiMatchMapper">
  <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.PoiMatch">
    <!--@mbg.generated-->
    <!--@Table poi_match-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="poi_geo" jdbcType="OTHER" property="poiGeo" />
    <result column="latitude" jdbcType="NUMERIC" property="latitude" />
    <result column="longitude" jdbcType="NUMERIC" property="longitude" />
    <result column="country_prefix" jdbcType="VARCHAR" property="countryPrefix" />
    <result column="poi_name" jdbcType="VARCHAR" property="poiName" />
    <result column="google_poi_name" jdbcType="VARCHAR" property="googlePoiName" />
    <result column="similarity" jdbcType="NUMERIC" property="similarity" />
    <result column="is_match" jdbcType="INTEGER" property="isMatch" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, poi_geo, latitude, longitude, country_prefix, poi_name, google_poi_name, similarity, 
    is_match, version, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from poi_match
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from poi_match
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.hll.mapdataservice.common.entity.PoiMatch">
    <!--@mbg.generated-->
    insert into poi_match (id, poi_geo, latitude, 
      longitude, country_prefix, poi_name, 
      google_poi_name, similarity, is_match, 
      version, create_time, update_time
      )
    values (#{id,jdbcType=VARCHAR}, #{poiGeo,jdbcType=OTHER}, #{latitude,jdbcType=NUMERIC}, 
      #{longitude,jdbcType=NUMERIC}, #{countryPrefix,jdbcType=VARCHAR}, #{poiName,jdbcType=VARCHAR}, 
      #{googlePoiName,jdbcType=VARCHAR}, #{similarity,jdbcType=NUMERIC}, #{isMatch,jdbcType=INTEGER}, 
      #{version,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.hll.mapdataservice.common.entity.PoiMatch">
    <!--@mbg.generated-->
    insert into poi_match
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="poiGeo != null">
        poi_geo,
      </if>
      <if test="latitude != null">
        latitude,
      </if>
      <if test="longitude != null">
        longitude,
      </if>
      <if test="countryPrefix != null">
        country_prefix,
      </if>
      <if test="poiName != null">
        poi_name,
      </if>
      <if test="googlePoiName != null">
        google_poi_name,
      </if>
      <if test="similarity != null">
        similarity,
      </if>
      <if test="isMatch != null">
        is_match,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="poiGeo != null">
        #{poiGeo,jdbcType=OTHER},
      </if>
      <if test="latitude != null">
        #{latitude,jdbcType=NUMERIC},
      </if>
      <if test="longitude != null">
        #{longitude,jdbcType=NUMERIC},
      </if>
      <if test="countryPrefix != null">
        #{countryPrefix,jdbcType=VARCHAR},
      </if>
      <if test="poiName != null">
        #{poiName,jdbcType=VARCHAR},
      </if>
      <if test="googlePoiName != null">
        #{googlePoiName,jdbcType=VARCHAR},
      </if>
      <if test="similarity != null">
        #{similarity,jdbcType=NUMERIC},
      </if>
      <if test="isMatch != null">
        #{isMatch,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.hll.mapdataservice.common.entity.PoiMatch">
    <!--@mbg.generated-->
    update poi_match
    <set>
      <if test="poiGeo != null">
        poi_geo = #{poiGeo,jdbcType=OTHER},
      </if>
      <if test="latitude != null">
        latitude = #{latitude,jdbcType=NUMERIC},
      </if>
      <if test="longitude != null">
        longitude = #{longitude,jdbcType=NUMERIC},
      </if>
      <if test="countryPrefix != null">
        country_prefix = #{countryPrefix,jdbcType=VARCHAR},
      </if>
      <if test="poiName != null">
        poi_name = #{poiName,jdbcType=VARCHAR},
      </if>
      <if test="googlePoiName != null">
        google_poi_name = #{googlePoiName,jdbcType=VARCHAR},
      </if>
      <if test="similarity != null">
        similarity = #{similarity,jdbcType=NUMERIC},
      </if>
      <if test="isMatch != null">
        is_match = #{isMatch,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hll.mapdataservice.common.entity.PoiMatch">
    <!--@mbg.generated-->
    update poi_match
    set poi_geo = #{poiGeo,jdbcType=OTHER},
      latitude = #{latitude,jdbcType=NUMERIC},
      longitude = #{longitude,jdbcType=NUMERIC},
      country_prefix = #{countryPrefix,jdbcType=VARCHAR},
      poi_name = #{poiName,jdbcType=VARCHAR},
      google_poi_name = #{googlePoiName,jdbcType=VARCHAR},
      similarity = #{similarity,jdbcType=NUMERIC},
      is_match = #{isMatch,jdbcType=INTEGER},
      version = #{version,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>