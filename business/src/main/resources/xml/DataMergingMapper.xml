<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.DataMergingMapper">

    <!-- Merge partition table data into consolidated table using PostgreSQL ON CONFLICT -->
    <insert id="mergePartitionTable">
        INSERT INTO ${targetTable} 
        SELECT * FROM ${sourceTable} 
        ON CONFLICT DO NOTHING
    </insert>

    <!-- Check if a table exists in the current database -->
    <select id="checkTableExists" resultType="int">
        SELECT COUNT(*)
        FROM information_schema.tables 
        WHERE table_name = #{tableName}
        AND table_schema = current_schema()
    </select>

    <!-- Get the count of records in a table -->
    <select id="getTableRecordCount" resultType="long">
        SELECT COUNT(*) FROM ${tableName}
    </select>

    <!-- Merge with custom conflict resolution -->
    <insert id="mergeWithCustomConflictResolution">
        INSERT INTO ${targetTable} 
        SELECT * FROM ${sourceTable} 
        ON CONFLICT (${conflictColumns}) DO NOTHING
    </insert>

    <insert id="transferRelationM2Relation">
        insert into relation(relationid, inlink_id, node_id, outlink_id, type, toll_type, pass_num, toll_form,
                             card_type, veh, name_ch, name_fo, name_ph, name_cht, gate_type, gate_fee, tl_locat,
                             tl_flag, slopetype, slopeangle, memo, mesh_id, cp, datasource, up_date, status, area)
        select relationid,
               inlink_id,
               node_id,
               outlink_id,
               type,
               toll_type,
               pass_num,
               toll_form,
               card_type,
               veh,
               name_ch,
               name_fo,
               name_ph,
               name_cht,
               gate_type,
               gate_fee,
               tl_locat,
               tl_flag,
               slopetype,
               slopeangle,
               memo,
               mesh_id,
               cp,
               datasource,
               up_date,
               status,
               area
        from relation_m;
    </insert>

    <insert id="transferRuleM2Rule">
        insert into rule(rule_id, inlink_id, node_id, outlink_id, pass, pass2, flag, vperiod, vehcl_type, vpdir,
                         mesh_list, memo, cp, datasource, up_date, status, link_angle, area)
        select rule_id,
               inlink_id,
               node_id,
               outlink_id,
               pass,
               pass2,
               flag,
               vperiod,
               vehcl_type,
               vpdir,
               mesh_list,
               memo,
               cp,
               datasource,
               up_date,
               status,
               link_angle,
               area
        from rule_m;
    </insert>
</mapper>
