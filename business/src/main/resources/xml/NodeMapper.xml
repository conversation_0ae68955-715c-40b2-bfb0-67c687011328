<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.NodeMapper">
  <sql id="Base_Column_List">
    id,
    hll_nodeid,
    kind,
    geom,
    name_ch,
    name_fo,
    name_cht,
    name_ph,
    adjoin_mid,
    adjoin_nid,
    `type`,
    mainnodeid,
    subnodeid,
    subnodeid2,
    light_flag,
    is_pbnode,
    cp,
    datasource,
    up_date,
    memo,
    `status`,
    geom_wkt
  </sql>
  <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.Node">
    <!--@mbg.generated-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="hll_nodeid" jdbcType="VARCHAR" property="hllNodeid" />
    <result column="kind" jdbcType="VARCHAR" property="kind" />
    <result column="geom" jdbcType="OTHER" property="geom" />
    <result column="name_ch" jdbcType="VARCHAR" property="nameCh" />
    <result column="name_fo" jdbcType="VARCHAR" property="nameFo" />
    <result column="name_cht" jdbcType="VARCHAR" property="nameCht" />
    <result column="name_ph" jdbcType="VARCHAR" property="namePh" />
    <result column="adjoin_mid" jdbcType="VARCHAR" property="adjoinMid" />
    <result column="adjoin_nid" jdbcType="VARCHAR" property="adjoinNid" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="mainnodeid" jdbcType="VARCHAR" property="mainnodeid" />
    <result column="subnodeid" jdbcType="VARCHAR" property="subnodeid" />
    <result column="subnodeid2" jdbcType="VARCHAR" property="subnodeid2" />
    <result column="light_flag" jdbcType="VARCHAR" property="lightFlag" />
    <result column="is_pbnode" jdbcType="VARCHAR" property="isPbnode" />
    <result column="cp" jdbcType="VARCHAR" property="cp" />
    <result column="datasource" jdbcType="VARCHAR" property="datasource" />
    <result column="up_date" jdbcType="TIMESTAMP" property="upDate" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="geom_wkt" jdbcType="VARCHAR" property="geomWkt" />
  </resultMap>

  <select id="selectUnhandleIds" resultMap="BaseResultMap">
    select * from node where mainnodeid is not null or subnodeid is not null or subnodeid2 is not null limit #{param1} offset #{param2}
  </select>
</mapper>