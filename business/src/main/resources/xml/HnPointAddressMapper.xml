<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.HnPointAddressMapper">

<select id="selectUnhandleIds" resultType="com.hll.mapdataservice.common.entity.HnPointAddress">
    select * from hn_point_address where length(ar_link_id) != 18 limit #{param1} offset #{param2}
</select>
</mapper>
