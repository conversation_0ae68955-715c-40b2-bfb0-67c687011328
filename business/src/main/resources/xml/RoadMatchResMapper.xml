<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.RoadMatchResMapper">
    <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.RoadMatchRes">
        <!--@mbg.generated-->
        <!--@Table road_match_res-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="link_id" jdbcType="VARCHAR" property="linkId"/>
        <result column="link_geom" jdbcType="OTHER" property="linkGeom"/>
        <result column="link_dir" jdbcType="VARCHAR" property="linkDir"/>
        <result column="road_geom" jdbcType="OTHER" property="roadGeom"/>
        <result column="road_dir" jdbcType="VARCHAR" property="roadDir"/>
        <result column="match_res" jdbcType="NUMERIC" property="matchRes"/>
    </resultMap>

    <select id="getAloneMergeRoadNum" resultType="int">
        select count(1)
        from road_match_res
        where match_res = 0
          and link_id is not null
          and osm_id not in
              (select osm_id from road_match_res where match_res = 0 group by osm_id having count(osm_id) > 1)
    </select>

    <select id="getAloneMergeRoad" resultMap="BaseResultMap">
        select *,st_astext(link_geom) linkGeomWkt,st_astext(road_geom) roadGeomWkt from road_match_res
        where match_res = 0
          and link_id is not null
          and osm_id not in
              (select osm_id from road_match_res where match_res = 0 group by osm_id having count(osm_id) > 1) order by id
        limit #{param1} offset #{param2}
    </select>

    <select id="getIndependentRoad" resultMap="BaseResultMap">
        select *,st_astext(link_geom) linkGeomWkt,st_astext(road_geom) roadGeomWkt
        from road_match_res
<!--        where match_res = 0-->
<!--          and osm_id in (select osm_id from road_match_res where match_res = 0 group by osm_id having count(osm_id) = 1)-->
<!--          and link_id in (select link_id from road_match_res where match_res = 0 group by link_id having count(link_id) = 1)-->
        where match_res = 0
        and osm_id in (select osm_id from road_match_res group by osm_id having count(osm_id) = 1) and link_id is not null
        and link_id in (select link_id from road_match_res  group by link_id having count(link_id) = 1)
    </select>

    <select id="getBranchRoadChangedNum" resultType="int">
        select count(osm_id)
        from (
                     select osm_id
                     from road_match_res
                     where osm_id in (
                             select osm_id from road_match_res group by osm_id having count(osm_id) = 3
                             )
                     group by osm_id, match_res
                     having count(match_res) = 2) a
    </select>

    <select id="getBranchRoadChangedId" resultType="java.lang.String">
        select osm_id
        from road_match_res
        where osm_id in (
                select osm_id from road_match_res group by osm_id having count(osm_id) = 3
                )
        group by osm_id, match_res
        having count(match_res) = 2
        order by osm_id limit #{param1}
        offset #{param2}
    </select>

    <select id="getTreeBranchMergeNum" resultType="int">
        select count(osm_id)
        from road_match_res
        where link_id is not null
          and match_res > 0
          and osm_id not in (
                select osm_id from road_match_res group by osm_id having count(osm_id) > 1)
    </select>

    <select id="getTreeBranchMergeOsmId" resultType="java.lang.String">
        select osm_id
        from road_match_res
        where link_id is not null
          and match_res > 0
          and osm_id not in (
                select osm_id from road_match_res group by osm_id having count(osm_id) > 1)
        order by osm_id limit #{param1}
        offset #{param2}
    </select>

    <select id="selectListByOsmId" resultMap="BaseResultMap">
        select id,link_id,road_id,osm_id,link_geom,ST_AsText(link_geom) linkGeomWkt,link_dir,road_geom,ST_AsText(road_geom) roadGeomWkt,road_dir,match_res,create_time,update_time,version
        from road_match_res
        <where>
            <if test="_parameter != null and _parameter != ''">
                osm_id = #{osmId}
            </if>
        </where>
    </select>
</mapper>