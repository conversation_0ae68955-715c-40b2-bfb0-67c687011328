<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.TollMapper">
  <resultMap id="BaseResultMap" type="com.hll.mapdataservice.common.entity.Toll">
    <!--@mbg.generated-->
    <!--@Table toll-->
    <id column="toll_id" jdbcType="VARCHAR" property="tollId" />
    <result column="toll_name_en" jdbcType="CHAR" property="tollNameEn" />
    <result column="toll_fee" jdbcType="VARCHAR" property="tollFee" />
    <result column="toll_name_multi_lang" jdbcType="VARCHAR" property="tollNameMultiLang" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="link_id" jdbcType="VARCHAR" property="linkId" />
    <result column="geom" jdbcType="OTHER" property="geom" />
    <result column="inlink_id" jdbcType="BIGINT" property="inlinkId" />
    <result column="outlink_id" jdbcType="BIGINT" property="outlinkId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    toll_id, toll_name_en, toll_fee, toll_name_multi_lang, create_time, update_time, 
    "status", link_id, geom, inlink_id, outlink_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from toll
    where toll_id = #{tollId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from toll
    where toll_id = #{tollId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.hll.mapdataservice.common.entity.Toll">
    <!--@mbg.generated-->
    insert into toll (toll_id, toll_name_en, toll_fee, 
      toll_name_multi_lang, create_time, update_time, 
      "status", link_id, geom, 
      inlink_id, outlink_id)
    values (#{tollId,jdbcType=VARCHAR}, #{tollNameEn,jdbcType=CHAR}, #{tollFee,jdbcType=VARCHAR}, 
      #{tollNameMultiLang,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=INTEGER}, #{linkId,jdbcType=VARCHAR}, #{geom,jdbcType=OTHER}, 
      #{inlinkId,jdbcType=BIGINT}, #{outlinkId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.hll.mapdataservice.common.entity.Toll">
    <!--@mbg.generated-->
    insert into toll
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tollId != null">
        toll_id,
      </if>
      <if test="tollNameEn != null">
        toll_name_en,
      </if>
      <if test="tollFee != null">
        toll_fee,
      </if>
      <if test="tollNameMultiLang != null">
        toll_name_multi_lang,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        "status",
      </if>
      <if test="linkId != null">
        link_id,
      </if>
      <if test="geom != null">
        geom,
      </if>
      <if test="inlinkId != null">
        inlink_id,
      </if>
      <if test="outlinkId != null">
        outlink_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tollId != null">
        #{tollId,jdbcType=VARCHAR},
      </if>
      <if test="tollNameEn != null">
        #{tollNameEn,jdbcType=CHAR},
      </if>
      <if test="tollFee != null">
        #{tollFee,jdbcType=VARCHAR},
      </if>
      <if test="tollNameMultiLang != null">
        #{tollNameMultiLang,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="linkId != null">
        #{linkId,jdbcType=VARCHAR},
      </if>
      <if test="geom != null">
        #{geom,jdbcType=OTHER},
      </if>
      <if test="inlinkId != null">
        #{inlinkId,jdbcType=BIGINT},
      </if>
      <if test="outlinkId != null">
        #{outlinkId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.hll.mapdataservice.common.entity.Toll">
    <!--@mbg.generated-->
    update toll
    <set>
      <if test="tollNameEn != null">
        toll_name_en = #{tollNameEn,jdbcType=CHAR},
      </if>
      <if test="tollFee != null">
        toll_fee = #{tollFee,jdbcType=VARCHAR},
      </if>
      <if test="tollNameMultiLang != null">
        toll_name_multi_lang = #{tollNameMultiLang,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=INTEGER},
      </if>
      <if test="linkId != null">
        link_id = #{linkId,jdbcType=VARCHAR},
      </if>
      <if test="geom != null">
        geom = #{geom,jdbcType=OTHER},
      </if>
      <if test="inlinkId != null">
        inlink_id = #{inlinkId,jdbcType=BIGINT},
      </if>
      <if test="outlinkId != null">
        outlink_id = #{outlinkId,jdbcType=BIGINT},
      </if>
    </set>
    where toll_id = #{tollId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hll.mapdataservice.common.entity.Toll">
    <!--@mbg.generated-->
    update toll
    set toll_name_en = #{tollNameEn,jdbcType=CHAR},
      toll_fee = #{tollFee,jdbcType=VARCHAR},
      toll_name_multi_lang = #{tollNameMultiLang,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      "status" = #{status,jdbcType=INTEGER},
      link_id = #{linkId,jdbcType=VARCHAR},
      geom = #{geom,jdbcType=OTHER},
      inlink_id = #{inlinkId,jdbcType=BIGINT},
      outlink_id = #{outlinkId,jdbcType=BIGINT}
    where toll_id = #{tollId,jdbcType=VARCHAR}
  </update>
</mapper>