<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hll.mapdataservice.common.mapper.CalculationMapper">

    <select id="calLineDistance" resultType="double">
        select st_distance(st_geomfromtext(#{line1}),st_geomfromtext(#{line2}))
    </select>

    <select id="calAngle" resultType="double">
        SELECT degrees(
                       ST_Azimuth(
                               ST_MakePoint(#{param1},#{param2} )::geometry,
                               ST_MakePoint(#{param3},#{param4} )::geometry)
                       )
    </select>

    <select id="isIntersection" resultType="java.lang.String">
        select st_astext(
                       ( select st_intersection(
                                        #{param1},
                                        #{param2}
                                        ))
                       )
    </select>

    <select id="calClosetPoint" resultType="java.lang.String">
        select st_astext(
                       (select st_closestpoint(#{param1},#{param2}
                                       ))
                       )
    </select>

    <select id="calContains" resultType="boolean">
        select st_contains(
        (select st_buffer(#{param1},0.0001,'endcap=round join=round')),
        #{param2}
        )
    </select>

    <select id="calAngleLine" resultType="java.lang.Double">
        select degrees(
                    st_angle(#{param1}::geometry,
                             #{param2}::geometry))
    </select>
</mapper>
