server:
  port: 10098
  tomcat:
    uri-encoding: UTF-8
spring:
  application:
    name: mapdataservice
  datasource:
    dynamic:
      primary: db1 #设置默认的数据源或者数据源组,默认值即为master
      datasource:
        db1:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://**************:15999/here_phl_2025_q2?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8
#          url: jdbc:postgresql://**************:15999/hll_oversea_h_phl_2024_q3?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8
          username: postgres
          password: Hu<PERSON><PERSON>@2021
        db2:
          driver-class-name: org.postgresql.Driver
          #driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://**************:15999/here_tha_2025_q2?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8
          username: postgres
          password: Huolala@2021
        db3:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://**************:15999/here_vnm_2025_q2?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8
          username: postgres
          password: Huolala@2021
        db23:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://**************:15999/here_india_2025_q2?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
            min-idle: 20
            max-pool-size: 20
            connection-init-sql: SELECT 1
            validation-timeout: 5000
        db24: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
          url: jdbc:postgresql://**************:15999/hll_oversea_h_india_2025_q2?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
          hikari:
            is-auto-commit: true
            idle-timeout: 600000
            connection-timeout: 600000
            max-lifetime: 1500000
            min-idle: 20
            max-pool-size: 20
            connection-init-sql: SELECT 1
            validation-timeout: 5000
        db27:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:postgresql://**************:15999/here_tur_2025_q2?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
        db28: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
          url: jdbc:postgresql://**************:15999/hll_oversea_h_tur_2025_q2?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
        db4: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
          url: jdbc:postgresql://**************:15999/hll_oversea_h_phl_2025_q2?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
        db5: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
          #driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
          url: jdbc:postgresql://**************:15999/hll_oversea_h_tha_2025_q2?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
        db6: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
          #driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
          url: jdbc:postgresql://**************:15999/hll_oversea_h_vnm_2025_q2?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
        db7: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
          #driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
          url: jdbc:postgresql://**************:15999/here_hkg_2025_q2?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
        db8: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
          #driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
          url: jdbc:postgresql://**************:15999/here_twn_2025_q2?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
        db9: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
#          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
          #          url: jdbc:p6spy:postgresql://**************:15999/hll_oversea_h_hkg_2025_q2?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          url: jdbc:postgresql://**************:15999/hll_oversea_h_hkg_2025_q2?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
        db10: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
          #driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
          url: jdbc:postgresql://**************:15999/hll_oversea_h_twn_2025_q2?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
        db11: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
          #driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
          url: jdbc:postgresql://**************:15999/here_sgp_2025_q2?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
        db12: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
          #driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
          url: jdbc:postgresql://**************:15999/here_mys_2025_q2?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
        db25: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
          #driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
          url: jdbc:postgresql://**************:15999/here_ban_2025_q2?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
        db26: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
          #driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
          url: jdbc:postgresql://**************:15999/hll_oversea_h_ban_2025_q2?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          username: postgres
          password: Huolala@2021
        db13: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
#          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
          url: ********************************************************************************************************************************************************************************
          username: postgres
          password: Huolala@2021
        db14: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
          #driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
          url: ********************************************************************************************************************************************************************************
          username: postgres
          password: Huolala@2021
        db15: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
          #driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
          url: ***********************************************************************************************************************************************************************
          username: postgres
          password: Huolala@2021
        db16: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
          #driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
          url: ********************************************************************************************************************************************************************************
          username: postgres
          password: Huolala@2021
        db17: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
          #driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
          url: ***********************************************************************************************************************************************************************
          username: postgres
          password: Huolala@2021
        db18: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
          #driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
          url: ********************************************************************************************************************************************************************************
          username: postgres
          password: Huolala@2021
        db19: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
          #driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
          url: ***********************************************************************************************************************************************************************
          username: postgres
          password: Huolala@2021
        db20: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
          #driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
          url: ********************************************************************************************************************************************************************************
          username: postgres
          password: Huolala@2021
#        db29: #hll_oversea_here_road
#          driver-class-name: org.postgresql.Driver
#          #driver-class-name: com.p6spy.engine.spy.P6SpyDriver
#          type: com.zaxxer.hikari.HikariDataSource
#          #url: ****************************************************************************************************
#          url: **********************************************************************************************************************************************************************
#          username: postgres
#          password: Huolala@2021
#        db21: #hll_oversea_here_road
#          driver-class-name: org.postgresql.Driver
#          #driver-class-name: com.p6spy.engine.spy.P6SpyDriver
#          type: com.zaxxer.hikari.HikariDataSource
#          #url: ****************************************************************************************************
#          url: ***************************************************************************************************************************************************************************
#          username: postgres
#          password: Huolala@2021
#        db22: #hll_oversea_here_road
#          driver-class-name: org.postgresql.Driver
#          #          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
#          type: com.zaxxer.hikari.HikariDataSource
#          #url: ****************************************************************************************************
#          url: ***************************************************************************************************************************************************************************
#            #          url: *********************************************************************************************************************************************************************************
#          username: postgres
#          password: Huolala@2021

third:
  aoibaseservice:
    url: "http://**************:8089"
  roadmatchservice:
    url: "http://***************:8080"
  hereroadmatchservice:
    url: "http://***************:9002"
  databasebackupservice:
    url: "http://**************:10098"
xxl:
  job:
    admin:
      ### 调度中心部署跟地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
      addresses: http://**************:30088/xxl-job-admin
    ### 执行器通讯TOKEN [选填]：非空时启用；
    accessToken: pO8fN4sI3hR2iQ6vF7oR5xJ5nR9qL6rO3qG9cP6rF3yY2yS4xZ8cD7qA4iG9yF2t
    executor:
      ### 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
      appname: mapdataservice
      ### 执行器注册 [选填]：优先使用该配置作为注册地址，为空时使用内嵌服务 ”IP:PORT“ 作为注册地址。从而更灵活的支持容器类型执行器动态IP和动态映射端口问题。
      address:
      ### 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
      ip:
      ### 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
      netty-port: 9999
      ### 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
      logpath: ./logs/jobhandler
      ### 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；
      logretentiondays: 30
logging:
  file:
    path: ./logs
#mybatis-plus:
#　　configuration:
#　　　　log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
minio:
  endPoint: "http://**************:31112/"
  accessKey: "admin"
  secretKey: "haoyiping"
  bucketName: "test"



