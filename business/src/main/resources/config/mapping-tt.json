{"aerialway": ["station"], "aeroway": ["aerodrome", "apron", "helipad", "heliport", "runway", "taxiway", "terminal"], "amenity": ["adult_entertainment", "animal_boarding", "animal_breeding", "animal_shelter", "arts_and_culture", "arts_centre", "atm", "baby_hatch", "baking_oven", "bank", "bar", "bbq", "bench", "bicycle_parking", "bicycle_rental", "bicycle_repair_station", "biergarten", "blood_bank", "boat_rental", "boat_sharing", "brothel", "bureau_de_change", "bus_station", "business", "cafe", "cafeteria", "car_rental", "car_sharing", "car_wash", "casino", "charging_location", "childcare", "cinema", "clinic", "clock", "coffee_shop", "college", "comedy_club", "commercial", "community_centre", "conference_centre", "courthouse", "crematorium", "cultural_centre", "dentist", "doctors", "dog_toilet", "drinking_water", "driving", "driving_school", "eat_and_drink", "education", "emergency_services", "events_venue", "facilities", "fast_food", "ferry_terminal", "finance", "fire_station", "food_court", "food_sharing", "fountain", "fuel", "funeral_hall", "gambling", "give_box", "gold_exchange", "grit_bin", "healthcare", "hospital", "hunting_stand", "internet_cafe", "juice_bar", "karaoke_box", "kindergarten", "kitchen", "kneipp_water_cure", "language_school", "leisure", "library", "lodging", "lounger", "love_hotel", "marketplace", "military", "monastery", "motorcycle_parking", "motorcycle_rental", "music_school", "nightclub", "parcel_locker", "parking", "payment_centre", "payment_terminal", "pedestrian_subway", "pharmacy", "photo_booth", "place_of_mourning", "place_of_worship", "planetarium", "police", "post_box", "post_depot", "post_office", "prep_school", "prison", "pub", "public", "public_bath", "public_bookcase", "public_services", "public_transport_ticket_counter", "ranger_station", "recycling", "refugee_site", "rent_a_car_parking", "rescue_station", "research_institute", "restaurant", "road_rescue", "sanitary_dump_station", "school", "shelter", "shower", "social_centre", "social_facility", "stock_exchange", "stripclub", "studio", "swingerclub", "taxi", "tea_house", "telephone", "theatre", "toilets", "townhall", "toy_library", "training", "trolley_bay", "truck_stop", "truck_wash", "university", "vehicle_inspection", "vending_machine", "veterinary", "waste_basket", "waste_disposal", "water_point", "weighbridge", "yes"], "barrier": ["border_control", "security_gate", "toll_booth"], "boundary": ["aboriginal_lands", "national_park", "protected_area"], "club": ["beach", "scout", "sport", "yes"], "craft": ["agricultural_engines", "atelier", "bakery", "basket_maker", "beekeeper", "blacksmith", "boatbuilder", "bookbinder", "brewery", "builder", "cabinet_maker", "car_painter", "carpenter", "carpet_layer", "caterer", "chimney_sweeper", "cleaning", "clockmaker", "confectionery", "cooper", "dental_technician", "distillery", "door_construction", "dressmaker", "electrician", "electronics_repair", "embroiderer", "engraver", "floorer", "gardener", "glaziery", "goldsmith", "grinding_mill", "handicraft", "hvac", "insulation", "interior_work", "jeweller", "joiner", "key_cutter", "locksmith", "metal_construction", "mint", "musical_instrument", "oil_mill", "optician", "organ_builder", "painter", "parquet_layer", "paver", "photographer", "photographic_laboratory", "piano_tuner", "plasterer", "plumber", "pottery", "printer", "printmaker", "rigger", "roofer", "saddler", "sailmaker", "sawmill", "scaffolder", "sculptor", "shoemaker", "signmaker", "stand_builder", "stonemason", "stove_fitter", "sun_protection", "tiler", "tinsmith", "toolmaker", "turner", "upholsterer", "watchmaker", "water_well_drilling", "window_construction", "winery"], "emergency:amenity": ["social_facility"], "emergency": ["ambulance_station", "assembly_point", "defibrillator", "emergency_ward_entrance", "fire_hydrant", "phone", "water_tank"], "geological": ["moraine", "palaeontological_site", "volcanic_caldera_rim", "volcanic_lava_field"], "healthcare": ["alternative", "audiologist", "blood_donation", "counselling", "hospice", "laboratory", "physiotherapist", "podiatrist", "psychotherapist", "sample_collection"], "highway": ["rest_area", "services", "toll_gantry", "trailhead"], "historic": ["aircraft", "archaeological_site", "battlefield", "bomb_crater", "boundary_stone", "building", "cannon", "castle", "church", "city_gate", "creamery", "farm", "fort", "gallows", "locomotive", "manor", "memorial", "milestone", "monastery", "monument", "pa", "pillory", "railway_car", "ruins", "rune_stone", "ship", "tank", "tomb", "vehicle", "wayside_cross", "wayside_shrine", "wreck"], "landuse": ["allotments", "artificial_ground", "brownfield", "builtup_area", "cemetery", "commercial", "construction", "farmland", "farmyard", "flowerbed", "garages", "grass", "greenfield", "greenhouse_horticulture", "industrial", "landfill", "managed_green", "meadow", "military", "orchard", "plant_nursery", "quarry", "railway", "recreation_ground", "religious", "residential", "retail", "village_green", "vineyard", "winter_sports"], "leisure": ["adult_gaming_centre", "amusement_arcade", "bandstand", "beach_resort", "bird_hide", "bowling_alley", "common", "dance", "disc_golf_course", "dog_park", "entertainment", "escape_game", "firepit", "fishing", "fitness_centre", "fitness_station", "flying_club", "garden", "golf_course", "hackerspace", "horse_riding", "ice_rink", "leisure_centre", "marina", "miniature_golf", "nature_reserve", "outdoor", "park", "picnic_table", "pitch", "playground", "resort", "sauna", "slipway", "sport", "sports_centre", "sports_hall", "stadium", "summer_camp", "swimming_pool", "track", "trampoline_park", "water_park"], "man_made": ["breakwater", "obelisk", "observatory", "pier", "stupa", "windmill", "works"], "military": ["airfield", "base", "checkpoint"], "mountain_pass": ["yes"], "natural": ["arch", "arete", "bare_rock", "beach", "blowhole", "cape", "cave", "cave_entrance", "cliff", "cove", "dune", "earth_bank", "fell", "fumarole", "geyser", "glacier", "grassland", "harbour", "heath", "hill", "hot_spring", "isthmus", "locale", "oasis", "pan", "peak", "plain", "plateau", "rapids", "reef", "reservoir", "ridge", "river_crossing", "rock", "saddle", "sand", "scree", "scrub", "shingle", "shoal", "shrubbery", "sinkhole", "spring", "tundra", "valley", "volcano", "wetland", "wood", "yes"], "office": ["accountant", "advertising_agency", "architect", "association", "bail_bond_agent", "charity", "company", "construction_company", "consulting", "courier", "coworking", "diplomatic", "educational_institution", "employment_agency", "energy_supplier", "engineer", "estate_agent", "financial", "financial_advisor", "forestry", "geodesist", "government", "graphic_design", "guide", "harbour_master", "insurance", "it", "lawyer", "logistics", "motoring_organization", "moving_company", "newspaper", "ngo", "notary", "political_party", "quango", "religion", "research", "security", "surveyor", "tax_advisor", "telecommunication", "therapist", "traffic", "travel_agent", "union", "visa", "water_utility", "wedding_planner", "yes"], "place": ["archipelago", "farm", "island", "islet"], "public_transport": ["platform", "station", "stop_position"], "railway": ["halt", "station", "subway_entrance", "train_station_entrance", "tram_stop"], "shop": ["agrarian", "alcohol", "anime", "antiques", "appliance", "art", "atv", "baby_goods", "bag", "bakery", "bathroom_furnishing", "beauty", "bed", "beverages", "bicycle", "boat", "boat_parts", "bookmaker", "books", "boutique", "brewing_supplies", "bus", "butcher", "camera", "candles", "cannabis", "car", "car_parts", "car_repair", "caravan", "carpet", "catalogue", "ceramics", "charity", "cheese", "chemist", "chocolate", "christmas", "clothes", "coffee", "collector", "computer", "confectionery", "convenience", "copyshop", "cosmetics", "country_store", "craft", "curtain", "dairy", "deli", "department_store", "doityourself", "doors", "dry_cleaning", "e-cigarette", "electrical", "electronics", "energy", "erotic", "fabric", "factory_outlet", "farm", "fashion_accessories", "fireplace", "fishing", "flooring", "florist", "food", "food_markets", "frame", "frozen_food", "fuel", "funeral_directors", "furniture", "games", "garden_centre", "garden_furniture", "gas", "general", "gift", "glaziery", "golf", "greengrocer", "grocery", "groundskeeping", "hairdresser", "hairdresser_supply", "hardware", "health_food", "hearing_aids", "herbalist", "hifi", "household_linen", "houseware", "hunting", "ice_cream", "interior_decoration", "jetski", "jewelry", "kiosk", "kitchen", "laundry", "leather", "lighting", "local_specialities", "locksmith", "lottery", "mall", "marine_electronics", "massage", "medical_supply", "military_surplus", "mobile_phone", "model", "money_lender", "motorcycle", "motorcycle_repair", "music", "musical_instrument", "newsagent", "nutrition_supplements", "nuts", "optician", "organic", "outdoor", "outpost", "paint", "party", "pasta", "pastry", "pawnbroker", "perfumery", "pest_control", "pet", "pet_grooming", "pet_supplies", "photo", "plant_hire", "pottery", "printer_ink", "pyrotechnics", "radiotechnics", "religion", "rental", "repair", "retail_outlet", "scuba_diving", "seafood", "second_hand", "security", "sewing", "shoes", "shopping_service", "ski", "snowmobile", "specialty_food", "spices", "sports", "stationery", "storage_rental", "supermarket", "swimming_pool", "tailor", "tattoo", "tea", "telecommunication", "ticket", "tiles", "tobacco", "tool_hire", "toys", "trade", "trailer", "travel_agency", "trophy", "truck", "truck_repair", "tyres", "vacuum_cleaner", "van", "variety_store", "video", "video_games", "watches", "water", "weapons", "wholesale", "window_blind", "windows", "wine", "wool", "yes"], "tourism": ["alpine_hut", "apartment", "aquarium", "artwork", "attraction", "bungalow", "cabin", "camp_site", "caravan_site", "chalet", "cottage", "gallery", "guest_house", "holiday_rental", "hostel", "hotel", "information", "motel", "museum", "picnic_site", "rest_camps", "theme_park", "viewpoint", "villa", "wilderness_hut", "zoo"], "transport": ["yes"], "utility": ["yes"], "road": ["motorway", "motorway_link", "trunk", "trunk_link", "primary", "primary_link", "secondary", "secondary_link", "tertiary", "tertiary_link", "unclassified", "residential", "living_street", "service", "pedestrian", "track", "road", "footway", "cycleway", "path", "steps"]}