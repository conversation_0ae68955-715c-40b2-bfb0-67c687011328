server:
  port: 8092
  tomcat:
    uri-encoding: UTF-8
spring:
  application:
    name: mapdataservice
  profiles:
    active: lone
  redis:
    host: **************
    port: 30184
    password: hll_map_road@2021
    database: 9

third:
  aoibaseservice:
    url: "http://**************:8089"
  roadmatchservice:
    url: "http://***************:8080"
  hereroadmatchservice:
    url: "http://***************:9002"
  googleNearByPoiSearch:
    url: "https://maps.googleapis.com"
  middlePlatformService:
    url: "http://map-provider-stg.myhll.sg"
  wrapperGoogleNearByPoiSearch:
    url: "http://map-facade-pre.myhll.sg"
  hereRoadRoute:
    url: "https://router.hereapi.com"
  databasebackupservice:
    url: "http://**************:10098"

logging:
  file:
    path: ~/logs
  level:
    default: debug
    aoibaseservice: info

mybatis-plus:
  mapper-locations: classpath:xml/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

hll:
  client:
    id:
      url: http://**************:8995/
management:
  endpoints:
    web:
      exposure:
        include: "*"



MapTT:
  mapttconfigfile:
    path: "./common/src/main/java/com/hll/mapdataservice/common/config/mapping-tt.json"


