# 定义通用配置模板
hikari-defaults: &hikari-defaults
  is-auto-commit: true
  idle-timeout: 600000
  connection-timeout: 600000
  max-lifetime: 1500000
  min-idle: 20
  max-pool-size: 20
  connection-init-sql: SELECT 1
  validation-timeout: 5000

datasource-defaults: &datasource-defaults
  driver-class-name: org.postgresql.Driver
  type: com.zaxxer.hikari.HikariDataSource
  username: postgres
  password: <PERSON><PERSON><PERSON>@2021

server:
  port: 10050
  tomcat:
    uri-encoding: UTF-8

spring:
  application:
    name: mapdataservice-lone
  redis:
    host: *************
    port: 16379
    password: hll_map_road@2021
    database: 0
  datasource:
    dynamic:
      primary: db1
      datasource:
        # phl
        db1:
          <<: *datasource-defaults
          url: **********************************************************************************************************************************************************************
          hikari:
            <<: *hikari-defaults
        
        db4:
          <<: *datasource-defaults
          url: ************************************************************************************************************************************************************************************
          hikari:
            <<: *hikari-defaults
        
        # hkg
        db7:
          <<: *datasource-defaults
          url: **********************************************************************************************************************************************************************
          hikari:
            <<: *hikari-defaults
        
        db9:
          <<: *datasource-defaults
          url: ************************************************************************************************************************************************************************************
          hikari:
            <<: *hikari-defaults
        
        # 其他数据源省略...可按同样方式配置
        # mys
        db12:
          <<: *datasource-defaults
          url: **********************************************************************************************************************************************************************
          hikari:
            <<: *hikari-defaults

        db14:
          <<: *datasource-defaults
          url: ************************************************************************************************************************************************************************************
          hikari:
            <<: *hikari-defaults

        # sgp
        db11:
          <<: *datasource-defaults
          url: **********************************************************************************************************************************************************************
          hikari:
            <<: *hikari-defaults

        db13:
          <<: *datasource-defaults
          url: ************************************************************************************************************************************************************************************
          hikari:
            <<: *hikari-defaults

        # ban
        db25:
          <<: *datasource-defaults
          url: **********************************************************************************************************************************************************************
          hikari:
            <<: *hikari-defaults

        db26:
          <<: *datasource-defaults
          url: ************************************************************************************************************************************************************************************
          hikari:
            <<: *hikari-defaults

        # tha
        db2:
          <<: *datasource-defaults
          url: **********************************************************************************************************************************************************************
          hikari:
            <<: *hikari-defaults

        db5:
          <<: *datasource-defaults
          url: ************************************************************************************************************************************************************************************
          hikari:
            <<: *hikari-defaults

        # vnm
        db3:
          <<: *datasource-defaults
          url: jdbc:postgresql://*************:15999/here_vnm_2025_q3?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          hikari:
            <<: *hikari-defaults

        db6:
          <<: *datasource-defaults
          url: jdbc:postgresql://*************:15999/hll_oversea_h_vnm_2025_q3_test?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          hikari:
            <<: *hikari-defaults

        # twn
        db8:
          <<: *datasource-defaults
          url: jdbc:postgresql://*************:15999/here_twn_2025_q3?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          hikari:
            <<: *hikari-defaults

        db10:
          <<: *datasource-defaults
          url: jdbc:postgresql://*************:15999/hll_oversea_h_twn_2025_q3_test?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          hikari:
            <<: *hikari-defaults

        # ind
        db15:
          <<: *datasource-defaults
          url: jdbc:postgresql://*************:15999/here_ind_2025_q3?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          hikari:
            <<: *hikari-defaults

        db16:
          <<: *datasource-defaults
          url: jdbc:postgresql://*************:15999/hll_oversea_h_ind_2025_q3_test?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          hikari:
            <<: *hikari-defaults

        # bra
        db17:
          <<: *datasource-defaults
          url: jdbc:postgresql://*************:15999/here_bra_2025_q3?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          hikari:
            <<: *hikari-defaults

        db18:
          <<: *datasource-defaults
          url: jdbc:postgresql://*************:15999/hll_oversea_h_bra_2025_q3_test?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          hikari:
            <<: *hikari-defaults

        # tur
        db27:
          <<: *datasource-defaults
          url: jdbc:postgresql://*************:15999/here_tur_2025_q3?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          hikari:
            <<: *hikari-defaults

        db28:
          <<: *datasource-defaults
          url: jdbc:postgresql://*************:15999/hll_oversea_h_tur_2025_q3_test?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          hikari:
            <<: *hikari-defaults

        # mex
        db19:
          <<: *datasource-defaults
          url: jdbc:postgresql://*************:15999/here_mex_2025_q3?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          hikari:
            <<: *hikari-defaults

        db20:
          <<: *datasource-defaults
          url: jdbc:postgresql://*************:15999/hll_oversea_h_mex_2025_q3_test?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          hikari:

        # ind
        db23:
          <<: *datasource-defaults
          url: jdbc:postgresql://*************:15999/here_india_2025_q3?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          hikari:
            <<: *hikari-defaults

        db24:
          <<: *datasource-defaults
          url: **************************************************************************************************************************************************************************************
          hikari:
            <<: *hikari-defaults





third:
  aoibaseservice:
    url: "http://**************:8089"
  roadmatchservice:
    url: "http://***************:8080"
  hereroadmatchservice:
    url: "http://***************:9002"
  databasebackupservice:
    url: "http://**************:10098"

hll:
  client:
    id:
      url: http://*************:8995/

minio:
  endPoint: "http://**************:31112/"
  accessKey: "admin"
  secretKey: "haoyiping"
  bucketName: "test"