server:
  port: 8092
  tomcat:
    uri-encoding: UTF-8
spring:
  application:
    name: mapdataservice
  datasource:
    dynamic:
      primary: db1 #设置默认的数据源或者数据源组,默认值即为master
      datasource:
#        db1:
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          #type: com.alibaba.druid.pool.DruidDataSource
#          #url: **************************************************************************************
#          url: ************************************************************************************************************************
#          username: postgres
#          password: 1q2w3e
        db1:
#          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          #type: com.alibaba.druid.pool.DruidDataSource
          #url: **************************************************************************************
#          url: **************************************************************************************************************************************
#          username: postgres
#          password: 1q2w3e
          url: ***********************************************************************************************************************************************************************
          username: postgres
          password: 1q2w3e
        db4:
#          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          #type: com.alibaba.druid.pool.DruidDataSource
          #url: **************************************************************************************
#          url: *****************************************************************************************************************************************
          url: *****************************************************************************************************************************************
#          url: ***********************************************************************************************************************************************
#          url: jdbc:p6spy:postgresql://**************:15999/hll_oversea_h_phl_q3?reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
#          url: *************************************************************************************************************************
          username: postgres
          password: 1q2w3e
#          password: Huolala@2021
#        db13:
#          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
#          type: com.zaxxer.hikari.HikariDataSource
#          #type: com.alibaba.druid.pool.DruidDataSource
#          #url: **************************************************************************************
#          #          url: ***********************************************************************************************************************************************
#          url: ***********************************************************************************************************************************************
#          #          url: jdbc:p6spy:postgresql://**************:15999/hll_oversea_h_phl_q3?reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
#          #          url: *************************************************************************************************************************
#          username: postgres
#          #          password: 1q2w3e
#          password: Huolala@2021
#        db7:
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          #type: com.alibaba.druid.pool.DruidDataSource
#          url: *********************************************************************************************************************************************
#          username: hllmapbasedb
#          password: hllmapbasedb@2020
        db7: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
          #driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
          url: **********************************************************************************************************************************************************
          username: postgres
          password: 1q2w3e
        db2:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          #type: com.alibaba.druid.pool.DruidDataSource
          #url: ****************************************************************************************************
          url: **********************************************************************************************************************************************************
          username: postgres
          password: 1q2w3e
        db3:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          #type: com.alibaba.druid.pool.DruidDataSource
          #url: **************************************************************************************
          url: *************************************************************************************************
          username: postgres
          password: 1q2w3e
#        db4:
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          #type: com.alibaba.druid.pool.DruidDataSource
#          #url: **************************************************************************************
#          url: *********************************************************************************************************************************
#          username: postgres
#          password: 1q2w3e
        db5:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          #type: com.alibaba.druid.pool.DruidDataSource
          url: *************************************************************************************************
          #url: ********************************************************************************************
          username: postgres
          password: 1q2w3e
        db6:
#          driver-class-name: org.postgresql.Driver
          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #type: com.alibaba.druid.pool.DruidDataSource
          #url: *************************************************************************************************
#          url: ********************************************************************************************
#          url: **********************************************************************************************************************************
          url: ****************************************************************************************************************************************
          username: postgres
          password: 1q2w3e
        db8:
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          #type: com.alibaba.druid.pool.DruidDataSource
          #url: **************************************************************************************
          url: ***************************************************************************************************
          username: postgres
          password: 1q2w3e
#        db7:  #AOI数据库-母库
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          #type: com.alibaba.druid.pool.DruidDataSource
#          #url: **************************************************************************************
#          url: **************************************************************************************************
#          username: postgres
#          password: hll@2020
#        db8: #hll_oversea_here_road
#          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
#          type: com.zaxxer.hikari.HikariDataSource
#          #type: com.alibaba.druid.pool.DruidDataSource
#          #url: **************************************************************************************
#          url: ***************************************************************************************************************************************
#          username: postgres
#          password: 1q2w3e
        db9: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
#          url: jdbc:postgresql://**************:15999/hll_oversea_h_phl_q3?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          url: **********************************************************************************************************************************************************
          username: postgres
          password: 1q2w3e
        db10: #hll_oversea_here_road
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          #url: ****************************************************************************************************
#          url: jdbc:postgresql://**************:15999/hll_oversea_h_phl_q3?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
          url: ***************************************************************************************************************************************************************************
          username: postgres
          password: 1q2w3e
        db17:
          driver-class-name: org.postgresql.Driver
#          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #type: com.alibaba.druid.pool.DruidDataSource
          #url: **************************************************************************************
#          url: ******************************************************************************************************************************************
          url: *******************************************************************************************************************************************************************
          username: postgres
          password: 1q2w3e
        db18:
          driver-class-name: org.postgresql.Driver
          #          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #type: com.alibaba.druid.pool.DruidDataSource
          #url: **************************************************************************************
          #          url: *************************************************************************************************************************************************************************
          url: *****************************************************************************************************************************************************************
          username: postgres
          password: 1q2w3e
        db19:
          driver-class-name: org.postgresql.Driver
#          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #type: com.alibaba.druid.pool.DruidDataSource
          #url: **************************************************************************************
#          url: ******************************************************************************************************************************************
          url: ********************************************************************************************************************************
          username: postgres
          password: 1q2w3e
        db20:
          driver-class-name: org.postgresql.Driver
          #          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #type: com.alibaba.druid.pool.DruidDataSource
          #url: **************************************************************************************
          #          url: ******************************************************************************************************************************************
          url: ************************************************************************************************************************************
          username: postgres
          password: 1q2w3e
        db21:
          driver-class-name: org.postgresql.Driver
          #          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #type: com.alibaba.druid.pool.DruidDataSource
          #url: **************************************************************************************
          #          url: ******************************************************************************************************************************************
          url: ************************************************************************************************************************************
          username: postgres
          password: 1q2w3e
        db22:
          driver-class-name: org.postgresql.Driver
          #          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          type: com.zaxxer.hikari.HikariDataSource
          #type: com.alibaba.druid.pool.DruidDataSource
          #url: **************************************************************************************
          #          url: ******************************************************************************************************************************************
          url: **********************************************************************************************************************************
          username: postgres
          password: 1q2w3e
#      hikari:
#        is-auto-commit: true
#        idle-timeout: 60000
#        connection-timeout: 60000
#        max-lifetime: 0
#        min-idle: 10
#        max-pool-size: 10

third:
  aoibaseservice:
    url: "http://**************:8089"
  roadmatchservice:
    url: "http://***************:8080"
  hereroadmatchservice:
    url: "http://***************:9002"
  databasebackupservice:
    url: "http://**************:10098"
xxl:
  job:
    admin:
      ### 调度中心部署跟地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
      addresses: http://**************:30088/xxl-job-admin
    ### 执行器通讯TOKEN [选填]：非空时启用；
    accessToken:
    executor:
      ### 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
      appname: mapdataservice
      ### 执行器注册 [选填]：优先使用该配置作为注册地址，为空时使用内嵌服务 ”IP:PORT“ 作为注册地址。从而更灵活的支持容器类型执行器动态IP和动态映射端口问题。
      address:
      ### 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
      ip:
      ### 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
      netty-port: 9999
      ### 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
      logpath: ./logs/jobhandler
      ### 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；
      logretentiondays: 30
minio:
  endPoint: "http://**************:31112/"
  accessKey: "admin"
  secretKey: "haoyiping"
  bucketName: "test"
logging:
  file:
    path: ./logs
#  level:
#    com.hll.mapdataservice.common.mapper.LinkMapper: debug
#    com.hll.mapdataservice.business.api.road.service: debug
