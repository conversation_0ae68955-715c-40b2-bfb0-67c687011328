# Here Link Convert - Python Implementation

This is a Python implementation of the `hereLinkConvert` method from the Java class `LinkMController.java`. The implementation focuses on the file reading operations and provides a framework for the conversion logic.

## Files

- `here_link_convert.py`: The main implementation file containing the `XmlFileUtils` class and the `LinkConverter` class.
- `test_here_link_convert.py`: A test script that demonstrates how to use the implementation with sample data.

## Usage

To use the implementation, you need to:

1. Import the `LinkConverter` class from `here_link_convert.py`
2. Create an instance of `LinkConverter`
3. Call the `here_link_convert` method with the appropriate parameters

Example:

```python
from here_link_convert import LinkConverter

converter = LinkConverter()
result = converter.here_link_convert(
    step=100,
    rdf_cf_link_file_path="/path/to/rdf_cf_link.txt",
    rdf_cf_file_path="/path/to/rdf_cf.txt",
    rdf_cf_node_file_path="/path/to/rdf_cf_node.txt",
    rdf_nav_link_file_path="/path/to/rdf_nav_link.txt",
    is_compile_node=True,
    is_compile_trans_eng=False,
    area="",
    country="USA"
)
```

## Testing

To run the tests, execute the `test_here_link_convert.py` script:

```bash
python test_here_link_convert.py
```

The test script will:
1. Create sample files for testing
2. Test the `XmlFileUtils` class
3. Test the `LinkConverter` class
4. Clean up the temporary files

## Implementation Details

The implementation includes:

1. An `XmlFileUtils` class with methods to read the three types of files:
   - `rdf_cf_file_read` (equivalent to `rdfCfFileRead` in Java)
   - `rdf_link_cf_file_read` (equivalent to `rdfLinkCfFileRead` in Java)
   - `rdf_nav_link_file_read` (equivalent to `rdfNavLinkFileRead` in Java)

2. A `LinkConverter` class with the `here_link_convert` method that:
   - Takes the same parameters as the Java method
   - Reads the configuration files using `XmlFileUtils`
   - Simulates the processing of streets data (since we can't access the database)
   - Logs the execution time

## Limitations

This implementation is a simplified version that focuses on the file reading operations. The actual data processing (converting Streets to LinkM and NodeM) is simulated, as it would require access to the database and many other services.

In a real implementation, you would need to:
1. Query the streets data from the database
2. Process the streets data in batches
3. Convert each street to link and node objects
4. Save the converted data back to the database

## Performance Comparison

To compare the performance of this Python implementation with the original Java implementation, you would need to:
1. Run both implementations with the same input files
2. Measure the execution time of each
3. Compare the results

The Python implementation includes timing code that logs the execution time, which can be used for this comparison.