#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for here_link_convert.py
"""

import os
import tempfile
import logging
from here_link_convert import XmlFileUtils, LinkConverter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_sample_files():
    """Create sample files for testing"""
    # Create temporary directory
    temp_dir = tempfile.mkdtemp()
    logger.info(f"Created temporary directory: {temp_dir}")
    
    # Create sample RDF CF file
    rdf_cf_file = os.path.join(temp_dir, "rdf_cf.txt")
    with open(rdf_cf_file, 'w') as f:
        f.write("CF001\tI\n")
        f.write("CF002\tI\n")
        f.write("CF003\tO\n")  # This one should be filtered out
    
    # Create sample RDF Link CF file
    rdf_link_cf_file = os.path.join(temp_dir, "rdf_link_cf.txt")
    with open(rdf_link_cf_file, 'w') as f:
        f.write("CF001\tLINK001\n")
        f.write("CF002\tLINK001\n")
        f.write("CF002\tLINK002\n")
    
    # Create sample RDF Nav Link file
    rdf_nav_link_file = os.path.join(temp_dir, "rdf_nav_link.txt")
    with open(rdf_nav_link_file, 'w') as f:
        # Format: LinkID, Country, ... (columns 33 and 35 are used)
        # We'll create a line with 36 tab-separated values
        values = [""] * 36
        values[0] = "LINK001"
        values[1] = "USA"
        values[33] = "Y"  # Limited access
        values[35] = "1"  # Nav link type
        f.write("\t".join(values) + "\n")
        
        values = [""] * 36
        values[0] = "LINK002"
        values[1] = "USA"
        values[33] = "N"  # Not limited access
        values[35] = "2"  # Nav link type
        f.write("\t".join(values) + "\n")
        
        values = [""] * 36
        values[0] = "LINK003"
        values[1] = "CAN"  # Different country, should be filtered out
        values[33] = "Y"
        values[35] = "1"
        f.write("\t".join(values) + "\n")
    
    # Create sample RDF CF Node file (not used in this test)
    rdf_cf_node_file = os.path.join(temp_dir, "rdf_cf_node.txt")
    with open(rdf_cf_node_file, 'w') as f:
        f.write("NODE001\tCF001\n")
    
    return {
        "temp_dir": temp_dir,
        "rdf_cf_file": rdf_cf_file,
        "rdf_link_cf_file": rdf_link_cf_file,
        "rdf_nav_link_file": rdf_nav_link_file,
        "rdf_cf_node_file": rdf_cf_node_file
    }

def test_xml_file_utils(file_paths):
    """Test the XmlFileUtils class"""
    logger.info("Testing XmlFileUtils...")
    
    xml_file_utils = XmlFileUtils()
    
    # Test rdf_cf_file_read
    rdf_cf_map = xml_file_utils.rdf_cf_file_read(file_paths["rdf_cf_file"])
    logger.info(f"RDF CF Map: {rdf_cf_map}")
    assert len(rdf_cf_map) == 2, "Expected 2 entries in RDF CF Map"
    assert "CF001" in rdf_cf_map, "Expected CF001 in RDF CF Map"
    assert "CF002" in rdf_cf_map, "Expected CF002 in RDF CF Map"
    assert "CF003" not in rdf_cf_map, "CF003 should be filtered out"
    
    # Test rdf_link_cf_file_read
    rdf_link_cf_map = xml_file_utils.rdf_link_cf_file_read(file_paths["rdf_link_cf_file"])
    logger.info(f"RDF Link CF Map: {rdf_link_cf_map}")
    assert len(rdf_link_cf_map) == 2, "Expected 2 entries in RDF Link CF Map"
    assert "LINK001" in rdf_link_cf_map, "Expected LINK001 in RDF Link CF Map"
    assert "LINK002" in rdf_link_cf_map, "Expected LINK002 in RDF Link CF Map"
    assert len(rdf_link_cf_map["LINK001"]) == 2, "Expected 2 CF IDs for LINK001"
    
    # Test rdf_nav_link_file_read
    rdf_nav_link_map = xml_file_utils.rdf_nav_link_file_read(file_paths["rdf_nav_link_file"], "USA")
    logger.info(f"RDF Nav Link Map: {rdf_nav_link_map}")
    assert len(rdf_nav_link_map) == 2, "Expected 2 entries in RDF Nav Link Map"
    assert "LINK001" in rdf_nav_link_map, "Expected LINK001 in RDF Nav Link Map"
    assert "LINK002" in rdf_nav_link_map, "Expected LINK002 in RDF Nav Link Map"
    assert "LINK003" not in rdf_nav_link_map, "LINK003 should be filtered out (different country)"
    assert rdf_nav_link_map["LINK001"][0] == "Y", "Expected limited access for LINK001"
    assert rdf_nav_link_map["LINK001"][1] == "1", "Expected nav link type 1 for LINK001"
    
    logger.info("XmlFileUtils tests passed!")

def test_link_converter(file_paths):
    """Test the LinkConverter class"""
    logger.info("Testing LinkConverter...")
    
    converter = LinkConverter()
    result = converter.here_link_convert(
        step=100,
        rdf_cf_link_file_path=file_paths["rdf_link_cf_file"],
        rdf_cf_file_path=file_paths["rdf_cf_file"],
        rdf_cf_node_file_path=file_paths["rdf_cf_node_file"],
        rdf_nav_link_file_path=file_paths["rdf_nav_link_file"],
        is_compile_node=True,
        is_compile_trans_eng=False,
        area="",
        country="USA"
    )
    
    assert result is True, "Expected LinkConverter to return True"
    
    logger.info("LinkConverter tests passed!")

def main():
    """Main function to run the tests"""
    logger.info("Starting tests...")
    
    # Create sample files
    file_paths = create_sample_files()
    
    try:
        # Test XmlFileUtils
        test_xml_file_utils(file_paths)
        
        # Test LinkConverter
        test_link_converter(file_paths)
        
        logger.info("All tests passed!")
    finally:
        # Clean up temporary files
        import shutil
        shutil.rmtree(file_paths["temp_dir"])
        logger.info(f"Cleaned up temporary directory: {file_paths['temp_dir']}")

if __name__ == "__main__":
    main()