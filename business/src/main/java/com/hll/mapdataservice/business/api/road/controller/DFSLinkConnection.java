package com.hll.mapdataservice.business.api.road.controller;

import java.util.*;

public class DFSLinkConnection {
    private Map<String, List<String>> graph; // 道路网络的图表示
    private List<List<String>> paths; // 存储所有连接路径的列表

    public List<List<String>> connectRoads(String[][] roads) {
        graph = new HashMap<>();
        paths = new ArrayList<>();

        // 构建道路网络的图表示
        buildGraph(roads);

        // 执行DFS算法，找到所有连接路径
        for (String start : graph.keySet()) {
//            boolean[] visited = new boolean[graph.size()];
            List<String> visited = new ArrayList<>();

            List<String> path = new ArrayList<>();
            dfs(start, visited, path);
        }

        return paths;
    }

    private void buildGraph(String[][] roads) {
        for (String[] road : roads) {
            String from = road[0];
            String to = road[1];

            // 添加双向连接
            graph.putIfAbsent(from, new ArrayList<>());
//            graph.putIfAbsent(to, new ArrayList<>());
            graph.get(from).add(to);
//            graph.get(to).add(from);
        }
    }

    private void dfs(String current, List<String> visited, List<String> path) {
        visited.add(current);
        path.add(current);

//        System.out.println("current: " + current + ", visited: " + visited + ", path: " + path);
//        if (graph.get(current).size() == 1 ) {
//            // 当前节点是末端节点，添加连接路径到结果列表
//            paths.add(new ArrayList<>(path));
//        } else {
        if(graph.get(current) != null) {
            for (String neighbor : graph.get(current)) {
                if (!visited.contains(neighbor)) {
                    dfs(neighbor, visited, path);
                }
            }
//            paths.add(new ArrayList<>(path));
        } else {
            paths.add(new ArrayList<>(path));
        }
//        }

//        visited[current] = false;
        path.remove(path.size() - 1);
    }

    public static void main(String[] args) {
        String[][] roads = {
                // {1, 2},
                // {2, 3},
                // {3, 4},
                // {4, 5},
                // {5, 11},
                // {5, 6},
                // {6, 7},
                // {7, 8},
                // {8, 10},
                // {8, 9},
                // {11,12}

        };

        DFSLinkConnection roadConnection = new DFSLinkConnection();
        List<List<String>> paths = roadConnection.connectRoads(roads);

        for (List<String> path : paths) {
            System.out.println(path);
        }
    }
}
