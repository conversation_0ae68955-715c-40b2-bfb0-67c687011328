package com.hll.mapdataservice.business.api.common.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.entity.DataMergingRequest;
import com.hll.mapdataservice.common.entity.DataMergingResponse;
import com.hll.mapdataservice.common.CountryAreaEnum;
import com.hll.mapdataservice.common.entity.GatherResult;
import com.hll.mapdataservice.common.mapper.DataMergingMapper;
import com.hll.mapdataservice.common.mapper.GatherResultMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hll.mapdataservice.common.service.IDataMergingService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Implementation of DataMergingService
 * Handles automated merging of partition tables into consolidated tables
 *
 * @since 2025-01-28
 */
@Service
@Slf4j
public class DataMergingServiceImpl implements IDataMergingService {

    @Resource
    private DataMergingMapper dataMergingMapper;
    @Resource
    private GatherResultMapper gatherResultMapper;

    /**
     * The 8 table types that need to be merged:
     * 4 material tables (with _m suffix) and 4 product tables (without _m)
     */
    private static final List<String> TABLE_TYPES = Arrays.asList(
            "link_m", "node_m", "relation_m", "rule_m", "zlevel_m",  // Material tables
            "link_e", "node_e", "relation_e", "rule_e", "zlevel_e",  // Edit tables
            "link", "node", "relation", "rule", "zlevel"           // Product tables
    );

    @Override
    public DataMergingResponse mergePartitionData(DataMergingRequest request) {
        DataMergingResponse response = new DataMergingResponse();
        response.setCountryPrefix(request.getCountryPrefix());
        response.setDryRun(request.isDryRun());
        response.setStartTime(LocalDateTime.now());

        try {
            // Validate country support
            if (!isCountrySupported(request.getCountryPrefix())) {
                response.setSuccess(false);
                response.getErrors().add("Unsupported country prefix: " + request.getCountryPrefix());
                return response;
            }

            // Get areas for the country
            List<String> areas = getCountryAreas(request.getCountryPrefix());
            response.setPartitionCount(areas.size());
            response.setProcessedAreas(areas);

            // Set up database context
            setupDatabaseContext(request.getCountryPrefix());

            // Calculate total operations
            List<String> modifyTableTypes = new ArrayList<>(TABLE_TYPES);
            if (CollUtil.isNotEmpty(areas)) {
                if (!request.isMergem2e()) {
                    //  filter content whose suffix is _e"
                    modifyTableTypes = modifyTableTypes.stream().filter(s -> !s.endsWith("_e")).collect(Collectors.toList());
                }
            } else {
                // handle single area
                if (!request.isMergem2e()) {
                    //  filter content whose suffix is _e _m
                    modifyTableTypes = modifyTableTypes.stream().filter(s -> !s.endsWith("_m") && !s.endsWith("_e")).collect(Collectors.toList());
                } else {
                    modifyTableTypes = modifyTableTypes.stream().filter(s -> !s.endsWith("_m")).collect(Collectors.toList());
                }
            }

            int totalOperations = modifyTableTypes.size();
            response.setTotalMergeOperations(totalOperations);

            // Process each table type
            int successCount = 0;
            for (String tableType : modifyTableTypes) {
                try {
                    log.info("start handling table: {}", tableType);
                    DataMergingResponse.TableMergeResult tableResult =
                            mergeTableType(tableType, areas, request);
                    response.getTableResults().put(tableType, tableResult);

                    if (tableResult.isSuccess()) {
                        successCount++;
                    } else {
                        response.getErrors().add("Failed to merge " + tableType + ": " + tableResult.getErrorMessage());
                        if (!request.isContinueOnError()) {
                            break;
                        }
                    }
                } catch (Exception e) {
                    log.error("Error merging table type: {}", tableType, e);
                    response.getErrors().add("Error merging " + tableType + ": " + e.getMessage());
                    if (!request.isContinueOnError()) {
                        break;
                    }
                }
            }

            response.setSuccessfulMergeOperations(successCount);
            response.setFailedMergeOperations(totalOperations - successCount);
            response.setSuccess(successCount == totalOperations);

        } catch (Exception e) {
            log.error("Unexpected error during data merging", e);
            response.setSuccess(false);
            response.getErrors().add("Unexpected error: " + e.getMessage());
        } finally {
            response.setEndTime(LocalDateTime.now());
            response.setDurationMs(
                    Duration.between(response.getStartTime(), response.getEndTime()).toMillis()
            );
        }
        // write merge result
        setupDatabaseContext("base");

        ObjectMapper objectMapper = new ObjectMapper();
        String responseJson;
        try {
            responseJson = objectMapper.writeValueAsString(response);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        int insert = gatherResultMapper.insert(GatherResult.builder().year(request.getYear()).quarter(request.getQuarter()).market(request.getCountryPrefix()).upDate(DateTime.now()).result(responseJson).build());
        if (insert != 1) {
            log.error("write merge result failed");
            throw new RuntimeException("write merge result failed");
        }


        return response;
    }

    @Override
    public boolean isCountrySupported(String countryPrefix) {
        return CountryAreaEnum.getAreaByCountry(countryPrefix) != null;
    }

    @Override
    public List<String> getCountryAreas(String countryPrefix) {
        return CountryAreaEnum.getAreaByCountry(countryPrefix);
    }

    /**
     * Set up database context for the country
     */
    private void setupDatabaseContext(String countryPrefix) {
        String dataSource = CommonUtils.getDsbyCountry(countryPrefix, false);
        DynamicDataSourceContextHolder.push(dataSource);
        MybatisPlusConfig.myTableName.set("");
        log.info("Set database context to: {} for country: {}", dataSource, countryPrefix);
    }

    /**
     * Merge a specific table type from all partitions into consolidated table
     */
    private DataMergingResponse.TableMergeResult mergeTableType(String tableType, List<String> areas,
                                                                DataMergingRequest request) {
        DataMergingResponse.TableMergeResult result = new DataMergingResponse.TableMergeResult();
        result.setTableType(tableType);
        result.setTargetTable(tableType);

        long startTime = System.currentTimeMillis();

        try {
            // Check if target table exists
            if (dataMergingMapper.checkTableExists(tableType) == 0) {
                result.setSuccess(false);
                result.setErrorMessage("Target table does not exist: " + tableType);
                return result;
            }

            int totalRecordsProcessed = 0;
            int totalRecordsInserted = 0;


            if (tableType.equals("relation") || tableType.equals("relation_e")) {
                // relation_m which has been merged -> relation
                totalRecordsInserted = exportRelationM2Relation();
                long recordsBefore = dataMergingMapper.getTableRecordCount(tableType + "_m");
                totalRecordsProcessed += recordsBefore;

                result.getSourcePartitions().add("relation_m");
                result.setRecordsProcessed(totalRecordsProcessed);
                result.setRecordsInserted(totalRecordsInserted);
                result.setRecordsSkipped(totalRecordsProcessed - totalRecordsInserted);
                result.setSuccess(true);
                return result;
            }
            if (tableType.equals("rule") || tableType.equals("rule_e")) {
                // rule_m which has been merged -> rule
                totalRecordsInserted = exportRuleM2Rule();
                long recordsBefore = dataMergingMapper.getTableRecordCount(tableType + "_m");
                totalRecordsProcessed += recordsBefore;

                result.getSourcePartitions().add("rule_m");
                result.setRecordsProcessed(totalRecordsProcessed);
                result.setRecordsInserted(totalRecordsInserted);
                result.setRecordsSkipped(totalRecordsProcessed - totalRecordsInserted);
                result.setSuccess(true);
                return result;
            }

            // Process each partition
            if (CollUtil.isNotEmpty(areas)) {

                for (String area : areas) {
                    String sourceTable = tableType + "_" + area;
                    result.getSourcePartitions().add(sourceTable);

                    // Check if source table exists
                    if (dataMergingMapper.checkTableExists(sourceTable) == 0) {
                        log.warn("Source table does not exist: {}, skipping", sourceTable);
                        continue;
                    }

                    // Get record count before merge
                    long recordsBefore = dataMergingMapper.getTableRecordCount(tableType);
                    long sourceRecords = dataMergingMapper.getTableRecordCount(sourceTable);
                    totalRecordsProcessed += sourceRecords;

                    if (!request.isDryRun()) {
                        // Perform the merge
                        int insertedRecords = dataMergingMapper.mergePartitionTable(tableType, sourceTable);
                        totalRecordsInserted += insertedRecords;

                        log.info("Merged {} records from {} to {}", insertedRecords, sourceTable, tableType);
                    } else {
                        log.info("Dry run: Would merge {} records from {} to {}", sourceRecords, sourceTable, tableType);
                    }
                }


            } else {
                // single area transfer. (hkg mys phl sgp ban)
                // remove _e suffix
                String sourceTable;
                if (tableType.endsWith("_e")) {
                    sourceTable = tableType.substring(0, tableType.length() - 2);
                } else {
                    sourceTable = tableType + "_m";
                }

                result.getSourcePartitions().add(sourceTable);

                // Check if source table exists
                if (dataMergingMapper.checkTableExists(sourceTable) == 0) {
                    log.warn("Source table does not exist: {}, skipping", sourceTable);
                    result.setSuccess(false);
                    result.setErrorMessage("Source table does not exist: " + sourceTable);
                    return result;
                }

                long sourceRecords = dataMergingMapper.getTableRecordCount(sourceTable);
                totalRecordsProcessed += sourceRecords;
                if (!request.isDryRun()) {
                    // Perform the merge
                    int insertedRecords = dataMergingMapper.mergePartitionTable(tableType, sourceTable);
                    totalRecordsInserted += insertedRecords;

                    log.info("Merged {} records from {} to {}", insertedRecords, sourceTable, tableType);
                } else {
                    log.info("Dry run: Would merge {} records from {} to {}", sourceRecords, sourceTable, tableType);
                }
            }
            result.setRecordsProcessed(totalRecordsProcessed);
            result.setRecordsInserted(totalRecordsInserted);
            result.setRecordsSkipped(totalRecordsProcessed - totalRecordsInserted);
            result.setSuccess(true);

        } catch (Exception e) {
            log.error("Error merging table type: {}", tableType, e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        } finally {
            result.setDurationMs(System.currentTimeMillis() - startTime);
        }

        return result;
    }

    private int exportRuleM2Rule() {
        return dataMergingMapper.transferRuleM2Rule();
    }

    private int exportRelationM2Relation() {
        return dataMergingMapper.transferRelationM2Relation();
    }
}
