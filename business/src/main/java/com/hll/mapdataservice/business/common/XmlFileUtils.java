package com.hll.mapdataservice.business.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.XmlUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.hll.mapdataservice.common.LangEnum;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.utils.EnDecryptUtils;
import com.hll.mapdataservice.common.utils.po.hereplacenamepo.*;
import com.vividsolutions.jts.io.ParseException;
import com.vividsolutions.jts.io.WKTReader;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.json.XML;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import java.io.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Component
public class XmlFileUtils {

    // @Autowired
    // private InheritIDService inheritIDService;

    // 从XML文件中读取herePoi
    public List<HereThaPoi> herePoiXmlRead(File file) {
        // HereThaPoi hereThaPoi = new HereThaPoi();
        List<HereThaPoi> hereThaPoiList = new ArrayList<>();
        Document document = XmlUtil.readXML(file);
        NodeList nodeList = XmlUtil.getNodeListByXPath("//DeliveryPackage/POI", document);
        for (int i = 0; i < nodeList.getLength(); i++) {
            HereThaPoi hereThaPoi = new HereThaPoi();
            Node item = nodeList.item(i);
            NodeList childNodes = item.getChildNodes();
            for (int j = 0; j < childNodes.getLength(); j++) {
                if (childNodes.item(j).getNodeName().compareTo("Action") == 0) {
                    hereThaPoi.setAction(childNodes.item(j).getTextContent());
                }
                if (childNodes.item(j).getNodeName().compareTo("SupplierID") == 0) {
                    hereThaPoi.setSupplierID(Integer.parseInt(childNodes.item(j).getTextContent()));
                }
                if (childNodes.item(j).getNodeName().compareTo("Identity") == 0) {
                    NodeList identityChildNodes = childNodes.item(j).getChildNodes();
                    for (int k = 0; k < identityChildNodes.getLength(); k++) {
                        if (identityChildNodes.item(k).getNodeName().compareTo("POI_Entity_ID") == 0) {
                            hereThaPoi.setPoiEntityId(Integer.parseInt(identityChildNodes.item(k).getTextContent()));
                        }
                        if (identityChildNodes.item(k).getNodeName().compareTo("Names") == 0) {
                            NodeList namesChildNodes = identityChildNodes.item(k).getChildNodes();
                            for (int l = 0; l < namesChildNodes.getLength(); l++) {
                                if (namesChildNodes.item(l).getNodeName().compareTo("POI_Name") == 0) {
                                    int textNum = 0;
                                    NodeList poiNameChildNodes = namesChildNodes.item(l).getChildNodes();
                                    for (int po = 0; po < poiNameChildNodes.getLength(); po++) {
                                        if (poiNameChildNodes.item(po).getNodeName().compareTo("Text") == 0) {
                                            if (textNum == 0) {
                                                hereThaPoi.setPoiName(poiNameChildNodes.item(po).getTextContent());
                                                textNum++;
                                            }
                                            if (textNum == 1) {
                                                hereThaPoi.setPoiNameThe(poiNameChildNodes.item(po).getTextContent());
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if (identityChildNodes.item(k).getNodeName().compareTo("Chain_ID") == 0) {
                            hereThaPoi.setChainId(Integer.parseInt(identityChildNodes.item(k).getTextContent()));
                        }
                        if (identityChildNodes.item(k).getNodeName().compareTo("Category_ID") == 0) {
                            hereThaPoi.setCategoryId(Integer.parseInt(identityChildNodes.item(k).getTextContent()));
                        }
                        if (identityChildNodes.item(k).getNodeName().compareTo("Product_Type") == 0) {
                            hereThaPoi.setProductType(Integer.parseInt(identityChildNodes.item(k).getTextContent()));
                        }
                    }
                }
                // Locations
                if (childNodes.item(j).getNodeName().compareTo("Locations") == 0) {
                    NodeList locationsChildNodes = childNodes.item(j).getChildNodes();
                    for (int m = 0; m < locationsChildNodes.getLength(); m++) {
                        if (locationsChildNodes.item(m).getNodeName().compareTo("Location") == 0) {
                            NodeList locationChildNodes = locationsChildNodes.item(m).getChildNodes();
                            for (int n = 0; n < locationChildNodes.getLength(); n++) {
                                // Address
                                if (locationChildNodes.item(n).getNodeName().compareTo("Address") == 0) {
                                    NodeList addressChildNodes = locationChildNodes.item(n).getChildNodes();
                                    for (int o = 0; o < addressChildNodes.getLength(); o++) {
                                        if (addressChildNodes.item(o).getNodeName().compareTo("ParsedAddress") == 0) {
                                            NodeList parsedAddChildNodes = addressChildNodes.item(o).getChildNodes();
                                            int zoneNum = 0;
                                            for (int oo = 0; oo < parsedAddChildNodes.getLength(); oo++) {
                                                if (parsedAddChildNodes.item(oo).getNodeName().compareTo("ParsedStreetAddress") == 0) {
                                                    NodeList parsedStreetChildNodes = parsedAddChildNodes.item(oo).getChildNodes();
                                                    for (int ooo = 0; ooo < parsedStreetChildNodes.getLength(); ooo++) {
                                                        if (parsedStreetChildNodes.item(ooo).getNodeName().compareTo("ParsedStreetName") == 0) {
                                                            NodeList parsedStreetNameChildNodes = parsedStreetChildNodes.item(ooo).getChildNodes();
                                                            for (int p = 0; p < parsedStreetNameChildNodes.getLength(); p++) {
                                                                if (parsedStreetNameChildNodes.item(p).getNodeName().compareTo("StreetName") == 0) {
                                                                    hereThaPoi.setStreetnameTha(parsedStreetNameChildNodes.item(p).getTextContent());
                                                                }
                                                                if (parsedStreetNameChildNodes.item(p).getNodeName().compareTo("StreetType") == 0) {
                                                                    hereThaPoi.setStreettypeTha(parsedStreetNameChildNodes.item(p).getTextContent());
                                                                }
                                                                if (parsedStreetNameChildNodes.item(p).getNodeName().compareTo("Trans_ParsedStreetName") == 0) {
                                                                    NodeList transChildNodes = parsedStreetNameChildNodes.item(p).getChildNodes();
                                                                    for (int q = 0; q < transChildNodes.getLength(); q++) {
                                                                        if (transChildNodes.item(q).getNodeName().compareTo("StreetName") == 0) {
                                                                            hereThaPoi.setStreetnameThe(transChildNodes.item(q).getTextContent());
                                                                        }
                                                                        if (transChildNodes.item(q).getNodeName().compareTo("StreetType") == 0) {
                                                                            hereThaPoi.setStreettypeThe(transChildNodes.item(q).getTextContent());
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                                if (parsedAddChildNodes.item(oo).getNodeName().compareTo("ParsedPlace") == 0) {
                                                    NodeList parsedPlaceChildNodes = parsedAddChildNodes.item(oo).getChildNodes();
                                                    int p2Num = 0, p3Num = 0, p4Num = 0;
                                                    for (int r = 0; r < parsedPlaceChildNodes.getLength(); r++) {
                                                        if (parsedPlaceChildNodes.item(r).getNodeName().compareTo("PlaceLevel2") == 0) {
                                                            if (p2Num == 0) {
                                                                hereThaPoi.setPlacelevel2Tha(parsedPlaceChildNodes.item(r).getTextContent());
                                                                p2Num++;
                                                            }
                                                            if (p2Num == 1) {
                                                                hereThaPoi.setPlacelevel2The(parsedPlaceChildNodes.item(r).getTextContent());
                                                            }
                                                        }
                                                        if (parsedPlaceChildNodes.item(r).getNodeName().compareTo("PlaceLevel3") == 0) {
                                                            if (p3Num == 0) {
                                                                hereThaPoi.setPlacelevel3Tha(parsedPlaceChildNodes.item(r).getTextContent());
                                                                p3Num++;
                                                            }
                                                            if (p3Num == 1) {
                                                                hereThaPoi.setPlacelevel3The(parsedPlaceChildNodes.item(r).getTextContent());
                                                            }
                                                        }
                                                        if (parsedPlaceChildNodes.item(r).getNodeName().compareTo("PlaceLevel4") == 0) {
                                                            if (p4Num == 0) {
                                                                hereThaPoi.setPlacelevel4Tha(parsedPlaceChildNodes.item(r).getTextContent());
                                                                p4Num++;
                                                            }
                                                            if (p4Num == 1) {
                                                                hereThaPoi.setPlacelevel4The(parsedPlaceChildNodes.item(r).getTextContent());
                                                            }
                                                        }
                                                    }
                                                }
                                                if (parsedAddChildNodes.item(oo).getNodeName().compareTo("Zone") == 0) {
                                                    if (zoneNum == 0) {
                                                        hereThaPoi.setZoneTha(parsedAddChildNodes.item(oo).getTextContent());
                                                        zoneNum++;
                                                    }
                                                    if (zoneNum == 1) {
                                                        hereThaPoi.setZoneThe(parsedAddChildNodes.item(oo).getTextContent());
                                                    }
                                                }
                                                if (parsedAddChildNodes.item(oo).getNodeName().compareTo("PostalCode") == 0) {
                                                    NodeList postChildNodes = parsedAddChildNodes.item(oo).getChildNodes();
                                                    for (int po = 0; po < postChildNodes.getLength(); po++) {
                                                        if (postChildNodes.item(po).getNodeName().compareTo("NT_Postal") == 0) {
                                                            hereThaPoi.setNtPostal(postChildNodes.item(po).getTextContent());
                                                        }
                                                    }
                                                }
                                                if (parsedAddChildNodes.item(oo).getNodeName().compareTo("CountryCode") == 0) {
                                                    hereThaPoi.setCountryCode(parsedAddChildNodes.item(oo).getTextContent());
                                                }
                                            }

                                        }

                                    }
                                }
                                if (locationChildNodes.item(n).getNodeName().compareTo("GeoPosition") == 0) {
                                    NodeList geoChildNodes = locationChildNodes.item(n).getChildNodes();
                                    for (int ge = 0; ge < geoChildNodes.getLength(); ge++) {
                                        if (geoChildNodes.item(ge).getNodeName().compareTo("Latitude") == 0) {
                                            hereThaPoi.setLatitude(Double.parseDouble(geoChildNodes.item(ge).getTextContent()));
                                        }
                                        if (geoChildNodes.item(ge).getNodeName().compareTo("Longitude") == 0) {
                                            hereThaPoi.setLongitude(Double.parseDouble(geoChildNodes.item(ge).getTextContent()));
                                        }
                                    }
                                }
                                if (locationChildNodes.item(n).getNodeName().compareTo("MapLinkID") == 0) {
                                    NodeList maplinkChildNodes = locationChildNodes.item(n).getChildNodes();
                                    for (int ma = 0; ma < maplinkChildNodes.getLength(); ma++) {
                                        if (maplinkChildNodes.item(ma).getNodeName().compareTo("LinkID") == 0) {
                                            hereThaPoi.setLinkID(Integer.parseInt(maplinkChildNodes.item(ma).getTextContent()));
                                        }
                                        if (maplinkChildNodes.item(ma).getNodeName().compareTo("Side_of_Street") == 0) {
                                            hereThaPoi.setSideOfStreet(maplinkChildNodes.item(ma).getTextContent());
                                        }
                                        if (maplinkChildNodes.item(ma).getNodeName().compareTo("Percent_from_RefNode") == 0) {
                                            hereThaPoi.setPercentFromRefnode(maplinkChildNodes.item(ma).getTextContent());
                                        }
                                    }
                                }
                                if (locationChildNodes.item(n).getNodeName().compareTo("Confidence") == 0) {
                                    NodeList confChildNodes = locationChildNodes.item(n).getChildNodes();
                                    for (int co = 0; co < confChildNodes.getLength(); co++) {
                                        if (confChildNodes.item(co).getNodeName().compareTo("Match_Level") == 0) {
                                            hereThaPoi.setMatchLevel(confChildNodes.item(co).getTextContent());
                                        }
                                    }
                                }

                            }
                        }
                    }
                }
                if (childNodes.item(j).getNodeName().compareTo("Details") == 0) {
                    NodeList detailsChildNodes = childNodes.item(j).getChildNodes();
                    for (int de = 0; de < detailsChildNodes.getLength(); de++) {
                        if (detailsChildNodes.item(de).getNodeName().compareTo("National_Importance") == 0) {
                            hereThaPoi.setNationalImportance(detailsChildNodes.item(de).getTextContent());
                        }
                        if (detailsChildNodes.item(de).getNodeName().compareTo("Private_Access") == 0) {
                            hereThaPoi.setPrivateAccess(detailsChildNodes.item(de).getTextContent());
                        }
                    }

                }
            }
            // System.out.println(hereThaPoi.toString());
            hereThaPoiList.add(hereThaPoi);
        }
        return hereThaPoiList;
    }

    public List<HereThaPoiCopy1> herePoiXmlReadCopy(File file) {
        long startTime = System.currentTimeMillis();
        // HereThaPoi hereThaPoi = new HereThaPoi();
        List<HereThaPoiCopy1> hereThaPoiList = new ArrayList<>();
        Document document = XmlUtil.readXML(file);
        NodeList nodeList = XmlUtil.getNodeListByXPath("//DeliveryPackage/POI", document);
        for (int i = 0; i < nodeList.getLength(); i++) {
            HereThaPoiCopy1 hereThaPoi = new HereThaPoiCopy1();
            Node item = nodeList.item(i);
            NodeList childNodes = item.getChildNodes();
            for (int j = 0; j < childNodes.getLength(); j++) {
                if (childNodes.item(j).getNodeName().compareTo("Action") == 0) {
                    hereThaPoi.setAction(childNodes.item(j).getTextContent());
                }
                if (childNodes.item(j).getNodeName().compareTo("SupplierID") == 0) {
                    hereThaPoi.setSupplierID(childNodes.item(j).getTextContent());
                }
                if (childNodes.item(j).getNodeName().compareTo("Identity") == 0) {
                    NodeList identityChildNodes = childNodes.item(j).getChildNodes();
                    for (int k = 0; k < identityChildNodes.getLength(); k++) {
                        if (identityChildNodes.item(k).getNodeName().compareTo("POI_Entity_ID") == 0) {
                            hereThaPoi.setPoiEntityId(identityChildNodes.item(k).getTextContent());
                        }
                        if (identityChildNodes.item(k).getNodeName().compareTo("Names") == 0) {
                            NodeList namesChildNodes = identityChildNodes.item(k).getChildNodes();
                            for (int l = 0; l < namesChildNodes.getLength(); l++) {
                                if (namesChildNodes.item(l).getNodeName().compareTo("POI_Name") == 0) {
                                    int textNum = 0;
                                    NodeList poiNameChildNodes = namesChildNodes.item(l).getChildNodes();
                                    for (int po = 0; po < poiNameChildNodes.getLength(); po++) {
                                        if (poiNameChildNodes.item(po).getNodeName().compareTo("Text") == 0) {
                                            if (textNum == 0) {
                                                hereThaPoi.setPoiName(poiNameChildNodes.item(po).getTextContent());
                                                textNum++;
                                            }
                                            if (textNum == 1) {
                                                hereThaPoi.setPoiNameThe(poiNameChildNodes.item(po).getTextContent());
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if (identityChildNodes.item(k).getNodeName().compareTo("Chain_ID") == 0) {
                            hereThaPoi.setChainId(identityChildNodes.item(k).getTextContent());
                        }
                        if (identityChildNodes.item(k).getNodeName().compareTo("Category_ID") == 0) {
                            hereThaPoi.setCategoryId(identityChildNodes.item(k).getTextContent());
                        }
                        if (identityChildNodes.item(k).getNodeName().compareTo("Product_Type") == 0) {
                            hereThaPoi.setProductType(identityChildNodes.item(k).getTextContent());
                        }
                    }
                }
                // Locations
                if (childNodes.item(j).getNodeName().compareTo("Locations") == 0) {
                    NodeList locationsChildNodes = childNodes.item(j).getChildNodes();
                    for (int m = 0; m < locationsChildNodes.getLength(); m++) {
                        if (locationsChildNodes.item(m).getNodeName().compareTo("Location") == 0) {
                            NodeList locationChildNodes = locationsChildNodes.item(m).getChildNodes();
                            for (int n = 0; n < locationChildNodes.getLength(); n++) {
                                // Address
                                if (locationChildNodes.item(n).getNodeName().compareTo("Address") == 0) {
                                    NodeList addressChildNodes = locationChildNodes.item(n).getChildNodes();
                                    for (int o = 0; o < addressChildNodes.getLength(); o++) {
                                        if (addressChildNodes.item(o).getNodeName().compareTo("ParsedAddress") == 0) {
                                            NodeList parsedAddChildNodes = addressChildNodes.item(o).getChildNodes();
                                            int zoneNum = 0;
                                            for (int oo = 0; oo < parsedAddChildNodes.getLength(); oo++) {
                                                if (parsedAddChildNodes.item(oo).getNodeName().compareTo("ParsedStreetAddress") == 0) {
                                                    NodeList parsedStreetChildNodes = parsedAddChildNodes.item(oo).getChildNodes();
                                                    for (int ooo = 0; ooo < parsedStreetChildNodes.getLength(); ooo++) {
                                                        if (parsedStreetChildNodes.item(ooo).getNodeName().compareTo("ParsedStreetName") == 0) {
                                                            NodeList parsedStreetNameChildNodes = parsedStreetChildNodes.item(ooo).getChildNodes();
                                                            for (int p = 0; p < parsedStreetNameChildNodes.getLength(); p++) {
                                                                if (parsedStreetNameChildNodes.item(p).getNodeName().compareTo("StreetName") == 0) {
                                                                    hereThaPoi.setStreetnameTha(parsedStreetNameChildNodes.item(p).getTextContent());
                                                                }
                                                                if (parsedStreetNameChildNodes.item(p).getNodeName().compareTo("StreetType") == 0) {
                                                                    hereThaPoi.setStreettypeTha(parsedStreetNameChildNodes.item(p).getTextContent());
                                                                }
                                                                if (parsedStreetNameChildNodes.item(p).getNodeName().compareTo("Trans_ParsedStreetName") == 0) {
                                                                    NodeList transChildNodes = parsedStreetNameChildNodes.item(p).getChildNodes();
                                                                    for (int q = 0; q < transChildNodes.getLength(); q++) {
                                                                        if (transChildNodes.item(q).getNodeName().compareTo("StreetName") == 0) {
                                                                            hereThaPoi.setStreetnameThe(transChildNodes.item(q).getTextContent());
                                                                        }
                                                                        if (transChildNodes.item(q).getNodeName().compareTo("StreetType") == 0) {
                                                                            hereThaPoi.setStreettypeThe(transChildNodes.item(q).getTextContent());
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                                if (parsedAddChildNodes.item(oo).getNodeName().compareTo("ParsedPlace") == 0) {
                                                    NodeList parsedPlaceChildNodes = parsedAddChildNodes.item(oo).getChildNodes();
                                                    int p2Num = 0, p3Num = 0, p4Num = 0;
                                                    for (int r = 0; r < parsedPlaceChildNodes.getLength(); r++) {
                                                        if (parsedPlaceChildNodes.item(r).getNodeName().compareTo("PlaceLevel2") == 0) {
                                                            if (p2Num == 0) {
                                                                hereThaPoi.setPlacelevel2Tha(parsedPlaceChildNodes.item(r).getTextContent());
                                                                p2Num++;
                                                            }
                                                            if (p2Num == 1) {
                                                                hereThaPoi.setPlacelevel2The(parsedPlaceChildNodes.item(r).getTextContent());
                                                            }
                                                        }
                                                        if (parsedPlaceChildNodes.item(r).getNodeName().compareTo("PlaceLevel3") == 0) {
                                                            if (p3Num == 0) {
                                                                hereThaPoi.setPlacelevel3Tha(parsedPlaceChildNodes.item(r).getTextContent());
                                                                p3Num++;
                                                            }
                                                            if (p3Num == 1) {
                                                                hereThaPoi.setPlacelevel3The(parsedPlaceChildNodes.item(r).getTextContent());
                                                            }
                                                        }
                                                        if (parsedPlaceChildNodes.item(r).getNodeName().compareTo("PlaceLevel4") == 0) {
                                                            if (p4Num == 0) {
                                                                hereThaPoi.setPlacelevel4Tha(parsedPlaceChildNodes.item(r).getTextContent());
                                                                p4Num++;
                                                            }
                                                            if (p4Num == 1) {
                                                                hereThaPoi.setPlacelevel4The(parsedPlaceChildNodes.item(r).getTextContent());
                                                            }
                                                        }
                                                    }
                                                }
                                                if (parsedAddChildNodes.item(oo).getNodeName().compareTo("Zone") == 0) {
                                                    if (zoneNum == 0) {
                                                        hereThaPoi.setZoneTha(parsedAddChildNodes.item(oo).getTextContent());
                                                        zoneNum++;
                                                    }
                                                    if (zoneNum == 1) {
                                                        hereThaPoi.setZoneThe(parsedAddChildNodes.item(oo).getTextContent());
                                                    }
                                                }
                                                if (parsedAddChildNodes.item(oo).getNodeName().compareTo("PostalCode") == 0) {
                                                    NodeList postChildNodes = parsedAddChildNodes.item(oo).getChildNodes();
                                                    for (int po = 0; po < postChildNodes.getLength(); po++) {
                                                        if (postChildNodes.item(po).getNodeName().compareTo("NT_Postal") == 0) {
                                                            hereThaPoi.setNtPostal(postChildNodes.item(po).getTextContent());
                                                        }
                                                    }
                                                }
                                                if (parsedAddChildNodes.item(oo).getNodeName().compareTo("CountryCode") == 0) {
                                                    hereThaPoi.setCountryCode(parsedAddChildNodes.item(oo).getTextContent());
                                                }
                                            }

                                        }

                                    }
                                }
                                if (locationChildNodes.item(n).getNodeName().compareTo("GeoPosition") == 0) {
                                    NodeList geoChildNodes = locationChildNodes.item(n).getChildNodes();
                                    for (int ge = 0; ge < geoChildNodes.getLength(); ge++) {
                                        if (geoChildNodes.item(ge).getNodeName().compareTo("Latitude") == 0) {
                                            hereThaPoi.setLatitude(geoChildNodes.item(ge).getTextContent());
                                        }
                                        if (geoChildNodes.item(ge).getNodeName().compareTo("Longitude") == 0) {
                                            hereThaPoi.setLongitude(geoChildNodes.item(ge).getTextContent());
                                        }
                                    }
                                }
                                if (locationChildNodes.item(n).getNodeName().compareTo("MapLinkID") == 0) {
                                    NodeList maplinkChildNodes = locationChildNodes.item(n).getChildNodes();
                                    for (int ma = 0; ma < maplinkChildNodes.getLength(); ma++) {
                                        if (maplinkChildNodes.item(ma).getNodeName().compareTo("LinkID") == 0) {
                                            hereThaPoi.setLinkID(maplinkChildNodes.item(ma).getTextContent());
                                        }
                                        if (maplinkChildNodes.item(ma).getNodeName().compareTo("Side_of_Street") == 0) {
                                            hereThaPoi.setSideOfStreet(maplinkChildNodes.item(ma).getTextContent());
                                        }
                                        if (maplinkChildNodes.item(ma).getNodeName().compareTo("Percent_from_RefNode") == 0) {
                                            hereThaPoi.setPercentFromRefnode(maplinkChildNodes.item(ma).getTextContent());
                                        }
                                    }
                                }
                                if (locationChildNodes.item(n).getNodeName().compareTo("Confidence") == 0) {
                                    NodeList confChildNodes = locationChildNodes.item(n).getChildNodes();
                                    for (int co = 0; co < confChildNodes.getLength(); co++) {
                                        if (confChildNodes.item(co).getNodeName().compareTo("Match_Level") == 0) {
                                            hereThaPoi.setMatchLevel(confChildNodes.item(co).getTextContent());
                                        }
                                    }
                                }

                            }
                        }
                    }
                }
                if (childNodes.item(j).getNodeName().compareTo("Details") == 0) {
                    NodeList detailsChildNodes = childNodes.item(j).getChildNodes();
                    for (int de = 0; de < detailsChildNodes.getLength(); de++) {
                        if (detailsChildNodes.item(de).getNodeName().compareTo("National_Importance") == 0) {
                            hereThaPoi.setNationalImportance(detailsChildNodes.item(de).getTextContent());
                        }
                        if (detailsChildNodes.item(de).getNodeName().compareTo("Private_Access") == 0) {
                            hereThaPoi.setPrivateAccess(detailsChildNodes.item(de).getTextContent());
                        }
                    }

                }
            }
            // System.out.println(hereThaPoi.toString());
            hereThaPoiList.add(hereThaPoi);
        }
        long endTime = System.currentTimeMillis();
        System.out.println("process file:" + file.getName() + " cost " + (endTime - startTime) / 1000 + "s");
        return hereThaPoiList;
    }

    public List<HerePhaPoi> herePhaPoiXmlRead(File file) {
        long startTime = System.currentTimeMillis();
        // HereThaPoi hereThaPoi = new HereThaPoi();
        List<HerePhaPoi> hereThaPoiList = new ArrayList<>();
        Document document = XmlUtil.readXML(file);
        NodeList nodeList = XmlUtil.getNodeListByXPath("//DeliveryPackage/POI", document);
        for (int i = 0; i < nodeList.getLength(); i++) {
            HerePhaPoi hereThaPoi = new HerePhaPoi();
            Node item = nodeList.item(i);
            NodeList childNodes = item.getChildNodes();
            for (int j = 0; j < childNodes.getLength(); j++) {
                if (childNodes.item(j).getNodeName().compareTo("Action") == 0) {
                    hereThaPoi.setAction(childNodes.item(j).getTextContent());
                }
                if (childNodes.item(j).getNodeName().compareTo("SupplierID") == 0) {
                    hereThaPoi.setSupplierID(childNodes.item(j).getTextContent());
                }
                if (childNodes.item(j).getNodeName().compareTo("Identity") == 0) {
                    NodeList identityChildNodes = childNodes.item(j).getChildNodes();
                    for (int k = 0; k < identityChildNodes.getLength(); k++) {
                        if (identityChildNodes.item(k).getNodeName().compareTo("POI_Entity_ID") == 0) {
                            hereThaPoi.setPoiEntityId(identityChildNodes.item(k).getTextContent());
                        }
                        if (identityChildNodes.item(k).getNodeName().compareTo("Names") == 0) {
                            NodeList namesChildNodes = identityChildNodes.item(k).getChildNodes();
                            for (int l = 0; l < namesChildNodes.getLength(); l++) {
                                if (namesChildNodes.item(l).getNodeName().compareTo("POI_Name") == 0) {
                                    int textNum = 0;
                                    NodeList poiNameChildNodes = namesChildNodes.item(l).getChildNodes();
                                    for (int po = 0; po < poiNameChildNodes.getLength(); po++) {
                                        if (poiNameChildNodes.item(po).getNodeName().compareTo("Text") == 0) {
                                            if (textNum == 0) {
                                                hereThaPoi.setPoiName(poiNameChildNodes.item(po).getTextContent());
                                                textNum++;
                                            }
                                            if (textNum == 1) {
                                                hereThaPoi.setPoiNameThe(poiNameChildNodes.item(po).getTextContent());
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if (identityChildNodes.item(k).getNodeName().compareTo("Chain_ID") == 0) {
                            hereThaPoi.setChainId(identityChildNodes.item(k).getTextContent());
                        }
                        if (identityChildNodes.item(k).getNodeName().compareTo("Category_ID") == 0) {
                            hereThaPoi.setCategoryId(identityChildNodes.item(k).getTextContent());
                        }
                        if (identityChildNodes.item(k).getNodeName().compareTo("Product_Type") == 0) {
                            hereThaPoi.setProductType(identityChildNodes.item(k).getTextContent());
                        }
                    }
                }
                // Locations
                if (childNodes.item(j).getNodeName().compareTo("Locations") == 0) {
                    NodeList locationsChildNodes = childNodes.item(j).getChildNodes();
                    for (int m = 0; m < locationsChildNodes.getLength(); m++) {
                        if (locationsChildNodes.item(m).getNodeName().compareTo("Location") == 0) {
                            NodeList locationChildNodes = locationsChildNodes.item(m).getChildNodes();
                            for (int n = 0; n < locationChildNodes.getLength(); n++) {
                                // Address
                                if (locationChildNodes.item(n).getNodeName().compareTo("Address") == 0) {
                                    NodeList addressChildNodes = locationChildNodes.item(n).getChildNodes();
                                    for (int o = 0; o < addressChildNodes.getLength(); o++) {
                                        if (addressChildNodes.item(o).getNodeName().compareTo("ParsedAddress") == 0) {
                                            NodeList parsedAddChildNodes = addressChildNodes.item(o).getChildNodes();
                                            int zoneNum = 0;
                                            for (int oo = 0; oo < parsedAddChildNodes.getLength(); oo++) {
                                                if (parsedAddChildNodes.item(oo).getNodeName().compareTo("ParsedStreetAddress") == 0) {
                                                    NodeList parsedStreetChildNodes = parsedAddChildNodes.item(oo).getChildNodes();
                                                    for (int ooo = 0; ooo < parsedStreetChildNodes.getLength(); ooo++) {
                                                        if (parsedStreetChildNodes.item(ooo).getNodeName().compareTo("ParsedStreetName") == 0) {
                                                            NodeList parsedStreetNameChildNodes = parsedStreetChildNodes.item(ooo).getChildNodes();
                                                            for (int p = 0; p < parsedStreetNameChildNodes.getLength(); p++) {
                                                                if (parsedStreetNameChildNodes.item(p).getNodeName().compareTo("StreetName") == 0) {
                                                                    hereThaPoi.setStreetnameTha(parsedStreetNameChildNodes.item(p).getTextContent());
                                                                }
                                                                if (parsedStreetNameChildNodes.item(p).getNodeName().compareTo("StreetType") == 0) {
                                                                    hereThaPoi.setStreettypeTha(parsedStreetNameChildNodes.item(p).getTextContent());
                                                                }
                                                                if (parsedStreetNameChildNodes.item(p).getNodeName().compareTo("Trans_ParsedStreetName") == 0) {
                                                                    NodeList transChildNodes = parsedStreetNameChildNodes.item(p).getChildNodes();
                                                                    for (int q = 0; q < transChildNodes.getLength(); q++) {
                                                                        if (transChildNodes.item(q).getNodeName().compareTo("StreetName") == 0) {
                                                                            hereThaPoi.setStreetnameThe(transChildNodes.item(q).getTextContent());
                                                                        }
                                                                        if (transChildNodes.item(q).getNodeName().compareTo("StreetType") == 0) {
                                                                            hereThaPoi.setStreettypeThe(transChildNodes.item(q).getTextContent());
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                                if (parsedAddChildNodes.item(oo).getNodeName().compareTo("ParsedPlace") == 0) {
                                                    NodeList parsedPlaceChildNodes = parsedAddChildNodes.item(oo).getChildNodes();
                                                    int p2Num = 0, p3Num = 0, p4Num = 0;
                                                    for (int r = 0; r < parsedPlaceChildNodes.getLength(); r++) {
                                                        if (parsedPlaceChildNodes.item(r).getNodeName().compareTo("PlaceLevel2") == 0) {
                                                            if (p2Num == 0) {
                                                                hereThaPoi.setPlacelevel2Tha(parsedPlaceChildNodes.item(r).getTextContent());
                                                                p2Num++;
                                                            }
                                                            if (p2Num == 1) {
                                                                hereThaPoi.setPlacelevel2The(parsedPlaceChildNodes.item(r).getTextContent());
                                                            }
                                                        }
                                                        if (parsedPlaceChildNodes.item(r).getNodeName().compareTo("PlaceLevel3") == 0) {
                                                            if (p3Num == 0) {
                                                                hereThaPoi.setPlacelevel3Tha(parsedPlaceChildNodes.item(r).getTextContent());
                                                                p3Num++;
                                                            }
                                                            if (p3Num == 1) {
                                                                hereThaPoi.setPlacelevel3The(parsedPlaceChildNodes.item(r).getTextContent());
                                                            }
                                                        }
                                                        if (parsedPlaceChildNodes.item(r).getNodeName().compareTo("PlaceLevel4") == 0) {
                                                            if (p4Num == 0) {
                                                                hereThaPoi.setPlacelevel4Tha(parsedPlaceChildNodes.item(r).getTextContent());
                                                                p4Num++;
                                                            }
                                                            if (p4Num == 1) {
                                                                hereThaPoi.setPlacelevel4The(parsedPlaceChildNodes.item(r).getTextContent());
                                                            }
                                                        }
                                                    }
                                                }
                                                if (parsedAddChildNodes.item(oo).getNodeName().compareTo("Zone") == 0) {
                                                    if (zoneNum == 0) {
                                                        hereThaPoi.setZoneTha(parsedAddChildNodes.item(oo).getTextContent());
                                                        zoneNum++;
                                                    }
                                                    if (zoneNum == 1) {
                                                        hereThaPoi.setZoneThe(parsedAddChildNodes.item(oo).getTextContent());
                                                    }
                                                }
                                                if (parsedAddChildNodes.item(oo).getNodeName().compareTo("PostalCode") == 0) {
                                                    NodeList postChildNodes = parsedAddChildNodes.item(oo).getChildNodes();
                                                    for (int po = 0; po < postChildNodes.getLength(); po++) {
                                                        if (postChildNodes.item(po).getNodeName().compareTo("NT_Postal") == 0) {
                                                            hereThaPoi.setNtPostal(postChildNodes.item(po).getTextContent());
                                                        }
                                                    }
                                                }
                                                if (parsedAddChildNodes.item(oo).getNodeName().compareTo("CountryCode") == 0) {
                                                    hereThaPoi.setCountryCode(parsedAddChildNodes.item(oo).getTextContent());
                                                }
                                            }

                                        }

                                    }
                                }
                                if (locationChildNodes.item(n).getNodeName().compareTo("GeoPosition") == 0) {
                                    NodeList geoChildNodes = locationChildNodes.item(n).getChildNodes();
                                    for (int ge = 0; ge < geoChildNodes.getLength(); ge++) {
                                        if (geoChildNodes.item(ge).getNodeName().compareTo("Latitude") == 0) {
                                            hereThaPoi.setLatitude(geoChildNodes.item(ge).getTextContent());
                                        }
                                        if (geoChildNodes.item(ge).getNodeName().compareTo("Longitude") == 0) {
                                            hereThaPoi.setLongitude(geoChildNodes.item(ge).getTextContent());
                                        }
                                    }
                                }
                                if (locationChildNodes.item(n).getNodeName().compareTo("MapLinkID") == 0) {
                                    NodeList maplinkChildNodes = locationChildNodes.item(n).getChildNodes();
                                    for (int ma = 0; ma < maplinkChildNodes.getLength(); ma++) {
                                        if (maplinkChildNodes.item(ma).getNodeName().compareTo("LinkID") == 0) {
                                            hereThaPoi.setLinkID(maplinkChildNodes.item(ma).getTextContent());
                                        }
                                        if (maplinkChildNodes.item(ma).getNodeName().compareTo("Side_of_Street") == 0) {
                                            hereThaPoi.setSideOfStreet(maplinkChildNodes.item(ma).getTextContent());
                                        }
                                        if (maplinkChildNodes.item(ma).getNodeName().compareTo("Percent_from_RefNode") == 0) {
                                            hereThaPoi.setPercentFromRefnode(maplinkChildNodes.item(ma).getTextContent());
                                        }
                                    }
                                }
                                if (locationChildNodes.item(n).getNodeName().compareTo("Confidence") == 0) {
                                    NodeList confChildNodes = locationChildNodes.item(n).getChildNodes();
                                    for (int co = 0; co < confChildNodes.getLength(); co++) {
                                        if (confChildNodes.item(co).getNodeName().compareTo("Match_Level") == 0) {
                                            hereThaPoi.setMatchLevel(confChildNodes.item(co).getTextContent());
                                        }
                                    }
                                }

                            }
                        }
                    }
                }
                if (childNodes.item(j).getNodeName().compareTo("Details") == 0) {
                    NodeList detailsChildNodes = childNodes.item(j).getChildNodes();
                    for (int de = 0; de < detailsChildNodes.getLength(); de++) {
                        if (detailsChildNodes.item(de).getNodeName().compareTo("National_Importance") == 0) {
                            hereThaPoi.setNationalImportance(detailsChildNodes.item(de).getTextContent());
                        }
                        if (detailsChildNodes.item(de).getNodeName().compareTo("Private_Access") == 0) {
                            hereThaPoi.setPrivateAccess(detailsChildNodes.item(de).getTextContent());
                        }
                    }

                }
            }
            // System.out.println(hereThaPoi.toString());
            hereThaPoiList.add(hereThaPoi);
        }
        long endTime = System.currentTimeMillis();
        System.out.println("process file:" + file.getName() + " cost " + (endTime - startTime) / 1000 + "s");
        return hereThaPoiList;
    }


    public List<HereThaPoi> herePoiXmlReadBatch(String filePath) {
        File filePaths = new File(filePath);
        File[] files = filePaths.listFiles(file -> file.getName().toLowerCase().endsWith(".xml"));
        System.out.println(files.toString());
        List<HereThaPoi> hereThaPoiListAll = new ArrayList<>();

        for (File file : files) {
            System.out.println("processing file:" + file.toString());
            hereThaPoiListAll.addAll(herePoiXmlRead(file));
        }
        return hereThaPoiListAll;
    }

    public List<HereThaPlaces> hereThaPlaceXmlRead(String fileName) throws IOException {
        // BufferedReader是可以按行读取文件
        FileInputStream inputStream = new FileInputStream(fileName);
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));

        String str = null;
        List<HereThaPlaces> hereThaPlacesList = new ArrayList<>();
        while ((str = bufferedReader.readLine()) != null) {
            // System.out.println(str);
            // 跳过表头信息和最后一行
            if (str.contains("<PlaceList") || str.contains("/PlaceList>")) {
                continue;
            }
            HereThaPlaces hereThaPlaces = new HereThaPlaces();
//            JSONObject xmlJSONObj = XML.toJSONObject(str);
//            com.alibaba.fastjson.JSONObject placeJson = JSON.parseObject(xmlJSONObj.get("Place").toString());
//            com.alibaba.fastjson.JSONObject locationListJson = JSON.parseObject(placeJson.get("LocationList").toString());
//            com.alibaba.fastjson.JSONObject identityJson = JSON.parseObject(placeJson.get("Identity").toString());
//            com.alibaba.fastjson.JSONObject contentJson = JSON.parseObject(placeJson.get("Content").toString());
//            LocationList locationList = JSON.toJavaObject(locationListJson, LocationList.class);
//            Identity identity = JSON.toJavaObject(identityJson, Identity.class);
//            Content content = JSON.toJavaObject(contentJson, Content.class);

            JSONObject xmlJSONObj = XML.toJSONObject(str);
            com.alibaba.fastjson.JSONObject placeJson = JSON.parseObject(xmlJSONObj.get("Place").toString());
            com.alibaba.fastjson.JSONObject locationListJson = JSON.parseObject(placeJson.get("LocationList").toString());
            com.alibaba.fastjson.JSONObject identityJson = JSON.parseObject(placeJson.get("Identity").toString());
            com.alibaba.fastjson.JSONObject contentJson = JSON.parseObject(placeJson.get("Content").toString());
            com.alibaba.fastjson.JSONObject categoryJson = JSON.parseObject(JSON.parseObject(contentJson.get("Base").toString()).get("CategoryList").toString());
            LocationList locationList = JSON.toJavaObject(locationListJson, LocationList.class);
            Identity identity = JSON.toJavaObject(identityJson, Identity.class);
            Content content = JSON.toJavaObject(contentJson, Content.class);
//            com.alibaba.fastjson.JSONObject categoryJson = JSON.parseObject(JSON.parseObject(contentJson.get("Base").toString()).get("CategoryList").toString());
            hereThaPlaces.setPlaceid(identity.getPlaceId());

            for (Name name : content.getBase().getNameList().getName()
            ) {
                if (name.getPrimaryFlag()) {
                    hereThaPlaces.setPlacename(name.getTextList().getText().get(0).getBaseText().getContent());
                }
            }

            hereThaPlaces.setPlaceaddress(locationList.getLocation().get(0).getAddress().getUnparsedList() == null ?
                    locationList.getLocation().get(0).getAddress().getParsedList().getParsed().get(0).getFullStreetName() :
                    locationList.getLocation().get(0).getAddress().getUnparsedList().getUnparsed().get(0).getContent());
            if (locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().size() > 1) {
                hereThaPlaces.setPlacelocationdisplay(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLongitude() + "," +
                        locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLatitude());
                hereThaPlaces.setPlacelocationdrive(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(1).getLongitude() + "," +
                        locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(1).getLatitude());
            } else {
                hereThaPlaces.setPlacelocationdisplay(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLongitude() + "," +
                        locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLatitude());
                hereThaPlaces.setPlacelocationdrive(null);
            }
            hereThaPlaces.setCategory(categoryJson.toString());
            hereThaPlacesList.add(hereThaPlaces);
        }
        return hereThaPlacesList;
    }

    public List<HerePhaPlaces> herePhaPlaceXmlRead(String fileName) throws IOException {
        // BufferedReader是可以按行读取文件
        FileInputStream inputStream = new FileInputStream(fileName);
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));

        String str = null;
        List<HerePhaPlaces> herePhaPlacesList = new ArrayList<>();
        while ((str = bufferedReader.readLine()) != null) {
            // System.out.println(str);
            // 跳过表头信息和最后一行
            if (str.contains("<PlaceList") || str.contains("/PlaceList>")) {
                continue;
            }
            HerePhaPlaces herePhaPlaces = new HerePhaPlaces();
//            JSONObject xmlJSONObj = XML.toJSONObject(str);
//            com.alibaba.fastjson.JSONObject placeJson = JSON.parseObject(xmlJSONObj.get("Place").toString());
//            com.alibaba.fastjson.JSONObject locationListJson = JSON.parseObject(placeJson.get("LocationList").toString());
//            com.alibaba.fastjson.JSONObject identityJson = JSON.parseObject(placeJson.get("Identity").toString());
//            com.alibaba.fastjson.JSONObject contentJson = JSON.parseObject(placeJson.get("Content").toString());
//            LocationList locationList = JSON.toJavaObject(locationListJson, LocationList.class);
//            Identity identity = JSON.toJavaObject(identityJson, Identity.class);
//            Content content = JSON.toJavaObject(contentJson, Content.class);

            JSONObject xmlJSONObj = XML.toJSONObject(str);
            com.alibaba.fastjson.JSONObject placeJson = JSON.parseObject(xmlJSONObj.get("Place").toString());
            com.alibaba.fastjson.JSONObject locationListJson = JSON.parseObject(placeJson.get("LocationList").toString());
            com.alibaba.fastjson.JSONObject identityJson = JSON.parseObject(placeJson.get("Identity").toString());
            com.alibaba.fastjson.JSONObject contentJson = JSON.parseObject(placeJson.get("Content").toString());
            com.alibaba.fastjson.JSONObject categoryJson = JSON.parseObject(JSON.parseObject(contentJson.get("Base").toString()).get("CategoryList").toString());
            LocationList locationList = JSON.toJavaObject(locationListJson, LocationList.class);
            Identity identity = JSON.toJavaObject(identityJson, Identity.class);
            Content content = JSON.toJavaObject(contentJson, Content.class);
//            com.alibaba.fastjson.JSONObject categoryJson = JSON.parseObject(JSON.parseObject(contentJson.get("Base").toString()).get("CategoryList").toString());
            herePhaPlaces.setPlaceid(identity.getPlaceId());
            for (Name name : content.getBase().getNameList().getName()
            ) {
                if (name.getPrimaryFlag()) {
                    herePhaPlaces.setPlacename(name.getTextList().getText().get(0).getBaseText().getContent());
                }
            }
            herePhaPlaces.setPlaceaddress(locationList.getLocation().get(0).getAddress().getUnparsedList() == null ?
                    locationList.getLocation().get(0).getAddress().getParsedList().getParsed().get(0).getFullStreetName() :
                    locationList.getLocation().get(0).getAddress().getUnparsedList().getUnparsed().get(0).getContent());
            if (locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().size() > 1) {
                herePhaPlaces.setPlacelocationdisplay(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLongitude() + "," +
                        locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLatitude());
                herePhaPlaces.setPlacelocationdrive(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(1).getLongitude() + "," +
                        locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(1).getLatitude());
            } else {
                herePhaPlaces.setPlacelocationdisplay(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLongitude() + "," +
                        locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLatitude());
                herePhaPlaces.setPlacelocationdrive(null);
            }
            herePhaPlaces.setCategory(categoryJson.toString());
            herePhaPlacesList.add(herePhaPlaces);
        }
        return herePhaPlacesList;
    }

    public static List<String> poiSourceIds(String fileName) {
        String str;
        List<String> sourceIds = new ArrayList<>();
        try {

            // BufferedReader是可以按行读取文件
            FileInputStream inputStream = new FileInputStream(fileName);
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
            while ((str = bufferedReader.readLine()) != null) {
                if (str.contains("<PlaceList") || str.contains("/PlaceList>")) {
                    continue;
                }
                JSONObject xmlJSONObj = XML.toJSONObject(str);
                com.alibaba.fastjson.JSONObject placeJson = JSON.parseObject(xmlJSONObj.get("Place").toString());
                com.alibaba.fastjson.JSONObject identityJson = JSON.parseObject(placeJson.get("Identity").toString());
                Identity identity = JSON.toJavaObject(identityJson, Identity.class);
                sourceIds.add(identity.getPlaceId());
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return sourceIds;
    }

    @Async("asyncTaskExecutor")
    public List<PoiM> poiHphaXmlRead(String fileName, List<Mtdarea> mtdareaList, Map<Long, Long> linkIdLAreaIdMap,
                                    Map<Long, Long> linkIdRAreaIdMap, boolean create, Map<String, String> sourceIdPoiIdMap, String country) throws IOException, ParseException {
        // BufferedReader是可以按行读取文件
        FileInputStream inputStream = new FileInputStream(fileName);
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));

        String str = null;
        List<PoiM> hereThaPlacesList = new ArrayList<>();
        int lineNum = 0;
        try {
            while ((str = bufferedReader.readLine()) != null) {
                // System.out.println(str);
                // 跳过表头信息和最后一行
                if (str.contains("<PlaceList") || str.contains("/PlaceList>")) {
                    lineNum++;
                    continue;
                }
                lineNum++;
                // System.out.println("lineNum is:" + lineNum);
                PoiM poi = new PoiM();
                poi.setSource("H");
//            JSONObject xmlJSONObj = XML.toJSONObject(str);
//            com.alibaba.fastjson.JSONObject placeJson = JSON.parseObject(xmlJSONObj.get("Place").toString());
//            com.alibaba.fastjson.JSONObject locationListJson = JSON.parseObject(placeJson.get("LocationList").toString());
//            com.alibaba.fastjson.JSONObject identityJson = JSON.parseObject(placeJson.get("Identity").toString());
//            com.alibaba.fastjson.JSONObject contentJson = JSON.parseObject(placeJson.get("Content").toString());
//            LocationList locationList = JSON.toJavaObject(locationListJson, LocationList.class);
//            Identity identity = JSON.toJavaObject(identityJson, Identity.class);
//            Content content = JSON.toJavaObject(contentJson, Content.class);

                JSONObject xmlJSONObj = XML.toJSONObject(str);
                com.alibaba.fastjson.JSONObject placeJson = JSON.parseObject(xmlJSONObj.get("Place").toString());
                com.alibaba.fastjson.JSONObject locationListJson = JSON.parseObject(placeJson.get("LocationList").toString());
                com.alibaba.fastjson.JSONObject identityJson = JSON.parseObject(placeJson.get("Identity").toString());
                com.alibaba.fastjson.JSONObject contentJson = JSON.parseObject(placeJson.get("Content").toString());
                com.alibaba.fastjson.JSONObject categoryJson = JSON.parseObject(JSON.parseObject(contentJson.get("Base").toString()).get("CategoryList").toString());
                LocationList locationList = JSON.toJavaObject(locationListJson, LocationList.class);
                Identity identity = JSON.toJavaObject(identityJson, Identity.class);
                Content content = JSON.toJavaObject(contentJson, Content.class);
                List<AdditionalAttribute> qualityAttrs = content.getRich().getAdditionalAttributeList().getAdditionalAttribute().stream().filter(s -> "QUALITY_SCORING".equals(s.getAttributeType())).collect(Collectors.toList());
                List<Attribute> attribute = qualityAttrs.get(0).getAttribute();
                if (!attribute.isEmpty()) {
                    List<Attribute> filterAttributes = attribute.stream().filter(s -> "overallScore".equals(s.getKey())).collect(Collectors.toList());
                    poi.setQualityscore(Integer.valueOf(filterAttributes.get(0).getContent()));
                }

                if (locationList.getLocation().get(0).getAddress().getParsedList() != null) {
                    Parsed parsed = locationList.getLocation().get(0).getAddress().getParsedList().getParsed().get(0);
                    poi.setStLangcd(parsed.getLanguageCode());
                    if ("en".equals(parsed.getLanguageCode())) {
                        if (parsed.getStreetName() != null) {
                            poi.setStNameEng(parsed.getStreetName().getBaseName());
                        }
                    }
                }


                CategoryList categoryList = JSON.toJavaObject(categoryJson, CategoryList.class);
                com.alibaba.fastjson.JSONObject nameListJson = null;
                NameList nameList = null;
                if (JSON.parseObject(contentJson.get("Base").toString()).get("NameList") != null) {
                    nameListJson = JSON.parseObject(JSON.parseObject(contentJson.get("Base").toString()).get("NameList").toString());
                    nameList = JSON.toJavaObject(nameListJson, NameList.class);
                }
                com.alibaba.fastjson.JSONObject contactListJson = null;
                ContactList contactList = null;
                if (JSON.parseObject(contentJson.get("Base").toString()).get("ContactList") != null) {
                    contactListJson = JSON.parseObject(JSON.parseObject(contentJson.get("Base").toString()).get("ContactList").toString());
                    contactList = JSON.toJavaObject(contactListJson, ContactList.class);
                }
                com.alibaba.fastjson.JSONObject externalReferenceListJson = null;
                ExternalReferenceList externalReferenceList = null;
                if (JSON.parseObject(contentJson.get("Base").toString()).get("ExternalReferenceList") != null) {
                    externalReferenceListJson = JSON.parseObject(JSON.parseObject(contentJson.get("Base").toString()).get("ExternalReferenceList").toString());
                    externalReferenceList = JSON.toJavaObject(externalReferenceListJson, ExternalReferenceList.class);
                }
                com.alibaba.fastjson.JSONObject relationshipListJson = null;
                RelationshipList relationshipList = null;
                if (JSON.parseObject(contentJson.get("Base").toString()).get("RelationshipList") != null) {
                    relationshipListJson = JSON.parseObject(JSON.parseObject(contentJson.get("Base").toString()).get("RelationshipList").toString());
                    relationshipList = JSON.toJavaObject(relationshipListJson, RelationshipList.class);
                }

//            com.alibaba.fastjson.JSONObject categoryJson = JSON.parseObject(JSON.parseObject(contentJson.get("Base").toString()).get("CategoryList").toString());

                // source_id
                poi.setSourceId(identity.getPlaceId());
                // poi_id
//                if (create) {
//                    // hereThaPlaces.setPoiId(UUID.randomUUID().toString());
//                    hereThaPlaces.setPoiMiddleId(EnDecryptUtils.convertHerePoiUUID2IDLongString(identity.getPlaceId()));
//                } else {
                // hereThaPlaces.setPoiId(sourceIdPoiIdMap.get(identity.getPlaceId()));
                poi.setPoiMiddleId(EnDecryptUtils.convertHerePoiUUID2IDLongString(identity.getPlaceId()));
//                }

                // kind
                if (categoryList.getCategory().size() == 1) {
                    if ("navteq-lcms".equals(categoryList.getCategory().get(0).getCategorySystem())) {
                        if (categoryList.getCategory().get(0).getCategoryName() != null) {
                            poi.setKind(categoryList.getCategory().get(0).getCategoryName().getText().get(0).getContent());
                        } else {
                            poi.setKind(null);
                        }
                        // kind_code
                        poi.setKindCode(categoryList.getCategory().get(0).getCategoryId());
                    }
                } else if (categoryList.getCategory().size() > 1) {
                    for (Category category : categoryList.getCategory()
                    ) {
                        if ("navteq-lcms".equals(category.getCategorySystem()) && category.getPrimaryFlag()) {
                            if (category.getCategoryName() != null) {
                                poi.setKind(category.getCategoryName().getText().get(0).getContent());
                            } else {
                                poi.setKind(null);
                            }
                            // kind_code
                            poi.setKindCode(category.getCategoryId());
                            break;
                        }
                    }
                }

                // System.out.println(nameList);
                // Name
                for (Name name : nameList.getName()
                ) {
                    StringBuilder stNameTrans = new StringBuilder();
                    StringBuilder stLanguageType = new StringBuilder();
                    StringBuilder stTransLanguage = new StringBuilder();
                    StringBuilder stNameS = new StringBuilder();
                    StringBuilder stNameSlanguage = new StringBuilder();
                    StringBuilder stAlias = new StringBuilder();
                    StringBuilder stAliasLanguage = new StringBuilder();
                    StringBuilder stExonym = new StringBuilder();
                    StringBuilder stExonymLanguage = new StringBuilder();
                    for (Text text : name.getTextList().getText()) {
                        // Name
                        if ("OFFICIAL".equals(text.getBaseText().getType())) {

                            if (text.getBaseText().getLanguageType() == null) {
                                // if (StrUtil.isWrap(text.getBaseText().getContent(), '\"')) {
                                if (text.getBaseText().getContent().startsWith("\"")&&text.getBaseText().getContent().endsWith("\"")) {
                                    poi.setName(text.getBaseText().getContent().substring(1, text.getBaseText().getContent().length() - 1));
                                } else {
                                    poi.setName(text.getBaseText().getContent());
                                }
                                // poi.setName(text.getBaseText().getContent());
                                // languageCode
                                poi.setLanguageCode(text.getBaseText().getLanguageCode());
                            } else {
                                if (StrUtil.isEmpty(poi.getName())) {
                                    // poi.setName(text.getBaseText().getContent());
                                    // if (StrUtil.isWrap(text.getBaseText().getContent(), '\"')) {
                                    if (text.getBaseText().getContent().startsWith("\"")&&text.getBaseText().getContent().endsWith("\"")) {
                                        poi.setName(text.getBaseText().getContent().substring(1, text.getBaseText().getContent().length() - 1));
                                    } else {
                                        poi.setName(text.getBaseText().getContent());
                                    }
                                }
                            }

                            // Language_type
                            if (text.getBaseText().getLanguageType() != null) {
                                stLanguageType.append(text.getBaseText().getLanguageType()).append("|");
                            }

                            // Name_ENG
                            if ("en".equals(text.getBaseText().getLanguageCode())) {
                                poi.setNameEng(text.getBaseText().getContent());
                            }

                            // Name_Trans
                            if (text.getBaseText().getLanguageType() != null) {
                                stNameTrans.append(text.getBaseText().getContent()).append("|");
                            }

                            // Trans_Language
                            if (text.getBaseText().getLanguageType() != null) {
                                stTransLanguage.append(text.getBaseText().getLanguageCode()).append("|");
                            }

                        }
                        // Name_S
                        if ("ABBREVIATION".equals(text.getBaseText().getType())) {
                            stNameS.append(text.getBaseText().getContent()).append("|");

                            if ("en".equals(text.getBaseText().getLanguageCode())) {
                                // Name_S_ENG
                                poi.setNameSEng(text.getBaseText().getContent());
                            }

                            // Name_S_Language
                            stNameSlanguage.append(text.getBaseText().getLanguageCode()).append("|");

                        } else {
                            if (text.getBaseText().getShortText() != null) {
                                stNameS.append(text.getBaseText().getContent()).append("|");
                                stNameSlanguage.append(text.getBaseText().getLanguageCode()).append("|");
                            }
                            poi.setNameSEng(text.getBaseText().getShortText());

                        }
                        // Alias
                        if ("SYNONYM".equals(text.getBaseText().getType())) {
                            stAlias.append(text.getBaseText().getContent()).append("|");

                            // Alias_ENG
                            if ("en".equals(text.getBaseText().getLanguageCode())) {
                                poi.setAliasEng(text.getBaseText().getContent());
                            }
                            // Alias_Language
                            stAliasLanguage.append(text.getBaseText().getLanguageCode()).append("|");

                        }
                        // EXONYM
                        if ("EXONYM".equals(text.getBaseText().getType())) {
                            stExonym.append(text.getBaseText().getContent()).append("|");
                            // EXONYM_language
                            stExonymLanguage.append(text.getBaseText().getLanguageCode()).append("|");
                        }
                    }
                    if (!"".equals(stNameTrans.toString())) {
                        stNameTrans.deleteCharAt(stNameTrans.lastIndexOf("|"));
                        poi.setNameTrans(stNameTrans.toString());
                    }
                    if (!"".equals(stLanguageType.toString())) {
                        stLanguageType.deleteCharAt(stLanguageType.lastIndexOf("|"));
                        poi.setLanguageType(stLanguageType.toString());
                    }
                    if (!"".equals(stTransLanguage.toString())) {
                        stTransLanguage.deleteCharAt(stTransLanguage.lastIndexOf("|"));
                        poi.setTransLanguage(stTransLanguage.toString());
                    }
                    if (!"".equals(stNameS.toString())) {
                        stNameS.deleteCharAt(stNameS.lastIndexOf("|"));
                        poi.setNameS(stNameS.toString());
                    }
                    if (!"".equals(stNameSlanguage.toString())) {
                        stNameSlanguage.deleteCharAt(stNameSlanguage.lastIndexOf("|"));
                        poi.setNameSlanguage(stNameSlanguage.toString());
                    }
                    if (!"".equals(stAlias.toString())) {
                        stAlias.deleteCharAt(stAlias.lastIndexOf("|"));
                        poi.setAlias(stAlias.toString());
                    }
                    if (!"".equals(stAliasLanguage.toString())) {
                        stAliasLanguage.deleteCharAt(stAliasLanguage.lastIndexOf("|"));
                        poi.setAliasLanguage(stAliasLanguage.toString());
                    }
                    if (!"".equals(stExonym.toString())) {
                        stExonym.deleteCharAt(stExonym.lastIndexOf("|"));
                        poi.setExonym(stExonym.toString());
                        if (StrUtil.isBlank(poi.getName())) {
                            // poi.setName(stExonym.toString());
                            // if (StrUtil.isWrap(stExonym.toString(), '\"')) {
                            if (stExonym.toString().startsWith("\"")&&stExonym.toString().endsWith("\"")) {
                                poi.setName(stExonym.toString().substring(1, stExonym.toString().length() - 1));
                            } else {
                                poi.setName(stExonym.toString());
                            }
                        }
                    }
                    if (!"".equals(stExonymLanguage.toString())) {
                        stExonymLanguage.deleteCharAt(stExonymLanguage.lastIndexOf("|"));
                        poi.setExonymLanguage(stExonymLanguage.toString());
                        if (StrUtil.isBlank(poi.getLanguageCode())) {
                            poi.setLanguageCode(stExonymLanguage.toString());
                        }
                    }

//                //Name
//                if ("OFFICIAL".equals(name.getTextList().getText().get(0).getBaseText().getType())) {
//                    hereThaPlaces.setName(name.getTextList().getText().get(0).getBaseText().getContent());
//                    //languageCode
//                    hereThaPlaces.setLanguageCode(name.getTextList().getText().get(0).getBaseText().getLanguageCode());
//
//                    //Language_type
//                    if ("TRANSLATION".equals(name.getTextList().getText().get(0).getBaseText().getLanguageType())){
//                        hereThaPlaces.setLanguageType(1);
//                    }
//                    if ("TRANSLITERATION".equals(name.getTextList().getText().get(0).getBaseText().getLanguageType())){
//                        hereThaPlaces.setLanguageType(2);
//                    }
//                    if ("TRANSCRIPTION".equals(name.getTextList().getText().get(0).getBaseText().getLanguageType())){
//                        hereThaPlaces.setLanguageType(3);
//                    }
//
//                    //Name_ENG
//                    if ("en".equals(name.getTextList().getText().get(0).getBaseText().getLanguageCode())) {
//                        hereThaPlaces.setNameEng(name.getTextList().getText().get(0).getBaseText().getContent());
//                    }
//
//                    //Name_Trans
//                    if (name.getTextList().getText().get(0).getBaseText().getLanguageType()!=null){
//                        hereThaPlaces.setNameTrans(name.getTextList().getText().get(0).getBaseText().getContent());
//                    }
//
//                    //Trans_Language
//                    if (name.getTextList().getText().get(0).getBaseText().getLanguageType()!=null){
//                        hereThaPlaces.setTransLanguage(name.getTextList().getText().get(0).getBaseText().getLanguageCode());
//                    }
//
//                }
//                //Name_S
//                if ("ABBREVIATION".equals(name.getTextList().getText().get(0).getBaseText().getType())) {
//                    hereThaPlaces.setNameS(name.getTextList().getText().get(0).getBaseText().getContent());
//
//                    //Name_S_Language
//                    hereThaPlaces.setNameSlanguage(name.getTextList().getText().get(0).getBaseText().getLanguageCode());
//
//
//                    if ("en".equals(name.getTextList().getText().get(0).getBaseText().getLanguageCode())) {
//                        //Name_S_ENG
//                        hereThaPlaces.setNameSEng(name.getTextList().getText().get(0).getBaseText().getContent());
//                    }
//                } else {
//                    hereThaPlaces.setNameS(name.getTextList().getText().get(0).getBaseText().getShortText());
//                    hereThaPlaces.setNameSEng(name.getTextList().getText().get(0).getBaseText().getShortText());
//                }
//                //Alias
//                if ("SYNONYM".equals(name.getTextList().getText().get(0).getBaseText().getType())) {
//                    hereThaPlaces.setAlias(name.getTextList().getText().get(0).getBaseText().getContent());
//                    //Alias_ENG
//                    if ("en".equals(name.getTextList().getText().get(0).getBaseText().getLanguageCode())) {
//                        hereThaPlaces.setAliasEng(name.getTextList().getText().get(0).getBaseText().getContent());
//                    }
//                   //Alias_Language
//                    hereThaPlaces.setAliasLanguage(name.getTextList().getText().get(0).getBaseText().getLanguageCode());
//
//                }
//                //EXONYM
//                if ("EXONYM".equals(name.getTextList().getText().get(0).getBaseText().getType())) {
//                    hereThaPlaces.setExonym(name.getTextList().getText().get(0).getBaseText().getContent());
//                    //EXONYM_language
//                    hereThaPlaces.setExonymLanguage(name.getTextList().getText().get(0).getBaseText().getLanguageCode());
//                }

                }

                // Address
                if (locationList.getLocation().get(0).getAddress().getUnparsedList() == null) {
                    // StreetName
                    poi.setStreetName(locationList.getLocation().get(0).getAddress().getParsedList().getParsed().get(0).getFullStreetName());
                    // HouseNumber
                    poi.setHouseNumber(locationList.getLocation().get(0).getAddress().getParsedList().getParsed().get(0).getHouseNumber());

                    if (locationList.getLocation().get(0).getAddress().getParsedList().getParsed().get(0).getHouseNumber() == null
                            && locationList.getLocation().get(0).getAddress().getParsedList().getParsed().get(0).getFullStreetName() == null) {
                        poi.setAddress("");
                        poi.setAddressEng("");
                    } else {
                        String hereAddress = "";
                        if (locationList.getLocation().get(0).getAddress().getParsedList().getParsed().get(0).getHouseNumber() != null) {
                            hereAddress += locationList.getLocation().get(0).getAddress().getParsedList().getParsed().get(0).getHouseNumber();
                            if (locationList.getLocation().get(0).getAddress().getParsedList().getParsed().get(0).getFullStreetName() != null) {
                                hereAddress += " " + locationList.getLocation().get(0).getAddress().getParsedList().getParsed().get(0).getFullStreetName();
                            }
                        } else {
                            if (locationList.getLocation().get(0).getAddress().getParsedList().getParsed().get(0).getFullStreetName() != null) {
                                hereAddress += locationList.getLocation().get(0).getAddress().getParsedList().getParsed().get(0).getFullStreetName();
                            }
                        }
                        if (locationList.getLocation().get(0).getAddress().getParsedList().getParsed().get(0).getPostalCode() != null) {
                            hereAddress += "," + locationList.getLocation().get(0).getAddress().getParsedList().getParsed().get(0).getPostalCode();
                        }

                        poi.setAddress(hereAddress);
                        if ("en".equals(locationList.getLocation().get(0).getAddress().getParsedList().getParsed().get(0).getLanguageCode())) {
                            poi.setAddressEng(hereAddress);
                        }
                    }
                } else {
                    poi.setAddress(locationList.getLocation().get(0).getAddress().getUnparsedList().getUnparsed().get(0).getContent());
                    if ("en".equals(locationList.getLocation().get(0).getAddress().getParsedList().getParsed().get(0).getLanguageCode())) {
                        poi.setAddressEng(locationList.getLocation().get(0).getAddress().getUnparsedList().getUnparsed().get(0).getContent());
                    }
                    // StreetName
                    poi.setStreetName(locationList.getLocation().get(0).getAddress().getParsedList().getParsed().get(0).getFullStreetName());
                    // HouseNumber
                    poi.setHouseNumber(locationList.getLocation().get(0).getAddress().getParsedList().getParsed().get(0).getHouseNumber());
                }
                // lng_84/Lat_84/Lon_Guide_84/Lat_Guide_84
                if (locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().size() > 1) {
                    if ("DISPLAY".equals(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getType())) {
                        poi.setLongitude(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLongitude());
                        poi.setLatitude(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLatitude());
                    }
                    if ("ROUTING".equals(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getType())) {
                        poi.setLonGuide(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLongitude());
                        poi.setLatGuide(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLatitude());
                    }
                    if ("DISPLAY".equals(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(1).getType())) {
                        poi.setLongitude(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(1).getLongitude());
                        poi.setLatitude(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(1).getLatitude());
                    }
                    if ("ROUTING".equals(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(1).getType())) {
                        poi.setLonGuide(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(1).getLongitude());
                        poi.setLatGuide(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(1).getLatitude());
                    }
                    if (poi.getLatitude() == null && poi.getLatGuide() != null) {
                        poi.setLongitude(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLongitude());
                        poi.setLatitude(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLatitude());
                    }
                } else {
                    if ("DISPLAY".equals(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getType())) {
                        poi.setLongitude(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLongitude());
                        poi.setLatitude(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLatitude());
                    }
                    if ("ROUTING".equals(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getType())) {
                        poi.setLonGuide(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLongitude());
                        poi.setLatGuide(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLatitude());
                    }
                    if (poi.getLatitude() == null && poi.getLatGuide() != null) {
                        poi.setLongitude(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLongitude());
                        poi.setLatitude(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLatitude());
                    }
                }
                // Link_ID
                String linkId = String.valueOf(locationList.getLocation().get(0).getMapReferenceList().getMapReference().getMap().getLink().getLinkPvid());
                // hereThaPlaces.setLinkId(linkId);
                if (StrUtil.isNotEmpty(linkId)) {
                    poi.setLinkId(linkId);
                } else {
                    poi.setLinkId(linkId);
                }
                String side = "";
                // Side
                switch (locationList.getLocation().get(0).getMapReferenceList().getMapReference().getMap().getLink().getSide()) {
                    case "right":
                        // hereThaPlaces.setSide("R");
                        side = "R";
                        break;
                    case "left":
                        // hereThaPlaces.setSide("L");
                        side = "L";
                        break;
                    case "neither":
                        // hereThaPlaces.setSide("N");
                        side = "N";
                        break;
                    default:
                        // hereThaPlaces.setSide("N");
                        side = "N";
                }
                poi.setSide(side);

                // Zip_Code
                poi.setZipCode(locationList.getLocation().get(0).getAddress().getParsedList().getParsed().get(0).getPostalCode());
                // Telephone
                if (contactList != null) {
                    String telePhone = "";
                    if (contactList.getContact().size() > 0) {
                        if ("PHONE".equals(contactList.getContact().get(0).getType())) {
                            telePhone += contactList.getContact().get(0).getContactString();
                        }
                        if ("Mobile".equals(contactList.getContact().get(0).getType())) {
                            telePhone += "," + contactList.getContact().get(0).getContactString();
                        }
                        poi.setTelephone(telePhone);
                    }
                }
                // Tel_Type
                // poi.setTelType("未调查");
                poi.setTelType("0");
                // POI_Class
                poi.setPoiClass("0");
                // Brand
                com.alibaba.fastjson.JSONObject chainListJson = null;
                com.alibaba.fastjson.JSONObject chainNameJson = null;
                ChainList chainList = null;
                if (JSON.parseObject(contentJson.get("Base").toString()).get("ChainList") != null) {
                    chainListJson = JSON.parseObject(JSON.parseObject(contentJson.get("Base").toString()).get("ChainList").toString());
                    if (chainListJson.get("Chain") instanceof JSONArray) {
                        chainNameJson = JSON.parseArray(chainListJson.get("Chain").toString()).getJSONObject(0);
                        com.alibaba.fastjson.JSONObject jsonObject = (com.alibaba.fastjson.JSONObject) chainNameJson.get("Name");
                        com.alibaba.fastjson.JSONObject text = (com.alibaba.fastjson.JSONObject) jsonObject.get("Text");
                        poi.setBrand((String) text.get("content"));
                    } else {
                        chainNameJson = JSON.parseObject(JSON.parseObject(chainListJson.get("Chain").toString()).get("Name").toString());
                        if (chainNameJson.get("Text") instanceof String || chainNameJson.get("Text") instanceof Integer) {
                            poi.setBrand(String.valueOf(chainNameJson.get("Text")));
                        } else {
                            chainList = JSON.toJavaObject(chainListJson, ChainList.class);
                            poi.setBrand(chainList.getChain().getName().getText().get(0).getContent());
                            // hereThaPlaces.setBrand(String.valueOf(chainListJson.getJSONObject("Chain").getJSONObject("Name").get("Text")));
                        }
                    }
                }
                // Country_Code
                if (linkId != null) {
                    Long areaId = 0L;
                    if ("L".equals(side)) {
                        areaId = linkIdLAreaIdMap.get(Long.parseLong(linkId));
                    } else if ("R".equals(side)) {
                        areaId = linkIdRAreaIdMap.get(Long.parseLong(linkId));
                    } else if ("N".equals(side)) {
                        areaId = linkIdLAreaIdMap.get(Long.parseLong(linkId));
                    }
                    if (areaId == null) {
                        // System.out.println("cannot find the linkId in streets:" + linkId);
                    } else {
                        // switch (country){
                        //    case :
                        //        lang="";
                        //}
                        //}
                        Long finalAreaId = areaId;
                        // System.out.println("linkId is:" + linkId + ",areaId is:" + areaId);
                        List<Mtdarea> mtdareaList1 = mtdareaList.stream().filter(mtdarea -> mtdarea.getAreaId().equals(finalAreaId))
                                .collect(Collectors.toList());
                        // Country_Code
                        // select * from mtdarea where  areacode_1 = '213' and admin_lvl ='1';
                        // Integer areaCode1 = mtdareaList1.get(0).getAreacode1();
                        Integer areaCode1 = mtdareaList1.get(0).getAreacode1();
                        // Country_Name
                        List<Mtdarea> cnList = mtdareaList.stream().filter(mtdarea -> mtdarea.getAreacode1().equals(areaCode1))
                                .filter(mtdarea -> mtdarea.getAdminLvl() == 1)
                                .filter(mtdarea -> "B".equals(mtdarea.getAreaType()))
                                .filter(mtdarea -> mtdarea.getLangCode().equals(LangEnum.getLangByCName(country))).collect(Collectors.toList());
                        if (cnList.size() > 0) {
                            String countryName = cnList.get(0).getAreaName();
                            poi.setAdminlvl1Name(countryName);
                            poi.setAdminlvl1Code(cnList.get(0).getGovtCode().toString());
                        } else {
                            poi.setAdminlvl1Name(null);
                            // System.out.println("cannot find areaName in mtdarea;linkId is:" + linkId + ",areaId is:" + areaId + "Country_Code is:" + areaCode1);
                        }
                        // Country_Name_ENG
                        List<Mtdarea> mtdareaListEng = mtdareaList.stream().filter(mtdarea -> mtdarea.getAreacode1().equals(areaCode1))
                                .filter(mtdarea -> mtdarea.getAdminLvl() == 1)
                                .filter(mtdarea -> "B".equals(mtdarea.getAreaType()))
                                .filter(mtdarea -> mtdarea.getLangCode().equals("ENG")).collect(Collectors.toList());
                        if (mtdareaListEng.size() > 0) {
                            String countryNameEng = mtdareaListEng.get(0).getAreaName();
                            poi.setAdminlvl1NameEng(countryNameEng);
                        } else {
                            poi.setAdminlvl1NameEng(null);
                            // System.out.println("cannot find eng in mtdarea;linkId is:" + linkId + ",areaId is:" + areaId + "Country_Code is:" + areaCode1);
                        }

                        // System.out.println("Country_Code is:"+areaCode1+",Country_Name is:"+countryName+",Country_Name_ENG is:"+countryNameEng);
                        // Region_Code
                        // Integer areaCode2 = mtdareaList1.get(0).getAreacode2();
                        Integer areaCode2 = mtdareaList1.get(0).getAreacode2();
//                    Integer govCode2 = mtdareaList1.stream().filter(mtdarea -> mtdarea.getAreacode2().equals(areaCode2))
//                    hereThaPlaces.setRegionCode(areaCode2.toString());
                        // Region_Name
                        List<Mtdarea> rnList = mtdareaList.stream().filter(mtdarea -> mtdarea.getAreacode1().equals(areaCode1))
                                .filter(mtdarea -> mtdarea.getAreacode2().equals(areaCode2))
                                .filter(mtdarea -> mtdarea.getAdminLvl() == 2)
                                .filter(mtdarea -> "B".equals(mtdarea.getAreaType()))
                                .filter(mtdarea -> mtdarea.getLangCode().equals(LangEnum.getLangByCName(country))).collect(Collectors.toList());
                        if (rnList.size() > 0) {
                            String regionName = rnList.get(0).getAreaName();
                            poi.setAdminlvl2Name(regionName);
                            poi.setAdminlvl2Code(rnList.get(0).getGovtCode().toString());
                        } else {
                            poi.setAdminlvl2Name(null);
                            // System.out.println("cannot find regionName in mtdarea;linkId is:" + linkId + ",areaId is:" + areaId + "Country_Code is:" + areaCode1);
                        }
                        // Region_Name_Eng
                        List<Mtdarea> regionNameEngList = mtdareaList.stream().filter(mtdarea -> mtdarea.getAreacode1().equals(areaCode1))
                                .filter(mtdarea -> mtdarea.getAreacode2().equals(areaCode2))
                                .filter(mtdarea -> mtdarea.getAdminLvl() == 2)
                                .filter(mtdarea -> "B".equals(mtdarea.getAreaType()))
                                .filter(mtdarea -> mtdarea.getLangCode().equals("ENG")).collect(Collectors.toList());
                        if (regionNameEngList.size() > 0) {
                            String regionNameEng = regionNameEngList.get(0).getAreaName();
                            poi.setAdminlvl2NameEng(regionNameEng);
                        } else {
                            poi.setAdminlvl2NameEng(null);
                            // System.out.println("cannot find eng in mtdarea;linkId is:" + linkId + ",areaId is:" + areaId + "Region_Code is:" + areaCode2);
                        }
                        // System.out.println("Region_Code is:"+areaCode2+",Region_Name is:"+regionName+",Region_Name_ENG is:"+regionNameEng);
                        // Prov_Code
                        // Integer areaCode3 = mtdareaList1.get(0).getAreacode3();
                        Integer areaCode3 = mtdareaList1.get(0).getAreacode3();
                        // hereThaPlaces.setProvCode(areaCode3.toString());
                        // Prov_Name
                        List<Mtdarea> pnList = mtdareaList.stream().filter(mtdarea -> mtdarea.getAreacode1().equals(areaCode1))
                                .filter(mtdarea -> mtdarea.getAreacode2().equals(areaCode2))
                                .filter(mtdarea -> mtdarea.getAreacode3().equals(areaCode3))
                                .filter(mtdarea -> mtdarea.getAdminLvl() == 3)
                                .filter(mtdarea -> "B".equals(mtdarea.getAreaType()))
                                .filter(mtdarea -> mtdarea.getLangCode().equals(LangEnum.getLangByCName(country))).collect(Collectors.toList());
                        if (pnList.size() > 0) {
                            String provName = pnList.get(0).getAreaName();
                            poi.setAdminlvl3Name(provName);
                            poi.setAdminlvl3Code(pnList.get(0).getGovtCode().toString());
                        } else {
                            poi.setAdminlvl3Name(null);
                            // System.out.println("cannot find provName in mtdarea;linkId is:" + linkId + ",areaId is:" + areaId + "Country_Code is:" + areaCode1);

                        }
                        // Prov_Name_Eng
                        List<Mtdarea> provNameList = mtdareaList.stream().filter(mtdarea -> mtdarea.getAreacode1().equals(areaCode1))
                                .filter(mtdarea -> mtdarea.getAreacode2().equals(areaCode2))
                                .filter(mtdarea -> mtdarea.getAreacode3().equals(areaCode3))
                                .filter(mtdarea -> mtdarea.getAdminLvl() == 3)
                                .filter(mtdarea -> "B".equals(mtdarea.getAreaType()))
                                .filter(mtdarea -> mtdarea.getLangCode().equals("ENG")).collect(Collectors.toList());
                        if (provNameList.size() > 0) {
                            String provNameEng = provNameList.get(0).getAreaName();
                            poi.setAdminlvl3NameEng(provNameEng);
                        } else {
                            poi.setAdminlvl3NameEng(null);
                            // System.out.println("cannot find eng in mtdarea;linkId is:" + linkId + ",areaId is:" + areaId + "Prov_Code is:" + areaCode3);
                        }

                        // System.out.println("Prov_Code is:"+areaCode3+",Prov_Name is:"+provName+",Prov_Name_ENG is:"+provNameEng);
                        // City_Code
                        // Integer areaCode4 = mtdareaList1.get(0).getAreacode4();
                        Integer areaCode4 = mtdareaList1.get(0).getAreacode4();
                        // hereThaPlaces.setCityCode(areaCode4.toString());
                        // City_Name
                        List<Mtdarea> cnList2 = mtdareaList.stream().filter(mtdarea -> mtdarea.getAreacode1().equals(areaCode1))
                                .filter(mtdarea -> mtdarea.getAreacode2().equals(areaCode2))
                                .filter(mtdarea -> mtdarea.getAreacode3().equals(areaCode3))
                                .filter(mtdarea -> mtdarea.getAreacode4().equals(areaCode4))
                                .filter(mtdarea -> mtdarea.getAdminLvl() == 4)
                                .filter(mtdarea -> "B".equals(mtdarea.getAreaType()))
                                .filter(mtdarea -> mtdarea.getLangCode().equals(LangEnum.getLangByCName(country))).collect(Collectors.toList());
                        if (cnList2.size() > 0) {
                            String cityName = cnList2.get(0).getAreaName();
                            poi.setAdminlvl4Name(cityName);
                            poi.setAdminlvl4Code(cnList2.get(0).getGovtCode().toString());
                        } else {
                            poi.setAdminlvl4Name(null);
                            // System.out.println("cannot find cityName in mtdarea;linkId is:" + linkId + ",areaId is:" + areaId + "Country_Code is:" + areaCode1);

                        }
                        // City_Name_Eng
                        List<Mtdarea> cityNameEngList = mtdareaList.stream().filter(mtdarea -> mtdarea.getAreacode1().equals(areaCode1))
                                .filter(mtdarea -> mtdarea.getAreacode2().equals(areaCode2))
                                .filter(mtdarea -> mtdarea.getAreacode3().equals(areaCode3))
                                .filter(mtdarea -> mtdarea.getAreacode4().equals(areaCode4))
                                .filter(mtdarea -> mtdarea.getAdminLvl() == 4)
                                .filter(mtdarea -> "B".equals(mtdarea.getAreaType()))
                                .filter(mtdarea -> mtdarea.getLangCode().equals("ENG")).collect(Collectors.toList());
                        if (cityNameEngList.size() > 0) {
                            String cityNameEng = cityNameEngList.get(0).getAreaName();
                            poi.setAdminlvl4NameEng(cityNameEng);
                        } else {
                            poi.setAdminlvl4NameEng(null);
                            // System.out.println("cannot find eng in mtdarea;linkId is:" + linkId + ",areaId is:" + areaId + "City_Code is:" + areaCode4);
                        }

                        // System.out.println("City_Code is:"+areaCode4+",City_Name is:"+cityName+",City_Name_ENG is:"+cityNameEng);
                        // Ad_Code
                        // Integer areaCode5 = mtdareaList1.get(0).getAreacode5();
                        Integer areaCode5 = mtdareaList1.get(0).getAreacode5();
                        // hereThaPlaces.setAdCode(areaCode5.toString());
                        // Ad_Name
                        List<Mtdarea> anList = mtdareaList.stream().filter(mtdarea -> mtdarea.getAreacode1().equals(areaCode1))
                                .filter(mtdarea -> mtdarea.getAreacode2().equals(areaCode2))
                                .filter(mtdarea -> mtdarea.getAreacode3().equals(areaCode3))
                                .filter(mtdarea -> mtdarea.getAreacode4().equals(areaCode4))
                                .filter(mtdarea -> mtdarea.getAreacode5().equals(areaCode5))
                                .filter(mtdarea -> mtdarea.getAdminLvl() == 5)
                                .filter(mtdarea -> "B".equals(mtdarea.getAreaType()))
                                .filter(mtdarea -> mtdarea.getLangCode().equals(LangEnum.getLangByCName(country))).collect(Collectors.toList());
                        if (anList.size() > 0) {
                            String adName = anList.get(0).getAreaName();
                            poi.setAdminlvl5Name(adName);
                            poi.setAdminlvl5Code(anList.get(0).getGovtCode().toString());
                        } else {
                            poi.setAdminlvl5Name(null);
                            // System.out.println("cannot find adName in mtdarea;linkId is:" + linkId + ",areaId is:" + areaId + "Country_Code is:" + areaCode1);
                        }
                        // Ad_Name_Eng
                        List<Mtdarea> adNameEngList = mtdareaList.stream().filter(mtdarea -> mtdarea.getAreacode1().equals(areaCode1))
                                .filter(mtdarea -> mtdarea.getAreacode2().equals(areaCode2))
                                .filter(mtdarea -> mtdarea.getAreacode3().equals(areaCode3))
                                .filter(mtdarea -> mtdarea.getAreacode4().equals(areaCode4))
                                .filter(mtdarea -> mtdarea.getAreacode5().equals(areaCode5))
                                .filter(mtdarea -> mtdarea.getAdminLvl() == 5)
                                .filter(mtdarea -> "B".equals(mtdarea.getAreaType()))
                                .filter(mtdarea -> mtdarea.getLangCode().equals("ENG")).collect(Collectors.toList());
                        if (adNameEngList.size() > 0) {
                            String adNameEng = adNameEngList.get(0).getAreaName();
                            poi.setAdminlvl5NameEng(adNameEng);
                        } else {
                            poi.setAdminlvl5NameEng(null);
                            // System.out.println("cannot find eng in mtdarea;linkId is:" + linkId + ",areaId is:" + areaId + "Ad_Code is:" + areaCode5);
                        }

                        // System.out.println("Ad_Code is:"+areaCode5+",Ad_Name is:"+adName+",Ad_Name_ENG is:"+adNameEng);
                    }
                }
                // Parent/Is_Parent/Children/Is_Child
                String parents = "", children = "";
                if (relationshipList != null) {
                    if (relationshipList.getRelationship().size() > 0) {
                        for (Relationship relationship : relationshipList.getRelationship()) {
                            if ("ParentChild".equals(relationship.getName()) && "places".equals(relationship.getSystem())
                                    && relationship.getReferenceList() != null) {
//                                if ("CHILD".equals(relationship.getName()) && ) {
                                // hereThaPlaces.setIsChild("1");
                                if (relationship.getReferenceList().getReference().size() > 0) {
                                    for (Reference reference : relationship.getReferenceList().getReference()) {
                                        if ("PARENT".equals(reference.getReferenceID().getType())) {
                                            parents += "," + reference.getReferenceID().getContent();
                                        }
                                        if ("CHILD".equals(reference.getReferenceID().getType())) {
                                            children += "," + reference.getReferenceID().getContent();
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                // if (hereThaPlaces.getIsChild() != "1") {
                //     hereThaPlaces.setIsChild("0");
                // }
                // if (hereThaPlaces.getIsParent() != "1") {
                //     hereThaPlaces.setIsParent("0");
                // }
                poi.setParent(parents.replaceFirst(",", ""));
                poi.setChildren(children.replaceFirst(",", ""));

                // POI_Geo
                if (poi.getLatitude() != null && poi.getLongitude() != null) {
                    String geoWkt = "POINT(" + poi.getLongitude() + " " + poi.getLatitude() + ")";
                    poi.setPoiGeo(new WKTReader().read(geoWkt).toString());
                }

                // Update_Time
                poi.setUpdateTime(LocalDateTime.now());
                poi.setCreateTime(LocalDateTime.now());
                // Status
                poi.setStatus(0);
                // hereThaPlaces.setKind(categoryJson.toString());
                hereThaPlacesList.add(poi);
//            if(hereThaPlacesList.size()>=100){
//                break;
//            }
            }
        } finally {
            bufferedReader.close();
            inputStream.close();
        }
        return hereThaPlacesList;
    }

    public List<RuleM> dividerFileRead(String fileName) throws IOException, ParseException {
        // BufferedReader是可以按行读取文件
        FileInputStream inputStream = new FileInputStream(fileName);
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));

        String str = null;
        int i = 0;
        List<RuleM> ruleList = new ArrayList<>();
        while ((str = bufferedReader.readLine()) != null) {
            // System.out.println(str);
            String[] splitResult = str.split("\t");
            // System.out.println(splitResult[2]+","+splitResult[3]+","+splitResult[4]);

            RuleM rule = new RuleM();
            rule.setRuleId(UUID.randomUUID().toString());
            // ruleSw2021q133.setRuleId(i++);
            rule.setInlinkId(splitResult[2]);
            rule.setNodeId(splitResult[3]);
            rule.setOutlinkId(splitResult[4]);
            rule.setFlag(1);
            rule.setUpDate(LocalDateTime.now());
            // ruleSw2021q133.setVperiod("null");
            // ruleSw2021q133.setVehclType("null");
            rule.setDatasource("7");
            rule.setStatus(0);
            ruleList.add(rule);
        }

        return ruleList;
    }

    public Map<String, String> rdfCfFileRead(String fileName) throws IOException, ParseException {
        // BufferedReader是可以按行读取文件
        FileInputStream inputStream = new FileInputStream(fileName);
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));

        String str = null;
        int i = 0;
        Map<String, String> rdfCfMap = new HashMap<>();
        while ((str = bufferedReader.readLine()) != null) {
            // System.out.println(str);
            String[] splitResult = str.split("\t");
            if ("I".equals(splitResult[1])) {
                rdfCfMap.put(splitResult[0], splitResult[1]);
            }
            // System.out.println(splitResult[0]+","+splitResult[1]);
        }

        return rdfCfMap;
    }

    public MultiValueMap<String, String> rdfCfLinkFileRead(String fileName) throws IOException, ParseException {
        // BufferedReader是可以按行读取文件
        FileInputStream inputStream = new FileInputStream(fileName);
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));

        String str = null;
        int i = 0;
        MultiValueMap<String, String> rdfCfLinkMap = new LinkedMultiValueMap<>();
        // Map<String,String> rdfCfLinkMap = new HashMap<>();
        while ((str = bufferedReader.readLine()) != null) {
            // System.out.println(str);
            String[] splitResult = str.split("\t");
            // rdfCfLinkMap.add(splitResult[1],splitResult[0]);
            rdfCfLinkMap.add(splitResult[0], splitResult[1]);
            // System.out.println(splitResult[1]+","+splitResult[0]);
        }

        return rdfCfLinkMap;
    }

    public MultiValueMap<String, String> rdfLinkCfFileRead(String fileName) throws IOException, ParseException {
        // BufferedReader是可以按行读取文件
        FileInputStream inputStream = new FileInputStream(fileName);
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));

        String str = null;
        int i = 0;
        MultiValueMap<String, String> rdfCfLinkMap = new LinkedMultiValueMap<>();
        // Map<String,String> rdfCfLinkMap = new HashMap<>();
        while ((str = bufferedReader.readLine()) != null) {
            // System.out.println(str);
            String[] splitResult = str.split("\t");
            rdfCfLinkMap.add(splitResult[1], splitResult[0]);
            // rdfCfLinkMap.add(splitResult[0],Integer.parseInt(splitResult[1]));
            // System.out.println(splitResult[1]+","+splitResult[0]);
        }

        return rdfCfLinkMap;
    }


    public Map<String, List<String>> rdfNavLinkFileRead(String fileName, String country) throws IOException {
        String[] singleFileArray = fileName.split(",");
        Map<String, List<String>> rdfNavLinkMap = new HashMap<>();
        for (String singleFile : singleFileArray) {
            // BufferedReader是可以按行读取文件
            FileInputStream inputStream = new FileInputStream(singleFile);
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
            String str;
            while ((str = bufferedReader.readLine()) != null) {
                String[] splitResult = str.split("\t", -1);
                // if (splitResult.length == 36) {
                if (country.toUpperCase().equals(splitResult[1])) {
                    // rdfNavLinkMap.put(splitResult[0], Arrays.asList(splitResult).subList(1, splitResult.length));
                    rdfNavLinkMap.put(splitResult[0], Arrays.asList(splitResult[33], splitResult[35]));
                }
            }
        }
        return rdfNavLinkMap;
    }

    public static void main(String[] args) throws IOException {
//        Document document = XmlUtil.readXML(FileUtil.file("/Users/<USER>/Downloads/London.xml"));
//        NodeList nodeList = XmlUtil.getNodeListByXPath("//place", document);
//        for(int i = 0; i < nodeList.getLength(); i++) {
//            Node item = nodeList.item(i);
//            NodeList childNodes = item.getChildNodes();
//            for(int j=0;j<childNodes.getLength();j++){
//                Node child = childNodes.item(j);
//                System.out.println(child.getTextContent());
//                if(child.getNodeName().compareTo("Action") == 0){
//                    System.out.println(child.getNodeName()+":"+child.getTextContent());
//                }
//                if(child.getNodeName().compareTo("SupplierID") == 0){
//                    System.out.println(child.getNodeName()+":"+child.getTextContent());
//                }
//                if(child.getNodeName().compareTo("POI_Entity_ID") == 0){
//                    System.out.println(child.getNodeName()+":"+child.getTextContent());
//                }
//                if(child.getNodeName().compareTo("Text") == 0){
//                    System.out.println(child.getNodeName()+":"+child.getTextContent());
//                }
//                System.out.println(child.getNamespaceURI());
//            }
//        }

        // BufferedReader是可以按行读取文件
        // FileInputStream inputStream = new FileInputStream("/Users/<USER>/Downloads/London.xml");
        FileInputStream inputStream = new FileInputStream("/Users/<USER>/Downloads/PZAM211F0WPZ000DPLAC/APAC_211F0/vnmunprocessed/vnmtest.xml");
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));

        String str = null;
        while ((str = bufferedReader.readLine()) != null) {
            if (str.contains("<PlaceList") || str.contains("/PlaceList>")) {
                continue;
            }
            JSONObject xmlJSONObj = XML.toJSONObject(str);
            com.alibaba.fastjson.JSONObject placeJson = JSON.parseObject(xmlJSONObj.get("Place").toString());
            com.alibaba.fastjson.JSONObject locationListJson = JSON.parseObject(placeJson.get("LocationList").toString());
            com.alibaba.fastjson.JSONObject identityJson = JSON.parseObject(placeJson.get("Identity").toString());
            com.alibaba.fastjson.JSONObject contentJson = JSON.parseObject(placeJson.get("Content").toString());
            com.alibaba.fastjson.JSONObject categoryJson = JSON.parseObject(JSON.parseObject(contentJson.get("Base").toString()).get("CategoryList").toString());
            com.alibaba.fastjson.JSONObject nameListJson = JSON.parseObject(JSON.parseObject(contentJson.get("Base").toString()).get("NameList").toString());
            // com.alibaba.fastjson.JSONObject ContactListJson = JSON.parseObject(JSON.parseObject(contentJson.get("Base").toString()).get("ContactList").toString());
            // com.alibaba.fastjson.JSONObject ExternalReferenceListJson = JSON.parseObject(JSON.parseObject(contentJson.get("Base").toString()).get("ExternalReferenceList").toString());
            com.alibaba.fastjson.JSONObject chainListJson = null;
            com.alibaba.fastjson.JSONObject chainNameJson = null;
            if (JSON.parseObject(contentJson.get("Base").toString()).get("ChainList") != null) {
                chainListJson = JSON.parseObject(JSON.parseObject(contentJson.get("Base").toString()).get("ChainList").toString());
                chainNameJson = JSON.parseObject(JSON.parseObject(chainListJson.get("Chain").toString()).get("Name").toString());
                if ("java.lang.String".equals(chainNameJson.get("Text").getClass().getName())) {
                    NameString chainName = JSON.toJavaObject(chainNameJson, NameString.class);
                }
            }


            LocationList locationList = JSON.toJavaObject(locationListJson, LocationList.class);
            Identity identity = JSON.toJavaObject(identityJson, Identity.class);
            // Content content = JSON.toJavaObject(contentJson, Content.class);

            CategoryList categoryList = JSON.toJavaObject(categoryJson, CategoryList.class);
            NameList nameList = JSON.toJavaObject(nameListJson, NameList.class);
            HereThaPlaces hereThaPlaces = new HereThaPlaces();
//            for (Name name : content.getBase().getNameList().getName()
//            ) {
//                if (name.getPrimaryFlag()) {
//                    hereThaPlaces.setPlacename(name.getTextList().getText().get(0).getBaseText().getContent());
//                }
//            }
            // hereThaPlaces.setPlacename(content.getBase().getNameList().getName().getTextList().getText().getBaseText().getContent());
            hereThaPlaces.setPlaceaddress(locationList.getLocation().get(0).getAddress().getUnparsedList() == null ?
                    locationList.getLocation().get(0).getAddress().getParsedList().getParsed().get(0).getFullStreetName() :
                    locationList.getLocation().get(0).getAddress().getUnparsedList().getUnparsed().get(0).getContent());
            if (locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().size() > 1) {
                hereThaPlaces.setPlacelocationdisplay(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLongitude() + "," +
                        locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLatitude());
                hereThaPlaces.setPlacelocationdrive(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(1).getLongitude() + "," +
                        locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(1).getLatitude());
            } else {
                hereThaPlaces.setPlacelocationdisplay(locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLongitude() + "," +
                        locationList.getLocation().get(0).getGeoPositionList().getGeoPosition().get(0).getLatitude());
                hereThaPlaces.setPlacelocationdrive(null);
            }
            hereThaPlaces.setCategory(categoryJson.toString());
            hereThaPlaces.setCreatetime(LocalDateTime.now());

//            //Location
//            com.alibaba.fastjson.JSONObject locationJson = JSON.parseObject(locationListJson.get("Location").toString());
//            String label = locationJson.getString("label");
//            String supplier = locationJson.getString("supplier");
//            String type = locationJson.getString("type");
//            String locationId = locationJson.getString("locationId");
//            String primary = locationJson.getString("primary");
//            //Address
//            com.alibaba.fastjson.JSONObject AddressJson = JSON.parseObject(locationJson.get("Address").toString());
//            //UnparsedList
//            com.alibaba.fastjson.JSONObject UnparsedListJson = JSON.parseObject(AddressJson.get("UnparsedList").toString());
//            com.alibaba.fastjson.JSONObject UnparsedJson = JSON.parseObject(UnparsedListJson.get("Unparsed").toString());
//
//            String defaultLanguage = UnparsedJson.getString("defaultLanguage");
//            String languageCode = UnparsedJson.getString("languageCode");
//            String Unparsed = UnparsedJson.getString("content");
//            //ParsedList
//            com.alibaba.fastjson.JSONObject ParsedListJson = JSON.parseObject(AddressJson.get("ParsedList").toString());
//
//            com.alibaba.fastjson.JSONObject parsedJson = JSON.parseObject(ParsedListJson.toString());
//
//            com.alibaba.fastjson.JSONArray parsedArray = JSON.parseArray(parsedJson.get("Parsed").toString());
//            for(int i=0;i<parsedArray.size();i++){
//                if("false".equals(JSON.parseObject(parsedArray.get(i).toString()).getString("defaultLanguage"))){
//
//                }
//            }
//
//
//            xmlJSONObj.toString();
//            String s = str.replaceAll("=", ":").replaceAll(" ", ",");
//            JSONObject js = JSONObject.parseObject(s);
//            JSONArray ja = js.getJSONArray("array");
//            Map<String,String> map = new HashMap<String, String>();
//            System.out.println(js.toString());
            // System.out.println(str);
        }

        // close
        inputStream.close();
        bufferedReader.close();


//        File filePaths = new File("/Users/<USER>/Downloads/WZAM20131WWZ000DBF00/Thailand/THA");
//        File[] files = filePaths.listFiles(file-> file.getName().toLowerCase().endsWith(".xml"));
//        System.out.println(files.toString());
//
//        HereThaPoi hereThaPoi = new HereThaPoi();
//        Document document = XmlUtil.readXML(FileUtil.file("/Users/<USER>/Downloads/WZAM20131WWZ000DBF00/Thailand/THA/NVTPOI_THA_3578_001.xml"));
//        NodeList nodeList = XmlUtil.getNodeListByXPath("//DeliveryPackage/POI", document);
//        for(int i = 0; i < nodeList.getLength(); i++) {
//            Node item = nodeList.item(i);
//            NodeList childNodes = item.getChildNodes();
//            for(int j=0;j<childNodes.getLength();j++){
//                if(childNodes.item(j).getNodeName().compareTo("Action") ==0 ){
//                    hereThaPoi.setAction(childNodes.item(j).getTextContent());
//                }
//                if(childNodes.item(j).getNodeName().compareTo("SupplierID") ==0 ){
//                    hereThaPoi.setSupplierID(Integer.parseInt(childNodes.item(j).getTextContent()));
//                }
//                if(childNodes.item(j).getNodeName().compareTo("Identity") ==0 ){
//                    NodeList identityChildNodes = childNodes.item(j).getChildNodes();
//                    for(int k = 0;k<identityChildNodes.getLength();k++){
//                        if(identityChildNodes.item(k).getNodeName().compareTo("POI_Entity_ID") ==0){
//                            hereThaPoi.setPoiEntityId(Integer.parseInt(identityChildNodes.item(k).getTextContent()));
//                        }
//                        if(identityChildNodes.item(k).getNodeName().compareTo("Names") ==0){
//                            NodeList namesChildNodes = identityChildNodes.item(k).getChildNodes();
//                            System.out.println(namesChildNodes.item(2).getNodeName());
//                            for(int l=0;l< namesChildNodes.getLength();l++){
//                                if(namesChildNodes.item(l).getNodeName().compareTo("POI_Name") == 0){
//                                    NodeList poiNameChildNodes = namesChildNodes.item(l).getChildNodes();
//                                    hereThaPoi.setPoiName(poiNameChildNodes.item(1).getTextContent());
//                                    hereThaPoi.setPoiNameThe(poiNameChildNodes.item(3).getTextContent());
//                                    System.out.println(poiNameChildNodes.item(3).getNodeName());
//                                }
//                            }
//                        }
//                        if(identityChildNodes.item(k).getNodeName().compareTo("Chain_ID") ==0){
//                            hereThaPoi.setChainId(Integer.parseInt(identityChildNodes.item(k).getTextContent()));
//                        }
//                        if(identityChildNodes.item(k).getNodeName().compareTo("Category_ID") ==0){
//                            hereThaPoi.setCategoryId(Integer.parseInt(identityChildNodes.item(k).getTextContent()));
//                        }
//                        if(identityChildNodes.item(k).getNodeName().compareTo("Product_Type") ==0){
//                            hereThaPoi.setProductType(Integer.parseInt(identityChildNodes.item(k).getTextContent()));
//                        }
//                    }
//                }
//                //Locations
//                if(childNodes.item(j).getNodeName().compareTo("Locations") ==0 ){
//                    NodeList locationsChildNodes = childNodes.item(j).getChildNodes();
//                    for(int m=0;m<locationsChildNodes.getLength();m++){
//                        if(locationsChildNodes.item(m).getNodeName().compareTo("Location") ==0){
//                            NodeList locationChildNodes = locationsChildNodes.item(m).getChildNodes();
//                            for(int n =0;n< locationChildNodes.getLength();n++){
//                                //Address
//                                if(locationChildNodes.item(n).getNodeName().compareTo("Address") ==0 ){
//                                    NodeList addressChildNodes = locationChildNodes.item(n).getChildNodes();
//                                    for(int o=0;o< addressChildNodes.getLength();o++){
//                                        if(addressChildNodes.item(o).getNodeName().compareTo("ParsedAddress") == 0){
//                                            NodeList parsedAddChildNodes = addressChildNodes.item(o).getChildNodes();
//                                            int zoneNum=0;
//                                            for (int oo =0;oo < parsedAddChildNodes.getLength();oo++){
//                                                System.out.println(parsedAddChildNodes.item(oo).getNodeName());
//                                                if(parsedAddChildNodes.item(oo).getNodeName().compareTo("ParsedStreetAddress")==0){
//                                                    NodeList parsedStreetChildNodes = parsedAddChildNodes.item(oo).getChildNodes();
//                                                    for(int ooo =0;ooo<parsedStreetChildNodes.getLength();ooo++){
//                                                        if(parsedStreetChildNodes.item(ooo).getNodeName().compareTo("ParsedStreetName") == 0){
//                                                            NodeList parsedStreetNameChildNodes = parsedStreetChildNodes.item(ooo).getChildNodes();
//                                                            for(int p=0;p< parsedStreetNameChildNodes.getLength();p++){
//                                                                if(parsedStreetNameChildNodes.item(p).getNodeName().compareTo("StreetName") == 0){
//                                                                    hereThaPoi.setStreetnameTha(parsedStreetNameChildNodes.item(p).getTextContent());
//                                                                }
//                                                                if(parsedStreetNameChildNodes.item(p).getNodeName().compareTo("StreetType") == 0){
//                                                                    hereThaPoi.setStreettypeTha(parsedStreetNameChildNodes.item(p).getTextContent());
//                                                                }
//                                                                if(parsedStreetNameChildNodes.item(p).getNodeName().compareTo("Trans_ParsedStreetName") == 0){
//                                                                    NodeList transChildNodes = parsedStreetNameChildNodes.item(p).getChildNodes();
//                                                                    for(int q=0;q< transChildNodes.getLength();q++){
//                                                                        if(transChildNodes.item(q).getNodeName().compareTo("StreetName")==0){
//                                                                            hereThaPoi.setStreetnameThe(transChildNodes.item(q).getTextContent());
//                                                                        }
//                                                                        if(transChildNodes.item(q).getNodeName().compareTo("StreetType")==0){
//                                                                            hereThaPoi.setStreettypeThe(transChildNodes.item(q).getTextContent());
//                                                                        }
//                                                                    }
//                                                                }
//                                                            }
//                                                        }
//                                                    }
//                                                }
//                                                if(parsedAddChildNodes.item(oo).getNodeName().compareTo("ParsedPlace")==0){
//                                                    NodeList parsedPlaceChildNodes = parsedAddChildNodes.item(oo).getChildNodes();
//                                                    int p2Num=0,p3Num=0,p4Num=0;
//                                                    for(int r=0;r<parsedPlaceChildNodes.getLength();r++){
//                                                        if(parsedPlaceChildNodes.item(r).getNodeName().compareTo("PlaceLevel2")==0){
//                                                            if(p2Num == 0){
//                                                                hereThaPoi.setPlacelevel2Tha(parsedPlaceChildNodes.item(r).getTextContent());
//                                                                p2Num++;
//                                                            }
//                                                            if(p2Num == 1){
//                                                                hereThaPoi.setPlacelevel2The(parsedPlaceChildNodes.item(r).getTextContent());
//                                                            }
//                                                        }
//                                                        if(parsedPlaceChildNodes.item(r).getNodeName().compareTo("PlaceLevel3")==0){
//                                                            if(p3Num == 0){
//                                                                hereThaPoi.setPlacelevel3Tha(parsedPlaceChildNodes.item(r).getTextContent());
//                                                                p3Num++;
//                                                            }
//                                                            if(p3Num == 1){
//                                                                hereThaPoi.setPlacelevel3The(parsedPlaceChildNodes.item(r).getTextContent());
//                                                            }
//                                                        }
//                                                        if(parsedPlaceChildNodes.item(r).getNodeName().compareTo("PlaceLevel4")==0){
//                                                            if(p4Num == 0){
//                                                                hereThaPoi.setPlacelevel4Tha(parsedPlaceChildNodes.item(r).getTextContent());
//                                                                p4Num++;
//                                                            }
//                                                            if(p4Num == 1){
//                                                                hereThaPoi.setPlacelevel4The(parsedPlaceChildNodes.item(r).getTextContent());
//                                                            }
//                                                        }
//                                                    }
//                                                }
//                                                if(parsedAddChildNodes.item(oo).getNodeName().compareTo("Zone")==0){
//                                                    if(zoneNum ==0){
//                                                        hereThaPoi.setZoneTha(parsedAddChildNodes.item(oo).getTextContent());
//                                                        zoneNum++;
//                                                    }
//                                                    if(zoneNum ==1){
//                                                        hereThaPoi.setZoneThe(parsedAddChildNodes.item(oo).getTextContent());
//                                                    }
//                                                }
//                                                if(parsedAddChildNodes.item(oo).getNodeName().compareTo("PostalCode")==0){
//                                                    NodeList postChildNodes = parsedAddChildNodes.item(oo).getChildNodes();
//                                                    for(int po =0;po <postChildNodes.getLength();po++){
//                                                        if(postChildNodes.item(po).getNodeName().compareTo("NT_Postal") == 0){
//                                                            hereThaPoi.setNtPostal(postChildNodes.item(po).getTextContent());
//                                                        }
//                                                    }
//                                                }
//                                                if(parsedAddChildNodes.item(oo).getNodeName().compareTo("CountryCode")==0){
//                                                    hereThaPoi.setCountryCode(parsedAddChildNodes.item(oo).getTextContent());
//                                                }
//                                            }
//
//                                        }
//
//                                    }
//                                }
//                                if(locationChildNodes.item(n).getNodeName().compareTo("GeoPosition") ==0 ){
//                                    NodeList geoChildNodes = locationChildNodes.item(n).getChildNodes();
//                                    for(int ge=0;ge<geoChildNodes.getLength();ge++){
//                                        if(geoChildNodes.item(ge).getNodeName().compareTo("Latitude") == 0){
//                                            hereThaPoi.setLatitude(Double.parseDouble(geoChildNodes.item(ge).getTextContent()));
//                                        }
//                                        if(geoChildNodes.item(ge).getNodeName().compareTo("Longitude") == 0){
//                                            hereThaPoi.setLongitude(Double.parseDouble(geoChildNodes.item(ge).getTextContent()));
//                                        }
//                                    }
//                                }
//                                if(locationChildNodes.item(n).getNodeName().compareTo("MapLinkID") ==0 ){
//                                    NodeList maplinkChildNodes = locationChildNodes.item(n).getChildNodes();
//                                    for(int ma=0;ma<maplinkChildNodes.getLength();ma++){
//                                        if(maplinkChildNodes.item(ma).getNodeName().compareTo("LinkID")==0){
//                                            hereThaPoi.setLinkID(Integer.parseInt(maplinkChildNodes.item(ma).getTextContent()));
//                                        }
//                                        if(maplinkChildNodes.item(ma).getNodeName().compareTo("Side_of_Street")==0){
//                                            hereThaPoi.setSideOfStreet(maplinkChildNodes.item(ma).getTextContent());
//                                        }
//                                        if(maplinkChildNodes.item(ma).getNodeName().compareTo("Percent_from_RefNode")==0){
//                                            hereThaPoi.setPercentFromRefnode(maplinkChildNodes.item(ma).getTextContent());
//                                        }
//                                    }
//                                }
//                                if(locationChildNodes.item(n).getNodeName().compareTo("Confidence") ==0 ){
//                                    NodeList confChildNodes = locationChildNodes.item(n).getChildNodes();
//                                    for(int co=0;co< confChildNodes.getLength();co++){
//                                        if(confChildNodes.item(co).getNodeName().compareTo("Match_Level") == 0){
//                                            hereThaPoi.setMatchLevel(confChildNodes.item(co).getTextContent());
//                                        }
//                                    }
//                                }
//
//                            }
//                        }
//                    }
//                }
//                if(childNodes.item(j).getNodeName().compareTo("Details") ==0 ){
//                    NodeList detailsChildNodes = childNodes.item(j).getChildNodes();
//                    for(int de=0;de<detailsChildNodes.getLength();de++){
//                        if(detailsChildNodes.item(de).getNodeName().compareTo("National_Importance") ==0){
//                            hereThaPoi.setNationalImportance(detailsChildNodes.item(de).getTextContent());
//                        }
//                        if(detailsChildNodes.item(de).getNodeName().compareTo("Private_Access") ==0){
//                            hereThaPoi.setPrivateAccess(detailsChildNodes.item(de).getTextContent());
//                        }
//                    }
//
//                }
//            }
//            System.out.println(hereThaPoi.toString());
//        }
    }
}
