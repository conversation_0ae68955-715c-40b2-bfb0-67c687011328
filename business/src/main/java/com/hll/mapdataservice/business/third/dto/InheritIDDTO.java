package com.hll.mapdataservice.business.third.dto;

import java.util.List;

/**
 * @Author: ares.chen
 * @Since: 2022/1/17
 */
public class InheritIDDTO {

    private Long idSource;

    private Long size;

    private List<String> ids;

    public InheritIDDTO() {
    }

    public InheritIDDTO(Long idSource, Long size) {
        this.idSource = idSource;
        this.size = size;
    }

    public InheritIDDTO(Long idSource, List<String> ids) {
        this.idSource = idSource;
        this.ids = ids;
    }

    public Long getIdSource() {
        return idSource;
    }

    public void setIdSource(Long idSource) {
        this.idSource = idSource;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public List<String> getIds() {
        return ids;
    }

    public void setIds(List<String> ids) {
        this.ids = ids;
    }

    @Override
    public String toString() {
        return "InheritIDDTO{" +
                "idSource=" + idSource +
                ", size=" + size +
                ", ids=" + ids +
                '}';
    }
}
