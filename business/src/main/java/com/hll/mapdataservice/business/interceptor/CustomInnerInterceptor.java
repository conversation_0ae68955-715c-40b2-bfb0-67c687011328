package com.hll.mapdataservice.business.interceptor;

import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import com.hll.mapdataservice.common.entity.Link;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CustomInnerInterceptor implements InnerInterceptor {
    @Override
    public void beforePrepare(StatementHandler sh, Connection connection, Integer transactionTimeout) {
        // do nothing
        MetaObject metaObject = SystemMetaObject.forObject(sh);
        MappedStatement ms = (MappedStatement) metaObject.getValue("delegate.mappedStatement");

        // check whether the table has geometry field
        boolean linkHasGeometry = false;
        boolean linkMHasGeometry = false;
        boolean linkSrcHasGeometry = false;
        boolean linkHasArea = false;
        boolean linkMHasArea = false;
        boolean linkSrcHasArea = false;
        boolean nodeHasGeomWkt = false;

        DatabaseMetaData metaData = null;
        try {
            metaData = connection.getMetaData();
            ResultSet columns = metaData.getColumns(null, null, "link", "geometry");
            if (columns.next()) {
                // 如果存在geometry字段，则不需要修改
                linkHasGeometry = true;
            }
            ResultSet columnsM = metaData.getColumns(null, null, "link_m", "geometry");
            if (columnsM.next()) {
                // 如果存在geometry字段，则不需要修改
                linkMHasGeometry = true;
            }
            ResultSet columnsSrc = metaData.getColumns(null, null, "link_25q2", "geometry");
            if (columnsSrc.next()) {
                // 如果存在geometry字段，则不需要修改
                linkSrcHasGeometry = true;
            }
            ResultSet columnsArea = metaData.getColumns(null, null, "link", "area");
            if (columnsArea.next()) {
                // 如果存在area字段，则不需要修改
                linkHasArea = true ;
            }
            ResultSet columnsMArea = metaData.getColumns(null, null, "link_m", "area");
            if (columnsMArea.next()) {
                // 如果存在area字段，则不需要修改
                linkMHasArea = true ;
            }
            ResultSet columnSrcArea = metaData.getColumns(null, null, "link_25q2", "area");
            if (columnSrcArea.next()) {
                // 如果存在area字段，则不需要修改
                linkSrcHasArea = true ;
            }
            ResultSet columnNodeWkt = metaData.getColumns(null, null, "node", "geom_wkt");
            if (columnNodeWkt.next()) {
                // 如果存在area字段，则不需要修改
                nodeHasGeomWkt = true ;
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }

//        String dbName;
//        try {
//            dbName = connection.getCatalog();
//        } catch (SQLException e) {
//            throw new RuntimeException(e);
//        }
//
//        Pattern pattern = Pattern.compile("hll_oversea_h_.*_(\\d{4}_q\\d)$");
//        Matcher matcher = pattern.matcher(dbName);
//
//        if (matcher.find()) {
//            String quarter = matcher.group(1); // 例如 2025_q1
//
//            String[] parts = quarter.split("_q");
//            int year = Integer.parseInt(parts[0]);
//            int q = Integer.parseInt(parts[1]);
//            // new version case
//            if ((year > 2025) || (2025 == year && q > 2) || "hll_oversea_h_mys_2025_q1".equals(dbName)){
//                hasGeometry = true;
//            }
//        }

        if("INSERT".equals(ms.getSqlCommandType().name())){
            // 在SQL语句执行前修改
            String originalSql = sh.getBoundSql().getSql();
            String area = MybatisPlusConfig.myTableName.get();
            String tableName = getTableName(originalSql, ms.getSqlCommandType().name(), area);
            String replaceString = tableName.replace(area, "");
            // System.out.println("beforeupdate originalSql：" + originalSql + "\narea is:" + area
            //         + "\ntableName is:" + tableName);
            if (!linkHasGeometry && originalSql.contains("geometry") && (originalSql.contains("insert into link")|| originalSql.contains("insert into node"))) {
                originalSql = originalSql.replace(",geometry", ",geom").replace(".geometry", ".geom");
            }
            if (!linkMHasGeometry && originalSql.contains("geometry") && (originalSql.contains("insert into link_m") || originalSql.contains("insert into node_m"))) {
                originalSql = originalSql.replace(",geometry", ",geom").replace(".geometry", ".geom");
            }
            String repaceSql = originalSql.replace(replaceString+".", tableName+".");
            // System.out.println("replaceSql：" + repaceSql);
            // 修改完成的sql 再设置回去
            metaObject.setValue("delegate.boundSql.sql", repaceSql);
        }
        if("SELECT".equals(ms.getSqlCommandType().name())){
            // 在SQL语句执行前修改
            String originalSql = sh.getBoundSql().getSql();
//            String area = MybatisPlusConfig.myTableName.get();
//            String tableName = getTableName(originalSql, ms.getSqlCommandType().name(), area);
//            String replaceString = tableName.replace(area, "");
//            // System.out.println("beforeupdate originalSql：" + originalSql + "\narea is:" + area
//            //         + "\ntableName is:" + tableName);
//            if (originalSql.contains("geometry") && (tableInfo.getTableName().equals("link") || tableInfo.getTableName().equals("node"))){
            if (!linkHasGeometry && originalSql.contains("geometry")
                    && (originalSql.contains(" FROM link") || originalSql.contains(" FROM node"))) {
                originalSql = originalSql.replace(",geometry,", ",geom as geometry,");
            }
            if (!linkMHasGeometry && originalSql.contains("geometry")
                    && (originalSql.contains(" FROM link_m") || originalSql.contains(" FROM node_m"))) {
                originalSql = originalSql.replace(",geometry,", ",geom as geometry,");
            }
            if (!linkSrcHasGeometry && originalSql.contains("geometry")
                    && (originalSql.contains(" FROM link_25q2") || originalSql.contains(" FROM node_25q2"))) {
                originalSql = originalSql.replace(",geometry,", ",geom as geometry,");
            }
            //area,name,nm_cho_langcd,nm_cha_langcd,nm_chf_langcd
            if (!linkHasArea && originalSql.contains(",area")
                    && (originalSql.contains(" FROM link") || originalSql.contains(" FROM node") || originalSql.contains(" FROM rule") || originalSql.contains(" FROM relation"))) {
                originalSql = originalSql.replace(",area,", ",").replace(",area", "")
                        .replace(",name,",",")
                        .replace(",nm_cho_langcd,", ",").replace(",nm_cho_langcd", "")
                        .replace(",nm_cha_langcd,", ",").replace(",nm_cha_langcd", "")
                        .replace(",nm_chf_langcd,", ",").replace(",nm_chf_langcd", "");
            }
            if (!linkMHasArea && originalSql.contains(",area")
                    && (originalSql.contains(" FROM link_m") || originalSql.contains(" FROM node_m") || originalSql.contains(" FROM rule_m") || originalSql.contains(" FROM relation_m"))) {
                originalSql = originalSql.replace(",area,", ",").replace(",area", "")
                        .replace(",name,",",")
                        .replace(",nm_cho_langcd,", ",").replace(",nm_cho_langcd", "")
                        .replace(",nm_cha_langcd,", ",").replace(",nm_cha_langcd", "")
                        .replace(",nm_chf_langcd,", ",").replace(",nm_chf_langcd", "");
            }
            if (!linkSrcHasArea && originalSql.contains(",area")
                    && (originalSql.contains(" FROM link_25q2") || originalSql.contains(" FROM node_25q2") || originalSql.contains(" FROM rule_25q2") || originalSql.contains(" FROM relation_25q2"))) {
                originalSql = originalSql.replace(",area,", ",").replace(",area", "")
                        .replace(",name,",",")
                        .replace(",nm_cho_langcd,", ",").replace(",nm_cho_langcd", "")
                        .replace(",nm_cha_langcd,", ",").replace(",nm_cha_langcd", "")
                        .replace(",nm_chf_langcd,", ",").replace(",nm_chf_langcd", "");
            }
            if (nodeHasGeomWkt && (originalSql.contains(" FROM node") || originalSql.contains(" FROM node_25q2") || originalSql.contains(" FROM node_m"))) {
                originalSql = originalSql.replace(",geomwkt,", ",geom_wkt,").replace(",geomwkt", ",geom_wkt");
            }
            // System.out.println("replaceSql：" + repaceSql);
            // 修改完成的sql 再设置回去
            metaObject.setValue("delegate.boundSql.sql", originalSql);
        }
    }
    public String getTableName(String originalSql, String commondType, String area){
        String tableName = "";
        if ("INSERT".equals(commondType) && area != null){
            tableName = originalSql.split(" ")[2];
        }
        return tableName;
    }
}
