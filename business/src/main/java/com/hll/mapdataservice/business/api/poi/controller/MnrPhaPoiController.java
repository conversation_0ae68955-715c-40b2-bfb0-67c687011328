package com.hll.mapdataservice.business.api.poi.controller;


import com.hll.mapdataservice.business.api.poi.service.MnrPhaPoiServiceImpl;
import com.hll.mapdataservice.common.entity.MnrNetwGeoLink;
import com.hll.mapdataservice.common.entity.MnrPhaPoi;
import com.hll.mapdataservice.common.mapper.MnrPhaPoiMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-19
 */
@RestController
@ResponseBody
@Api(tags ="poi")
@RequestMapping("/api/poi//mnrPhaPoi")
public class MnrPhaPoiController {
    @Resource
    private MnrPhaPoiMapper mnrPhaPoiMapper;

    @Resource
    private MnrPhaPoiServiceImpl mnrPhaPoiService;

    @GetMapping("getById")
    @ApiOperation(value = "getById")
    public MnrPhaPoi getPoiById(@RequestParam(value = "featId") String featId) {
        //return mnrNetwGeoLinkService.listByMap();
        //return mnrNetwGeoLinkMapper.selectById(UUID.fromString("00005448-**************-000000f09a71"));
        return mnrPhaPoiService.lambdaQuery().eq(MnrPhaPoi::getFeatId, UUID.fromString(featId)).list().get(0);
        //return mnrNetwGeoLinkService.getById(UUID.fromString("00005448-**************-000000f09a71"));
    }

}
