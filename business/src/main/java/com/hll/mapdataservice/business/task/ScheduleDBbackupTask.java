package com.hll.mapdataservice.business.task;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSONObject;
import com.hll.mapdataservice.business.third.DatabaseBackupService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 *
 */
@Component
@EnableScheduling
public class ScheduleDBbackupTask {

    private static final Logger logger = LoggerFactory.getLogger(ScheduleDBbackupTask.class);
    @Resource
    DatabaseBackupService databaseBackupService;

    @Value("${minio.endPoint}")
    String endPoint;

    @Value("${minio.accessKey}")
    String accessKey;

    @Value("${minio.secretKey}")
    String secretKey;

    @Value("${minio.bucketName}")
    String bucketName;

    @XxlJob("ScheduleDBbackupTaskHandler")
    public void ScheduleDBbackupTaskHandler() {
        //String version = XxlJobHelper.getJobParam();
        String dbNameString = "here_hk_q3", userName = "postgres", host = "**************", port = "15999",
                dbPath = "/usr/pgsql-12/bin/",backupPathPre = "/data/oversea/databasebackup/",
                uploadPathPre = "test/dbbackup/";
        /**
         * paramJson示例
         * { "dbName": "here_hk_q3", "userName": "postgres", "host": "**************", "port": "15999", "dbPath": "/usr/pgsql-12/bin/" }
        */
        JSONObject paramJson = JSONObject.parseObject(XxlJobHelper.getJobParam());
        if (paramJson != null) {
            logger.info("开始执行「ScheduleDBbackupTask」任务!参数：" + paramJson);
            if (paramJson.get("host") != null) {
                host = (String) paramJson.get("host");
            }
            if (paramJson.get("userName") != null) {
                userName = (String) paramJson.get("userName");
            }
            if (paramJson.get("port") != null) {
                port = (String) paramJson.get("port");
            }
            if (paramJson.get("dbPath") != null) {
                dbPath = (String) paramJson.get("dbPath");
            }
            if (paramJson.get("dbName") != null) {
                dbNameString = (String) paramJson.get("dbName");
            }
            if (paramJson.get("backupPathPre") != null) {
                backupPathPre = (String) paramJson.get("backupPathPre");
            }
            if (paramJson.get("uploadPathPre") != null) {
                uploadPathPre = (String) paramJson.get("uploadPathPre");
            }
        } else {
            logger.info("开始执行「ScheduleDBbackupTask」任务!无参数,使用默认参数");
        }
        Date d = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        if (dbNameString.split(",").length > 0) {
            String[] dbNameList = dbNameString.split(",");
            for (String dbName : dbNameList
            ) {
                String backupPath = backupPathPre + dbName + "/backup_" + sdf.format(d) + ".sql";
//RPC调用
//                String isSuccess = databaseBackupService.dbbackup(dbName, userName, host, port, backupPath, dbPath);
//                JSONObject successJson = JSONObject.parseObject(isSuccess);
//                if ((Boolean) successJson.get("data")) {
//                    logger.info("执行「ScheduleDBbackupTask」任务完成!备份文件路径为：" + backupPath);
//                } else {
//                    logger.info("执行「ScheduleDBbackupTask」任务失败!");
//                    continue;
//                }

                Boolean isSuccess = CommonUtils.backupDB(dbName, userName, host, port, backupPath, dbPath);
                if (isSuccess) {
                    logger.info("执行「ScheduleDBbackupTask」任务完成!备份文件路径为：" + backupPath);
                } else {
                    logger.info("执行「ScheduleDBbackupTask」任务失败!");
                    continue;
                }
                //String backupPath = "/Users/<USER>/Downloads/result.txt";
                logger.info("上传文件" + backupPath + "到minio");
                String objectName = uploadPathPre + dbName + "/backup_" + sdf.format(d) + ".sql";
                //RPC调用
//                String uploadSuccess = databaseBackupService.minioUpload(endPoint, accessKey, secretKey, bucketName, objectName, backupPath);
//                JSONObject uploadSuccessJson = JSONObject.parseObject(uploadSuccess);
//                if ((Boolean) uploadSuccessJson.get("data")) {
//                    logger.info("执行「ScheduleDBbackupTask」任务完成!备份文件路径为：" + backupPath);
//                } else {
//                    logger.info("执行「ScheduleDBbackupTask」任务失败!");
//                }
                Boolean uploadSuccess = CommonUtils.minioUpload(endPoint, accessKey, secretKey, bucketName, objectName, backupPath);
                if (uploadSuccess) {
                    logger.info("执行「ScheduleDBbackupTask」任务完成!备份文件路径为：" + backupPath);
                } else {
                    logger.info("执行「ScheduleDBbackupTask」任务失败!");
                }
            }
        }
    }
    //http://**************:10098/common/dbbackup?dbName=here_vnm_q3&userName=postgres&host=**************&port=15999&backupPath=/data/oversea/databasebackup/backup.sql&dbPath=/usr/pgsql-12/bin/
}