package com.hll.mapdataservice.business.api.road.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.mapper.RestrictionMapper;
import com.hll.mapdataservice.common.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

/**
 * 道路限行控制器
 *
 * <AUTHOR>
 * @Date 2022/10/9
 */
@RestController
@RequestMapping("/restriction")
@Slf4j
public class RestrictionController {

    @Resource
    RestrictionMapper restrictionMapper;

    @GetMapping("/save")
    public ResponseResult<String> saveRestriction(String srcFilePath,String country,String area){

        try {
            TimeInterval timer = DateUtil.timer();

            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }

            // 1.读取excel数据
            List<RestrictionVO> restrictionVOS = new ArrayList<>();
            EasyExcel.read(srcFilePath, RestrictionVO.class,new PageReadListener(data->{
                restrictionVOS.addAll((Collection<? extends RestrictionVO>) data);
            })).sheet().doRead();

            // 2.封装数据
            // 2.1 移除空数据
            restrictionVOS.removeIf(s -> StrUtil.isEmpty(s.getMarket()));
            List<Restriction> restrictions = new ArrayList<>();
            for (RestrictionVO restrictionVO : restrictionVOS) {
                Restriction restriction = new Restriction();
                // restriction.setId(UUID.randomUUID().toString());
                // restriction.setGroupId("");
                restriction.setMarket(restrictionVO.getMarket());
                restriction.setCity(restrictionVO.getCity());
                restriction.setCityId(restrictionVO.getCityId());
                restriction.setLinkId(restrictionVO.getHereLinkId());
                restriction.setRoadNameEn(restrictionVO.getRestrictedRoadName());
                restriction.setRoadNameMultiLang(restrictionVO.getRoadNameMultiLang());
                restriction.setGeom("SRID=4326;"+ restrictionVO.getRoadLine());
                restriction.setRestrictedOrderVehicleId(restrictionVO.getRestrictedOrderVehicleId());

                RestrictedInfo restrictedInfo = new RestrictedInfo();

                restrictedInfo.setRestrict_time(restrictionVO.getRestrictedTime());

                restrictedInfo.setWhite_list_weekday(restrictionVO.getWhiteListWeekday());
                restrictedInfo.setWhite_list_day(restrictionVO.getWhiteListDay());
                restrictedInfo.setRestricted_service_type(restrictionVO.getRestrictedServiceType());
                if (StrUtil.isNotEmpty(restrictionVO.getParams())) {
                    String[] splitParam = restrictionVO.getParams().split(";");
                    restrictedInfo.setParam(new Param(StrUtil.trim(splitParam[0]), StrUtil.trim(splitParam[1])));
                }

                restriction.setRestrictedInfo(JSONUtil.toJsonStr(restrictedInfo));
                if ("b".equals(restrictionVO.getDirection())) {
                    // 双向路，拆分成两条数据
                    Restriction restriction2 = new Restriction();
                    BeanUtil.copyProperties(restriction, restriction2);

                    // String roadLine = restrictionVO.getRoadLine();
                    // String pureRoadLine = StrUtil.unWrap(roadLine, "LINESTRING (", ")");
                    // String[] pureLines = pureRoadLine.split(",");
                    // List<String> reversePureLine = CollUtil.reverse(Arrays.asList(pureLines));
                    // String reverseLine = StrUtil.wrap(CollUtil.join(reversePureLine,","), "LINESTRING(", ")");
                    // restriction2.setGeom("SRID=4326;"+ reverseLine);

                    // 反转link_id
                    String linkId = restriction2.getLinkId();
                    String[] split = linkId.split(",");
                    List<String> linkIds = Arrays.asList(split);
                    List<String> reverse = CollUtil.reverse(linkIds);
                    restriction2.setLinkId(CollUtil.join(reverse, ","));
                    restrictions.add(restriction2);
                }
                restrictions.add(restriction);
            }

            // 3.数据入库
            // restrictionMapper.mysqlInsertOrUpdateBath(restrictions);
            restrictionMapper.insertBatch(restrictions);
            log.info("限行数据录入耗时：{}s", timer.intervalSecond());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("限行数据录入异常："+e.getMessage());
            return ResponseResult.otherInfo("500","限行数据录入异常！",false);
        }
        return ResponseResult.OK("限行数据录入成功！",true);
    }

}
