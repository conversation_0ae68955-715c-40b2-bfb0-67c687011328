package com.hll.mapdataservice.business.api.road.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.mapper.LinkEMapper;
import com.hll.mapdataservice.common.mapper.LinkMapper;
import com.hll.mapdataservice.common.mapper.ZlevelEMapper;
import com.hll.mapdataservice.common.mapper.ZlevelMapper;
import com.hll.mapdataservice.common.service.ILinkEService;
import com.hll.mapdataservice.common.service.IZlevelEService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import com.hll.mapdataservice.common.utils.StatusUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

@Slf4j
@Service
public class ZlevelEServiceImpl extends ServiceImpl<ZlevelEMapper, ZlevelE> implements IZlevelEService {

    @Resource
    private ZlevelMapper zlevelMapper;

    @Async("asyncTaskExecutor")
    public void zlevelE2zlevel(String area, String country, CountDownLatch countDownLatch, List<ZlevelE> zlevelEList) {
        try {
            log.info("area is {},country is{},db is{}", area, country, DynamicDataSourceContextHolder.peek());
            List<Zlevel> resList = new ArrayList<>();
            for (ZlevelE zlevelE : zlevelEList) {
                Zlevel zlevel = new Zlevel();
                BeanUtil.copyProperties(zlevelE, zlevel);
                zlevel.setId(Long.valueOf(zlevelE.getZlevelId()));
                zlevel.setGeometry(zlevelE.getGeometry());
                zlevel.setStatus(0);
                //差异字段对应赋值
                resList.add(zlevel);
            }

            int fieldNum = BeanUtil.beanToMap(new Zlevel()).keySet().size();
            int batchSize = 32767 / fieldNum;
            log.info("batchInsert size is {}", batchSize);
            List<List<Zlevel>> splitList = ListUtil.split(resList, batchSize);
            for (List<Zlevel> zlevels : splitList) {
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
                zlevelMapper.mysqlInsertOrUpdateBath(zlevels);
            }
        } catch (Exception e) {
            log.error("sync zlevel_e to zlevel error,detail is {}", e.getMessage());
            throw e;
        } finally {
            countDownLatch.countDown();
        }
    }
}