package com.hll.mapdataservice.business.api.road.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.hll.mapdataservice.business.third.HereRoadRouteService;
import com.hll.mapdataservice.common.entity.AcDtlMatch;
import com.hll.mapdataservice.common.entity.RoadRestrictionInfo;
import com.hll.mapdataservice.common.mapper.RoadRestrictionInfoMapper;
import com.hll.mapdataservice.common.service.IRoadRestrictionInfoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: ares.chen
 * @Since: 2022/5/23
 */
@Service
public class RoadRestrictionInfoServiceImpl extends ServiceImpl<RoadRestrictionInfoMapper, RoadRestrictionInfo> implements IRoadRestrictionInfoService {

    @Resource
    RoadRestrictionInfoMapper roadRestrictionInfoMapper;

    @Resource
    HereRoadRouteService hereRoadRouteService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int supplementSegmentIds(String filePath) {
        try {
            AtomicInteger sourceNum = new AtomicInteger();
            AtomicInteger handleNum = new AtomicInteger();
            // 1.读取excel数据
            EasyExcel.read(filePath, RoadRestrictionInfo.class, new PageReadListener<RoadRestrictionInfo>(dataList -> {
                dataList.removeIf(s -> StrUtil.isEmpty(s.getMarket()));
                sourceNum.addAndGet(dataList.size());
                handleNum.addAndGet(processSegmentIds(dataList));
            })).sheet().doRead();

            // EasyExcel.read(filePath, RoadRestrictionInfo.class, new ReadListener() {
            //             public static final int BATCH_COUNT = 30;
            //
            //             private List<RoadRestrictionInfo> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            //
            //             @Override
            //             public void invoke(Object data, AnalysisContext context) {
            //                 cachedDataList.add((RoadRestrictionInfo) data);
            //                 if (cachedDataList.size() >= BATCH_COUNT) {
            //                     saveData();
            //                     // 存储完成清理 list
            //                     cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
            //                 }
            //             }
            //
            //             @Override
            //             public void doAfterAllAnalysed(AnalysisContext context) {
            //                 saveData();
            //             }
            //
            //             private void saveData() {
            //                 processSegmentIds(cachedDataList);
            //             }
            //         }
            // ).sheet().doRead();
            return sourceNum.get() == handleNum.get() ? 1 : 0;
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    private int processSegmentIds(List<RoadRestrictionInfo> dataList) {
        for (RoadRestrictionInfo roadRestrictionInfo : dataList) {
            // 1.调用here api获取segmentId
            String roadWkt = roadRestrictionInfo.getRoadWkt();
            String[] lines = removeBracket(roadWkt).split(",");
            String radius = ";radius=50";
            // 转换经纬度
            String swapLineWkt = "";
            for (int i = 0; i < lines.length; i++) {
                swapLineWkt += reverseOrder(StrUtil.trim(lines[i]), ' ') + ",";
            }
            roadRestrictionInfo.setRoadWktSwitch("LINESTRING(" + StrUtil.removeSuffix(swapLineWkt, ",") + ")");

            // 获取首位点
            String startPoint = reverseOrder(lines[0].trim(), ',');
            String endPoint = reverseOrder(lines[lines.length - 1].trim(), ',');
            // 拼接途经点
            List<String> crossPoints = new ArrayList<>();
            for (int i = 1; i < lines.length - 1; i++) {
                crossPoints.add(reverseOrder(StrUtil.trim(lines[i]), ',') + radius);
            }

            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("apiKey", "rVqutlCN82O2LI8Hx5rwma63LWYqO38ybZfVgxRxPhs");
            paramMap.put("transportMode", "car");
            paramMap.put("origin", startPoint + radius);
            paramMap.put("destination", endPoint + radius);
            paramMap.put("spans", "segmentId");
            paramMap.put("return", "polyline,summary");
            paramMap.put("via", crossPoints);
            String callRes = hereRoadRouteService.getRoutesInfo(paramMap);
            List<String> segmentIds = getAssembledSegmentIds(callRes);
            roadRestrictionInfo.setSegmentId(CollUtil.join(segmentIds, ","));

            // 2.再次调用here api
            // if (segmentIds.size() < 250) {
            //     Map<String, Object> paramMap2 = new HashMap<>();
            //     paramMap2.put("apiKey", "rVqutlCN82O2LI8Hx5rwma63LWYqO38ybZfVgxRxPhs");
            //     paramMap2.put("transportMode", "truck");
            //     paramMap2.put("routingMode", "short");
            //     paramMap2.put("truck[grossWeight]", "5000");
            //     paramMap2.put("departureTime", processDate(roadRestrictionInfo.getCallParams()));
            //     paramMap2.put("origin", startPoint);
            //     paramMap2.put("destination", endPoint);
            //     paramMap2.put("spans", "segmentId");
            //     paramMap2.put("return", "polyline");
            //     paramMap2.put("avoid[segments]", roadRestrictionInfo.getSegmentId());
            //     String callRes2 = hereRoadRouteService.getRoutesInfo(paramMap2);
            //     List<String> segmentId2 = getAssembledSegmentIds(callRes2);
            //
            //     Collection<String> intersection1 = CollUtil.intersection(segmentIds, segmentId2);
            //     if (intersection1.size() > 0) {
            //         roadRestrictionInfo.setFinalRouteRes("N");
            //         roadRestrictionInfo.setSecondIncludeIds(CollUtil.join(intersection1, ","));
            //     } else {
            //         roadRestrictionInfo.setFinalRouteRes("Y");
            //     }
            // } else {
            //     roadRestrictionInfo.setFinalRouteRes("too many segmentIds...");
            // }

            // 3.再次调用here api
            Map<String, Object> paramMap3 = new HashMap<>();
            paramMap3.put("apiKey", "rVqutlCN82O2LI8Hx5rwma63LWYqO38ybZfVgxRxPhs");
            paramMap3.put("transportMode", "truck");
            paramMap3.put("routingMode", "short");
            paramMap3.put("truck[grossWeight]", "5000");
            paramMap3.put("departureTime", processDate(roadRestrictionInfo.getCallParams()));
            paramMap3.put("origin", startPoint);
            paramMap3.put("destination", endPoint);
            paramMap3.put("spans", "segmentId");
            paramMap3.put("return", "polyline");
            String callRes3 = hereRoadRouteService.getRoutesInfo(paramMap3);
            List<String> segmentId3 = getAssembledSegmentIds(callRes3);

            Collection<String> intersection2 = CollUtil.intersection(segmentIds, segmentId3);
            if (intersection2.size() > 0) {
                roadRestrictionInfo.setHereOriginalRouteRes("N");
                roadRestrictionInfo.setThirdIncludeIds(CollUtil.join(intersection2, ","));
            } else {
                roadRestrictionInfo.setHereOriginalRouteRes("Y");
            }
        }
        dataList.removeIf(s -> StrUtil.isEmpty(s.getMarket()));
        // return roadRestrictionInfoMapper.mysqlInsertOrUpdateBath(dataList);
        if (CollUtil.isNotEmpty(dataList)) {
            Integer insertBatch = roadRestrictionInfoMapper.insertBatch(dataList);
            return insertBatch;
        } else {
            return 0;
        }
    }

    private String reverseOrder(String point, char separator) {
        String[] points = point.split(" ");
        return points[1] + separator + points[0];
    }

    /**
     * wkt格式去除两边括号
     *
     * @param wktStr
     * @return
     */
    private String removeBracket(String wktStr) {
        String subStr = StrUtil.subBetween(wktStr, "(", ")");
        if (subStr.contains("(") || subStr.contains(")")) {
            subStr = StrUtil.removeAny(subStr, "(", ")");
        }
        return subStr;
    }

    private String processDate(String callParams) {
        String time = callParams.split(";")[0];
        time = StrUtil.removeSuffix(time, "am");
        String handleDate = DateUtil.beginOfWeek(new Date()).toString();
        String replaceStr;
        if (time.length() == 1) {
            replaceStr = "T0" + time + ":00:00";
        } else {
            replaceStr = "T" + time + ":00:00";
        }
        // 2022-05-23 00:00:00
        return StrUtil.split(handleDate, ' ').get(0) + replaceStr;
    }

    private List<String> getAssembledSegmentIds(String callRes) {
        JSONObject jsonObject = JSON.parseObject(callRes);
        JSONArray routes = jsonObject.getJSONArray("routes");
        JSONObject internalObj = routes.getJSONObject(0);
        JSONArray sections = internalObj.getJSONArray("sections");
        List<String> segmentIds = new ArrayList<>();
        for (int i = 0; i < sections.size(); i++) {
            JSONObject sectionObj = sections.getJSONObject(i);
            JSONArray spans = sectionObj.getJSONArray("spans");
            for (int j = 0; j < spans.size(); j++) {
                JSONObject internalSpan = spans.getJSONObject(j);
                String segmentId = (String) internalSpan.get("topologySegmentId");
                if (StrUtil.isNotEmpty(segmentId)) {
                    if (segmentId.startsWith("-")) {
                        segmentIds.add(StrUtil.removePrefix(segmentId, "-"));
                    }
                    if (segmentId.startsWith("+")) {
                        segmentIds.add(StrUtil.removePrefix(segmentId, "+"));
                    }
                }
            }
        }
        return segmentIds;
    }
}
