package com.hll.mapdataservice.business.api.poi.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.hll.mapdataservice.business.api.poi.service.PoiMServiceImpl;
import com.hll.mapdataservice.business.api.poi.service.PoiServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.LinkM;
import com.hll.mapdataservice.common.entity.PoiM;
import com.hll.mapdataservice.common.utils.CommonUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @Date 2025/1/2
 */
@RestController
@RequestMapping("/api/poi/release")
@Slf4j
public class PoiController {

    @Resource
    private PoiServiceImpl poiService;
    @Resource
    private PoiMServiceImpl poiMService;


    @ApiOperation("map poi_m diff column to poi")
    @GetMapping("convert2release")
    public ResponseResult<Boolean> convert2release(@RequestParam(value = "step", required = false, defaultValue = "1") int step,
                                              @RequestParam(value = "area", required = false, defaultValue = "") String area,
                                              @RequestParam(value = "country", required = false, defaultValue = "") String country) throws InterruptedException {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        TimeInterval timer = DateUtil.timer();
        // calculate loop times
        int poiCount = poiMService.count();
        int loop = poiCount % step != 0 ? (poiCount / step) + 1 : poiCount / step;
        CountDownLatch countDownLatch = new CountDownLatch(loop);
        for (int i = 0; i < loop; i++) {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            log.info("query poi_m db is:" + DynamicDataSourceContextHolder.peek());
            List<PoiM> poiMList = poiMService.lambdaQuery()
                    .orderByDesc(PoiM::getPoiId).last("limit " + step + " offset " + i * step).list();
            log.info("convert poi_m db is:" + DynamicDataSourceContextHolder.peek());
            poiService.convert2release(area, country, countDownLatch, poiMList);
        }
        countDownLatch.await();
        log.info("map poi_m diff column to poi cost time is {}s", timer.intervalSecond());
        return ResponseResult.OK(true, true);
    }
}
