package com.hll.mapdataservice.business.api.road.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.hll.mapdataservice.business.api.poi.service.MtdareaServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.third.InheritIDService;
import com.hll.mapdataservice.business.third.dto.InheritIDDTO;
import com.hll.mapdataservice.common.constant.Const;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.mapper.*;
import com.hll.mapdataservice.common.service.ILinkMService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import com.vividsolutions.jts.io.ParseException;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * Optimized version of LinkMService with better performance and memory management
 */
@Service
@Slf4j
public class OptimizedLinkMService extends ServiceImpl<LinkMMapper, LinkM> implements ILinkMService {

    @Resource
    private LinkMMapper linkMMapper;

    @Resource
    private NodeMMapper nodeMMapper;

    @Resource
    private StreetsServiceImpl streetsService;

    @Resource
    private ZlevelsServiceImpl zlevelsService;

    @Resource
    private CdmsServiceImpl cdmsService;

    @Resource
    private HerePhaAltstreetsServiceImpl herePhaAltstreetsService;

    @Resource
    private StreettransServiceImpl streettransService;

    @Resource
    private MtdareaServiceImpl mtdareaService;

    @Resource
    private MtddstServiceImpl mtddstService;

    @Resource
    private InheritIDService inheritIDService;

    /**
     * Optimized version of linkConvert that uses CompletableFuture for better async handling
     * and reduces database queries by batching and caching
     */
    @Async("optimizedAsyncTaskExecutor")
    public CompletableFuture<Void> optimizedLinkConvert(
            List<Streets> streetsList,
            Set<Integer> nodeIdSet,
            MultiValueMap<String, String> rdfCfLinkMap,
            Map<String, String> rdfCfMap,
            Map<String, List<String>> rdfNavLinkMap,
            Map<Long, Mtdarea> mtdareaCacheMap,
            Map<Long, Mtddst> mtddstCacheMap,
            List<Mtdarea> allMtdareaCacheList,
            Boolean isCompileNode,
            Boolean isCompileTransEng,
            String area,
            String country,
            CountDownLatch countDownLatch) {
        Map<Long, List<Zlevels>> zlevelsMap = null;
        Map<Long, List<Cdms>> cdmsMap = null;
        Map<Long, List<HerePhaAltstreets>> altStreetsMap = null;
        Map<Long, List<Streettrans>> streettransMap = null;

        try {
            // Log memory usage at start
            logMemoryUsage("Start of optimizedLinkConvert");

            // Set database context
            configureDatabaseContext(area, country, true);

            // Pre-load timezone and admin data to avoid repeated database queries
            // Map<Long, Mtdarea> mtdareaCache = preloadMtdareaData(streetsList);
            // Map<Long, Mtddst> mtddstCache = preloadMtddstData(mtdareaCache.values());

            log.info("Thread: {}, Processing {} streets", Thread.currentThread().getName(), streetsList.size());

            // Prepare collections for batch processing
            List<LinkM> linkList = new ArrayList<>(streetsList.size());
            List<NodeM> nodeList = new ArrayList<>();

            // Pre-fetch data to reduce database queries
            Set<Long> refNodeIds = new HashSet<>();
            Set<Long> nrefNodeIds = new HashSet<>();
            Set<Long> linkIds = new HashSet<>();
            Set<Long> featIds = new HashSet<>();

            // Collect IDs for batch fetching
            for (Streets streets : streetsList) {
                refNodeIds.add(streets.getRefInId());
                nrefNodeIds.add(streets.getNrefInId());
                linkIds.add(streets.getLinkId());
                if (streets.getFeatId() > 0) {
                    featIds.add(streets.getFeatId());
                }
            }

            // Batch fetch Zlevels data
            zlevelsMap = new HashMap<>();
            if (isCompileNode) {
                List<Zlevels> allZlevels = zlevelsService.lambdaQuery()
                        .in(Zlevels::getNodeId, CollUtil.union(refNodeIds, nrefNodeIds))
                        .list();

                // Group by nodeId for faster lookup
                for (Zlevels zlevels : allZlevels) {
                    zlevelsMap.computeIfAbsent(zlevels.getNodeId(), k -> new ArrayList<>()).add(zlevels);
                }
            }

            // Batch fetch Cdms data
            cdmsMap = new HashMap<>();
            List<Cdms> allCdms = cdmsService.lambdaQuery()
                    .in(Cdms::getLinkId, linkIds)
                    .list();

            // Group by linkId for faster lookup
            for (Cdms cdms : allCdms) {
                cdmsMap.computeIfAbsent(cdms.getLinkId(), k -> new ArrayList<>()).add(cdms);
            }

            // Batch fetch HerePhaAltstreets data
            altStreetsMap = new HashMap<>();
            List<HerePhaAltstreets> allAltStreets = herePhaAltstreetsService.lambdaQuery()
                    .in(HerePhaAltstreets::getLinkId, linkIds)
                    .list();

            // Group by linkId for faster lookup
            for (HerePhaAltstreets altStreet : allAltStreets) {
                altStreetsMap.computeIfAbsent(altStreet.getLinkId(), k -> new ArrayList<>()).add(altStreet);
            }

            // Batch fetch Streettrans data if needed
            streettransMap = new HashMap<>();
            if (isCompileTransEng && !featIds.isEmpty()) {
                List<Streettrans> allStreettrans = streettransService.lambdaQuery()
                        .in(Streettrans::getFeatureId, featIds)
                        .list();

                // Group by featureId for faster lookup
                for (Streettrans streettrans : allStreettrans) {
                    streettransMap.computeIfAbsent(streettrans.getFeatureId(), k -> new ArrayList<>()).add(streettrans);
                }
            }

            final int areaAssign = StrUtil.isNotBlank(area)
                    ? Integer.parseInt(area.replaceAll("\\D+", ""))
                    : 0;
            // Process each street
            for (Streets streets : streetsList) {
                LinkM link = convertStreetToLink(
                        streets,
                        rdfCfLinkMap,
                        rdfCfMap,
                        rdfNavLinkMap,
                        cdmsMap.getOrDefault(streets.getLinkId(), Collections.emptyList()),
                        altStreetsMap.getOrDefault(streets.getLinkId(), Collections.emptyList()),
                        streettransMap,
                        isCompileTransEng,
                        area,  // FIX: Add missing area parameter
                        mtdareaCacheMap,  // Pass preloaded cache for optimized timezone processing
                        mtddstCacheMap,    // Pass preloaded cache for optimized timezone processing
                        allMtdareaCacheList
                );

                linkList.add(link);

                // Process nodes if needed
                if (isCompileNode) {
                    processNodes(
                            streets,
                            nodeIdSet,
                            nodeList,
                            zlevelsMap.getOrDefault(streets.getRefInId(), Collections.emptyList()),
                            zlevelsMap.getOrDefault(streets.getNrefInId(), Collections.emptyList())
                    );
                    nodeList.forEach(s -> s.setArea(areaAssign));
                }
            }

            // Save links in batches
            saveLinksInBatches(linkList, country, area);

            // Save nodes in batches if needed
            if (isCompileNode && !nodeList.isEmpty()) {
                saveNodesInBatches(nodeList, country, area);
            }

            // Log memory usage after processing
            logMemoryUsage("After processing data");

        } catch (Exception e) {
            log.error("Error in optimizedLinkConvert", e);
        } finally {
            if(zlevelsMap!=null) zlevelsMap.clear();
            if(cdmsMap!=null) cdmsMap.clear();
            if(altStreetsMap!=null) altStreetsMap.clear();
            if(streettransMap!=null) streettransMap.clear();
            // Cleanup resources
            // cleanupResources();

            // Log final memory usage
            logMemoryUsage("End of optimizedLinkConvert");

            // Always count down the latch to prevent deadlocks
            countDownLatch.countDown();
        }

        return CompletableFuture.completedFuture(null);
    }

    /**
     * Convert a Streets object to a LinkM object
     * FIXED: Added missing area assignment and timezone/admin processing
     */
    private LinkM convertStreetToLink(
            Streets streets,
            MultiValueMap<String, String> rdfCfLinkMap,
            Map<String, String> rdfCfMap,
            Map<String, List<String>> rdfNavLinkMap,
            List<Cdms> cdmsList,
            List<HerePhaAltstreets> herePhaAltstreetsList,
            Map<Long, List<Streettrans>> streettransMap,
            Boolean isCompileTransEng,
            String area,
            Map<Long, Mtdarea> mtdareaCache,
            Map<Long, Mtddst> mtddstCache,
            List<Mtdarea> allMtdareaCache
    ) {

        LinkM link = new LinkM();

        final int areaAssign = StrUtil.isNotBlank(area)
                ? Integer.parseInt(area.replaceAll("\\D+", ""))
                : 0;
        link.setArea(areaAssign);

        // Set basic properties
        link.setLinkId(streets.getLinkId().toString());
        link.setHllLinkid(link.getLinkId());
        link.setHllSNid(streets.getRefInId().toString());
        link.setHllENid(streets.getNrefInId().toString());

        // Set divider properties
        link.setDivider(streets.getDivider());
        link.setDividerLeg(streets.getDividerleg());

        // Set KIND based on conditions
        setKind(link, streets, rdfNavLinkMap);

        // FIX 2: Add missing timezone and admin processing (critical business logic) - OPTIMIZED VERSION
        processTimeZoneAndAdminOptimized(link, streets, mtdareaCache, mtddstCache, allMtdareaCache);

        // Set FORMWAY based on conditions
        setFormway(link, streets, rdfCfLinkMap, rdfCfMap, rdfNavLinkMap);

        // Set direction
        setDirection(link, streets);

        // Set APP based on conditions
        setApp(link, streets, cdmsList);

        // Set TOLL
        setToll(link, streets);

        // Set MD
        link.setMd(streets.getMultidigit() != null && "Y".equals(streets.getMultidigit()) ? "1" : "0");

        // Set SPET
        link.setSpet(streets.getSpectrfig() != null && "Y".equals(streets.getSpectrfig()) ? "1" : "0");

        // Set FUNCT
        link.setFunct(streets.getFuncClass());

        // Set URBAN
        link.setUrban("Y".equals(streets.getUrban()) ? "1" : "0");

        // Set PAVE
        link.setPave("Y".equals(streets.getPaved()) ? "1" : "0");

        // Set lane information
        link.setLaneN(streets.getPhysLanes());
        link.setLaneL(streets.getToLanes());
        link.setLaneR(streets.getFromLanes());
        link.setLaneC(streets.getLaneCat());

        // Set admin areas
        link.setLAdmin(streets.getlAreaId().toString());
        link.setRAdmin(streets.getrAreaId().toString());

        // Process geometry
        processGeometry(link, streets);

        // Set length
        link.setLen(streets.getLen());

        // Set speed information
        setSpeedInfo(link, streets);

        // Set SP_CLASS
        link.setSpClass(streets.getSpeedCat());

        // Set AR_VEH
        setArVeh(link, streets);

        // Set VERIFYFLAG
        // link.setVerifyflag("Y".equals(streets.getInprocdata()) ? "2" : "0");
        if ("Y".equals(streets.getInprocdata())) {
            link.setVerifyflag("2");
        } else if ("N".equals(streets.getInprocdata())) {
            link.setVerifyflag("0");
        }

        // Process names
        if (streets.getNumStnmes() > 0) {
            processNames(link, streets, herePhaAltstreetsList, streettransMap, isCompileTransEng);
        }

        // Set NAME_TYPE
        if ("Y".equals(streets.getVanityname())) {
            link.setNameType("2");
        } else if ("Y".equals(streets.getScenicNm())) {
            link.setNameType("5");
        } else {
            link.setNameType("0");
        }

        // Set SRC_FLAG
        link.setSrcFlag("Y".equals(streets.getNameonrdsn()) ? "0" : "6");

        // Set metadata
        link.setUpDate(LocalDateTime.now());
        link.setStatus(0);
        link.setDatasource("7");
        link.setPubAccess(streets.getPubAccess());
        link.setTileId(CommonUtils.getH3IndexByCentroid(link.getGeomwkt(), 7));
        link.setTileType(Const.H3);

        return link;
    }

    /**
     * FIX 2: Add missing timezone and admin processing (critical business logic from original)
     */
    private void processTimeZoneAndAdmin(LinkM link, Streets streets) {
        // Get the areaId info of streets.lAreaId (the smallest level)
        List<Mtdarea> mtdareaList = mtdareaService.lambdaQuery().eq(Mtdarea::getAreaId, streets.getlAreaId()).list();

        // Set the timeZone and tAdmin from areacode1 to areacode5
        if (mtdareaList != null && mtdareaList.size() > 0) {
            Mtdarea mtdarea = mtdareaList.get(0);
            if (mtdarea != null) {
                // Try to set timezone and admin for each level (0-4 corresponding to areacode1-5)
                for (int i = 0; i < 5; i++) {
                    boolean isTimeZoneSet = setTimeZoneAndAdmin(link, mtdarea, i);
                    if (isTimeZoneSet) {
                        break;
                    }
                }
            }
        }
    }

    /**
     * FIX 2 continued: Helper method for timezone and admin processing
     */
    private boolean setTimeZoneAndAdmin(LinkM link, Mtdarea mtdarea, int level) {
        try {
            List<Mtdarea> list = null;

            switch (level) {
                case 0: // areacode1
                    list = mtdareaService.lambdaQuery()
                            .eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                            .eq(Mtdarea::getAdminLvl, level + 1)
                            .list();
                    break;
                case 1: // areacode1, areacode2
                    list = mtdareaService.lambdaQuery()
                            .eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                            .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2())
                            .eq(Mtdarea::getAdminLvl, level + 1)
                            .list();
                    break;
                case 2: // areacode1, areacode2, areacode3
                    list = mtdareaService.lambdaQuery()
                            .eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                            .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2())
                            .eq(Mtdarea::getAreacode3, mtdarea.getAreacode3())
                            .eq(Mtdarea::getAdminLvl, level + 1)
                            .list();
                    break;
                case 3: // areacode1, areacode2, areacode3, areacode4
                    list = mtdareaService.lambdaQuery()
                            .eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                            .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2())
                            .eq(Mtdarea::getAreacode3, mtdarea.getAreacode3())
                            .eq(Mtdarea::getAreacode4, mtdarea.getAreacode4())
                            .eq(Mtdarea::getAdminLvl, level + 1)
                            .list();
                    break;
                case 4: // areacode1, areacode2, areacode3, areacode4, areacode5
                    list = mtdareaService.lambdaQuery()
                            .eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                            .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2())
                            .eq(Mtdarea::getAreacode3, mtdarea.getAreacode3())
                            .eq(Mtdarea::getAreacode4, mtdarea.getAreacode4())
                            .eq(Mtdarea::getAreacode5, mtdarea.getAreacode5())
                            .eq(Mtdarea::getAdminLvl, level + 1)
                            .list();
                    break;
            }

            if (CollUtil.isNotEmpty(list)) {
                List<Mtddst> mtddstList = mtddstService.lambdaQuery()
                        .eq(Mtddst::getAreaId, list.get(0).getAreaId())
                        .list();

                if (CollUtil.isNotEmpty(mtddstList)) {
                    Mtddst mtddst = mtddstList.get(0);
                    if (mtddst.getTimeZone() != null) {
                        link.setTimeZone(mtddst.getTimeZone());
                        link.setTAdmin(mtddst.getAreaId().toString());
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Error setting timezone and admin for level {}: {}", level, e.getMessage());
        }

        return false;
    }

    /**
     * Pre-load Mtdarea data to avoid repeated database queries
     * Follows the established preloading pattern used by other optimized methods
     */
    private Map<Long, Mtdarea> preloadMtdareaData(List<Streets> streetsList) {
        Map<Long, Mtdarea> mtdareaCache = new HashMap<>();

        // Extract unique area IDs from streets
        Set<Long> areaIds = streetsList.stream()
                .map(Streets::getlAreaId)
                .collect(Collectors.toSet());

        if (!areaIds.isEmpty()) {
            // Batch query all Mtdarea data
            List<Mtdarea> allMtdarea = mtdareaService.lambdaQuery()
                    .in(Mtdarea::getAreaId, areaIds)
                    .list();

            // Create lookup map by area ID for fast access
            mtdareaCache = allMtdarea.stream()
                    .collect(Collectors.toMap(Mtdarea::getAreaId, mtdarea -> mtdarea, (existing, replacement) -> existing));

            log.info("Pre-loaded {} Mtdarea entries for timezone/admin processing", mtdareaCache.size());
        }

        return mtdareaCache;
    }

    /**
     * Pre-load Mtddst data to avoid repeated database queries
     * Follows the established preloading pattern used by other optimized methods
     */
    private Map<Long, Mtddst> preloadMtddstData(Collection<Mtdarea> mtdareaList) {
        Map<Long, Mtddst> mtddstCache = new HashMap<>();

        if (mtdareaList.isEmpty()) {
            return mtddstCache;
        }

        // Collect all area IDs that need timezone data lookup
        Set<Long> allAreaIds = new HashSet<>();

        for (Mtdarea mtdarea : mtdareaList) {
            // For each area level (0-4), collect potential area IDs for timezone lookup
            for (int level = 0; level < 5; level++) {
                Set<Long> levelAreaIds = getMtdareaIdsByLevel(mtdarea, level);
                allAreaIds.addAll(levelAreaIds);
            }
        }

        if (!allAreaIds.isEmpty()) {
            // Batch query all Mtddst data
            List<Mtddst> allMtddst = mtddstService.lambdaQuery()
                    .in(Mtddst::getAreaId, allAreaIds)
                    .isNotNull(Mtddst::getTimeZone)
                    .list();

            // Create lookup map by area ID for fast access
            mtddstCache = allMtddst.stream()
                    .collect(Collectors.toMap(Mtddst::getAreaId, mtddst -> mtddst, (existing, replacement) -> existing));

            log.info("Pre-loaded {} Mtddst entries for timezone/admin processing", mtddstCache.size());
        }

        return mtddstCache;
    }

    /**
     * Helper method to get area IDs by level for preloading
     */
    private Set<Long> getMtdareaIdsByLevel(Mtdarea mtdarea, int level) {
        Set<Long> areaIds = new HashSet<>();

        try {
            List<Mtdarea> list = null;

            switch (level) {
                case 0: // areacode1
                    list = mtdareaService.lambdaQuery()
                            .eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                            .eq(Mtdarea::getAdminLvl, level + 1)
                            .list();
                    break;
                case 1: // areacode1, areacode2
                    list = mtdareaService.lambdaQuery()
                            .eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                            .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2())
                            .eq(Mtdarea::getAdminLvl, level + 1)
                            .list();
                    break;
                case 2: // areacode1, areacode2, areacode3
                    list = mtdareaService.lambdaQuery()
                            .eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                            .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2())
                            .eq(Mtdarea::getAreacode3, mtdarea.getAreacode3())
                            .eq(Mtdarea::getAdminLvl, level + 1)
                            .list();
                    break;
                case 3: // areacode1, areacode2, areacode3, areacode4
                    list = mtdareaService.lambdaQuery()
                            .eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                            .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2())
                            .eq(Mtdarea::getAreacode3, mtdarea.getAreacode3())
                            .eq(Mtdarea::getAreacode4, mtdarea.getAreacode4())
                            .eq(Mtdarea::getAdminLvl, level + 1)
                            .list();
                    break;
                case 4: // areacode1, areacode2, areacode3, areacode4, areacode5
                    list = mtdareaService.lambdaQuery()
                            .eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                            .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2())
                            .eq(Mtdarea::getAreacode3, mtdarea.getAreacode3())
                            .eq(Mtdarea::getAreacode4, mtdarea.getAreacode4())
                            .eq(Mtdarea::getAreacode5, mtdarea.getAreacode5())
                            .eq(Mtdarea::getAdminLvl, level + 1)
                            .list();
                    break;
            }

            if (CollUtil.isNotEmpty(list)) {
                areaIds.addAll(list.stream().map(Mtdarea::getAreaId).collect(Collectors.toSet()));
            }
        } catch (Exception e) {
            log.warn("Error getting area IDs for level {}: {}", level, e.getMessage());
        }

        return areaIds;
    }

    /**
     * Optimized version of processTimeZoneAndAdmin using preloaded cache data
     * Follows the established preloading pattern used by other optimized methods
     */
    private void processTimeZoneAndAdminOptimized(LinkM link, Streets streets,
                                                  Map<Long, Mtdarea> mtdareaCache,
                                                  Map<Long, Mtddst> mtddstCache,
                                                  List<Mtdarea> allMtdareaCache) {
        // Get the areaId info of streets.lAreaId (the smallest level) from cache
        Mtdarea mtdarea = mtdareaCache.get(streets.getlAreaId());

        // Set the timeZone and tAdmin from areacode1 to areacode5
        if (mtdarea != null) {
            // Try to set timezone and admin for each level (0-4 corresponding to areacode1-5)
            for (int i = 0; i < 5; i++) {
                boolean isTimeZoneSet = setTimeZoneAndAdminOptimized(link, mtdarea, i, mtddstCache, allMtdareaCache);
                if (isTimeZoneSet) {
                    break;
                }
            }
        } else {
            log.warn("Cannot find Mtdarea for area ID: {},street link id : {}", streets.getlAreaId(),streets.getLinkId());
        }
    }

    /**
     * Optimized helper method for timezone and admin processing using preloaded cache data
     * Maintains 100% functional parity with the original setTimeZoneAndAdmin method
     */
    private boolean setTimeZoneAndAdminOptimized(LinkM link, Mtdarea mtdarea, int level,
                                                 Map<Long, Mtddst> mtddstCache, List<Mtdarea> allMtdareaCache) {
        try {
            List<Mtdarea> list = null;

            switch (level) {
                case 0: // areacode1
                    // list = mtdareaService.lambdaQuery()
                    //         .eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                    //         .eq(Mtdarea::getAdminLvl, level + 1)
                    //         .list();
                    list = allMtdareaCache.stream().filter(s -> Objects.equals(s.getAreacode1(), mtdarea.getAreacode1())
                            && s.getAdminLvl() == level + 1).collect(Collectors.toList());
                    break;
                case 1: // areacode1, areacode2
                    // list = mtdareaService.lambdaQuery()
                    //         .eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                    //         .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2())
                    //         .eq(Mtdarea::getAdminLvl, level + 1)
                    //         .list();
                    list = allMtdareaCache.stream().filter(s -> Objects.equals(s.getAreacode1(), mtdarea.getAreacode1())
                            && Objects.equals(s.getAreacode2(), mtdarea.getAreacode2())
                            && s.getAdminLvl() == level + 1).collect(Collectors.toList());
                    break;
                case 2: // areacode1, areacode2, areacode3
                    // list = mtdareaService.lambdaQuery()
                    //         .eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                    //         .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2())
                    //         .eq(Mtdarea::getAreacode3, mtdarea.getAreacode3())
                    //         .eq(Mtdarea::getAdminLvl, level + 1)
                    //         .list();
                    list = allMtdareaCache.stream().filter(s -> Objects.equals(s.getAreacode1(), mtdarea.getAreacode1())
                            && Objects.equals(s.getAreacode2(), mtdarea.getAreacode2())
                            && Objects.equals(s.getAreacode3(), mtdarea.getAreacode3())
                            && s.getAdminLvl() == level + 1).collect(Collectors.toList());
                    break;
                case 3: // areacode1, areacode2, areacode3, areacode4
                    // list = mtdareaService.lambdaQuery()
                    //         .eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                    //         .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2())
                    //         .eq(Mtdarea::getAreacode3, mtdarea.getAreacode3())
                    //         .eq(Mtdarea::getAreacode4, mtdarea.getAreacode4())
                    //         .eq(Mtdarea::getAdminLvl, level + 1)
                    //         .list();
                    list = allMtdareaCache.stream().filter(s -> Objects.equals(s.getAreacode1(), mtdarea.getAreacode1())
                            && Objects.equals(s.getAreacode2(), mtdarea.getAreacode2())
                            && Objects.equals(s.getAreacode3(), mtdarea.getAreacode3())
                            && Objects.equals(s.getAreacode4(), mtdarea.getAreacode4())
                            && s.getAdminLvl() == level + 1).collect(Collectors.toList());
                    break;
                case 4: // areacode1, areacode2, areacode3, areacode4, areacode5
                    // list = mtdareaService.lambdaQuery()
                    //         .eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                    //         .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2())
                    //         .eq(Mtdarea::getAreacode3, mtdarea.getAreacode3())
                    //         .eq(Mtdarea::getAreacode4, mtdarea.getAreacode4())
                    //         .eq(Mtdarea::getAreacode5, mtdarea.getAreacode5())
                    //         .eq(Mtdarea::getAdminLvl, level + 1)
                    //         .list();
                    list = allMtdareaCache.stream().filter(s -> Objects.equals(s.getAreacode1(), mtdarea.getAreacode1())
                            && Objects.equals(s.getAreacode2(), mtdarea.getAreacode2())
                            && Objects.equals(s.getAreacode3(), mtdarea.getAreacode3())
                            && Objects.equals(s.getAreacode4(), mtdarea.getAreacode4())
                            && Objects.equals(s.getAreacode5(), mtdarea.getAreacode5())
                            && s.getAdminLvl() == level + 1).collect(Collectors.toList());
                    break;
            }

            if (CollUtil.isNotEmpty(list)) {
                // Use preloaded cache instead of database query
                Mtddst mtddst = mtddstCache.get(list.get(0).getAreaId());
                // log.info("Level: {}", level);
                // log.info("Level: {}", level);
                // log.info("Mtdarea: {}", mtdarea);
                // log.info("MtdareaList: {}", list);
                // log.info("Mtddst: {}", mtddst);

                if (mtddst != null && mtddst.getTimeZone() != null) {
                    link.setTimeZone(mtddst.getTimeZone());
                    link.setTAdmin(mtddst.getAreaId().toString());
                    return true;
                }
            }
        } catch (Exception e) {
            log.info("Mtdarea:{}",mtdarea);
            log.info("allMtdareaCache size is:{}",allMtdareaCache.size());
            log.info("mtddstCache size is:{}",mtddstCache.size());
            log.warn("Error setting timezone and admin for level {}", level, e);
        }

        return false;
    }

    /**
     * Set the KIND property based on conditions
     */
    private void setKind(LinkM link, Streets streets, Map<String, List<String>> rdfNavLinkMap) {
        String limitedAccess = Optional.ofNullable(rdfNavLinkMap.get(streets.getLinkId().toString()))
                .map(navLink -> navLink.get(0))
                .orElse("");

        if ("Y".equals(streets.getContracc()) || "Y".equals(limitedAccess)) {
            link.setKind("1");
        } else if ("B".equals(streets.getFerryType()) && "N".equals(streets.getArAuto()) && "N".equals(streets.getArBus())
                && "N".equals(streets.getArTaxis()) && "N".equals(streets.getArCarpool())
                && "N".equals(streets.getArTrucks()) && "N".equals(streets.getArDeliv())
                && "N".equals(streets.getArEmerveh()) && "N".equals(streets.getArMotor())
                && "Y".equals(streets.getArPedest())) {
            link.setKind("11");
        } else if ("B".equals(streets.getFerryType())) {
            link.setKind("13");
        } else if ("N".equals(streets.getArAuto()) && "N".equals(streets.getArBus())
                && "N".equals(streets.getArTaxis()) && "N".equals(streets.getArCarpool())
                && "N".equals(streets.getArTrucks()) && "N".equals(streets.getArDeliv())
                && "N".equals(streets.getArEmerveh()) && "N".equals(streets.getArMotor())
                && "Y".equals(streets.getArPedest())) {
            link.setKind("10");
        } else {
            link.setKind("8");
        }
    }

    /**
     * Set the FORMWAY property based on conditions
     */
    private void setFormway(LinkM link, Streets streets, MultiValueMap<String, String> rdfCfLinkMap,
                            Map<String, String> rdfCfMap, Map<String, List<String>> rdfNavLinkMap) {
        StringBuilder formWay = new StringBuilder();

        if ("Y".equals(streets.getInprocdata())) {
            formWay.append(",0");
        }
        if ("Y".equals(streets.getRamp()) && "Y".equals(streets.getContracc())) {
            formWay.append(",11");
        }
        if ("Y".equals(streets.getRamp()) && "N".equals(streets.getContracc())) {
            formWay.append(",15");
        }
        if ("Y".equals(streets.getContracc()) && "N".equals(streets.getRamp())) {
            formWay.append(",14");
        }

        String navLink = Optional.ofNullable(rdfNavLinkMap.get(streets.getLinkId().toString()))
                .map(tmpNavLink -> tmpNavLink.get(1))
                .orElse("");

        if ("1".equals(navLink)) {
            formWay.append(",16");
        }
        if ("2".equals(navLink)) {
            formWay.append(",17");
        }
        if ("Y".equals(streets.getPrivateInfo())) {
            formWay.append(",18");
        }
        if ("N".equals(streets.getArAuto()) && "N".equals(streets.getArBus())
                && "N".equals(streets.getArTaxis()) && "N".equals(streets.getArCarpool())
                && "N".equals(streets.getArTrucks()) && "N".equals(streets.getArDeliv())
                && "N".equals(streets.getArEmerveh()) && "N".equals(streets.getArMotor())
                && "Y".equals(streets.getArPedest())) {
            formWay.append(",20");
        }
        if ("N".equals(streets.getArAuto()) && "Y".equals(streets.getArBus())
                && "N".equals(streets.getArTaxis()) && "N".equals(streets.getArCarpool())
                && "N".equals(streets.getArTrucks()) && "N".equals(streets.getArDeliv())
                && "N".equals(streets.getArMotor()) && "N".equals(streets.getArPedest())) {
            formWay.append(",22");
        }
        if ("Y".equals(streets.getBridge())) {
            formWay.append(",30");
        }
        if ("Y".equals(streets.getTunnel())) {
            formWay.append(",31");
        }
        if ("Y".equals(streets.getRoundabout())) {
            formWay.append(",33");
        }
        if ("Y".equals(streets.getFrontage())) {
            formWay.append(",34");
        }
        if ("Y".equals(streets.getManoeuvre())) {
            formWay.append(",90");
        }
        if ("Y".equals(streets.getPoiaccess())) {
            formWay.append(",36");
        }
        if ("Y".equals(streets.getScenicRt())) {
            formWay.append(",60");
        }
        if ("Y".equals(streets.getMultidigit())) {
            formWay.append(",81");
        }

        // Process formway=50
        if (rdfCfLinkMap.get(link.getLinkId()) != null && !rdfCfLinkMap.get(link.getLinkId()).isEmpty()) {
            List<String> cfids = rdfCfLinkMap.get(link.getLinkId());
            for (String cfid : cfids) {
                if (rdfCfMap.get(cfid) != null && !rdfCfMap.get(cfid).isEmpty()) {
                    formWay.append(",50");
                    break;
                }
            }
        }

        if ("N".equals(streets.getPubAccess())) {
            formWay.append(",52");
        }
        if ("N".equals(streets.getArTraff())) {
            formWay.append(",55");
        }

        String forwayReplace = formWay.length() > 0 ? formWay.substring(1) : "";
        link.setFormway(forwayReplace.isEmpty() ? "1" : forwayReplace);
    }

    /**
     * Set the direction property
     */
    private void setDirection(LinkM link, Streets streets) {
        switch (streets.getDirTravel()) {
            case "B":
                link.setDir("1");
                break;
            case "F":
                link.setDir("2");
                break;
            case "T":
                link.setDir("3");
                break;
            case "N":
                link.setDir("4");
                break;
            default:
                link.setDir("0");
        }
    }

    /**
     * Set the APP property based on conditions
     */
    private void setApp(LinkM link, Streets streets, List<Cdms> cdmsList) {
        if (!cdmsList.isEmpty() && cdmsList.stream().map(Cdms::getCondType).collect(Collectors.toList()).contains(3)) {
            link.setApp("4");
        } else if ("Y".equals(streets.getArAuto()) || "Y".equals(streets.getArBus())
                || "Y".equals(streets.getArTaxis()) || "Y".equals(streets.getArCarpool())
                || "Y".equals(streets.getArTrucks()) || "Y".equals(streets.getArDeliv())
                || "Y".equals(streets.getArEmerveh()) || "Y".equals(streets.getArMotor())
                || "Y".equals(streets.getArPedest())) {
            link.setApp("1");
        } else if ("N".equals(streets.getArAuto()) && "N".equals(streets.getArBus())
                && "N".equals(streets.getArTaxis()) && "N".equals(streets.getArCarpool())
                && "N".equals(streets.getArTrucks()) && "N".equals(streets.getArDeliv())
                && "N".equals(streets.getArEmerveh()) && "N".equals(streets.getArMotor())
                && "N".equals(streets.getArPedest())) {
            link.setApp("2");
        }
    }

    /**
     * Set the TOLL property
     */
    private void setToll(LinkM link, Streets streets) {
        if ("Y".equals(streets.getTollway())) {
            link.setToll("1");
        } else if ("N".equals(streets.getTollway())) {
            link.setToll("2");
        } else {
            link.setToll("0");
        }
    }

    /**
     * Process geometry data
     */
    private void processGeometry(LinkM link, Streets streets) {
        String srcLineGeom = streets.getGeometry();
        String resLineGeom = StrUtil.subBetween(srcLineGeom, "((", "))");
        link.setGeomwkt(StrUtil.wrap(resLineGeom, "LINESTRING(", ")"));
        link.setGeometry("SRID=4326;" + link.getGeomwkt());
    }

    /**
     * Set speed information
     */
    private void setSpeedInfo(LinkM link, Streets streets) {
        // F_SPEED
        String fSpeed = streets.getFrSpdLim().toString();
        if ("0".equals(fSpeed)) {
            link.setFSpeed(null);
        } else {
            StringBuilder speedBuilder = new StringBuilder(fSpeed);
            if ("1".equals(streets.getSpdLmSrc())) {
                speedBuilder.append(",1,");
            } else if ("2".equals(streets.getSpdLmSrc())) {
                speedBuilder.append(",5,");
            } else if ("".equals(streets.getSpdLmSrc())) {
                speedBuilder.append(",0,");
            }
            speedBuilder.append("1");
            link.setFSpeed(speedBuilder.toString());
        }

        // T_SPEED
        String tSpeed = streets.getToSpdLim().toString();
        if ("0".equals(tSpeed)) {
            link.setTSpeed(null);
        } else {
            StringBuilder speedBuilder = new StringBuilder(tSpeed);
            if ("1".equals(streets.getSpdLmSrc())) {
                speedBuilder.append(",1,");
            } else if ("2".equals(streets.getSpdLmSrc())) {
                speedBuilder.append(",5,");
            } else if ("".equals(streets.getSpdLmSrc())) {
                speedBuilder.append(",0,");
            }
            speedBuilder.append("1");
            link.setTSpeed(speedBuilder.toString());
        }
    }

    /**
     * Set AR_VEH property
     */
    private void setArVeh(LinkM link, Streets streets) {
        char[] vehValue = new char[35];
        Arrays.fill(vehValue, '0');

        if ("Y".equals(streets.getArAuto())) vehValue[0] = '1';
        if ("Y".equals(streets.getArTrucks())) vehValue[2] = '1';
        if ("Y".equals(streets.getArPedest())) vehValue[3] = '1';
        if ("Y".equals(streets.getArMotor())) vehValue[5] = '1';
        if ("Y".equals(streets.getArEmerveh())) vehValue[7] = '1';
        if ("Y".equals(streets.getArTaxis())) vehValue[8] = '1';
        if ("Y".equals(streets.getArBus())) vehValue[9] = '1';
        if ("Y".equals(streets.getArCarpool())) vehValue[13] = '1';
        if ("Y".equals(streets.getArDeliv())) vehValue[26] = '1';

        link.setArVeh(String.valueOf(vehValue));
    }

    /**
     * Process street names
     */
    private void processNames(LinkM link, Streets streets, List<HerePhaAltstreets> herePhaAltstreetsList,
                              Map<Long, List<Streettrans>> streettransMap, Boolean isCompileTransEng) {
        // Process Chinese names
        processChineseNames(link, streets, herePhaAltstreetsList);

        // Process English names if needed
        if (isCompileTransEng && streets.getFeatId() > 0) {
            processEnglishNames(link, streets, herePhaAltstreetsList, streettransMap);
        }
    }

    /**
     * Process Chinese names
     */
    private void processChineseNames(LinkM link, Streets streets, List<HerePhaAltstreets> herePhaAltstreetsList) {
        // Process NAME_CH_O (official name)
        if (streets.getNumStnmes() == 1) {
            link.setNameChO(streets.getStName());
            link.setNmChoLangcd(streets.getStLangcd());
            if("Y".equals(streets.getVanityname())){
                link.setNameChA(streets.getStName());
                link.setNmChaLangcd(streets.getStLangcd());
            }
            if("Y".equals(streets.getStalename())){
                link.setNameChF(streets.getStName());
                link.setNmChfLangcd(streets.getStLangcd());
            }
        } else if (streets.getNumStnmes() > 1) {
            processMultipleChineseNames(link, streets, herePhaAltstreetsList);
        }
    }

    /**
     * Process multiple Chinese names
     */
    private void processMultipleChineseNames(LinkM link, Streets streets, List<HerePhaAltstreets> herePhaAltstreetsList) {
        // Official names
        StringBuilder nameCho = new StringBuilder();
        StringBuilder nmChoLangcd = new StringBuilder();
        JSONArray nameChoArray = new JSONArray();

        if ("N".equals(streets.getStalename())) {
            nameCho.append(streets.getStName());
            nmChoLangcd.append(streets.getStLangcd());
        }

        for (HerePhaAltstreets altStreet : herePhaAltstreetsList) {
            if ("N".equals(altStreet.getStalename())) {
                if (nameCho.length() > 0) {
                    nameCho.append("|");
                    nmChoLangcd.append("|");
                }
                nameCho.append(altStreet.getStName());
                nmChoLangcd.append(altStreet.getStLangcd());
            }
        }

        if (nameCho.length() > 0) {
            link.setNameChO(nameCho.toString());
            link.setNmChoLangcd(nmChoLangcd.toString());
            nameChoArray = nameConverterList(nameCho.toString(), nmChoLangcd.toString());
        }

        // Alternate names
        StringBuilder nameCha = new StringBuilder();
        StringBuilder nmChaLangcd = new StringBuilder();
        JSONArray nameChaArray = new JSONArray();

        if ("Y".equals(streets.getVanityname())) {
            nameCha.append(streets.getStName());
            nmChaLangcd.append(streets.getStLangcd());
        }

        for (HerePhaAltstreets altStreet : herePhaAltstreetsList) {
            if ("Y".equals(altStreet.getVanityname())) {
                if (nameCha.length() > 0) {
                    nameCha.append("|");
                    nmChaLangcd.append("|");
                }
                nameCha.append(altStreet.getStName());
                nmChaLangcd.append(altStreet.getStLangcd());
            }
        }

        if (nameCha.length() > 0) {
            link.setNameChA(nameCha.toString());
            link.setNmChaLangcd(nmChaLangcd.toString());
            nameChaArray = nameConverterList(nameCha.toString(), nmChaLangcd.toString());
        }

        // Former names
        StringBuilder nameChf = new StringBuilder();
        StringBuilder nmChfLangcd = new StringBuilder();
        JSONArray nameChfArray = new JSONArray();

        if ("Y".equals(streets.getStalename())) {
            nameChf.append(streets.getStName());
            nmChfLangcd.append(streets.getStLangcd());
        }

        for (HerePhaAltstreets altStreet : herePhaAltstreetsList) {
            if ("Y".equals(altStreet.getStalename())) {
                if (nameChf.length() > 0) {
                    nameChf.append("|");
                    nmChfLangcd.append("|");
                }
                nameChf.append(altStreet.getStName());
                nmChfLangcd.append(altStreet.getStLangcd());
            }
        }

        if (nameChf.length() > 0) {
            link.setNameChF(nameChf.toString());
            link.setNmChfLangcd(nmChfLangcd.toString());
            nameChfArray = nameConverterList(nameChf.toString(), nmChfLangcd.toString());
        }

        // Combine all names into a JSON object
        JSONObject nameJson = new JSONObject();
        nameJson.put("nameChO", nameChoArray);
        nameJson.put("nameChA", nameChaArray);
        nameJson.put("nameChF", nameChfArray);
        link.setName(nameJson.toMap());
    }

    /**
     * Process English names
     */
    private void processEnglishNames(LinkM link, Streets streets, List<HerePhaAltstreets> herePhaAltstreetsList,
                                     Map<Long, List<Streettrans>> streettransMap) {
        List<Streettrans> streettransList = streettransMap.getOrDefault(streets.getFeatId(), Collections.emptyList());

        if (streettransList.isEmpty()) {
            log.info("Cannot find the featureid, linkId is: {}, featureid is: {}", streets.getLinkId(), streets.getFeatId());
            return;
        }

        // Process NAME_EN_O (official name)
        if (streets.getNumStnmes() == 1) {
            link.setNameEnO(streettransList.get(0).getStNameTr());
            if("Y".equals(streets.getVanityname())){
                link.setNameEnA(streettransList.get(0).getStNameTr());
            }
            if("Y".equals(streets.getStalename())){
                link.setNameEnF(streettransList.get(0).getStNameTr());
            }
        } else if (streets.getNumStnmes() > 1) {
            processMultipleEnglishNames(link, streets, herePhaAltstreetsList, streettransList, streettransMap);
        }
    }

    /**
     * Process multiple English names
     */
    private void processMultipleEnglishNames(LinkM link, Streets streets, List<HerePhaAltstreets> herePhaAltstreetsList,
                                             List<Streettrans> streettransList, Map<Long, List<Streettrans>> streettransMap) {
        // Official names
        StringBuilder nameEno = new StringBuilder();

        if ("N".equals(streets.getStalename())) {
            nameEno.append(streettransList.get(0).getStNameTr());
            for (HerePhaAltstreets altStreet : herePhaAltstreetsList) {
                if ("N".equals(altStreet.getStalename())) {
                    List<Streettrans> altStreettransList = streettransMap.getOrDefault(altStreet.getFeatId(), Collections.emptyList());
                    if (!altStreettransList.isEmpty()) {
                        if (nameEno.length() > 0) {
                            nameEno.append("|");
                        }
                        nameEno.append(altStreettransList.get(0).getStNameTr());
                    }
                }
            }
        } else {
            for (HerePhaAltstreets altStreet : herePhaAltstreetsList) {
                List<Streettrans> altStreettransList = streettransMap.getOrDefault(altStreet.getFeatId(), Collections.emptyList());
                if (!altStreettransList.isEmpty()) {
                    if (nameEno.length() > 0) {
                        nameEno.append("|");
                    }
                    nameEno.append(altStreettransList.get(0).getStNameTr());
                }
            }
        }


        if (nameEno.length() > 0) {
            link.setNameEnO(nameEno.toString());
        }

        // Alternate names
        StringBuilder nameEha = new StringBuilder();

        if ("Y".equals(streets.getVanityname())) {
            nameEha.append(streettransList.get(0).getStNameTr());
        }

        for (HerePhaAltstreets altStreet : herePhaAltstreetsList) {
            if ("Y".equals(altStreet.getVanityname())) {
                List<Streettrans> altStreettransList = streettransMap.getOrDefault(altStreet.getFeatId(), Collections.emptyList());
                if (!altStreettransList.isEmpty()) {
                    if (nameEha.length() > 0) {
                        nameEha.append("|");
                    }
                    nameEha.append(altStreettransList.get(0).getStNameTr());
                }
            }
        }

        if (nameEha.length() > 0) {
            link.setNameEnA(nameEha.toString());
        }

        // Former names
        StringBuilder nameEhf = new StringBuilder();

        if ("Y".equals(streets.getStalename())) {
            nameEhf.append(streettransList.get(0).getStNameTr());
        }

        for (HerePhaAltstreets altStreet : herePhaAltstreetsList) {
            if ("Y".equals(altStreet.getStalename())) {
                List<Streettrans> altStreettransList = streettransMap.getOrDefault(altStreet.getFeatId(), Collections.emptyList());
                if (!altStreettransList.isEmpty()) {
                    if (nameEhf.length() > 0) {
                        nameEhf.append("|");
                    }
                    nameEhf.append(altStreettransList.get(0).getStNameTr());
                }
            }
        }

        if (nameEhf.length() > 0) {
            link.setNameEnF(nameEhf.toString());
        }
    }

    /**
     * Process nodes
     */
    private void processNodes(Streets streets, Set<Integer> nodeIdSet, List<NodeM> nodeList,
                              List<Zlevels> refZlevels, List<Zlevels> nrefZlevels) {
        // Process ref node
        if (!refZlevels.isEmpty() && !nodeIdSet.contains(streets.getRefInId().intValue())) {
            NodeM node = new NodeM();
            node.setNodeId(streets.getRefInId().toString());
            node.setHllNodeid(node.getNodeId());
            node.setGeometry(refZlevels.get(0).getGeom());
            node.setGeomwkt(refZlevels.get(0).getGeomwkt());
            node.setUpDate(LocalDateTime.now());
            node.setDatasource("7");
            node.setStatus(0);
            node.setTileId(CommonUtils.getH3IndexByCentroid(node.getGeomwkt(), 7));
            node.setTileType(Const.H3);

            nodeList.add(node);
            nodeIdSet.add(streets.getRefInId().intValue());
        }

        // Process nref node
        if (!nrefZlevels.isEmpty() && !nodeIdSet.contains(streets.getNrefInId().intValue())) {
            NodeM node = new NodeM();
            node.setNodeId(streets.getNrefInId().toString());
            node.setHllNodeid(node.getNodeId());
            node.setGeometry(nrefZlevels.get(0).getGeom());
            node.setGeomwkt(nrefZlevels.get(0).getGeomwkt());
            node.setUpDate(LocalDateTime.now());
            node.setDatasource("7");
            node.setStatus(0);
            node.setTileId(CommonUtils.getH3IndexByCentroid(node.getGeomwkt(), 7));
            node.setTileType(Const.H3);

            nodeList.add(node);
            nodeIdSet.add(streets.getNrefInId().intValue());
        }
    }

    /**
     * Save links in batches
     */
    private void saveLinksInBatches(List<LinkM> linkList, String country, String area) {
        if (linkList.isEmpty()) {
            return;
        }

        log.info("Saving {} links", linkList.size());

        List<List<LinkM>> linkListPartition = Lists.partition(linkList, 65535 / BeanUtil.beanToMap(new LinkM()).keySet().size());

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }

        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }

        for (List<LinkM> partitionList : linkListPartition) {
            List<Long> linkIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(LinkM::getLinkId).collect(Collectors.toList())));
            List<Long> sNodeIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(LinkM::getHllSNid).collect(Collectors.toList())));
            List<Long> eNodeIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(LinkM::getHllENid).collect(Collectors.toList())));

            for (int i = 0; i < partitionList.size(); i++) {
                partitionList.get(i).setHllLinkid(String.valueOf(linkIds.get(i)));
                partitionList.get(i).setHllSNid(String.valueOf(sNodeIds.get(i)));
                partitionList.get(i).setHllENid(String.valueOf(eNodeIds.get(i)));
            }

            linkMMapper.mysqlInsertOrUpdateBath(partitionList);
        }
    }

    /**
     * Save nodes in batches
     */
    private void saveNodesInBatches(List<NodeM> nodeList, String country, String area) {
        if (nodeList.isEmpty()) {
            return;
        }

        log.info("Saving {} nodes", nodeList.size());

        List<List<NodeM>> nodeListPartition = Lists.partition(nodeList, 65535 / BeanUtil.beanToMap(new NodeM()).keySet().size());

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }

        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }

        for (List<NodeM> partitionList : nodeListPartition) {
            List<Long> nodeIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(NodeM::getNodeId).collect(Collectors.toList())));

            for (int i = 0; i < partitionList.size(); i++) {
                partitionList.get(i).setHllNodeid(String.valueOf(nodeIds.get(i)));
            }

            nodeMMapper.mysqlInsertOrUpdateBath(partitionList);
        }
    }

    /**
     * Convert name list to JSON array
     */
    private JSONArray nameConverterList(String nameValue, String nmLangcd) {
        JSONArray nameArray = new JSONArray();

        if (StrUtil.isNotEmpty(nameValue) && StrUtil.isNotEmpty(nmLangcd)) {
            String[] nameValues = nameValue.split("\\|");
            String[] langcds = nmLangcd.split("\\|");

            for (int i = 0; i < nameValues.length && i < langcds.length; i++) {
                JSONObject nameObj = new JSONObject();
                nameObj.put("name", nameValues[i]);
                nameObj.put("langcd", langcds[i]);
                nameArray.put(nameObj);
            }
        }

        return nameArray;
    }

    /**
     * Monitor and log memory usage during processing
     */
    public void logMemoryUsage(String phase) {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();

        log.info("Memory Usage - {}: Used: {} MB, Free: {} MB, Total: {} MB, Max: {} MB",
                phase,
                usedMemory / (1024 * 1024),
                freeMemory / (1024 * 1024),
                totalMemory / (1024 * 1024),
                maxMemory / (1024 * 1024));

        // Log memory usage percentage
        double memoryUsagePercent = (double) usedMemory / maxMemory * 100;
        log.info("Memory Usage Percentage - {}: {}%", phase, String.format("%.2f", memoryUsagePercent));

        // Warn if memory usage is high
        if (memoryUsagePercent > 80) {
            log.warn("High memory usage detected: {}% - Consider garbage collection", String.format("%.2f", memoryUsagePercent));
        }
    }

    /**
     * Cleanup resources and suggest garbage collection
     */
    public void cleanupResources() {
        // Suggest garbage collection to help with memory management
        // Note: This is just a suggestion to the JVM, not a command
        System.gc();

        log.debug("Resource cleanup completed and garbage collection suggested");
    }

    /**
     * Process streets data in smaller chunks to reduce memory pressure
     */
    @Async("adaptiveAsyncTaskExecutor")
    public CompletableFuture<Void> processStreetsChunk(
            List<Streets> streetsChunk,
            Set<Integer> nodeIdSet,
            MultiValueMap<String, String> rdfCfLinkMap,
            Map<String, String> rdfCfMap,
            Map<String, List<String>> rdfNavLinkMap,
            Boolean isCompileNode,
            Boolean isCompileTransEng,
            String area,
            String country) {

        try {
            logMemoryUsage("Start of processStreetsChunk");

            // Use a dummy CountDownLatch since this is a chunk operation
            CountDownLatch dummyLatch = new CountDownLatch(1);

            // Process the chunk using the optimized method
            // optimizedLinkConvert(streetsChunk, nodeIdSet, rdfCfLinkMap, rdfCfMap, rdfNavLinkMap,
            //         isCompileNode, isCompileTransEng, area, country, dummyLatch);

            logMemoryUsage("End of processStreetsChunk");

        } catch (Exception e) {
            log.error("Error in processStreetsChunk", e);
        }

        return CompletableFuture.completedFuture(null);
    }

    /**
     * Batch process multiple chunks with memory management
     */
    public CompletableFuture<Void> batchProcessStreets(
            List<List<Streets>> streetsBatches,
            Set<Integer> nodeIdSet,
            MultiValueMap<String, String> rdfCfLinkMap,
            Map<String, String> rdfCfMap,
            Map<String, List<String>> rdfNavLinkMap,
            Boolean isCompileNode,
            Boolean isCompileTransEng,
            String area,
            String country,
            CountDownLatch countDownLatch) {

        try {
            logMemoryUsage("Start of batchProcessStreets");

            for (int i = 0; i < streetsBatches.size(); i++) {
                List<Streets> batch = streetsBatches.get(i);
                log.info("Processing batch {}/{} with {} streets", i + 1, streetsBatches.size(), batch.size());

                // Process each batch
                processStreetsChunk(batch, nodeIdSet, rdfCfLinkMap, rdfCfMap, rdfNavLinkMap,
                        isCompileNode, isCompileTransEng, area, country);

                // Cleanup after every few batches
                if (i % 3 == 0) {
                    cleanupResources();
                    logMemoryUsage("After batch " + (i + 1));
                }
            }

            logMemoryUsage("End of batchProcessStreets");

        } catch (Exception e) {
            log.error("Error in batchProcessStreets", e);
        } finally {
            countDownLatch.countDown();
        }

        return CompletableFuture.completedFuture(null);
    }

    @Override
    public void aloneRoadMerge(List<RoadMatchRes> batchList, CountDownLatch downLatch, String country, String area) throws ParseException {

    }

    @Override
    public void branchRoadMerge(List<String> resList, String country, String area, CountDownLatch downLatch) throws ParseException {

    }

    @Override
    public void branchLinkMergeOfMove(List<String> resList, String country, String area, String countryOsm, CountDownLatch downLatch) {

    }

    @Override
    public void aloneLinkMerge(List<RoadMatchRes> matchResList, CountDownLatch downLatch, String countryHere, String area, String countryOsm) {

    }

    /**
     * Configure database context for source or target operations
     */
    private void configureDatabaseContext(String area, String country, boolean isSource) {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, isSource));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
    }
}