package com.hll.mapdataservice.business.api.road.controller;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.hll.mapdataservice.business.api.road.service.LinkEServiceImpl;
import com.hll.mapdataservice.business.api.road.service.LinkMServiceImpl;
import com.hll.mapdataservice.business.api.road.service.NodeEServiceImpl;
import com.hll.mapdataservice.business.api.road.service.OptimizedNodeMService;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.LinkE;
import com.hll.mapdataservice.common.entity.LinkM;
import com.hll.mapdataservice.common.entity.NodeE;
import com.hll.mapdataservice.common.service.INodeService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@RestController
@Slf4j
@RequestMapping("/common/link-e")
public class LinkEController {


    @Resource
    LinkEServiceImpl linkEService;

    @ApiOperation("map link_e diff column to link")
    @GetMapping("linkE2link")
    public ResponseResult<Boolean> linkE2link(@RequestParam(value = "step", required = false, defaultValue = "1") int step,
                                              @RequestParam(value = "area", required = false, defaultValue = "") String area,
                                              @RequestParam(value = "country", required = false, defaultValue = "") String country) throws InterruptedException {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        TimeInterval timer = DateUtil.timer();
        // calculate loop times
        int linkCount = linkEService.lambdaQuery().ne(LinkE::getStatus, 1).count();
        int loop = linkCount % step != 0 ? (linkCount / step) + 1 : linkCount / step;
        CountDownLatch countDownLatch = new CountDownLatch(loop);
        for (int i = 0; i < loop; i++) {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            log.info("query link db is:" + DynamicDataSourceContextHolder.peek());
            List<LinkE> linkEList = linkEService.lambdaQuery().ne(LinkE::getStatus, 1)
                    .orderByDesc(LinkE::getHllLinkid).last("limit " + step + " offset " + i * step).list();
            log.info("convert link db is:" + DynamicDataSourceContextHolder.peek());
            linkEService.linkE2link(area, country, countDownLatch, linkEList);
        }
        countDownLatch.await();
        log.info("map link_e diff column to link cost time is {}s", timer.intervalSecond());
        return ResponseResult.OK(true, true);
    }

}
