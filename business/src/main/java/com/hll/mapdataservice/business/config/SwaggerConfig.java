package com.hll.mapdataservice.business.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import springfox.documentation.annotations.ApiIgnore;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.Contact;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.List;

@EnableSwagger2
@Configuration
//@Profile({"dev", "test","publish"})
public class SwaggerConfig {

    @Bean
    public Docket docketAll() {
        ParameterBuilder ticketPar = new ParameterBuilder();
        List<Parameter> pars = new ArrayList<>();
        ticketPar.name("token").description("token")
                .modelRef(new ModelRef("string")).parameterType("header")
                .required(false).build();
        pars.add(ticketPar.build());

        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(new ApiInfoBuilder()
                        .contact(new Contact("shawn2", "url", "<EMAIL>"))
                        .title("mapdataservice api")
                        .build())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.hll.mapdataservice.business"))
                .paths(PathSelectors.any())
                .build()
                .globalOperationParameters(pars)
                .ignoredParameterTypes(ApiIgnore.class)
                .enableUrlTemplating(true);
    }
}
