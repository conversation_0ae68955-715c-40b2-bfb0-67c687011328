package com.hll.mapdataservice.business.api.road.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.hll.mapdataservice.business.api.road.service.TollServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.Toll;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-06
 */
@RestController
@RequestMapping("/common/toll")
@Slf4j
public class TollController {
    @Resource
    TollServiceImpl tollService;
    @GetMapping("/getById")
    public ResponseResult<Toll> getById(@RequestParam(value = "id", required = false, defaultValue = "1") Long id,
                                        @RequestParam(value = "area", required = false, defaultValue = "") String area,
                                        @RequestParam(value = "country", required = false, defaultValue = "") String country){
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push("db20");
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        Toll tollResult = tollService.getById(id);
        JSONArray tollFee = JSON.parseArray(tollResult.getTollFee());
        Toll tollInsert = tollResult;
        tollInsert.setTollId(2L);
        tollService.save(tollInsert);
        return ResponseResult.OK(tollService.getById(id),true);
    }
}
