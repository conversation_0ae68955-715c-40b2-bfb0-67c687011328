package com.hll.mapdataservice.business.api.poi.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.entity.PoiM;
import com.hll.mapdataservice.common.entity.Poi;
import com.hll.mapdataservice.common.entity.PoiM;
import com.hll.mapdataservice.common.mapper.PoiMapper;
import com.hll.mapdataservice.common.service.IPoiService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @Date 2025/1/2
 */
@Service
@Slf4j
public class PoiServiceImpl extends ServiceImpl<PoiMapper, Poi> implements IPoiService {
    @Resource
    private PoiMapper poiMapper;

    @Async("asyncTaskExecutor")
    public void convert2release(String area, String country, CountDownLatch countDownLatch, List<PoiM> poiMList) {

        try {
            log.info("area is {},country is{},db is{}", area, country, DynamicDataSourceContextHolder.peek());
            int fieldNum = BeanUtil.beanToMap(new Poi()).keySet().size();
            int batchSize = 32767 / fieldNum;
            log.info("batchInsert size is {}", batchSize);
            List<List<PoiM>> splitList = ListUtil.split(poiMList, batchSize);
            for (List<PoiM> poiMS : splitList) {
                //Link linkRp;
                List<Poi> poiList = new ArrayList<>();
                for (PoiM poiM : poiMS) {
                    Poi poi = new Poi();
                    BeanUtil.copyProperties(poiM, poi);
                    //差异字段对应赋值
                    poiList.add(poi);
                }
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
                log.info("insert db is:" + DynamicDataSourceContextHolder.peek());
                poiMapper.mysqlInsertOrUpdateBath(poiList);
                //this.saveBatch(rpList);

            }
        } catch (Exception e) {
            log.error("sync to link_rp error,detail is {}", e.getMessage());
            throw e;
        } finally {
            //DynamicDataSourceContextHolder.poll();
            countDownLatch.countDown();
        }
    }
}
