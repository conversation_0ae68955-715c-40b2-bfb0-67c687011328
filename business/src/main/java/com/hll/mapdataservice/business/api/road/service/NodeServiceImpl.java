package com.hll.mapdataservice.business.api.road.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.third.InheritIDService;
import com.hll.mapdataservice.business.third.dto.InheritIDDTO;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.entity.Node;
import com.hll.mapdataservice.common.mapper.NodeMMapper;
import com.hll.mapdataservice.common.mapper.NodeMapper;
import com.hll.mapdataservice.common.service.INodeService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * @Author: ares.chen
 * @Since: 2021/8/17
 */
@Service
@Slf4j
//@Transactional(rollbackFor = Exception.class)
public class NodeServiceImpl extends ServiceImpl<NodeMapper, Node> implements INodeService {

    @Resource
    NodeMapper nodeMapper;
    @Resource
    NodeMMapper nodeMMapper;
    @Resource
    CdmsServiceImpl cdmsService;

    @Resource
    InheritIDService inheritIDService;

    @Override
    @Async("asyncTaskExecutor")
    public void convert2rp(String area, String country, CountDownLatch countDownLatch, List<NodeM> nodeList) {
        try {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            int fieldNum = BeanUtil.beanToMap(new NodeM()).keySet().size();
            int batchSize = 32767 / fieldNum;
            log.info("batchInsert size is {}", batchSize);
            List<List<NodeM>> splitList = ListUtil.split(nodeList, batchSize);
            for (List<NodeM> nodes : splitList) {
                List<Node> nodeRpList = new ArrayList<>();
                Node nodeRp;
                for (NodeM node : nodes) {
                    nodeRp = new Node();
                    BeanUtil.copyProperties(node, nodeRp);
                    // 差异字段对应赋值
                    nodeRp.setId(node.getNodeId());
                    nodeRp.setLightFlag(node.getLight());
                    nodeRpList.add(nodeRp);
                }
                log.info("insert datasource is {}", DynamicDataSourceContextHolder.peek());
                nodeMapper.insertBatch(nodeRpList);
            }
        } catch (Exception e) {
            log.error("sync to node_rp error,detail is {}", e.getMessage());
            throw e;
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    @Async("optimizedAsyncTaskExecutor")
    public void convert2rpOptimized(String area, String country, CountDownLatch countDownLatch, List<NodeM> nodeList, ConcurrentLinkedQueue<Exception> exceptions) {
        try {
            log.info("Starting optimized convert2rp for area: {}, country: {}, records: {}", area, country, nodeList.size());

            // Configure database context for source data reading
            configureDatabaseContextForConvert2rp(area, country);

            // Calculate optimized batch size based on field count and PostgreSQL parameter limit
            int fieldNum = BeanUtil.beanToMap(new NodeM()).keySet().size();
            int batchSize = 65535 / fieldNum;
            log.info("Optimized batchInsert size: {}", batchSize);

            // Split into optimized batches
            List<List<NodeM>> splitList = ListUtil.split(nodeList, batchSize);

            for (List<NodeM> nodeBatch : splitList) {
                // Process conversion for this batch
                List<Node> nodeRpList = convertNodeMToNodeOptimized(nodeBatch);

                // Configure database context for target data writing
                configureDatabaseContextForConvert2rp(area, country);

                // Insert batch with optimized method
                log.info("Inserting batch of {} converted nodes to database: {}", nodeRpList.size(), DynamicDataSourceContextHolder.peek());
                nodeMapper.mysqlInsertOrUpdateBath(nodeRpList);

                // Clear batch to help memory management
                nodeRpList.clear();
            }

            log.info("Optimized convert2rp completed for {} records", nodeList.size());

        } catch (Exception e) {
            log.error("Error in optimized convert2rp for area: {}, country: {}, detail: {}", area, country, e.getMessage(), e);
            exceptions.offer(e);
            throw e;
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    @Async("asyncTaskExecutor")
    public void handleId(String area, String country, CountDownLatch countDownLatch, List<Node> nodeList) {
        try {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            int fieldNum = BeanUtil.beanToMap(new NodeM()).keySet().size();
            int batchSize = 32767 / fieldNum;
            log.info("batchInsert size is {}", batchSize);
            List<List<Node>> splitList = ListUtil.split(nodeList, batchSize);
            for (List<Node> nodes : splitList) {
                List<Node> nodeRpList = new ArrayList<>();
                for (Node node : nodes) {
                    // 处理 mainnodeid
                    if (StrUtil.isNotEmpty(node.getMainnodeid())) {
                        node.setMainnodeid(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(node.getMainnodeid()))).get(0)));
                    } else {
                        node.setMainnodeid(node.getMainnodeid());
                    }
                    // 处理 subnodeid
                    if (StrUtil.isNotEmpty(node.getSubnodeid())) {
                        if (node.getSubnodeid().contains("|")) {
                            List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(node.getSubnodeid().split("\\|"))));
                            String resSubNodeid = CollUtil.join(inheritID, "|");
                            node.setSubnodeid(resSubNodeid);
                        } else if (node.getSubnodeid().length() < 18) {
                            node.setSubnodeid(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(node.getSubnodeid()))).get(0)));
                        }
                    } else {
                        node.setSubnodeid(node.getSubnodeid());
                    }

                    // 处理 subnodeid2
                    if (StrUtil.isNotEmpty(node.getSubnodeid2())) {
                        if (node.getSubnodeid2().contains("|")) {
                            List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(node.getSubnodeid2().split("\\|"))));
                            String resSubNodeid = CollUtil.join(inheritID, "|");
                            node.setSubnodeid2(resSubNodeid);
                        } else if (node.getSubnodeid2().length() < 18) {
                            node.setSubnodeid2(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(node.getSubnodeid2()))).get(0)));
                        }
                    } else {
                        node.setSubnodeid2(node.getSubnodeid2());
                    }
                    nodeRpList.add(node);
                }
                // log.info("insert datasource is {}", DynamicDataSourceContextHolder.peek());
                nodeMapper.mysqlInsertOrUpdateBath(nodeRpList);
            }
        } catch (Exception e) {
            log.error("handle node_rp inherited id error,detail is {}", e.getMessage());
            throw e;
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    @Async("asyncTaskExecutor")
    public void updateNodeLight(String area, String country, CountDownLatch countDownLatch, List<Node> nodeList) {

        try {
            for (Node node : nodeList) {
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
                List<Cdms> cdmsList = cdmsService.lambdaQuery().eq(Cdms::getLinkId, node.getId()).list();
                if (cdmsList.size() > 0 && cdmsList.get(0).getCondType()==16) {
                    node.setLightFlag("1");
                } else {
                    node.setLightFlag("0");
                }
            }
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            nodeMapper.mysqlInsertOrUpdateBath(nodeList);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            countDownLatch.countDown();
        }
    }

    /**
     * 递归分批，批量录入数据
     *
     * @param list
     * @param start
     * @param limit
     */
    private void transferData(List<Node> list, long start, long limit) {
        List<Node> collect = list.stream().skip(start).limit(limit).collect(Collectors.toList());
        if (CollUtil.isEmpty(collect)) {
            return;
        }
        nodeMapper.insertBatch(collect);
        transferData(list, start + limit, limit);
    }

    /**
     * Convert NodeM entities to Node entities with optimized field mapping
     * Rewritten from scratch to ensure 100% functional parity with original logic
     */
    private List<Node> convertNodeMToNodeOptimized(List<NodeM> nodeList) {
        List<Node> nodeRpList = new ArrayList<>();

        for (NodeM nodeM : nodeList) {
            Node nodeRp = new Node();

            // Copy all common properties from NodeM to Node
            BeanUtil.copyProperties(nodeM, nodeRp);

            // Apply specific field mappings for convert2rp operation
            // These are the key differences between NodeM and Node entities
            nodeRp.setId(nodeM.getNodeId());           // nodeId -> id
            nodeRp.setLightFlag(nodeM.getLight());     // light -> lightFlag

            nodeRpList.add(nodeRp);
        }

        return nodeRpList;
    }

    /**
     * Configure database context for convert2rp operations
     */
    private void configureDatabaseContextForConvert2rp(String area, String country) {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
    }
}
