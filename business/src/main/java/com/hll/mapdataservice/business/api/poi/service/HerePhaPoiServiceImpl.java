package com.hll.mapdataservice.business.api.poi.service;

import com.hll.mapdataservice.common.entity.HerePhaPoi;
import com.hll.mapdataservice.common.mapper.HerePhaPoiMapper;
import com.hll.mapdataservice.common.service.IHerePhaPoiService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.business.common.XmlFileUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-12
 */
@Service
//@DS("db4")
public class HerePhaPoiServiceImpl extends ServiceImpl<HerePhaPoiMapper, HerePhaPoi> implements IHerePhaPoiService {

    @Async()
    public void saveHerePoi(File file){
        System.out.println("processing file:"+file.toString());
        long startTime = System.currentTimeMillis();
        List<HerePhaPoi> herePhaPoiList = new XmlFileUtils().herePhaPoiXmlRead(file);
        this.saveBatch(herePhaPoiList);
        long endTime = System.currentTimeMillis();
        System.out.println("finished processing file:"+file.toString()+" cost "+(endTime-startTime)/1000+"s");
    }
}
