package com.hll.mapdataservice.business.api.aoi.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hll.mapdataservice.business.api.poi.service.ReportPoiServiceImpl;
import com.hll.mapdataservice.business.third.AoiBaseService;
import com.hll.mapdataservice.common.entity.ReportPoi;
import com.hll.mapdataservice.common.mapper.ReportPoiMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
@RestController
@ResponseBody
@RequestMapping("/api/aoi")
@Api(tags ="aoi")
public class AoiFeignController {
    @Resource
    private AoiBaseService aoiBaseService;

    @GetMapping("/getQueryAoi")
    @ApiOperation(value = "getQueryAoi")
    public String getQueryAoi(){
        return aoiBaseService.getQueryAoi();
    }
}
