package com.hll.mapdataservice.business.api.road.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.third.InheritIDService;
import com.hll.mapdataservice.business.third.dto.InheritIDDTO;
import com.hll.mapdataservice.common.entity.Node;
import com.hll.mapdataservice.common.entity.NodeE;
import com.hll.mapdataservice.common.entity.NodeM;
import com.hll.mapdataservice.common.mapper.NodeEMapper;
import com.hll.mapdataservice.common.mapper.NodeMMapper;
import com.hll.mapdataservice.common.mapper.NodeMapper;
import com.hll.mapdataservice.common.service.INodeEService;
import com.hll.mapdataservice.common.service.INodeMService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-28
 */
@Slf4j
@Service
public class NodeEServiceImpl extends ServiceImpl<NodeEMapper, NodeE> implements INodeEService {

    @Resource
    private InheritIDService inheritIDService;

    @Resource
    private NodeEMapper nodeEMapper;

    @Resource
    private NodeMapper nodeMapper;

    @Async("optimizedAsyncTaskExecutor")
    public void nodeE2node(String area, String country, CountDownLatch countDownLatch, List<NodeE> nodeEList) {
        try {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            int fieldNum = BeanUtil.beanToMap(new Node()).keySet().size();
            int batchSize = 65535 / fieldNum;
            log.info("batchInsert size is {}", batchSize);
            List<List<NodeE>> splitList = ListUtil.split(nodeEList, batchSize);
            for (List<NodeE> nodeES : splitList) {
                List<Node> nodeList = new ArrayList<>();
                Node node;
                for (NodeE nodeE : nodeES) {
                    node = new Node();
                    BeanUtil.copyProperties(nodeE, node);
                    // 差异字段对应赋值
                    node.setId(nodeE.getNodeId());
                    node.setLightFlag(nodeE.getLight());
                    node.setStatus(0);
                    nodeList.add(node);
                }
                log.info("insert datasource is {}", DynamicDataSourceContextHolder.peek());
                nodeMapper.mysqlInsertOrUpdateBath(nodeList);
            }
        } catch (Exception e) {
            log.error("sync to node error,detail is {}", e.getMessage());
            throw e;
        } finally {
            countDownLatch.countDown();
        }
    }
}
