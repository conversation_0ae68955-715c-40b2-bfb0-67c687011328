package com.hll.mapdataservice.business.api.poi.controller;


import com.hll.mapdataservice.business.api.poi.service.HerePhaPoiServiceImpl;
import com.hll.mapdataservice.common.entity.HerePhaPoi;
import com.hll.mapdataservice.common.entity.HereThaPoiCopy1;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import org.springframework.stereotype.Controller;

import javax.annotation.Resource;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-12
 */
@RestController
@ResponseBody
@Api(tags ="poi")
@RequestMapping("/api/poi/herePhaPoi")
public class HerePhaPoiController {
    @Resource
    private HerePhaPoiServiceImpl herePhaPoiService;

    @GetMapping("getById")
    @ApiOperation(value = "getById")
    public HerePhaPoi getPoiById(@RequestParam(value = "featId") String featId) {
        //return mnrNetwGeoLinkService.listByMap();
        //return mnrNetwGeoLinkMapper.selectById(UUID.fromString("00005448-**************-000000f09a71"));
        return herePhaPoiService.lambdaQuery().eq(HerePhaPoi::getPoiEntityId, featId).list().get(0);
        //return mnrNetwGeoLinkService.getById(UUID.fromString("00005448-**************-000000f09a71"));
    }
}
