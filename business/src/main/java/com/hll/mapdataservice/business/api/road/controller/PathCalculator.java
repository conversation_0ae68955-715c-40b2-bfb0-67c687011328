package com.hll.mapdataservice.business.api.road.controller;

import cn.hutool.core.collection.CollUtil;
import com.hll.mapdataservice.business.api.road.service.LinkServiceImpl;
import com.hll.mapdataservice.business.api.road.service.NodeServiceImpl;
import com.hll.mapdataservice.business.common.SpringContainerGetter;
import com.hll.mapdataservice.common.entity.Link;
import lombok.Getter;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2023-10-01
 */
@Getter
public class PathCalculator {

    private final String inlinkId;

    private final String outlinkId;

    private final List<Integer> directionList_1 = new ArrayList<>(Arrays.asList(0, 1, 2));

    private final List<Integer> directionList_2 = new ArrayList<>(Arrays.asList(0, 1, 3));

    private final Map<String, Link> linkMap;


    public PathCalculator(String inlinkId, String outlinkId, List<String> srcList) {
        LinkServiceImpl linkService = (LinkServiceImpl) SpringContainerGetter.getBean(LinkServiceImpl.class);

        List<Link> links = linkService.lambdaQuery().select(Link::getHllSNid,Link::getHllENid,Link::getDirection,Link::getId,Link::getHllLinkid).in(Link::getId, srcList).list();
        Map<String, Link> linkMap = new LinkedHashMap<>();
        for (Link link : links) {
            linkMap.put(link.getId(), link);
        }
        this.inlinkId = inlinkId;
        this.outlinkId = outlinkId;
        this.linkMap = linkMap;
    }

    private List<String> findTopology(Link autoRule) {
        String hllLinkId = autoRule.getHllLinkid();
        String hll_s_nid = autoRule.getHllSNid();
        String hll_e_nid = autoRule.getHllENid();
        String dir = autoRule.getDirection();
        // if (dir > 3) {
        //     return null;
        // }
        List<Link> topologyRuleList = new LinkedList<>();

        Link temporaryAutoPath;
        String tapHllSNid;
        String tapHllENid;
        for (Map.Entry<String, Link> map : linkMap.entrySet()) {
            temporaryAutoPath = map.getValue();
            tapHllSNid = temporaryAutoPath.getHllSNid();
            tapHllENid = temporaryAutoPath.getHllENid();
            if (dir.equals("2")) {
                if (tapHllSNid.equals(hll_e_nid) || tapHllENid.equals(hll_e_nid)) {
                    topologyRuleList.add(temporaryAutoPath);
                }
            } else if (dir.equals("3")) {
                if (tapHllSNid.equals(hll_s_nid) || tapHllENid.equals(hll_s_nid)) {
                    topologyRuleList.add(temporaryAutoPath);
                }
            } else {
                if (tapHllSNid.equals(hll_s_nid) || tapHllSNid.equals(hll_e_nid)
                        || tapHllENid.equals(hll_s_nid) || tapHllENid.equals(hll_e_nid)) {
                    topologyRuleList.add(temporaryAutoPath);
                }

            }
        }

        List<String> topologyLinks = new LinkedList<>();
        String outlinkId;
        String outlink_s_nid;
        String outlink_e_nid;
        String outlink_dir;
        for (Link autoRuleDTO : topologyRuleList) {
            outlinkId = autoRuleDTO.getHllLinkid();
            outlink_s_nid = autoRuleDTO.getHllSNid();
            outlink_e_nid = autoRuleDTO.getHllENid();
            outlink_dir = autoRuleDTO.getDirection();

            if (outlinkId.equals(hllLinkId)) {
                continue;
            }
            if ((outlink_s_nid.equals(hll_s_nid)
                    || outlink_s_nid.equals(hll_e_nid))
                    && !directionList_1.contains(outlink_dir)) {
                continue;
            }
            if ((outlink_e_nid.equals(hll_s_nid)
                    || outlink_e_nid.equals(hll_e_nid))
                    && !directionList_2.contains(outlink_dir)) {
                continue;
            }
            topologyLinks.add(outlinkId);
        }
        return topologyLinks;
    }


    private List<String> minPath(List<List<String>> pathArray) {
        int minPathSize = Integer.MAX_VALUE;
        for (List<String> path : pathArray) {
            if (path.size() <= minPathSize) {
                minPathSize = path.size();
            }
        }
        for (List<String> path : pathArray) {
            if (path.size() == minPathSize) {
                pathArray.remove(path);
                return path;
            }
        }
        return null;
    }

    public List<String> ruleComplete() {
        if (this.inlinkId.equals(outlinkId)) {
            return new ArrayList<>(Arrays.asList(this.inlinkId, this.outlinkId));
        }
        List<List<String>> pathArray = new LinkedList<>();
        pathArray.add(new ArrayList<>(Collections.singletonList(this.inlinkId)));
        List<String> path;
        List<String> temporaryPath;
        List<String> temp;
        int index = 0;
        Link autoRule;
        while (true) {
            index++;
            if (index > 5000) {
                return null;
            }
            path = minPath(pathArray);
            assert path != null;
            if (path.size() == 0) {
                return null;
            }
            if (path.size() > 15) {
                return null;
            }

            autoRule = linkMap.get(path.get(path.size() - 1));
            if (Objects.isNull(autoRule)) {
                throw new RuntimeException("link is null");
            }

            temporaryPath = findTopology(autoRule);
            assert temporaryPath != null;
            for (String link : temporaryPath) {
                temp = new LinkedList<>(path);
                temp.add(link);
                if (link.equals(this.outlinkId)) {
                    set.add(CollUtil.join(temp, ","));
                }
                pathArray.add(temp);
            }
        }
    }

    public static Set<String> set = new HashSet<>();


}