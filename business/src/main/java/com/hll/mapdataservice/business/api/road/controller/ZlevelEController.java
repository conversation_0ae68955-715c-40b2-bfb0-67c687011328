package com.hll.mapdataservice.business.api.road.controller;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.hll.mapdataservice.business.api.road.service.LinkEServiceImpl;
import com.hll.mapdataservice.business.api.road.service.ZlevelEServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.LinkE;
import com.hll.mapdataservice.common.entity.ZlevelE;
import com.hll.mapdataservice.common.utils.CommonUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CountDownLatch;

@RestController
@Slf4j
@RequestMapping("/common/zlevel-e")
public class ZlevelEController {

    @Resource
    ZlevelEServiceImpl zlevelEService;


    @ApiOperation("map zlevel_e diff column to zlevel")
    @GetMapping("zlevelE2zlevel")
    public ResponseResult<Boolean> zlevelE2zlevel(@RequestParam(value = "step", required = false, defaultValue = "1") int step,
                                              @RequestParam(value = "area", required = false, defaultValue = "") String area,
                                              @RequestParam(value = "country", required = false, defaultValue = "") String country) throws InterruptedException {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        TimeInterval timer = DateUtil.timer();
        // calculate loop times
        int zlevelECount = zlevelEService.lambdaQuery().ne(ZlevelE::getStatus, 1).count();
        int loop = zlevelECount % step != 0 ? (zlevelECount / step) + 1 : zlevelECount / step;
        CountDownLatch countDownLatch = new CountDownLatch(loop);
        for (int i = 0; i < loop; i++) {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            log.info("query zlevel_e db is:" + DynamicDataSourceContextHolder.peek());
            List<ZlevelE> zlevelEList = zlevelEService.lambdaQuery().ne(ZlevelE::getStatus, 1)
                    .orderByDesc(ZlevelE::getZlevelId).last("limit " + step + " offset " + i * step).list();
            log.info("convert zlevel_e db is:" + DynamicDataSourceContextHolder.peek());
            zlevelEService.zlevelE2zlevel(area, country, countDownLatch, zlevelEList);
        }
        countDownLatch.await();
        log.info("map zlevel_e diff column to zlevel cost time is {}s", timer.intervalSecond());
        return ResponseResult.OK(true, true);
    }

}
