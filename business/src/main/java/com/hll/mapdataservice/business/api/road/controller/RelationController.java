package com.hll.mapdataservice.business.api.road.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.hll.mapdataservice.business.api.road.service.*;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.third.InheritIDService;
import com.hll.mapdataservice.business.third.dto.InheritIDDTO;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.utils.CommonUtils;
import com.linuxense.javadbf.DBFField;
import com.linuxense.javadbf.DBFWriter;
import com.vividsolutions.jts.io.ParseException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.CaseUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.File;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

import static com.hll.mapdataservice.common.utils.DbfFileUtils.getFieldNameByTableName;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-01
 */
@RestController
@ResponseBody
@Api(tags = "road")
@Component
@Slf4j
@RequestMapping("/api/road/herelinkreleation")
public class RelationController {

    @Resource
    CdmsServiceImpl cdmsService;
    @Resource
    RelationMServiceImpl relationService;
    @Resource
    InheritIDService inheritIDService;
    @Resource
    LinkMServiceImpl linkMService;
    @Resource
    NodeMServiceImpl nodeMService;
    @Resource
    RdmsServiceImpl rdmsService;
    @Resource
    HerePhaStreetsServiceImpl herePhaStreetsService;
    @Resource
    StreetsServiceImpl streetsService;
    @Resource
    CndmodServiceImpl cndmodService;

    @ApiOperation(value = "here link releations convert")
    @PostMapping("/convert")
    public ResponseResult<Boolean> hereLinkReleationsConvert(
            @RequestParam(value = "step",
                    required = false,
                    defaultValue = "1") int step,
            @RequestParam(value = "area",
                    required = false,
                    defaultValue = "") String area,
            @RequestParam(value = "country",
                    required = false,
                    defaultValue = "") String country)
            throws SQLException, InterruptedException {

        // List<HerePhaZlevels> herePhaZlevelsList = herePhaZlevelsService.list();
        // MybatisPlusConfig.myTableName ="_"+area;
        // MybatisPlusConfig.myTableName.set("_"+area);
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        TimeInterval timer = DateUtil.timer();
        Integer listSize = cdmsService.count();
        log.info("The records to be transfered:" + listSize);
        int batchNum = listSize % step == 0 ? listSize / step : (listSize / step) + 1;
        CountDownLatch countDownLatch = new CountDownLatch(batchNum);

        for (int i = 0; i < batchNum; i++) {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<Cdms> cdmsList = cdmsService.lambdaQuery()
                    .orderByDesc(Cdms::getLinkId).last("limit " + step + " offset " + i * step).list();
            log.info("limit " + step + " offset " + i * step);
            if (cdmsList.size() > 0) {
                log.info("cdmsList size is:" + cdmsList.size());
                // linkSw2021q133Service.linkConvert(herePhaStreetsListi,nodeIdSet,nodeSw2021q133List);
                cdmsService.releationConvert(countDownLatch, cdmsList, area, country);
            }
        }
        countDownLatch.await();
        log.info("relation convert finished!");
        log.info("relation convert finished! cost time is {}s", timer.intervalSecond());

        // nodeSw2021q133Service.saveOrUpdateBatch(nodeSw2021q133List);
        return ResponseResult.OK(true, true);
    }


    @ApiOperation(value = "here link releations convert2")
    @PostMapping("/convert2")
    public ResponseResult<Boolean> hereLinkReleationsConvert2(
            @RequestParam(value = "step",
                    required = false,
                    defaultValue = "2000") int step,
            @RequestParam(value = "area",
                    required = false,
                    defaultValue = "") String area,
            @RequestParam(value = "country",
                    required = false,
                    defaultValue = "") String country) throws InterruptedException {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        TimeInterval timer = DateUtil.timer();

        int res = 0;
        int cdmsCount = cdmsService.lambdaQuery().count();
        int loop = cdmsCount % step != 0 ? (cdmsCount / step) + 1 : cdmsCount / step;
        CountDownLatch countDownLatch = new CountDownLatch(loop);
        for (int i = 0; i < loop; i++) {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<Cdms> cdmsList = cdmsService.lambdaQuery()
                    .orderByDesc(Cdms::getGid).last("limit " + step + " offset " + i * step).list();
//            List<Cdms> cdmsList = cdmsService.lambdaQuery().in(Cdms::getLinkId,CollUtil.newArrayList(732007707,1242105727,1169433182)).list();
//            List<Cdms> cdmsList = cdmsService.lambdaQuery()
//                    .orderByDesc(Cdms::getLinkId).last("limit " + step + " offset " + i * step).list();

//            List<Cdms> cdmsList = cdmsService.lambdaQuery().eq(Cdms::getLinkId,878123623).list();
//        List<Cdms> cdmsList = cdmsService.lambdaQuery().eq(Cdms::getLinkId,1169433182).list();
            log.info("limit " + step + " offset " + i * step);
//            res+=cdmsList.stream().filter(s->s.getCondType()==1||s.getCondType()==4||s.getCondType()==9||s.getCondType()==11||s.getCondType()==16).collect(Collectors.toList()).size();
            res += cdmsList.size();
            if (cdmsList.size() > 0) {
                log.info("cdmsList size is:" + cdmsList.size());
                cdmsService.releationConvert2(countDownLatch, cdmsList, area, country);
            }
        }
        countDownLatch.await();
        log.info("relation convert finished!");
        log.info("relation convert finished! cost time is {}s,handle num is{}", timer.intervalSecond(), res);

        return ResponseResult.OK(true, true);
    }

    @ApiOperation(value = "here link releation export")
    @PostMapping("/export")
    public ResponseResult<Boolean> hereLinkRuleExport(
            @RequestParam(value = "path",
                    required = true) String path,
            @RequestParam(value = "area",
                    required = false,
                    defaultValue = "") String area,
            @RequestParam(value = "country",
                    required = false,
                    defaultValue = "") String country)
            throws SQLException, ParseException, InterruptedException {
        // MybatisPlusConfig.myTableName ="_"+area;
        // MybatisPlusConfig.myTableName.set("_"+area);
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }

        List<RelationM> relationList = relationService.list();
        log.info("relation to be exported:" + relationList.size());
        // RuleSw2021q133 ruleSw2021q133List = ruleSw2021q133Service.list().get(0);
        DBFField[] fields = getFieldNameByTableName("releation");
        // 定义DBFWriter实例用来写DBF文件
        DBFWriter dbfWriter = new DBFWriter(new File(path + "/relation_" + LocalDateTime.now() + ".dbf"));

        dbfWriter.setFields(fields);

        for (RelationM relation : relationList
        ) {
            Object[] data = new Object[fields.length];

            for (int i = 0; i < fields.length; i++) {
//                System.out.println("field name is:"+fields[i].getName()+",camel filed name is:"+
//                        CaseUtils.toCamelCase(String.valueOf(fields[i].getName()),false,'_'));
//                System.out.println("rule info is:"+ JSONObject.parseObject(JSONObject.toJSONString(rule)));
                if (JSONObject.parseObject(JSONObject.toJSONString(relation))
                        .get(CaseUtils.toCamelCase(String.valueOf(fields[i].getName()), false, '_')) != null) {
                    data[i] = JSONObject.parseObject(JSONObject.toJSONString(relation))
                            .get(CaseUtils.toCamelCase(String.valueOf(fields[i].getName()), false, '_'));
                } else {
                    data[i] = null;
                }
            }
//            for (String datai:data
//            ) {
//                System.out.println(datai);
//            }
            dbfWriter.addRecord(data);
        }

        dbfWriter.close();
        log.info("relation export finished");
        return ResponseResult.OK(true, true);
    }

    @ApiOperation(value = "here link releations convert optimized - NEW OPTIMIZED VERSION")
    @PostMapping("/convert-optimized")
    public ResponseResult<Boolean> hereLinkReleationsConvertOptimized(
            @RequestParam(value = "step",
                    required = false,
                    defaultValue = "0") int step,
            @RequestParam(value = "area",
                    required = false,
                    defaultValue = "") String area,
            @RequestParam(value = "country",
                    required = false,
                    defaultValue = "") String country) throws InterruptedException {

        log.info("Starting optimized here link relations convert process for area: {}, country: {}", area, country);
        TimeInterval timer = DateUtil.timer();

        // Configure database context
        configureDatabaseContext(area, country, true);

        // Log memory usage at start
        logMemoryUsage("Start of hereLinkRelationsConvertOptimized");

        try {
            // Calculate total records and optimal batch size
            int totalRecords = cdmsService.lambdaQuery().in(Cdms::getCondType,CollUtil.newArrayList(1, 4, 9, 11, 16)).count();
            int optimizedStep = calculateOptimalBatchSize(totalRecords, step);

            log.info("Total records to process: {}, optimized step size: {}", totalRecords, optimizedStep);
            ConcurrentLinkedQueue<Exception> exceptions = new ConcurrentLinkedQueue<>();

            // Process data in optimized batches
            processRelationDataInOptimizedBatches(area, country, totalRecords, optimizedStep,exceptions);
            if (!exceptions.isEmpty()) {
                Exception firstException = exceptions.peek();
                log.error("Error in optimized relation convert", firstException);
                return ResponseResult.otherInfo("500", "handle " + country + " failed[relation convert]: " + firstException.getMessage(), false);
            }

            log.info("Optimized relation convert finished! cost time is {}s,country is {},area is {}", timer.intervalSecond(), country, area);
            logMemoryUsage("End of hereLinkReleationsConvertOptimized");

            return ResponseResult.OK(true, true);

        } catch (Exception e) {
            log.error("Error in optimized relation convert", e);
            throw new RuntimeException("Optimized relation convert failed", e);
        } finally {
            // Cleanup resources
            cleanupResources();
        }
    }

    @ApiOperation(value = "link diff and merge")
    @PostMapping("/diffCheck")
    public ResponseResult<String> diffCheck(@RequestParam(value = "area", required = false, defaultValue = "") String area,
                                            @RequestParam(value = "country", required = false, defaultValue = "") String country) {


        log.info("start to diff check...");
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        // 1. 获取cdms,中cond_type在1 4 9 11 16的link_id
        List<Cdms> cdmsList = cdmsService.lambdaQuery().select(Cdms::getLinkId).in(Cdms::getCondType, CollUtil.newArrayList(1, 4, 9, 11, 16)).list();
        List<Long> pureCdmsLinkIds = cdmsList.stream().distinct().map(Cdms::getLinkId).collect(Collectors.toList());

        // 将集合从integer转为string
        List<String> pureCdmsLinkIdsStr = pureCdmsLinkIds.stream().map(String::valueOf).collect(Collectors.toList());
        List<List<String>> split = ListUtil.split(pureCdmsLinkIdsStr, 1000);
        List<Long> inheritPureCdmsLinkIds = new ArrayList<>();
        for (List<String> strings : split) {
            List<Long> partInheritIds = inheritIDService.inheritID(new InheritIDDTO(12L, strings));
            inheritPureCdmsLinkIds.addAll(partInheritIds);
        }


        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        // 2. 获取relation中的link_id
        List<RelationM> relationList = relationService.lambdaQuery().select(RelationM::getInlinkId).list();
        List<String> resRelationListInlinkids = relationList.stream().distinct().map(RelationM::getInlinkId).collect(Collectors.toList());
        List<Long> resRelationListInlinkidsLong = resRelationListInlinkids.stream().map(Long::valueOf).collect(Collectors.toList());

        // 3.取二者集合的差集
        Collection<Long> subtract = CollUtil.subtract(inheritPureCdmsLinkIds, resRelationListInlinkidsLong);

        log.info("diff check finished, result is:" + subtract.toString());
        return ResponseResult.OK(subtract.toString(), true);
    }

    /**
     * Process relation data in optimized batches with proper memory management
     */
    private void processRelationDataInOptimizedBatches(String area, String country,
                                                       int totalRecords, int optimizedStep,ConcurrentLinkedQueue<Exception> exceptions) throws InterruptedException {

        // Calculate total batches
        int totalBatches = (totalRecords + optimizedStep - 1) / optimizedStep;
        CountDownLatch countDownLatch = new CountDownLatch(totalBatches);

        log.info("Processing {} total batches with optimized step size: {}", totalBatches, optimizedStep);


        log.info("Starting preloading cache data ......");
        configureDatabaseContext(area, country, true);
        List<Cdms> allCdmsList = cdmsService.lambdaQuery().list();

        // Pre-load configuration data to avoid repeated database queries
        Map<Integer, List<Rdms>> rdmsCache = preloadRdmsData(allCdmsList);
        Map<Long, List<HerePhaStreets>> streetsCache = preloadStreetsData(allCdmsList, area, country);
        Map<Integer, List<Cndmod>> cndmodCache = preloadCndmodData(allCdmsList);


        configureDatabaseContext(area, country, false);
        Map<String, LinkM> allLinkMCache = preloadLinkMData();
        Map<String, NodeM> allNodeMCache = preloadNodeMData();
        log.info("Finished preloading cache data ......");

        // Process in chunks to avoid loading all data into memory at once
        for (int i = 0; i < totalBatches; i++) {
            try {
                // Set database context for each batch
                configureDatabaseContext(area, country, true);

                // Fetch data for this batch
                List<Cdms> cdmsList = cdmsService.lambdaQuery().in(Cdms::getCondType, CollUtil.newArrayList(1, 4, 9, 11, 16))
                        .orderByDesc(Cdms::getGid)
                        .last("limit " + optimizedStep + " offset " + i * optimizedStep)
                        .list();

                log.info("Processing batch {}/{}: limit {} offset {}, batch size: {}",
                        i + 1, totalBatches, optimizedStep, i * optimizedStep, cdmsList.size());

                if (!cdmsList.isEmpty()) {
                    // Use optimized service for better performance and memory management
                    cdmsService.relationConvertOptimized(countDownLatch, cdmsList, area, country, rdmsCache, streetsCache, cndmodCache,allLinkMCache,allNodeMCache,exceptions);
                } else {
                    // No data in this batch, count down the latch
                    countDownLatch.countDown();
                }

                // Cleanup after every few batches to manage memory
                // if (i % 3 == 0) {
                //     cleanupResources();
                //     logMemoryUsage("After batch " + (i + 1));
                // }

            } catch (Exception e) {
                log.error("Error processing batch {}: {}", i + 1, e.getMessage());
                countDownLatch.countDown(); // Ensure latch is decremented even on error
            }
        }

        // Wait for all batches to complete
        countDownLatch.await();
        if(rdmsCache!=null) rdmsCache.clear();
        if(streetsCache!=null) streetsCache.clear();
        if(cndmodCache!=null) cndmodCache.clear();
        if(allLinkMCache!=null) allLinkMCache.clear();
        if(allNodeMCache!=null) allNodeMCache.clear();

        log.info("All relation conversion batches completed");
    }

    private Map<String, NodeM> preloadNodeMData() {
        List<NodeM> allNodeMList = nodeMService.lambdaQuery().list();
        return allNodeMList.stream()
                .collect(Collectors.toMap(NodeM::getNodeId, nodeM -> nodeM, (existing, replacement) -> existing));
    }

    private Map<String, LinkM> preloadLinkMData() {
        List<LinkM> allLinkMList = linkMService.lambdaQuery().list();
        return allLinkMList.stream()
                .collect(Collectors.toMap(LinkM::getLinkId, linkM -> linkM, (existing, replacement) -> existing));
    }

    /**
     * Calculate optimal batch size based on available memory and data volume
     */
    private int calculateOptimalBatchSize(int totalRecords, int requestedStep) {
        if (requestedStep > 0) {
            return requestedStep; // Use user-specified step if provided
        }

        Runtime runtime = Runtime.getRuntime();
        long availableMemoryMB = (runtime.maxMemory() - (runtime.totalMemory() - runtime.freeMemory())) / (1024 * 1024);

        // Calculate optimal batch size based on available memory
        int optimalStep;
        if (availableMemoryMB > 2048) { // > 2GB available
            optimalStep = Math.min(5000, totalRecords / 10);
        } else if (availableMemoryMB > 1024) { // > 1GB available
            optimalStep = Math.min(3000, totalRecords / 15);
        } else if (availableMemoryMB > 512) { // > 512MB available
            optimalStep = Math.min(2000, totalRecords / 20);
        } else {
            optimalStep = Math.min(1000, totalRecords / 30); // Conservative for low memory
        }

        // Ensure minimum batch size
        optimalStep = Math.max(500, optimalStep);

        log.info("Calculated optimal batch size: {} (available memory: {} MB, total records: {})",
                optimalStep, availableMemoryMB, totalRecords);

        return optimalStep;
    }

    /**
     * Monitor and log memory usage during processing
     */
    private void logMemoryUsage(String phase) {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();

        log.info("Memory Usage - {}: Used: {} MB, Free: {} MB, Total: {} MB, Max: {} MB",
                phase,
                usedMemory / (1024 * 1024),
                freeMemory / (1024 * 1024),
                totalMemory / (1024 * 1024),
                maxMemory / (1024 * 1024));
    }

    /**
     * Cleanup resources and force garbage collection
     */
    private void cleanupResources() {
        // Clear any thread-local variables
        DynamicDataSourceContextHolder.clear();

        // Suggest garbage collection
        System.gc();

        log.debug("Resources cleaned up and garbage collection suggested");
    }


    /**
     * Pre-load RDMS data to avoid repeated database queries
     */
    private Map<Integer, List<Rdms>> preloadRdmsData(List<Cdms> cdmsList) {
        Map<Integer, List<Rdms>> rdmsCache = new HashMap<>();

        // Extract unique condition IDs
        Set<Integer> condIds = cdmsList.stream()
                .map(Cdms::getCondId)
                .collect(Collectors.toSet());

        if (!condIds.isEmpty()) {
            List<Rdms> allRdmsList = new ArrayList<>();
            if (condIds.size() > 65535) {
                CollUtil.split(condIds, 65535).forEach(condId -> {
                    allRdmsList.addAll(rdmsService.lambdaQuery()
                            .in(Rdms::getCondId, condId)
                            .list());
                });
            } else {
               allRdmsList.addAll(rdmsService.lambdaQuery()
                       .in(Rdms::getCondId, condIds)
                       .list());
            }
            // Batch query all RDMS data
            // List<Rdms> allRdms = rdmsService.lambdaQuery()
            //         .in(Rdms::getCondId, condIds)
            //         .list();

            // Group by condition ID for fast lookup
            rdmsCache = allRdmsList.stream()
                    .collect(Collectors.groupingBy(Rdms::getCondId));
        }

        return rdmsCache;
    }

    /**
     * Pre-load Streets data to avoid repeated database queries
     */
    private Map<Long, List<HerePhaStreets>> preloadStreetsData(List<Cdms> cdmsList, String area, String country) {
        Map<Long, List<HerePhaStreets>> streetsCache = new HashMap<>();

        // Set database context for streets query
        configureDatabaseContext(area,country,true);

        // Extract unique link IDs
        Set<Long> linkIds = cdmsList.stream()
                .map(Cdms::getLinkId)
                .collect(Collectors.toSet());

        if (!linkIds.isEmpty()) {
            List<HerePhaStreets> allStreetList = new ArrayList<>();
            if (linkIds.size() > 65535) {
                CollUtil.split(linkIds, 65535).forEach(linkId -> {
                    allStreetList.addAll(herePhaStreetsService.lambdaQuery()
                            .in(HerePhaStreets::getLinkId, linkId)
                            .list());
                });
            } else {
               allStreetList.addAll(herePhaStreetsService.lambdaQuery()
                    .in(HerePhaStreets::getLinkId, linkIds)
                    .list());
            }
            // Batch query all Streets data
            // List<HerePhaStreets> allStreets = herePhaStreetsService.lambdaQuery()
            //         .in(HerePhaStreets::getLinkId, linkIds)
            //         .list();

            // Group by link ID for fast lookup
            streetsCache = allStreetList.stream()
                    .collect(Collectors.groupingBy(HerePhaStreets::getLinkId));
        }

        return streetsCache;
    }

    /**
     * Pre-load Cndmod data to avoid repeated database queries
     */
    private Map<Integer, List<Cndmod>> preloadCndmodData(List<Cdms> cdmsList) {
        Map<Integer, List<Cndmod>> cndmodCache = new HashMap<>();

        // Extract unique condition IDs
        Set<Integer> condIds = cdmsList.stream()
                .map(Cdms::getCondId)
                .collect(Collectors.toSet());

        if (!condIds.isEmpty()) {
            List<Cndmod> allCndmodList = new ArrayList<>();
            if (condIds.size() > 65535) {
                CollUtil.split(condIds, 65535).forEach(condId -> {
                    allCndmodList.addAll(cndmodService.lambdaQuery()
                            .in(Cndmod::getCondId, condId)
                            .list());
                });
            } else {
               allCndmodList.addAll(cndmodService.lambdaQuery()
                    .in(Cndmod::getCondId, condIds)
                    .list());
            }
            // Batch query all Cndmod data
            // List<Cndmod> allCndmod = cndmodService.lambdaQuery()
            //         .in(Cndmod::getCondId, condIds)
            //         .list();

            // Group by condition ID for fast lookup
            cndmodCache = allCndmodList.stream()
                    .collect(Collectors.groupingBy(Cndmod::getCondId));
        }

        return cndmodCache;
    }

    private void configureDatabaseContext(String area, String country, boolean isSource) {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, isSource));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
    }
}
