package com.hll.mapdataservice.business.api.road.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.DirEnum;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.mapper.CalculationMapper;
import com.hll.mapdataservice.common.mapper.RoadMatchResMapper;
import com.hll.mapdataservice.common.service.IRoadMatchResService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;

/**
 * @Author: ares.chen
 * @Since: 2021/11/18
 */
@Service
@Slf4j
public class RoadMatchResServiceImpl extends ServiceImpl<RoadMatchResMapper, RoadMatchRes> implements IRoadMatchResService {

    @Resource
    CalculationMapper calculationMapper;
    @Resource
    RoadMatchResMapper roadMatchResMapper;
    @Resource
    LinkMServiceImpl linkMService;

    @Override
    @Async("asyncTaskExecutor")
    public void roadMatch(List<RoadBreak> roadBreaks, String country, String area, CountDownLatch countDownLatch) {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }

        List<RoadMatchRes> roadInsertList = new ArrayList<>();
        try {
            for (RoadBreak road : roadBreaks) {

                //System.out.println("osm坐标：" + road.getRoadGeomWkt());
                //String tmp = "MULTILINESTRING((106.6616781 10.8006677,106.6622897 10.8006093,106.662475 10.8005955,106.6627663 10.8005739,106.6630188 10.8005551,106.663336 10.8005316,106.6634835 10.8005206,106.6638326 10.8005567))";
                String roadGeomWkt = Optional.ofNullable(road.getRoadGeomWkt()).orElse(road.getOsmGeomWKt());
                String lastSql = "where st_intersects(st_buffer(st_geomfromtext('" + roadGeomWkt + "',4326), 0.0002, 'endcap=round join=round'), geom)";
                List<LinkM> linkList = linkMService.lambdaQuery().last(lastSql).list();

                List<Location> roadStartEndCoord;
                if ("T".equals(road.getOneway())) {
                    roadStartEndCoord = CommonUtils.extractCoord(roadGeomWkt, true);
                } else {
                    roadStartEndCoord = CommonUtils.extractCoord(roadGeomWkt, false);
                }
                //double roadAngle = CommonUtils.calAngle(roadStartEndCoord.get(0).getLatitude(), roadStartEndCoord.get(0).getLongitude(),
                //        roadStartEndCoord.get(1).getLatitude(), roadStartEndCoord.get(1).getLongitude());
                Double roadAngle = calculationMapper.calAngle(roadStartEndCoord.get(0).getLongitude(), roadStartEndCoord.get(0).getLatitude(),
                        roadStartEndCoord.get(1).getLongitude(), roadStartEndCoord.get(1).getLatitude());
                if (roadAngle != null) {
                    // 2.1 遍历空间查询here道路，计算方位角
                    if (CollUtil.isNotEmpty(linkList)) {
                        for (LinkM linkM : linkList) {
                            List<Location> linkStartEndCoord;
                            if ("3".equals(linkM.getDir())) {
                                linkStartEndCoord = CommonUtils.extractCoord(linkM.getGeomwkt(), true);
                            } else {
                                linkStartEndCoord = CommonUtils.extractCoord(linkM.getGeomwkt(), false);
                            }
                            //System.out.println("link坐标" + link.getGeomwkt());
                            //double linkAngle = CommonUtils.calAngle(linkStartEndCoord.get(0).getLatitude(), linkStartEndCoord.get(0).getLongitude(),
                            //        linkStartEndCoord.get(1).getLatitude(), linkStartEndCoord.get(1).getLongitude());
                            Double linkAngle = calculationMapper.calAngle(linkStartEndCoord.get(0).getLongitude(), linkStartEndCoord.get(0).getLatitude(),
                                    linkStartEndCoord.get(1).getLongitude(), linkStartEndCoord.get(1).getLatitude());
                            double diff = Math.abs(roadAngle - linkAngle);

                            if ("B".equals(road.getOneway())) {
                                if (diff > 90) {
                                    diff = Math.abs(180 - (Math.abs(roadAngle - linkAngle)));
                                }
                                if (diff > 45) {
                                    unmatchAssemble(roadInsertList, road, linkM);
                                } else {
                                    matchAssemble(roadInsertList, road, linkM);
                                }
                            } else {
                                if (diff > 45) {
                                    unmatchAssemble(roadInsertList, road, linkM);
                                } else {
                                    matchAssemble(roadInsertList, road, linkM);
                                }
                            }
                        }
                    } else {
                        RoadMatchRes roadMatchRes = new RoadMatchRes();
                        roadMatchRes.setId(UUID.randomUUID().toString());
                        String roadGeom = Optional.ofNullable(road.getRoadGeom()).orElse(road.getOsmGeom());
                        roadMatchRes.setRoadGeom(roadGeom);
                        roadMatchRes.setRoadDir(DirEnum.getDirValueByDName(road.getOneway()));
                        roadMatchRes.setMatchRes(new BigDecimal(0));
                        roadMatchRes.setCreateTime(LocalDateTime.now());
                        roadMatchRes.setUpdateTime(LocalDateTime.now());
                        roadMatchRes.setRoadId(String.valueOf(road.getRoadId()));
                        roadMatchRes.setOsmId(String.valueOf(road.getOsmId()));
                        roadMatchRes.setVersion(road.getVersion());
                        roadInsertList.add(roadMatchRes);
                    }
                } else {
                    RoadMatchRes roadMatchRes = new RoadMatchRes();
                    roadMatchRes.setId(UUID.randomUUID().toString());
                    String roadGeom = Optional.ofNullable(road.getRoadGeom()).orElse(road.getOsmGeom());
                    roadMatchRes.setRoadGeom(roadGeom);
                    roadMatchRes.setRoadDir(DirEnum.getDirValueByDName(road.getOneway()));
                    roadMatchRes.setMatchRes(new BigDecimal(-1));
                    roadMatchRes.setCreateTime(LocalDateTime.now());
                    roadMatchRes.setUpdateTime(LocalDateTime.now());
                    roadMatchRes.setRoadId(String.valueOf(road.getRoadId()));
                    roadMatchRes.setOsmId(String.valueOf(road.getOsmId()));
                    roadMatchRes.setVersion(road.getVersion());
                    roadInsertList.add(roadMatchRes);
                }
            }
            // 获取道路匹配模型一次最大持久化数据个数
            //int fieldNum = BeanUtil.beanToMap(new RoadMatchRes()).keySet().size();
            //int batchSize = 32767 / fieldNum;
            List<List<RoadMatchRes>> splitList = CollUtil.splitList(roadInsertList, roadBreaks.size());
            for (List<RoadMatchRes> resList : splitList) {
                roadMatchResMapper.insertBatch(resList);
            }
            //this.saveBatch(roadInsertList, roadBreaks.size());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("osm,here道路匹配出现异常：{}", e.getMessage());
            throw new RuntimeException();
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    public int getAloneMergeRoadNum() {
        return roadMatchResMapper.getAloneMergeRoadNum();
    }

    @Override
    public List<RoadMatchRes> getAloneMergeRoad(int limit, int offset) {
        return roadMatchResMapper.getAloneMergeRoad(limit, offset);
    }

    @Override
    public List<RoadMatchRes> getIndependentRoad() {
        return roadMatchResMapper.getIndependentRoad();
    }

    @Override
    public int getBranchRoadChangedNum() {
        return roadMatchResMapper.getBranchRoadChangedNum();
    }

    @Override
    public List<String> getBranchRoadChangedId(int limit, int offset) {
        return roadMatchResMapper.getBranchRoadChangedId(limit, offset);
    }

    @Override
    public int getTreeBranchMergeNum() {
        return roadMatchResMapper.getTreeBranchMergeNum();
    }

    @Override
    public List<String> getTreeBranchMergeOsmId(int step, int i) {
        return roadMatchResMapper.getTreeBranchMergeOsmId(step, i);
    }

    @Override
    @Async("asyncTaskExecutor")
    public void linkMatch(List<LinkBreak> batchList, String country, String area, CountDownLatch countDownLatch) {
        //if (!country.isEmpty()) {
        //    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        //}
        //if (!area.isEmpty()) {
        //    MybatisPlusConfig.myTableName.set("_" + area);
        //} else {
        //    MybatisPlusConfig.myTableName.set("");
        //}

        List<RoadMatchRes> roadInsertList = new ArrayList<>();
        try {
            for (LinkBreak road : batchList) {

                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
                String roadGeomWkt = Optional.ofNullable(road.getSubGeomWkt()).orElse(road.getGeomwkt());
                String lastSql = "where st_intersects(st_buffer(st_geomfromtext('" + roadGeomWkt + "',4326), 0.0001, 'endcap=round join=round'), geom)";
                List<LinkM> linkList = linkMService.lambdaQuery().last(lastSql).list();
                List<Location> roadStartEndCoord;
                if (DirEnum.getDirValueByDName("T").equals(road.getDir())) {
                    roadStartEndCoord = CommonUtils.extractCoord(roadGeomWkt, true);
                } else {
                    roadStartEndCoord = CommonUtils.extractCoord(roadGeomWkt, false);
                }
                //double roadAngle = CommonUtils.calAngle(roadStartEndCoord.get(0).getLatitude(), roadStartEndCoord.get(0).getLongitude(),
                //        roadStartEndCoord.get(1).getLatitude(), roadStartEndCoord.get(1).getLongitude());
                Double roadAngle = calculationMapper.calAngle(roadStartEndCoord.get(0).getLongitude(), roadStartEndCoord.get(0).getLatitude(),
                        roadStartEndCoord.get(1).getLongitude(), roadStartEndCoord.get(1).getLatitude());
                if (roadAngle != null) {
                    // 2.1 遍历空间查询here道路，计算方位角
                    if (CollUtil.isNotEmpty(linkList)) {
                        for (LinkM linkM : linkList) {
                            List<Location> linkStartEndCoord;
                            if ("3".equals(linkM.getDir())) {
                                linkStartEndCoord = CommonUtils.extractCoord(linkM.getGeomwkt(), true);
                            } else {
                                linkStartEndCoord = CommonUtils.extractCoord(linkM.getGeomwkt(), false);
                            }
                            Double linkAngle = calculationMapper.calAngle(linkStartEndCoord.get(0).getLongitude(), linkStartEndCoord.get(0).getLatitude(),
                                    linkStartEndCoord.get(1).getLongitude(), linkStartEndCoord.get(1).getLatitude());
                            if (linkAngle==null) {
                                // 环形道路过滤掉，不参与匹配
                                log.info("here道路计算角度为null，id为{}",linkM.getLinkId());
                                continue;
                            }
                            //log.info("osm道路角度：{}",roadAngle);
                            //log.info("here道路角度：{}",linkAngle);
                            double diff = Math.abs(roadAngle - linkAngle);

                            if (DirEnum.getDirValueByDName("B").equals(road.getDir())) {
                                if (diff > 90) {
                                    diff = Math.abs(180 - (Math.abs(roadAngle - linkAngle)));
                                }
                                if (diff > 45) {
                                    unmatchAssembleLink(roadInsertList, road, linkM);
                                } else {
                                    matchAssembleLink(roadInsertList, road, linkM);
                                }
                            } else {
                                if (diff > 45) {
                                    unmatchAssembleLink(roadInsertList, road, linkM);
                                } else {
                                    matchAssembleLink(roadInsertList, road, linkM);
                                }
                            }
                        }
                    } else {
                        RoadMatchRes roadMatchRes = new RoadMatchRes();
                        roadMatchRes.setId(UUID.randomUUID().toString());
                        String roadGeom = Optional.ofNullable(road.getSubGeom()).orElse(road.getGeom());
                        roadMatchRes.setRoadGeom(roadGeom);
                        roadMatchRes.setRoadDir(road.getDir());
                        roadMatchRes.setMatchRes(new BigDecimal(0));
                        roadMatchRes.setCreateTime(LocalDateTime.now());
                        roadMatchRes.setUpdateTime(LocalDateTime.now());
                        roadMatchRes.setRoadId(String.valueOf(road.getSubLinkId()));
                        roadMatchRes.setOsmId(String.valueOf(road.getLinkId()));
                        roadMatchRes.setVersion(road.getVersion());
                        roadInsertList.add(roadMatchRes);
                    }
                } else {
                    RoadMatchRes roadMatchRes = new RoadMatchRes();
                    roadMatchRes.setId(UUID.randomUUID().toString());
                    String roadGeom = Optional.ofNullable(road.getSubGeom()).orElse(road.getGeom());
                    roadMatchRes.setRoadGeom(roadGeom);
                    roadMatchRes.setRoadDir(road.getDir());
                    roadMatchRes.setMatchRes(new BigDecimal(-1));
                    roadMatchRes.setCreateTime(LocalDateTime.now());
                    roadMatchRes.setUpdateTime(LocalDateTime.now());
                    roadMatchRes.setRoadId(String.valueOf(road.getSubLinkId()));
                    roadMatchRes.setOsmId(String.valueOf(road.getLinkId()));
                    roadMatchRes.setVersion(road.getVersion());
                    roadInsertList.add(roadMatchRes);
                }
            }
            // 获取道路匹配模型一次最大持久化数据个数
            //int fieldNum = BeanUtil.beanToMap(new RoadMatchRes()).keySet().size();
            //int batchSize = 32767 / fieldNum;
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<List<RoadMatchRes>> splitList = CollUtil.splitList(roadInsertList, batchList.size());
            for (List<RoadMatchRes> resList : splitList) {
                roadMatchResMapper.insertBatch(resList);
            }
            //this.saveBatch(roadInsertList, roadBreaks.size());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("osm,here道路匹配出现异常：{}", e.getMessage());
            throw new RuntimeException();
        } finally {
            countDownLatch.countDown();
        }
    }

    private void matchAssemble(List<RoadMatchRes> roadInsertList, RoadBreak road, LinkM linkM) {
        RoadMatchRes roadMatchRes = new RoadMatchRes();
        roadMatchRes.setId(UUID.randomUUID().toString());
        roadMatchRes.setLinkId(linkM.getLinkId());
        roadMatchRes.setRoadId(String.valueOf(road.getRoadId()));
        roadMatchRes.setOsmId(String.valueOf(road.getOsmId()));
        roadMatchRes.setLinkGeom(linkM.getGeometry());
        roadMatchRes.setLinkDir(linkM.getDir());
        String roadGeom = Optional.ofNullable(road.getRoadGeom()).orElse(road.getOsmGeom());
        roadMatchRes.setRoadGeom(roadGeom);
        roadMatchRes.setRoadDir(DirEnum.getDirValueByDName(road.getOneway()));

        log.info("road wkt:" + road.getRoadGeomWkt());
        log.info("link wkt:" + linkM.getGeomwkt());
        String roadGeomWkt = Optional.ofNullable(road.getRoadGeomWkt()).orElse(road.getOsmGeomWKt());
        Double similarityRes = CommonUtils.calRoadSimilarity(roadGeomWkt, linkM.getGeomwkt());
        //System.out.println(similarityRes);
        log.info("相似度：{}", similarityRes);
        roadMatchRes.setMatchRes(new BigDecimal(similarityRes).setScale(2, BigDecimal.ROUND_HALF_UP));
        roadMatchRes.setCreateTime(LocalDateTime.now());
        roadMatchRes.setUpdateTime(LocalDateTime.now());
        roadMatchRes.setVersion(road.getVersion());
        roadInsertList.add(roadMatchRes);
    }

    private void unmatchAssemble(List<RoadMatchRes> roadInsertList, RoadBreak road, LinkM linkM) {
        RoadMatchRes roadMatchRes = new RoadMatchRes();
        roadMatchRes.setId(UUID.randomUUID().toString());
        String roadGeom = Optional.ofNullable(road.getRoadGeom()).orElse(road.getOsmGeom());
        roadMatchRes.setRoadGeom(roadGeom);
        roadMatchRes.setRoadDir(DirEnum.getDirValueByDName(road.getOneway()));
        roadMatchRes.setMatchRes(new BigDecimal(0));
        roadMatchRes.setCreateTime(LocalDateTime.now());
        roadMatchRes.setUpdateTime(LocalDateTime.now());
        roadMatchRes.setRoadId(String.valueOf(road.getRoadId()));
        roadMatchRes.setOsmId(String.valueOf(road.getOsmId()));
        roadMatchRes.setLinkId(linkM.getLinkId());
        roadMatchRes.setLinkGeom(linkM.getGeometry());
        roadMatchRes.setLinkDir(linkM.getDir());
        roadMatchRes.setVersion(road.getVersion());
        roadInsertList.add(roadMatchRes);
    }

    private void matchAssembleLink(List<RoadMatchRes> roadInsertList, LinkBreak linkBreak, LinkM linkM) {
        RoadMatchRes roadMatchRes = new RoadMatchRes();
        roadMatchRes.setId(UUID.randomUUID().toString());
        roadMatchRes.setLinkId(linkM.getLinkId());
        roadMatchRes.setRoadId(String.valueOf(linkBreak.getSubLinkId()));
        roadMatchRes.setOsmId(String.valueOf(linkBreak.getLinkId()));
        roadMatchRes.setLinkGeom(linkM.getGeometry());
        roadMatchRes.setLinkDir(linkM.getDir());
        String roadGeom = Optional.ofNullable(linkBreak.getSubGeom()).orElse(linkBreak.getGeom());
        roadMatchRes.setRoadGeom(roadGeom);
        roadMatchRes.setRoadDir(linkBreak.getDir());

        String roadGeomWkt = Optional.ofNullable(linkBreak.getSubGeomWkt()).orElse(linkBreak.getGeomwkt());
        Double similarityRes = CommonUtils.calRoadSimilarity(roadGeomWkt, linkM.getGeomwkt());
        roadMatchRes.setMatchRes(new BigDecimal(similarityRes).setScale(2, BigDecimal.ROUND_HALF_UP));
        roadMatchRes.setCreateTime(LocalDateTime.now());
        roadMatchRes.setUpdateTime(LocalDateTime.now());
        roadMatchRes.setVersion(linkBreak.getVersion());
        roadInsertList.add(roadMatchRes);
    }

    private void unmatchAssembleLink(List<RoadMatchRes> roadInsertList, LinkBreak linkBreak, LinkM linkM) {
        RoadMatchRes roadMatchRes = new RoadMatchRes();
        roadMatchRes.setId(UUID.randomUUID().toString());
        String roadGeom = Optional.ofNullable(linkBreak.getSubGeom()).orElse(linkBreak.getGeom());
        roadMatchRes.setRoadGeom(roadGeom);
        roadMatchRes.setRoadDir(linkBreak.getDir());
        roadMatchRes.setMatchRes(new BigDecimal(0));
        roadMatchRes.setCreateTime(LocalDateTime.now());
        roadMatchRes.setUpdateTime(LocalDateTime.now());
        roadMatchRes.setRoadId(String.valueOf(linkBreak.getSubLinkId()));
        roadMatchRes.setOsmId(String.valueOf(linkBreak.getLinkId()));
        roadMatchRes.setLinkId(linkM.getLinkId());
        roadMatchRes.setLinkGeom(linkM.getGeometry());
        roadMatchRes.setLinkDir(linkM.getDir());
        roadMatchRes.setVersion(linkBreak.getVersion());
        roadInsertList.add(roadMatchRes);
    }
}
