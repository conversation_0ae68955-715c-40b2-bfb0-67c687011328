package com.hll.mapdataservice.business.api.basemap.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.third.InheritIDService;
import com.hll.mapdataservice.business.third.dto.InheritIDDTO;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.mapper.NamedplcMapper;
import com.hll.mapdataservice.common.service.INamedplcService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.common.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.hll.mapdataservice.common.LangEnum;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-02
 */
@Service
@Slf4j
public class NamedplcServiceImpl extends ServiceImpl<NamedplcMapper, Namedplc> implements INamedplcService {

    @Resource
    InheritIDService inheritIDService;
    @Resource
    NamedplcMapper namedplcMapper;
    @Async("asyncTaskExecutor")
    //@Async()
    public void namedplcConvert(List<NamedplcH> namedplcHList, Boolean isCompileTransEng, String area,
                                String country, CountDownLatch countDownLatch, Boolean isMultiBatch) {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            log.info("processing country:" + CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        log.info("tmp thread is:" + Thread.currentThread().getName());
        List<Namedplc> namedplcList = new ArrayList<>();

        try {
            if (namedplcHList.size() > 0) {
                String defaultLan = LangEnum.getLangByCName(country);

                if(isMultiBatch){
//                    Map<Long, Long> idCounts = namedplcHList.stream()
//                            .collect(Collectors.groupingBy(NamedplcH::getPoiId, Collectors.counting()));
//                    List<List<NamedplcH>> duplicatedList = namedplcHList.stream()
//                            .filter(namedplcH -> idCounts.get(namedplcH.getPoiId()) > 1)
//                            .map(CollUtil::newArrayList)
//                            .collect(Collectors.toList());
                    Map<Long, List<NamedplcH>> groupedById = namedplcHList.stream()
                            .collect(Collectors.groupingBy(NamedplcH::getPoiId));

                    List<List<NamedplcH>> duplicatedList = groupedById.values().stream()
                            .filter(list -> list.size() > 1)
                            .collect(Collectors.toList());
                    log.info("isMultiBatch duplicatedList size is:" + duplicatedList.size());
                    if(duplicatedList.size() > 0){
                        for(List<NamedplcH> namedplcHList1:duplicatedList){
                            Namedplc namedplc = new Namedplc();
                            //id
                            namedplc.setId(namedplcHList1.get(0).getPoiId().toString());
                            //scource_id
                            namedplc.setSourceId(namedplcHList1.get(0).getPoiId().toString());
                            //fac_type
                            namedplc.setFacType(namedplcHList1.get(0).getFacType().toString());
                            Map<String, String> eNameMap = new HashMap<>();
                            for(NamedplcH namedplcH:namedplcHList1){
                                if("B".equals(namedplcH.getPoiNmtype()) && defaultLan.equals(namedplcH.getPoiLangcd())){
                                    //name
                                    namedplc.setName(namedplcH.getPoiName());
                                    //nm_langcd
                                    namedplc.setNmLangcd(namedplcH.getPoiLangcd());
                                }
                                //nm_en
                                if("B".equals(namedplcH.getPoiNmtype()) && "ENG".equals(namedplcH.getPoiLangcd())){
                                    namedplc.setNmEn(namedplcH.getPoiName());
                                }
                                //nm_s
                                if("S".equals(namedplcH.getPoiNmtype())){
                                    namedplc.setNmS(namedplcH.getPoiName());
                                    //nm_s_langcd
                                    namedplc.setNmSLangcd(namedplcH.getPoiLangcd());
                                }
                                //nm_e
                                if("E".equals(namedplcH.getPoiNmtype())){
                                    eNameMap.put(namedplcH.getPoiLangcd(), namedplcH.getPoiName());
                                }
                            }
                            if(eNameMap.size() > 0){
                                ObjectMapper objectMapper = new ObjectMapper();
                                String jsonString = objectMapper.writeValueAsString(eNameMap);
                                log.info("jsonString is:" + jsonString);
                                namedplc.setNmE(jsonString);
                            }
                            //population
                            namedplc.setPopulation(namedplcHList1.get(0).getPopulation().toString());
                            //capital
                            namedplc.setCapital(namedplcHList1.get(0).getCapital());
                            //claimed_by
                            namedplc.setClaimedBy(namedplcHList1.get(0).getClaimedBy());
                            //countrol_by
                            namedplc.setControlBy(namedplcHList1.get(0).getControlBy());
                            //geometry
                            namedplc.setGeometry(namedplcHList1.get(0).getGeom());
                            // UP_DATE
                            namedplc.setUpDate(LocalDateTime.now());
                            // STATUS
                            namedplc.setStatus(0);
                            // DATASOURCE
                            namedplc.setDatasource("7");

                            namedplcList.add(namedplc);
                        }
                    }
                    log.info("duplicatedList namedplcList size is:" + namedplcList.size());
                } else {
                    for (NamedplcH namedplcH : namedplcHList
                    ) {
                        Namedplc namedplc = new Namedplc();
                        //id
                        namedplc.setId(namedplcH.getPoiId().toString());
                        //scource_id
                        namedplc.setSourceId(namedplcH.getPoiId().toString());
                        //fac_type
                        namedplc.setFacType(namedplcH.getFacType().toString());
                        if("B".equals(namedplcH.getPoiNmtype()) && defaultLan.equals(namedplcH.getPoiLangcd())){
                            //name
                            namedplc.setName(namedplcH.getPoiName());
                            //nm_langcd
                            namedplc.setNmLangcd(namedplcH.getPoiLangcd());
                        }
                        //nm_en
                        if("B".equals(namedplcH.getPoiNmtype()) && "ENG".equals(namedplcH.getPoiLangcd())){
                            namedplc.setNmEn(namedplcH.getPoiName());
                        }
                        //nm_s
                        if("S".equals(namedplcH.getPoiNmtype())){
                            namedplc.setNmS(namedplcH.getPoiName());
                            //nm_s_langcd
                            namedplc.setNmSLangcd(namedplcH.getPoiLangcd());
                        }
                        //nm_e
                        if("E".equals(namedplcH.getPoiNmtype())){
                            Map<String, String> eNameMap = new HashMap<>();
                            eNameMap.put(namedplcH.getPoiLangcd(), namedplcH.getPoiName());
                            ObjectMapper objectMapper = new ObjectMapper();
                            String jsonString = objectMapper.writeValueAsString(eNameMap);
                            log.info("jsonString is:" + jsonString);
                            namedplc.setNmE(jsonString);
//                            namedplc.setNmE(namedplcH.getPoiName());
//                            //nm_s_langcd
//                            namedplc.setNmELangcd(namedplcH.getPoiLangcd());
                        }
                        //population
                        namedplc.setPopulation(namedplcH.getPopulation().toString());
                        //capital
                        namedplc.setCapital(namedplcH.getCapital());
                        //claimed_by
                        namedplc.setClaimedBy(namedplcH.getClaimedBy());
                        //countrol_by
                        namedplc.setControlBy(namedplcH.getControlBy());
                        //geometry
                        namedplc.setGeometry(namedplcH.getGeom());
                        // UP_DATE
                        namedplc.setUpDate(LocalDateTime.now());
                        // STATUS
                        namedplc.setStatus(0);
                        // DATASOURCE
                        namedplc.setDatasource("7");

                        namedplcList.add(namedplc);
                    }
                    log.info("uniqueList namedplcList size is:" + namedplcList.size());
                }


                // this.saveOrUpdateBatch(linList);
                List<List<Namedplc>> bbuildingListPartition = Lists.partition(namedplcList, 32767/BeanUtil.beanToMap(new Namedplc()).keySet().size());
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }

                for (List<Namedplc> partitionList : bbuildingListPartition) {
                    // log.info("linkMapper threadlocal info is:" + Thread.currentThread().getName());
                    // linMapper.mysqlInsertOrUpdateBath(partitionList);
                    List<Long> namedplcIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(Namedplc::getId).collect(Collectors.toList())));
                    for (int i = 0; i < partitionList.size(); i++) {
                        partitionList.get(i).setId(String.valueOf(namedplcIds.get(i)));
                    }
//                    if(isMultiBatch){
//                        this.saveOrUpdateBatch(partitionList);
//                    } else {
                        namedplcMapper.mysqlInsertOrUpdateBath(partitionList);
//                    }
                    //namedplcMapper.mysqlInsertOrUpdateBath(partitionList);
                    //this.(partitionList);
                }
            }
        } catch (Exception e) {
            log.error("building convert error,msg is {}", e);
            throw new RuntimeException("building convert error,msg is {}" + e.getMessage());
        } finally {
            countDownLatch.countDown();
        }
    }

}
