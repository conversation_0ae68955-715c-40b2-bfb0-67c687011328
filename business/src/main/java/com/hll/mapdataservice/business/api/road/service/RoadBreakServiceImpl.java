package com.hll.mapdataservice.business.api.road.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.entity.Road;
import com.hll.mapdataservice.common.entity.RoadBreak;
import com.hll.mapdataservice.common.mapper.RoadBreakMapper;
import com.hll.mapdataservice.common.service.IRoadBreakService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import com.vividsolutions.jts.geom.Geometry;
import com.vividsolutions.jts.io.ParseException;
import com.vividsolutions.jts.io.WKTReader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;

/**
 * @Author: ares.chen
 * @Since: 2021/11/26
 */
@Slf4j
@Service
public class RoadBreakServiceImpl extends ServiceImpl<RoadBreakMapper, RoadBreak> implements IRoadBreakService {

    @Resource
    RoadBreakMapper roadBreakMapper;

    @Override
    @Async("asyncTaskExecutor")
    public void batchInsertRoadBreak(List<RoadBreak> roadBreaks, CountDownLatch countDownLatch, String country, String area) {
        try {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            roadBreakMapper.batchInsertRoadBreak(roadBreaks);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    @Async("asyncTaskExecutor")
    public void roadBreak(List<Road> roads, CountDownLatch downLatch, Long version,String country,String area) throws ParseException {
        log.info("各线程截断道路数量：{}",roads.size());
        log.info("各线程截断版本号：{}",version);
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        List<RoadBreak> roadBreakList = new ArrayList<>();
        try {
            for (Road road : roads) {
                String geomWkt = road.getGeomWkt();
                Geometry roadLine = new WKTReader().read(geomWkt);
                double length = roadLine.getLength() * 111000;
                if (length > 20020) {
                    breakHandle(version, roadBreakList, road, geomWkt);
                } else {
                    RoadBreak roadBreak = new RoadBreak();
                    BeanUtil.copyProperties(road, roadBreak);
                    roadBreak.setRoadId(UUID.randomUUID().toString());
                    roadBreak.setOsmGeom(road.getGeom());
                    roadBreak.setRoadGeom(road.getGeomWkt());
                    roadBreak.setCreateTime(LocalDateTime.now());
                    roadBreak.setUpdateTime(LocalDateTime.now());
                    roadBreak.setVersion(version);
                    roadBreakList.add(roadBreak);
                }
            }
            //this.saveBatch(roadBreakList, roads.size());
            List<List<RoadBreak>> splitList = CollUtil.splitList(roadBreakList, roads.size());
            for (List<RoadBreak> roadBreaks : splitList) {
                roadBreakMapper.batchInsertRoadBreak(roadBreaks);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("道路截断出现异常：{}",e.getMessage());
        }finally {
            downLatch.countDown();
        }
    }

    private void breakHandle(Long version, List<RoadBreak> roadBreakList, Road road, String geomWkt) {
        String subStr = StrUtil.subBetween(geomWkt, "(", ")");
        if (subStr.contains("(") || subStr.contains(")")) {
            subStr = StrUtil.removeAny(subStr, "(", ")");
        }
        //MULTILINESTRING((108.19109 12.30391,108.19136 12.30389,108.19178 12.30428))
        String[] coordList = subStr.split(",");
        for (int i = 0; i < coordList.length; i++) {
            if (i == coordList.length - 1) {
                break;
            }
            RoadBreak roadBreak = new RoadBreak();
            BeanUtil.copyProperties(road, roadBreak);
            String start = coordList[i];
            String end = coordList[i + 1];

            String geoWkt = "MULTILINESTRING((" + start.split(" ")[0] + " " + start.split(" ")[1] +
                    "," + end.split(" ")[0] + " " + end.split(" ")[1] + "))";
            //try {
            //    roadBreak.setRoadGeom(new WKTReader().read(geoWkt).toString());
            //} catch (ParseException e) {
            //    e.printStackTrace();
            //}
            //try {
            //    roadBreak.setRoadGeom(new WKTReader(new GeometryFactory(new PrecisionModel(),4326)).read(geoWkt).toString());
            //} catch (ParseException e) {
            //    e.printStackTrace();
            //}
            roadBreak.setRoadGeom(geoWkt);
            roadBreak.setRoadId(UUID.randomUUID().toString());
            roadBreak.setOsmGeom(road.getGeom());
            roadBreak.setCreateTime(LocalDateTime.now());
            roadBreak.setUpdateTime(LocalDateTime.now());
            roadBreak.setVersion(version);
            roadBreakList.add(roadBreak);
        }
    }
}
