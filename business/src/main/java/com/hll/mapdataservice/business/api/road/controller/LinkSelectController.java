package com.hll.mapdataservice.business.api.road.controller;


import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hll.mapdataservice.business.api.poi.service.*;
import com.hll.mapdataservice.business.api.poi.service.dto.PoiExportInfo;
import com.hll.mapdataservice.business.api.poi.service.dto.SelectPoiObjDto;
import com.hll.mapdataservice.business.api.poi.service.dto.SelectPoiReturn;
import com.hll.mapdataservice.business.api.road.service.HerePhaStreetsServiceImpl;
import com.hll.mapdataservice.business.api.road.service.HereThaStreetsServiceImpl;
import com.hll.mapdataservice.business.api.road.service.LinkSelectServiceImpl;
import com.hll.mapdataservice.business.api.road.service.dto.SelectObjDto;
import com.hll.mapdataservice.business.api.road.service.dto.SelectReturn;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.vo.ExcelPage;
import com.hll.mapdataservice.business.vo.LinkSelectVo;
import com.hll.mapdataservice.business.vo.PoiSelectVo;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.HerePhaStreets;
import com.hll.mapdataservice.common.entity.HereThaStreets;
import com.hll.mapdataservice.common.entity.LinkSelect;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.mapper.*;
import com.hll.mapdataservice.common.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jxls.common.Context;
import org.jxls.util.JxlsHelper;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.apache.poi.hssf.usermodel.HSSFWorkbookFactory.createWorkbook;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-26
 */
@RestController
@ResponseBody
@Api(tags = "road")
@Component
@RequestMapping("/api/road/linkSelect")
//@DS("db2")
@Slf4j
public class LinkSelectController {

    @Resource
    private LinkSelectServiceImpl linkSelectService;
    @Resource
    private LinkSelectMapper linkSelectMapper;

    @Resource
    private MnrNetwGeoLinkMapper mnrNetwGeoLinkMapper;

    @Resource
    private MnrNetwGeoLinkController mnrNetwGeoLinkController;

    @Resource
    private HereThaStreetsMapper hereThaStreetsMapper;

    @Resource
    private MnrPhaNetwGeoLinkMapper mnrPhaNetwGeoLink;

    @Resource
    private HerePhaStreetsMapper herePhaStreetsMapper;

    @Resource
    private PoiSelectServiceImpl poiSelectService;

    @Resource
    private PoiSelectMapper poiSelectMapper;

    @Resource
    private MnrPoiMapper mnrPoiMapper;

    @Resource
    private MnrPoiServiceImpl mnrPoiService;

    @Resource
    private MnrPhaPoiMapper mnrPhaPoiMapper;

    @Resource
    private MnrPhaPoiServiceImpl mnrPhaPoiService;

    @Resource
    private HereThaPoiMapper hereThaPoiMapper;

    @Resource
    private HereThaPoiCopy1ServiceImpl hereThaPoiService;

    @Resource
    private HerePhaPoiMapper herePhaPoiMapper;

    @Resource
    private HerePhaPoiServiceImpl herePhaPoiService;

    @Resource
    private HerePhaPlacesServiceImpl herePhaPlacesService;

    @Resource
    private HereThaPlacesServiceImpl hereThaPlacesService;

    @Resource
    private FoursquareThaPoiMapper foursquareThaPoiMapper;

    @Resource
    private FoursquareThaPoiServiceImpl foursquareThaPoiService;

    @Resource
    private FoursquarePhaPoiMapper foursquarePhaPoiMapper;

    @Resource
    private FoursquarePhaPoiServiceImpl foursquarePhaPoiService;

    @Resource
    private ThaMgOrderMapper thaMgOrderMapper;

    @Resource
    private ThaMgOrderServiceImpl thaMgOrderService;

    @Resource
    private PhMnlOrderServiceImpl phMnlOrderService;

    @Resource
    private HereThaPointaddressServiceImpl hereThaPointaddressService;

    @Resource
    private HerePhaPointaddressServiceImpl herePhaPointaddressService;

    @Resource
    private HereThaPointaddressMapper hereThaPointaddressMapper;

    @Resource
    private HerePhaPointaddressMapper herePhaPointaddressMapper;
    @Resource
    private HereThaStreetsServiceImpl hereThaStreetsService;
    @Resource
    private HerePhaStreetsServiceImpl herePhaStreetsService;

    @ApiOperation(value = "添加link")
    @PostMapping("/add")
    public ResponseResult<Boolean> addLink(@RequestBody List<LinkSelectVo> linkSelect) {
        System.setProperty("user.timezone", "GMT+08");
        List<LinkSelect> ll = new ArrayList<>();
        for (LinkSelectVo lsv : linkSelect) {
            LinkSelect ls = new LinkSelect();
            ls.setCreateTime(LocalDateTime.now());
            ls.setLinkid(lsv.getLinkid());
            ls.setOrderID(lsv.getOrderID());
            ls.setSource(lsv.getSource());
            ls.setMapType(lsv.getMapType());
            ls.setCity(lsv.getCity());
            ls.setTrueId(lsv.getTrueId());
            //查找库中是否已经存在
            List<LinkSelect> linkSelectList = linkSelectService.lambdaQuery().eq(LinkSelect::getOrderId, lsv.getOrderID()).eq(LinkSelect::getLinkid, lsv.getLinkid())
                    .eq(LinkSelect::getSource, lsv.getSource()).eq(LinkSelect::getMapType, lsv.getMapType())
                    .eq(LinkSelect::getCity, lsv.getCity()).list();
            if (linkSelectList.size() > 0) {
                ls.setId(linkSelectList.get(0).getId());
            }

            ll.add(ls);
        }
        boolean ret = linkSelectService.saveOrUpdateBatch(ll);
        return ResponseResult.OK(ret, true);
    }

    @ApiOperation(value = "添加poi信息")
    @PostMapping("/poi/add")
    public ResponseResult<Boolean> addPoi(@RequestBody List<PoiSelectVo> poiSelectVos) {
        List<PoiSelect> ll = new ArrayList<>();
        System.setProperty("user.timezone", "GMT+08");
        for (PoiSelectVo lsv : poiSelectVos) {
            PoiSelect ls = new PoiSelect();
            ls.setCreateTime(LocalDateTime.now());
            ls.setLinkid(lsv.getLinkid());
            ls.setOrderId(lsv.getOrderID());
            ls.setSource(lsv.getSource());
            ls.setMapType(lsv.getMapType());
            ls.setTrueId(lsv.getTrueId());
            ls.setCity(lsv.getCity());

            //查找库中是否已经存在
            List<PoiSelect> poiSelectList = poiSelectService.lambdaQuery().eq(PoiSelect::getOrderId, lsv.getOrderID()).eq(PoiSelect::getLinkid, lsv.getLinkid())
                    .eq(PoiSelect::getSource, lsv.getSource()).eq(PoiSelect::getMapType, lsv.getMapType())
                    .eq(PoiSelect::getCity, lsv.getCity()).list();
            if (poiSelectList.size() > 0) {
                ls.setId(poiSelectList.get(0).getId());
            }

            ll.add(ls);
        }
        boolean ret = poiSelectService.saveOrUpdateBatch(ll);
        return ResponseResult.OK(ret, true);
    }

    @ApiOperation(value = "获取选择路线")
    @GetMapping("/getRoads")
    public ResponseResult<List<RoadSelectVo>> getRoads(@RequestParam(value = "city") String city) {
        List<RoadSelectVo> list = new ArrayList<>();
//        linkSelectService.list()
        QueryWrapper<LinkSelect> scenicFileQueryWrapper = new QueryWrapper<>();
        scenicFileQueryWrapper.ne("source", "poi");
        scenicFileQueryWrapper.eq("city", city);
        list = linkSelectMapper.getRoads(scenicFileQueryWrapper);
        return ResponseResult.OK(list, true);
    }

    @ApiOperation(value = "获取选择的点组")
    @GetMapping("/getPointsList")
    public ResponseResult<List<RoadSelectVo>> getPointsList(@RequestParam(value = "city") String city) {
        List<RoadSelectVo> list = new ArrayList<>();
//        linkSelectService.list()
        QueryWrapper<PoiSelect> scenicFileQueryWrapper = new QueryWrapper<>();
//        scenicFileQueryWrapper.eq("source", "poi");
        scenicFileQueryWrapper.eq("city", city);
        list = poiSelectMapper.getRoads(scenicFileQueryWrapper);
        return ResponseResult.OK(list, true);
    }

    @GetMapping("/getRoadsLink")
    @ApiOperation(value = "根据路线id，获得对应的路")
    public ResponseResult<SelectObjDto> getLinkInfoByRoadid(@RequestParam(value = "id") String id,
                                                            @RequestParam(value = "source", defaultValue = "") String source,
                                                            @RequestParam(value = "city") String city) {
        SelectObjDto selectObjDto = new SelectObjDto();

        SelectReturn selectReturn = new SelectReturn();
        SelectReturn hereReturn = new SelectReturn();

        List<RoadProperty> mnrList = new ArrayList<>();
        List<ManeuverVo> maneuverVoList = new ArrayList<>();
        List<RoadProperty> hereList = new ArrayList<>();

        QueryWrapper<LinkSelect> scenicFileQueryWrapper = new QueryWrapper<>();
        scenicFileQueryWrapper.eq("order_id", id);
        if (!source.isEmpty()) {
            scenicFileQueryWrapper.eq("source", source);
        }
        List<LinkSelect> ls = new ArrayList<>();
        ls = linkSelectService.list(scenicFileQueryWrapper);
        for (LinkSelect select : ls) {
            RoadProperty roadProperty = new RoadProperty();
            ManeuverVo maneuverVo = new ManeuverVo();


            if (select.getMapType().equals("tomtom")) {
                QueryWrapper<RoadProperty> scenicFileQueryWrapper1 = new QueryWrapper<>();
                scenicFileQueryWrapper1.eq("n.feat_id", UUID.fromString(select.getLinkid()));

                if (city.equals("manila")) {
                    if (select.getSource().equals("line")) {
                        roadProperty = mnrPhaNetwGeoLink.getRoadInfo(scenicFileQueryWrapper1);
                        mnrList.add(roadProperty);
                    } else if (select.getSource().equals("link")) {
                        List<LinkMainVo> linkMainVos = new ArrayList<>();
                        Maneuver maneuver = mnrPhaNetwGeoLink.getManeuverBase(scenicFileQueryWrapper1);
                        maneuverVo.setId(maneuver.getFeatId());
                        maneuverVo.setFeatType(maneuver.getFeatType());
                        linkMainVos = mnrPhaNetwGeoLink.getManeuverLink(scenicFileQueryWrapper1);
                        maneuverVo.setLinkInfo(linkMainVos);

                        maneuverVoList.add(maneuverVo);
                    }
                } else if (city.equals("mangu")) {
                    if (select.getSource().equals("line")) {
                        roadProperty = mnrNetwGeoLinkMapper.getRoadInfo(scenicFileQueryWrapper1);
                        mnrList.add(roadProperty);
                    } else if (select.getSource().equals("link")) {
                        List<LinkMainVo> linkMainVos = new ArrayList<>();
                        Maneuver maneuver = mnrNetwGeoLinkMapper.getManeuverBase(scenicFileQueryWrapper1);
                        maneuverVo.setId(maneuver.getFeatId());
                        maneuverVo.setFeatType(maneuver.getFeatType());
                        linkMainVos = mnrNetwGeoLinkMapper.getManeuverLink(scenicFileQueryWrapper1);
                        maneuverVo.setLinkInfo(linkMainVos);

                        maneuverVoList.add(maneuverVo);
                    }
                }

            } else if (select.getMapType().equals("here")) {

                if (city.equals("manila")) {
                    QueryWrapper<HerePhaStreets> scenicFileQueryWrapper1 = new QueryWrapper<>();
                    scenicFileQueryWrapper1.eq("\"LINK_ID\"", Integer.parseInt(select.getLinkid()));

                    HerePhaStreets herePhaStreets = herePhaStreetsMapper.selectOne(scenicFileQueryWrapper1);

                    RoadProperty rp = new RoadProperty();

                    rp.setFeatId(herePhaStreets.getLinkId().toString());
                    rp.setGeom(herePhaStreets.getGeometry());
                    rp.setName(herePhaStreets.getStName());
                    rp.setSpeedmaxPos(herePhaStreets.getFrSpdLim().toString());
                    rp.setRoadCondition(herePhaStreets.getPaved());
                    rp.setRoutingClass(herePhaStreets.getFuncClass());
                    rp.setNumOfLanes(herePhaStreets.getPhysLanes().toString());
                    rp.setRamp(herePhaStreets.getRamp());
                    rp.setSpeedmaxNeg(herePhaStreets.getToSpdLim().toString());
                    rp.setSimpleTrafficDirection(herePhaStreets.getDirTravel());
//                    herePhaStreets.getGeometry()
//                    rp.setCentimeters();

                    hereList.add(rp);
                } else if (city.equals("mangu")) {
                    QueryWrapper<HereThaStreets> scenicFileQueryWrapper1 = new QueryWrapper<>();
                    scenicFileQueryWrapper1.eq("\"LINK_ID\"", Integer.parseInt(select.getLinkid()));
                    HereThaStreets hereThaStreets = hereThaStreetsMapper.selectOne(scenicFileQueryWrapper1);

                    RoadProperty rp = new RoadProperty();

                    rp.setFeatId(hereThaStreets.getLinkId().toString());
                    rp.setGeom(hereThaStreets.getGeometry());
                    rp.setName(hereThaStreets.getStName());
                    rp.setSpeedmaxPos(hereThaStreets.getFrSpdLim());
                    rp.setRoadCondition(hereThaStreets.getPaved());
                    rp.setRoutingClass(hereThaStreets.getFuncClass());
                    rp.setNumOfLanes(hereThaStreets.getPhysLanes());
                    rp.setRamp(hereThaStreets.getRamp());
                    rp.setSpeedmaxNeg(hereThaStreets.getToSpdLim());
                    rp.setSimpleTrafficDirection(hereThaStreets.getDirTravel());

                    hereList.add(rp);
                }


                //HereThaStreets hereThaStreets = hereThaStreetsService.lambdaQuery().eq(HereThaStreets::getLinkId,Integer.parseInt(id)).list().get(0);

                //RoadPropertyHere rp = new RoadPropertyHere();

            }

        }
//        mnrList = linkSelectMapper.getRoadInfo(scenicFileQueryWrapper);
        selectReturn.setLine(mnrList);
        selectReturn.setLink(maneuverVoList);
        hereReturn.setLine(hereList);
        selectObjDto.setTomtomList(selectReturn);
        selectObjDto.setHereList(hereReturn);
//        linkData.setTomtomList(mnrList);
//        List<RoadProperty> osmList = new ArrayList<>();
//        linkData.setOsmList(osmList);
//        List<RoadProperty> hereList = new ArrayList<>();
//        linkData.setHereList(hereList);
        return ResponseResult.OK(selectObjDto, true);
    }

    @GetMapping("/getPoiInfo")
    @ApiOperation(value = "根据poiid，获得对应的poi信息")
    public ResponseResult<PoiInfo> getPoiAddress(@RequestParam(value = "id") String id,
                                                 @RequestParam(value = "mapType") String mapType,
                                                 @RequestParam(value = "city") String city) {
        PoiInfo poiInfo = new PoiInfo();
        if (mapType.equals("tomtom")) {
            if ("mangu".equals(city)) {
                List<MnrPoi> mnr_list = mnrPoiService.lambdaQuery().eq(MnrPoi::getFeatId, UUID.fromString(id)).list();
                MnrPoi mnrPoi = new MnrPoi();
                if (mnr_list.size() > 0) {
                    mnrPoi = mnr_list.get(0);

                    poiInfo.setId(mnrPoi.getFeatId());
                    PoiAddress poiAddress = mnrPoiMapper.getAddressInfoById(new QueryWrapper<>()
                            .eq("p.poi_address_id", UUID.fromString(mnrPoi.getPoiAddressId())));
                    poiInfo.setAddress((poiAddress.getHsn() == null ? "" : poiAddress.getHsn())
                            .concat(" ")
                            .concat(poiAddress.getStreetName() == null ? "" : poiAddress.getStreetName()));
                    poiInfo.setAddress(poiInfo.getAddress().concat(" ").concat(poiAddress.getPlaceName() == null ? "" : poiAddress.getPlaceName()));

                    poiInfo.setLon(mnrPoi.getGeom() == null ? "" : mnrPoi.getGeom().split(" ")[0].replace("POINT(", ""));
                    poiInfo.setLat(mnrPoi.getGeom() == null ? "" : mnrPoi.getGeom().split(" ")[1].replace(")", ""));
                    poiInfo.setPoiGeom(mnrPoi.getGeom());
                    poiInfo.setName(mnrPoi.getName() == null ? "" : mnrPoi.getName());
                } else {
                    List<PoiAddress> list = mnrPoiMapper.getMzAddressInfoById(new QueryWrapper<>()
                            .eq("a.feat_id", UUID.fromString(id)).isNull("ma.tokenized"));
                    poiInfo.setId(list.get(0).getId());
                    if (list.size() > 1) {
//                        poiInfo.setAddress(list.get(0).getPlaceName().concat(list.get(0).getStreetName())
//                                .concat(list.get(0).getHsn()));
//                        poiInfo.setAddress1(list.get(1).getPlaceName().concat(list.get(1).getStreetName())
//                                .concat(list.get(1).getHsn()));
                        poiInfo.setAddress((list.get(0).getHsn() == null ? "" : list.get(0).getHsn())
                                .concat(" ")
                                .concat(list.get(0).getStreetName() == null ? "" : list.get(0).getStreetName())
                                .concat(" ")
                                .concat(list.get(0).getPlaceName() == null ? "" : list.get(0).getPlaceName())
                        );
                        poiInfo.setAddress1((list.get(1).getHsn() == null ? "" : list.get(1).getHsn())
                                .concat(" ")
                                .concat(list.get(1).getStreetName() == null ? "" : list.get(1).getStreetName())
                                .concat(" ")
                                .concat(list.get(1).getPlaceName() == null ? "" : list.get(1).getPlaceName())
                        );
                    } else if (list.size() == 1) {
                        poiInfo.setAddress((list.get(0).getHsn() == null ? "" : list.get(0).getHsn())
                                .concat(" ")
                                .concat(list.get(0).getStreetName() == null ? "" : list.get(0).getStreetName())
                                .concat(" ")
                                .concat(list.get(0).getPlaceName() == null ? "" : list.get(0).getPlaceName())
                        );
                    }
                    if (list.size() > 0) {
                        poiInfo.setPoiGeom(list.get(0).getGeom());
                        poiInfo.setLon(list.get(0).getGeom().split(" ")[0].replace("POINT(", ""));
                        poiInfo.setLat(list.get(0).getGeom().split(" ")[1].replace(")", ""));
                    }
                }
            } else {
                DynamicDataSourceContextHolder.push("db6");
                MybatisPlusConfig.myTableName.set("");
                List<MnrPhaPoi> mnrPhaPois = mnrPhaPoiService.lambdaQuery().eq(MnrPhaPoi::getFeatId, UUID.fromString(id)).list();

                if (mnrPhaPois.size() > 0) {

                    MnrPhaPoi mnrPoi = mnrPhaPois.get(0);
                    poiInfo.setId(mnrPoi.getFeatId());
//                        poiInfo.setAddress(mnrPoi.getPoiAddressId());
                    PoiAddress poiAddress = mnrPhaPoiMapper.getAddressInfoById(new QueryWrapper<>()
                            .eq("p.poi_address_id", UUID.fromString(mnrPoi.getPoiAddressId())));
//                    poiInfo.setAddress(poiAddress.getPlaceName().concat(poiAddress.getStreetName()).concat(poiAddress.getHsn()));
                    poiInfo.setAddress((poiAddress.getHsn() == null ? "" : poiAddress.getHsn())
                            .concat(" ")
                            .concat(poiAddress.getStreetName() == null ? "" : poiAddress.getStreetName())
                            .concat(" ")
                            .concat(poiAddress.getPlaceName() == null ? "" : poiAddress.getPlaceName()));
                    poiInfo.setLon(mnrPoi.getGeom().split(" ")[0].replace("POINT(", ""));
                    poiInfo.setLat(mnrPoi.getGeom().split(" ")[1].replace(")", ""));
                    poiInfo.setPoiGeom(mnrPoi.getGeom());
                    poiInfo.setName(mnrPoi.getName());
                } else {
                    List<PoiAddress> list = mnrPhaPoiMapper.getMzAddressInfoById(new QueryWrapper<>()
                            .eq("a.feat_id", UUID.fromString(id)).isNull("ma.tokenized"));
                    if (list.size() > 0) {
                        poiInfo.setId(list.get(0).getId());
                        if (list.size() > 1) {

                            poiInfo.setAddress((list.get(0).getHsn() == null ? "" : list.get(0).getHsn()).concat(" ")
                                    .concat(list.get(0).getStreetName() == null ? "" : list.get(0).getStreetName())
                                    .concat(" ")
                                    .concat(list.get(0).getPlaceName() == null ? "" : list.get(0).getPlaceName())
                            );
                            poiInfo.setAddress1((list.get(1).getHsn() == null ? "" : list.get(1).getHsn())
                                    .concat(" ")
                                    .concat(list.get(1).getStreetName() == null ? "" : list.get(1).getStreetName())
                                    .concat(" ")
                                    .concat(list.get(1).getPlaceName() == null ? "" : list.get(1).getPlaceName())
                            );
                        } else if (list.size() == 1) {
                            poiInfo.setAddress((list.get(0).getHsn() == null ? "" : list.get(0).getHsn())
                                    .concat(" ")
                                    .concat(list.get(0).getStreetName() == null ? "" : list.get(0).getStreetName())
                                    .concat(" ")
                                    .concat(list.get(0).getPlaceName() == null ? "" : list.get(0).getPlaceName())
                            );
                        }
                        poiInfo.setPoiGeom(list.get(0).getGeom());
                        poiInfo.setLon(list.get(0).getGeom().split(" ")[0].replace("POINT(", ""));
                        poiInfo.setLat(list.get(0).getGeom().split(" ")[1].replace(")", ""));
                    }
                }


            }
        } else if ("here".equals(mapType)) {
            if ("mangu".equals(city)) {
//                List<HereThaPoiCopy1> hereThaPoiCopy1List = hereThaPoiService.lambdaQuery().
//                        eq(HereThaPoiCopy1::getPoiEntityId, id).list();
                List<HereThaPointaddress> hereThaPoiCopy1List = hereThaPointaddressService.lambdaQuery().
                        eq(HereThaPointaddress::getGid, Integer.parseInt(id)).list();
                if (hereThaPoiCopy1List.size() > 0) {
                    HereThaPointaddress hereThaPoi = hereThaPoiCopy1List.get(0);
                    HereThaStreets hereThaStreets = hereThaStreetsService.lambdaQuery().eq(HereThaStreets::getLinkId, Integer.parseInt(hereThaPoi.getLinkId())).list().get(0);
                    poiInfo.setId(hereThaPoi.getGid().toString());
                    log.info("gid is:" + hereThaPoi.getGid());
                    if (hereThaStreets.getStName() == null) {
                        poiInfo.setAddress("_" + hereThaPoi.getAddress());
                    } else {
                        poiInfo.setAddress(hereThaStreets.getStName() + "_" + hereThaPoi.getAddress());
                    }

                    poiInfo.setLon(hereThaPoi.getDispLon());
                    poiInfo.setLat(hereThaPoi.getDispLat());
                    poiInfo.setPoiGeom(hereThaPoi.getGeom());
                    poiInfo.setName(hereThaPoi.getAddress());
                } else {
                    HereThaPlaces hereThaPlaces = hereThaPlacesService.lambdaQuery().eq(HereThaPlaces::getId,
                            Integer.parseInt(id)).list().get(0);

                    poiInfo.setId(hereThaPlaces.getId().toString());
                    poiInfo.setAddress(hereThaPlaces.getPlaceaddress());
                    poiInfo.setLon(hereThaPlaces.getPlacelocationdisplay().split(",")[0]);
                    poiInfo.setLat(hereThaPlaces.getPlacelocationdisplay().split(",")[1]);
                    poiInfo.setPoiGeom(hereThaPlaces.getGeometry());
                    poiInfo.setName(hereThaPlaces.getPlacename());
                }

            } else if ("manila".equals(city)) {
                DynamicDataSourceContextHolder.push("db5");
                MybatisPlusConfig.myTableName.set("");

//                List<HerePhaPoi> herePhaPoiList = herePhaPoiService.lambdaQuery().
//                        eq(HerePhaPoi::getPoiEntityId, id).list();
                List<HerePhaPointaddress> herePhaPointaddresses = herePhaPointaddressService.lambdaQuery()
                        .eq(HerePhaPointaddress::getGid, Integer.parseInt(id)).list();
                if (herePhaPointaddresses.size() > 0) {
                    HerePhaPointaddress herePhaPoi = herePhaPointaddresses.get(0);
                    poiInfo.setId(herePhaPoi.getGid().toString());

                    DynamicDataSourceContextHolder.peek();
                    MybatisPlusConfig.myTableName.set("");
                    //HerePhaStreets herePhaStreets = herePhaStreetsMapper.selectById(Integer.parseInt(herePhaPoi.getGid()));
                    HerePhaStreets herePhaStreets = herePhaStreetsService.lambdaQuery().eq(HerePhaStreets::getLinkId, Integer.parseInt(herePhaPoi.getLinkId())).list().get(0);
                    poiInfo.setAddress(herePhaStreets.getStName() + "_" + herePhaPoi.getAddress());
                    poiInfo.setLon(herePhaPoi.getDispLon().toString());
                    poiInfo.setLat(herePhaPoi.getDispLat().toString());
                    poiInfo.setPoiGeom(herePhaPoi.getGeom());
                    poiInfo.setName(herePhaPoi.getAddress());
                } else {
                    HerePhaPlaces herePhaPlaces = herePhaPlacesService.lambdaQuery().eq(HerePhaPlaces::getId, Integer.parseInt(id)).list().get(0);

                    poiInfo.setId(herePhaPlaces.getId().toString());
                    poiInfo.setAddress(herePhaPlaces.getPlaceaddress());
                    poiInfo.setLon(herePhaPlaces.getPlacelocationdisplay().split(",")[0]);
                    poiInfo.setLat(herePhaPlaces.getPlacelocationdisplay().split(",")[1]);
                    poiInfo.setPoiGeom(herePhaPlaces.getGeometry());
                    poiInfo.setName(herePhaPlaces.getPlacename());
                }


            }
        } else if (mapType.equals("foursquare")) {
            if ("mangu".equals(city)) {
                FoursquareThaPoi foursquareThaPoi = foursquareThaPoiService.lambdaQuery()
                        .eq(FoursquareThaPoi::getFsqId, id).list().get(0);
                poiInfo.setId(foursquareThaPoi.getFsqId());
                poiInfo.setAddress(foursquareThaPoi.getAddress());
                poiInfo.setLon(foursquareThaPoi.getLongitude());
                poiInfo.setLat(foursquareThaPoi.getLatitude());
                poiInfo.setPoiGeom("POINT(" + foursquareThaPoi.getLongitude() + " " + foursquareThaPoi.getLatitude() + ")");
                poiInfo.setName(foursquareThaPoi.getName());

            } else if ("manila".equals(city)) {
                List<FoursquarePhaPoi> foursquarePhaPoiList = foursquarePhaPoiService.lambdaQuery().
                        eq(FoursquarePhaPoi::getFsqId, id).list();
                if(foursquarePhaPoiList.size()>0){
                    FoursquarePhaPoi foursquarePhaPoi = foursquarePhaPoiList.get(0);
                    poiInfo.setId(foursquarePhaPoi.getFsqId());
                    poiInfo.setAddress(foursquarePhaPoi.getAddress());
                    poiInfo.setLon(foursquarePhaPoi.getLongitude());
                    poiInfo.setLat(foursquarePhaPoi.getLatitude());
                    poiInfo.setPoiGeom("POINT(" + foursquarePhaPoi.getLongitude() + " " + foursquarePhaPoi.getLatitude() + ")");
                    poiInfo.setName(foursquarePhaPoi.getName());
                }
            }
        }

        return ResponseResult.OK(poiInfo, true);
    }

    @GetMapping("/getPoiAttributes")
    @ApiOperation(value = "根据POIid，获得对应的信息")
    public ResponseResult<SelectPoiObjDto> getPoiAttributesByIds(@RequestParam(value = "id") String id,
                                                                 @RequestParam(value = "source", defaultValue = "") String source) {
        SelectPoiObjDto selectPoiObjDto = new SelectPoiObjDto();

        SelectPoiReturn selectTomtomReturn = new SelectPoiReturn();
        SelectPoiReturn selectHereReturn = new SelectPoiReturn();
        SelectPoiReturn selectFoursquareReturn = new SelectPoiReturn();

        List<PoiInfo> mnrList = new ArrayList<>();
        List<PoiInfo> hereList = new ArrayList<>();
        List<PoiInfo> foursquareList = new ArrayList<>();
        List<PoiInfo> orderList = new ArrayList<>();

        QueryWrapper<PoiSelect> scenicFileQueryWrapper = new QueryWrapper<>();
        scenicFileQueryWrapper.eq("order_id", id);
        if (!source.isEmpty()) {
            scenicFileQueryWrapper.eq("source", source);
        }
        List<PoiSelect> ls = new ArrayList<>();
        ls = poiSelectService.list(scenicFileQueryWrapper);
        for (PoiSelect select : ls) {
            PoiInfo poiInfo = new PoiInfo();
            if ("mangu".equals(select.getCity())) {
                ThaMgOrder thaMgOrder = thaMgOrderService.lambdaQuery().eq(ThaMgOrder::getId, Integer.parseInt(select.getTrueId())).list().get(0);
                poiInfo.setTrueAddress(thaMgOrder.getAddressString());
                poiInfo.setTrueGeom(thaMgOrder.getGeom());
                poiInfo.setTrueId(select.getTrueId());
            } else if ("manila".equals(select.getCity())) {
                PhMnlOrder phMnlOrder = phMnlOrderService.lambdaQuery().eq(PhMnlOrder::getId, Integer.parseInt(select.getTrueId())).list().get(0);
                poiInfo.setTrueAddress(phMnlOrder.getAddressString());
                poiInfo.setTrueGeom(phMnlOrder.getGeom());
                poiInfo.setTrueId(select.getTrueId());
            }
            if (select.getMapType().equals("tomtom")) {
                if ("mangu".equals(select.getCity())) {
                    if (select.getSource().equals("poi")) {
                        List<MnrPoi> mnr_list = mnrPoiService.lambdaQuery().eq(MnrPoi::getFeatId, UUID.fromString(select.getLinkid())).list();
                        MnrPoi mnrPoi = new MnrPoi();
                        if (mnr_list.size() > 0) {
                            mnrPoi = mnr_list.get(0);

                            poiInfo.setId(mnrPoi.getFeatId());
                            PoiAddress poiAddress = mnrPoiMapper.getAddressInfoById(new QueryWrapper<>()
                                    .eq("p.poi_address_id", UUID.fromString(mnrPoi.getPoiAddressId())));
                            poiInfo.setAddress((poiAddress.getHsn() == null ? "" : poiAddress.getHsn())
                                    .concat(" ")
                                    .concat(poiAddress.getStreetName() == null ? "" : poiAddress.getStreetName()));
                            poiInfo.setAddress(poiInfo.getAddress().concat(" ").concat(poiAddress.getPlaceName() == null ? "" : poiAddress.getPlaceName()));

                            poiInfo.setLon(mnrPoi.getGeom() == null ? "" : mnrPoi.getGeom().split(" ")[0].replace("POINT(", ""));
                            poiInfo.setLat(mnrPoi.getGeom() == null ? "" : mnrPoi.getGeom().split(" ")[1].replace(")", ""));
                            poiInfo.setPoiGeom(mnrPoi.getGeom());
                            poiInfo.setName(mnrPoi.getName() == null ? "" : mnrPoi.getName());
                        } else {
                            List<PoiAddress> list = mnrPoiMapper.getMzAddressInfoById(new QueryWrapper<>()
                                    .eq("a.feat_id", UUID.fromString(select.getLinkid())).isNull("ma.tokenized"));
                            poiInfo.setId(select.getLinkid());
                            if (list.size() > 1) {
//                        poiInfo.setAddress(list.get(0).getPlaceName().concat(list.get(0).getStreetName())
//                                .concat(list.get(0).getHsn()));
//                        poiInfo.setAddress1(list.get(1).getPlaceName().concat(list.get(1).getStreetName())
//                                .concat(list.get(1).getHsn()));
                                poiInfo.setAddress((list.get(0).getHsn() == null ? "" : list.get(0).getHsn())
                                        .concat(" ")
                                        .concat(list.get(0).getStreetName() == null ? "" : list.get(0).getStreetName())
                                        .concat(" ")
                                        .concat(list.get(0).getPlaceName() == null ? "" : list.get(0).getPlaceName())
                                );
                                poiInfo.setAddress1((list.get(1).getHsn() == null ? "" : list.get(1).getHsn())
                                        .concat(" ")
                                        .concat(list.get(1).getStreetName() == null ? "" : list.get(1).getStreetName())
                                        .concat(" ")
                                        .concat(list.get(1).getPlaceName() == null ? "" : list.get(1).getPlaceName())
                                );
                            } else if (list.size() == 1) {
                                poiInfo.setAddress((list.get(0).getHsn() == null ? "" : list.get(0).getHsn())
                                        .concat(" ")
                                        .concat(list.get(0).getStreetName() == null ? "" : list.get(0).getStreetName())
                                        .concat(" ")
                                        .concat(list.get(0).getPlaceName() == null ? "" : list.get(0).getPlaceName())
                                );
                            }
                            if (list.size() > 0) {
                                poiInfo.setPoiGeom(list.get(0).getGeom());
                                poiInfo.setLon(list.get(0).getGeom().split(" ")[0].replace("POINT(", ""));
                                poiInfo.setLat(list.get(0).getGeom().split(" ")[1].replace(")", ""));
                            }
                        }
                        //MnrPoi mnrPoi = mnrPoiMapper.selectById(new QueryWrapper<>().eq("feat_id",select.getLinkid()));

                        mnrList.add(poiInfo);
                    }
                } else if ("manila".equals(select.getCity())) {
                    if (select.getSource().equals("poi")) {
                        List<MnrPhaPoi> mnrPhaPois = mnrPhaPoiService.lambdaQuery().eq(MnrPhaPoi::getFeatId, UUID.fromString(select.getLinkid())).list();

                        if (mnrPhaPois.size() > 0) {
                            MnrPhaPoi mnrPoi = mnrPhaPois.get(0);
                            poiInfo.setId(mnrPoi.getFeatId());
//                        poiInfo.setAddress(mnrPoi.getPoiAddressId());
                            PoiAddress poiAddress = mnrPhaPoiMapper.getAddressInfoById(new QueryWrapper<>()
                                    .eq("p.poi_address_id", UUID.fromString(mnrPoi.getPoiAddressId())));
//                    poiInfo.setAddress(poiAddress.getPlaceName().concat(poiAddress.getStreetName()).concat(poiAddress.getHsn()));
                            poiInfo.setAddress((poiAddress.getHsn() == null ? "" : poiAddress.getHsn())
                                    .concat(" ")
                                    .concat(poiAddress.getStreetName() == null ? "" : poiAddress.getStreetName())
                                    .concat(" ")
                                    .concat(poiAddress.getPlaceName() == null ? "" : poiAddress.getPlaceName()));
                            poiInfo.setLon(mnrPoi.getGeom().split(" ")[0].replace("POINT(", ""));
                            poiInfo.setLat(mnrPoi.getGeom().split(" ")[1].replace(")", ""));
                            poiInfo.setPoiGeom(mnrPoi.getGeom());
                            poiInfo.setName(mnrPoi.getName());
                        } else {
                            List<PoiAddress> list = mnrPhaPoiMapper.getMzAddressInfoById(new QueryWrapper<>()
                                    .eq("a.feat_id", UUID.fromString(select.getLinkid())).isNull("ma.tokenized"));
                            poiInfo.setId(select.getLinkid());
                            if (list.size() > 1) {

                                poiInfo.setAddress((list.get(0).getHsn() == null ? "" : list.get(0).getHsn()).concat(" ")
                                        .concat(list.get(0).getStreetName() == null ? "" : list.get(0).getStreetName())
                                        .concat(" ")
                                        .concat(list.get(0).getPlaceName() == null ? "" : list.get(0).getPlaceName())
                                );
                                poiInfo.setAddress1((list.get(1).getHsn() == null ? "" : list.get(1).getHsn())
                                        .concat(" ")
                                        .concat(list.get(1).getStreetName() == null ? "" : list.get(1).getStreetName())
                                        .concat(" ")
                                        .concat(list.get(1).getPlaceName() == null ? "" : list.get(1).getPlaceName())
                                );
                            } else if (list.size() == 1) {
                                poiInfo.setAddress((list.get(0).getHsn() == null ? "" : list.get(0).getHsn())
                                        .concat(" ")
                                        .concat(list.get(0).getStreetName() == null ? "" : list.get(0).getStreetName())
                                        .concat(" ")
                                        .concat(list.get(0).getPlaceName() == null ? "" : list.get(0).getPlaceName())
                                );
                            }
                            if (list.size() > 0) {
                                poiInfo.setPoiGeom(list.get(0).getGeom());
                                poiInfo.setLon(list.get(0).getGeom().split(" ")[0].replace("POINT(", ""));
                                poiInfo.setLat(list.get(0).getGeom().split(" ")[1].replace(")", ""));
                            }
                        }
                        //MnrPhaPoi mnrPhaPoi = mnrPhaPoiMapper.selectById(new QueryWrapper<>().eq("feat_id",select.getLinkid()));
//                        MnrPhaPoi mnrPhaPoi = mnrPhaPoiService.lambdaQuery().eq(MnrPhaPoi::getFeatId,UUID.fromString(select.getLinkid())).list().get(0);
//                        poiInfo.setId(mnrPhaPoi.getFeatId());
////                        poiInfo.setAddress(mnrPhaPoi.getPoiAddressId());
//                        PoiAddress poiAddress = mnrPhaPoiMapper.getAddressInfoById(new QueryWrapper<>()
//                                .eq("p.poi_address_id", UUID.fromString(mnrPhaPoi.getPoiAddressId())));
//                        poiInfo.setAddress(poiAddress.getPlaceName().concat(poiAddress.getStreetName()));
//                        poiInfo.setLon(mnrPhaPoi.getGeom().split(" ")[0].replace("POINT(",""));
//                        poiInfo.setLat(mnrPhaPoi.getGeom().split(" ")[1].replace(")",""));
//                        poiInfo.setPoiGeom(mnrPhaPoi.getGeom());
//                        poiInfo.setName(mnrPhaPoi.getName());
                        mnrList.add(poiInfo);
                    }
                }
            } else if (select.getMapType().equals("here")) {
                if ("mangu".equals(select.getCity())) {
                    if (select.getSource().equals("poi")) {
                        List<HereThaPointaddress> hereThaPoiCopy1List = hereThaPointaddressService.lambdaQuery().
                                eq(HereThaPointaddress::getGid, Integer.parseInt(select.getLinkid())).list();
                        if (hereThaPoiCopy1List.size() > 0) {
                            HereThaPointaddress hereThaPoi = hereThaPoiCopy1List.get(0);
                            //HereThaStreets hereThaStreets = hereThaStreetsMapper.selectById(Integer.parseInt(hereThaPoi.getLinkId()));
                            HereThaStreets hereThaStreets = hereThaStreetsService.lambdaQuery().eq(HereThaStreets::getLinkId, Integer.parseInt(hereThaPoi.getLinkId())).list().get(0);
                            poiInfo.setId(hereThaPoi.getGid().toString());
                            poiInfo.setAddress(hereThaStreets.getStName() + "_" + hereThaPoi.getAddress());
                            poiInfo.setLon(hereThaPoi.getDispLon().toString());
                            poiInfo.setLat(hereThaPoi.getDispLat().toString());
                            poiInfo.setPoiGeom(hereThaPoi.getGeom());
                            poiInfo.setName(hereThaPoi.getAddress());
                        } else {
                            List<HereThaPlaces> hereThaPlacesList = hereThaPlacesService.lambdaQuery().eq(HereThaPlaces::getId,
                                    Integer.parseInt(select.getLinkid())).list();
                            if (hereThaPlacesList.size() > 0) {
                                HereThaPlaces hereThaPlaces = hereThaPlacesList.get(0);
                                poiInfo.setId(hereThaPlaces.getId().toString());
                                poiInfo.setAddress(hereThaPlaces.getPlaceaddress());
                                poiInfo.setLon(hereThaPlaces.getPlacelocationdisplay().split(",")[0]);
                                poiInfo.setLat(hereThaPlaces.getPlacelocationdisplay().split(",")[1]);
                                poiInfo.setPoiGeom(hereThaPlaces.getGeometry());
                                poiInfo.setName(hereThaPlaces.getPlacename());
                            }

                        }
//                        HereThaPoiCopy1 hereThaPoi = hereThaPoiService.lambdaQuery().eq(HereThaPoiCopy1::getPoiEntityId,select.getLinkid()).list().get(0);
//                        poiInfo.setId(hereThaPoi.getPoiEntityId().toString());
//                        poiInfo.setAddress("");
//                        poiInfo.setLon(hereThaPoi.getLongitude().toString());
//                        poiInfo.setLat(hereThaPoi.getLatitude().toString());
//                        poiInfo.setPoiGeom("POINT("+hereThaPoi.getLongitude().toString()+" "+hereThaPoi.getLatitude().toString()+")");
//                        poiInfo.setName(hereThaPoi.getPoiName());
                        hereList.add(poiInfo);
                    }
                } else if ("manila".equals(select.getCity())) {
                    if (select.getSource().equals("poi")) {
                        log.info("id is:" + id, "linkId is:" + select.getLinkid());
                        List<HerePhaPointaddress> herePhaPointaddresses = herePhaPointaddressService.lambdaQuery()
                                .eq(HerePhaPointaddress::getGid, Integer.parseInt(select.getLinkid())).list();
                        if (herePhaPointaddresses.size() > 0) {
                            HerePhaPointaddress herePhaPoi = herePhaPointaddresses.get(0);
                            poiInfo.setId(herePhaPoi.getGid().toString());
                            //HerePhaStreets herePhaStreets = herePhaStreetsMapper.selectById(Integer.parseInt(herePhaPoi.getGid()));
                            HerePhaStreets herePhaStreets = herePhaStreetsService.lambdaQuery().eq(HerePhaStreets::getLinkId, Integer.parseInt(herePhaPoi.getLinkId())).list().get(0);
                            poiInfo.setAddress(herePhaStreets.getStName() + "_" + herePhaPoi.getAddress());
                            poiInfo.setLon(herePhaPoi.getDispLon().toString());
                            poiInfo.setLat(herePhaPoi.getDispLat().toString());
                            poiInfo.setPoiGeom(herePhaPoi.getGeom());
                            poiInfo.setName(herePhaPoi.getAddress());
                        } else {
                            List<HerePhaPlaces> herePhaPlacesList = herePhaPlacesService.lambdaQuery()
                                    .eq(HerePhaPlaces::getId, Integer.parseInt(select.getLinkid())).list();
                            if (herePhaPlacesList.size() > 0) {
                                HerePhaPlaces herePhaPlaces = herePhaPlacesList.get(0);
                                poiInfo.setId(herePhaPlaces.getId().toString());
                                poiInfo.setAddress(herePhaPlaces.getPlaceaddress());
                                poiInfo.setLon(herePhaPlaces.getPlacelocationdisplay().split(",")[0]);
                                poiInfo.setLat(herePhaPlaces.getPlacelocationdisplay().split(",")[1]);
                                poiInfo.setPoiGeom(herePhaPlaces.getGeometry());
                                poiInfo.setName(herePhaPlaces.getPlacename());
                            }

                        }
//                        HerePhaPoi herePhaPoi = herePhaPoiService.lambdaQuery().eq(HerePhaPoi::getPoiEntityId,select.getLinkid()).list().get(0);
//                        poiInfo.setId(herePhaPoi.getPoiEntityId().toString());
//                        poiInfo.setAddress("");
//                        poiInfo.setLon(herePhaPoi.getLongitude().toString());
//                        poiInfo.setLat(herePhaPoi.getLatitude().toString());
//                        poiInfo.setPoiGeom("POINT("+herePhaPoi.getLongitude().toString()+" "+herePhaPoi.getLatitude().toString()+")");
//                        poiInfo.setName(herePhaPoi.getPoiName());
                        hereList.add(poiInfo);
                    }
                }
            } else if (select.getMapType().equals("foursquare")) {
                if ("mangu".equals(select.getCity())) {
                    if (select.getSource().equals("poi")) {
                        FoursquareThaPoi foursquareThaPoi = foursquareThaPoiService.lambdaQuery().eq(FoursquareThaPoi::getFsqId, select.getLinkid()).list().get(0);
                        poiInfo.setId(foursquareThaPoi.getFsqId());
                        poiInfo.setAddress(foursquareThaPoi.getAddress());
                        poiInfo.setLon(foursquareThaPoi.getLongitude());
                        poiInfo.setLat(foursquareThaPoi.getLatitude());
                        poiInfo.setPoiGeom("POINT(" + foursquareThaPoi.getLongitude() + " " + foursquareThaPoi.getLatitude() + ")");
                        poiInfo.setName(foursquareThaPoi.getName());
                        foursquareList.add(poiInfo);
                    }
                } else if ("manila".equals(select.getCity())) {
                    if (select.getSource().equals("poi")) {
                        FoursquarePhaPoi foursquarePhaPoi = foursquarePhaPoiService.lambdaQuery().eq(FoursquarePhaPoi::getFsqId, select.getLinkid()).list().get(0);
                        poiInfo.setId(foursquarePhaPoi.getFsqId());
                        poiInfo.setAddress(foursquarePhaPoi.getAddress());
                        poiInfo.setLon(foursquarePhaPoi.getLongitude());
                        poiInfo.setLat(foursquarePhaPoi.getLatitude());
                        poiInfo.setPoiGeom("POINT(" + foursquarePhaPoi.getLongitude() + " " + foursquarePhaPoi.getLatitude() + ")");
                        poiInfo.setName(foursquarePhaPoi.getName());
                        foursquareList.add(poiInfo);
                    }
                }
            }

        }
//        mnrList = linkSelectMapper.getRoadInfo(scenicFileQueryWrapper);
        selectTomtomReturn.setPoiInfoList(mnrList);
        selectHereReturn.setPoiInfoList(hereList);
        selectFoursquareReturn.setPoiInfoList(foursquareList);
        selectPoiObjDto.setTomtomList(selectTomtomReturn);
        selectPoiObjDto.setHereList(selectHereReturn);
        selectPoiObjDto.setFoursquareList(selectFoursquareReturn);
//        linkData.setTomtomList(mnrList);
//        List<RoadProperty> osmList = new ArrayList<>();
//        linkData.setOsmList(osmList);
//        List<RoadProperty> hereList = new ArrayList<>();
//        linkData.setHereList(hereList);
        return ResponseResult.OK(selectPoiObjDto, true);
    }

    public ResponseResult<String> downRoad(@RequestParam(value = "ids") String ids) {

        return ResponseResult.OK("", true);
    }

    @ApiOperation(value = "保存选择结果到xls")
    @GetMapping("/saveResult2xls")
    public void saveResult2xls(@RequestParam("routeList") List<String> routeList, HttpServletResponse response) throws IOException {

        System.out.println(routeList);
        //List<Map<String, String>> roadPropertyLists = roadPropertyList;
        String filePath = "/tmp/test.xlsx";

        //添加Sheets
        List<String> sheetNames = new ArrayList<String>();

        List<RoadProperty> selectedTomtomList = new ArrayList<>();
        List<RoadProperty> selectedHereList = new ArrayList<>();
        List<ManeuverVo> selectedJointTomtomList = new ArrayList<>();
        List<ManeuverVo> selectedJointHereList = new ArrayList<>();
        for (String route : routeList) {
            System.out.println(route);
            List<LinkSelect> linkSelectList = linkSelectService.lambdaQuery().eq(LinkSelect::getOrderId, route).list();
            for (LinkSelect linkSelect : linkSelectList) {
                System.out.println(linkSelect);
                if ("line".equals(linkSelect.getSource())) {
                    if ("tomtom".equals(linkSelect.getMapType())) {
                        selectedTomtomList.add(mnrNetwGeoLinkController.getLinkInfoByRoadid(linkSelect.getLinkid(), linkSelect.getMapType(), linkSelect.getCity()).getData().getTomtomList().get(0));
                    } else if ("here".equals(linkSelect.getMapType())) {
                        selectedHereList.add(mnrNetwGeoLinkController.getLinkInfoByRoadid(linkSelect.getLinkid(), linkSelect.getMapType(), linkSelect.getCity()).getData().getHereList().get(0));
                    }
                } else if ("link".equals(linkSelect.getSource())) {
                    if ("tomtom".equals(linkSelect.getMapType())) {
                        selectedJointTomtomList.add(mnrNetwGeoLinkController.getLinkInfoByManeuverid(linkSelect.getLinkid(), linkSelect.getMapType(), linkSelect.getCity()).getData().getTomtomList().get(0));
                    } else if ("here".equals(linkSelect.getMapType())) {
                        selectedJointHereList.add(mnrNetwGeoLinkController.getLinkInfoByManeuverid(linkSelect.getLinkid(), linkSelect.getMapType(), linkSelect.getCity()).getData().getHereList().get(0));
                    }
                }
            }
        }

        //add sheet name
        sheetNames.add("tomtom_road_attributes");
        sheetNames.add("here_road_attributes");
        sheetNames.add("tomtom_roadjoints_attributes");

        List<ExcelPage> paramPages = new ArrayList<>();
        ExcelPage excelPageRoadProperty = new ExcelPage();
        excelPageRoadProperty.setPageName("tomtomroad");
        excelPageRoadProperty.setPageData(selectedTomtomList);
        ExcelPage excelPageRoadPropertyHere = new ExcelPage();
        excelPageRoadPropertyHere.setPageData(selectedHereList);
        excelPageRoadPropertyHere.setPageName("hereroad");
        ExcelPage excelPageRoadJointsPropertyTomtom = new ExcelPage();
        excelPageRoadJointsPropertyTomtom.setPageData(selectedJointTomtomList);
        excelPageRoadJointsPropertyTomtom.setPageName("tomtomjoints");
        paramPages.add(excelPageRoadProperty);
        paramPages.add(excelPageRoadPropertyHere);
        paramPages.add(excelPageRoadJointsPropertyTomtom);

        //读取模板
        InputStream in = this.getClass().getResourceAsStream("/templates/roadresults-template.xlsx");
        if (null == in) {
            log.error("can't find excel template by path:" + "/templates/roadresults-template.xlsx");
            throw new FileNotFoundException("找不到excel模板！,位置:" + "/templates/roadresults-template.xlsx");
        }

        OutputStream out = new FileOutputStream(filePath);
        Context context = new Context();
        context.putVar("pages", paramPages);
        context.putVar("sheetNames", sheetNames);
        JxlsHelper.getInstance().processTemplate(in, out, context);

        // 告诉浏览器用什么软件可以打开此文件
        response.setHeader("content-Type", "application/vnd.ms-excel");
        //返回导出数据文件
        response.setHeader("Content-Disposition", "attachment;filename=attributes.xlsx");
        // 读取文件并且输出
        FileInputStream fin = new FileInputStream(filePath);
        byte[] tempBytes = new byte[2048];
        while (fin.read(tempBytes) != -1) {
            response.getOutputStream().write(tempBytes);
        }
        response.getOutputStream().close();
//        HSSFWorkbook workBook = createWorkbook();
//        ServletOutputStream outputStream = response.getOutputStream();
//        workBook.write(outputStream);
//        outputStream.flush();
//        outputStream.close();
        //return ResponseResult.OK(out,true);
        //删除临时文件
//        File del_file = new File(filePath);
//        deleteDirectory(del_file);
    }

    @ApiOperation(value = "保存选择结果到xls")
    @GetMapping("/savePoiResult2xls")
    public void savePoiResult2xls(@RequestParam("poiOrderList") List<String> poiOrderList, HttpServletResponse response) throws IOException {

        System.out.println(poiOrderList);
        //List<Map<String, String>> roadPropertyLists = roadPropertyList;
        String filePath = "/tmp/testPoi.xlsx";

        //添加Sheets
        List<String> sheetNames = new ArrayList<String>();

        List<PoiInfo> selectedTomtomList = new ArrayList<>();
        List<PoiInfo> selectedHereList = new ArrayList<>();
        List<PoiInfo> selectedSquareList = new ArrayList<>();
        List<PoiExportInfo> poiExportInfos = new ArrayList<>();
        PoiInfo poiInfo = new PoiInfo();
        for (String poiOrder : poiOrderList) {
            System.out.println(poiOrder);

            QueryWrapper<PoiSelect> trueIdQueryWrapper = new QueryWrapper<>();
            trueIdQueryWrapper.eq("order_id", poiOrder);
            List<String> poiTrueListId = poiSelectMapper.getTrueIds(trueIdQueryWrapper);

            for (String trueId : poiTrueListId) {
                PoiExportInfo poiExportInfo = new PoiExportInfo();
                List<PoiSelect> poiSelectList = poiSelectService.lambdaQuery().eq(PoiSelect::getOrderId, poiOrder).eq(PoiSelect::getTrueId, trueId).list();
                for (PoiSelect poiSelect : poiSelectList) {
                    //ThaMgOrder thaMgOrder = thaMgOrderMapper.selectById(new QueryWrapper<>().eq("id",Integer.parseInt(poiSelect.getTrueId())));
                    if ("mangu".equals(poiSelect.getCity())) {
                        ThaMgOrder thaMgOrder = thaMgOrderService.lambdaQuery().eq(ThaMgOrder::getId, Integer.parseInt(poiSelect.getTrueId())).list().get(0);
                        poiExportInfo.setTrueAddress(thaMgOrder.getAddressString());
                        poiExportInfo.setTruePos(thaMgOrder.getGeom());
                        poiExportInfo.setTrueId(poiSelect.getTrueId());
                        poiExportInfo.setCity("mangu");
                    } else if ("manila".equals(poiSelect.getCity())) {
                        PhMnlOrder phMnlOrder = phMnlOrderService.lambdaQuery().eq(PhMnlOrder::getId, Integer.parseInt(poiSelect.getTrueId())).list().get(0);
                        poiExportInfo.setTrueAddress(phMnlOrder.getAddressString());
                        poiExportInfo.setTruePos(phMnlOrder.getGeom());
                        poiExportInfo.setTrueId(poiSelect.getTrueId());
                        poiExportInfo.setCity("manila");
                    }
                    System.out.println(poiSelect);
                    if ("poi".equals(poiSelect.getSource())) {
                        if ("tomtom".equals(poiSelect.getMapType())) {
                            if ("mangu".equals(poiSelect.getCity())) {
                                if (poiSelect.getSource().equals("poi")) {
                                    //MnrPoi mnrPoi = mnrPoiMapper.selectById(new QueryWrapper<>().eq("feat_id", poiSelect.getLinkid()));
                                    List<MnrPoi> mnrPoiList = mnrPoiService.lambdaQuery().eq(MnrPoi::getFeatId, UUID.fromString(poiSelect.getLinkid())).list();
                                    if (mnrPoiList.size() > 0) {
                                        PoiAddress poiAddress = mnrPoiMapper.getAddressInfoById(new QueryWrapper<>()
                                                .eq("p.poi_address_id", UUID.fromString(mnrPoiList.get(0).getPoiAddressId())));
                                        poiInfo.setAddress((poiAddress.getHsn() == null ? "" : poiAddress.getHsn())
                                                .concat(" ")
                                                .concat(poiAddress.getStreetName() == null ? "" : poiAddress.getStreetName()));
                                        poiInfo.setAddress(poiInfo.getAddress().concat(" ").concat(poiAddress.getPlaceName() == null ? "" : poiAddress.getPlaceName()));

                                        //poiInfo.setAddress(mnrPoiList.get(0).getPoiAddressId());
                                        poiInfo.setLon(mnrPoiList.get(0).getGeom().split(" ")[0].replace("POINT(", ""));
                                        poiInfo.setLat(mnrPoiList.get(0).getGeom().split(" ")[1].replace(")", ""));
                                        poiInfo.setName(mnrPoiList.get(0).getName());
                                        selectedTomtomList.add(poiInfo);

                                        poiExportInfo.setTomtomId(poiSelect.getLinkid());
                                        poiExportInfo.setTomtomName(mnrPoiList.get(0).getName());
                                        poiExportInfo.setTomtomAddress(poiInfo.getAddress());
                                        poiExportInfo.setTomtomPos(poiInfo.getLon() + "," + poiInfo.getLat());
                                    } else {
                                        List<PoiAddress> list = mnrPoiMapper.getMzAddressInfoById(new QueryWrapper<>()
                                                .eq("a.feat_id", UUID.fromString(poiSelect.getLinkid())).isNull("ma.tokenized"));
                                        poiInfo.setId(poiSelect.getLinkid());
                                        if (list.size() > 1) {
                                            poiInfo.setAddress(list.get(0).getHsn() == null ? "" : list.get(0).getHsn()
                                                    .concat(list.get(0).getStreetName() == null ? "" : list.get(0).getStreetName())
                                                    .concat(list.get(0).getPlaceName() == null ? "" : list.get(0).getPlaceName())
                                            );
                                            poiInfo.setAddress1(list.get(1).getHsn() == null ? "" : list.get(1).getHsn()
                                                    .concat(list.get(1).getStreetName() == null ? "" : list.get(1).getStreetName())
                                                    .concat(list.get(1).getPlaceName() == null ? "" : list.get(1).getPlaceName())
                                            );
                                            System.out.println(poiInfo.getAddress1());
                                        } else if (list.size() == 1) {
                                            poiInfo.setAddress(list.get(0).getHsn() == null ? "" : list.get(0).getHsn()
                                                    .concat(list.get(0).getStreetName() == null ? "" : list.get(0).getStreetName())
                                                    .concat(list.get(0).getPlaceName() == null ? "" : list.get(0).getPlaceName())
                                            );
                                        }
                                        if (list.size() > 0) {
                                            poiInfo.setPoiGeom(list.get(0).getGeom());
                                            poiInfo.setLon(list.get(0).getGeom().split(" ")[0].replace("POINT(", ""));
                                            poiInfo.setLat(list.get(0).getGeom().split(" ")[1].replace(")", ""));
                                        }

                                        poiExportInfo.setTomtomId(poiSelect.getLinkid());
                                        //poiExportInfo.setTomtomName(poiInfo.getName());
                                        poiExportInfo.setTomtomAddress(poiInfo.getAddress());
                                        poiExportInfo.setTomtomAddress1(poiInfo.getAddress1());
                                        poiExportInfo.setTomtomPos(poiInfo.getLon() + "," + poiInfo.getLat());
                                    }

                                }
                            } else if ("manila".equals(poiSelect.getCity())) {
                                if (poiSelect.getSource().equals("poi")) {
                                    System.out.println(poiSelect.getLinkid());
                                    //MnrPhaPoi mnrPhaPoi = mnrPhaPoiMapper.selectById(new QueryWrapper<>().eq("feat_id", poiSelect.getLinkid()));
                                    List<MnrPhaPoi> mnrPhaPoiList = mnrPhaPoiService.lambdaQuery().eq(MnrPhaPoi::getFeatId, UUID.fromString(poiSelect.getLinkid())).list();
                                    if (mnrPhaPoiList.size() > 0) {
                                        poiInfo.setId(mnrPhaPoiList.get(0).getFeatId());
                                        PoiAddress poiAddress = mnrPhaPoiMapper.getAddressInfoById(new QueryWrapper<>()
                                                .eq("p.poi_address_id", UUID.fromString(mnrPhaPoiList.get(0).getPoiAddressId())));
                                        poiInfo.setAddress((poiAddress.getHsn() == null ? "" : poiAddress.getHsn())
                                                .concat(" ")
                                                .concat(poiAddress.getStreetName() == null ? "" : poiAddress.getStreetName()));
                                        poiInfo.setAddress(poiInfo.getAddress().concat(" ").concat(poiAddress.getPlaceName() == null ? "" : poiAddress.getPlaceName()));

                                        poiInfo.setLon(mnrPhaPoiList.get(0).getGeom().split(" ")[0].replace("POINT(", ""));
                                        poiInfo.setLat(mnrPhaPoiList.get(0).getGeom().split(" ")[1].replace(")", ""));
                                        poiInfo.setName(mnrPhaPoiList.get(0).getName());
                                        selectedTomtomList.add(poiInfo);

                                        poiExportInfo.setTomtomId(poiSelect.getLinkid());
                                        poiExportInfo.setTomtomName(mnrPhaPoiList.get(0).getName());
                                        poiExportInfo.setTomtomAddress(poiInfo.getAddress());
                                        poiExportInfo.setTomtomPos(poiInfo.getLon() + "," + poiInfo.getLat());

                                    } else {
                                        List<PoiAddress> list = mnrPhaPoiMapper.getMzAddressInfoById(new QueryWrapper<>()
                                                .eq("a.feat_id", UUID.fromString(poiSelect.getLinkid())).isNull("ma.tokenized"));
                                        poiInfo.setId(poiSelect.getLinkid());
                                        if (list.size() > 1) {
                                            poiInfo.setAddress(list.get(0).getHsn() == null ? "" : list.get(0).getHsn()
                                                    .concat(list.get(0).getStreetName() == null ? "" : list.get(0).getStreetName())
                                                    .concat(list.get(0).getPlaceName() == null ? "" : list.get(0).getPlaceName())
                                            );
                                            poiInfo.setAddress1(list.get(1).getHsn() == null ? "" : list.get(1).getHsn()
                                                    .concat(list.get(1).getStreetName() == null ? "" : list.get(1).getStreetName())
                                                    .concat(list.get(1).getPlaceName() == null ? "" : list.get(1).getPlaceName())
                                            );
                                            System.out.println(poiInfo.getAddress1());
                                        } else if (list.size() == 1) {
                                            poiInfo.setAddress(list.get(0).getHsn() == null ? "" : list.get(0).getHsn()
                                                    .concat(list.get(0).getStreetName() == null ? "" : list.get(0).getStreetName())
                                                    .concat(list.get(0).getPlaceName() == null ? "" : list.get(0).getPlaceName())
                                            );
                                        }
                                        if (list.size() > 0) {
                                            poiInfo.setPoiGeom(list.get(0).getGeom());
                                            poiInfo.setLon(list.get(0).getGeom().split(" ")[0].replace("POINT(", ""));
                                            poiInfo.setLat(list.get(0).getGeom().split(" ")[1].replace(")", ""));
                                        }

                                        poiExportInfo.setTomtomId(poiSelect.getLinkid());
                                        //poiExportInfo.setTomtomName(poiInfo.getName());
                                        poiExportInfo.setTomtomAddress(poiInfo.getAddress());
                                        poiExportInfo.setTomtomAddress1(poiInfo.getAddress1());
                                        poiExportInfo.setTomtomPos(poiInfo.getLon() + "," + poiInfo.getLat());
                                    }

                                }
                            }
                        } else if ("here".equals(poiSelect.getMapType())) {
                            if ("mangu".equals(poiSelect.getCity())) {
                                if (poiSelect.getSource().equals("poi")) {
                                    List<HereThaPointaddress> hereThaPoiCopy1List = hereThaPointaddressService.lambdaQuery().
                                            eq(HereThaPointaddress::getGid, Integer.parseInt(poiSelect.getLinkid())).list();
                                    if (hereThaPoiCopy1List.size() > 0) {
                                        HereThaPointaddress hereThaPoi = hereThaPoiCopy1List.get(0);
                                        //HereThaStreets hereThaStreets = hereThaStreetsMapper.selectById(Integer.parseInt(hereThaPoi.getLinkId()) );
                                        HereThaStreets hereThaStreets = hereThaStreetsService.lambdaQuery().eq(HereThaStreets::getLinkId, Integer.parseInt(hereThaPoi.getLinkId())).list().get(0);
                                        poiInfo.setId(hereThaPoi.getGid().toString());
                                        poiInfo.setAddress(hereThaStreets.getStName() + "_" + hereThaPoi.getAddress());
                                        poiInfo.setLon(hereThaPoi.getDispLon().toString());
                                        poiInfo.setLat(hereThaPoi.getDispLat().toString());
//                                        poiInfo.setPoiGeom("POINT("+hereThaPoi.getLongitude().toString()+" "+hereThaPoi.getLatitude().toString()+")");
                                        poiInfo.setName(hereThaPoi.getAddress());

                                        poiExportInfo.setHereId(poiSelect.getLinkid());
                                        poiExportInfo.setHereName(hereThaPoi.getAddress());
                                        poiExportInfo.setHereAddress(poiInfo.getAddress());
                                        poiExportInfo.setHerePos(hereThaPoi.getDispLon() + "," + hereThaPoi.getDispLat());

                                    } else {
                                        List<HereThaPlaces> hereThaPlacesList = hereThaPlacesService.lambdaQuery().
                                                eq(HereThaPlaces::getId, Integer.parseInt(poiSelect.getLinkid())).list();

                                        if (hereThaPlacesList.size() > 0) {
                                            HereThaPlaces hereThaPlaces = hereThaPlacesList.get(0);
                                            poiInfo.setId(hereThaPlaces.getId().toString());
                                            poiInfo.setAddress(hereThaPlaces.getPlaceaddress());
                                            poiInfo.setLon(hereThaPlaces.getPlacelocationdisplay().split(",")[0]);
                                            poiInfo.setLat(hereThaPlaces.getPlacelocationdisplay().split(",")[1]);
//                                        poiInfo.setPoiGeom(hereThaPlaces.getGeometry());
                                            poiInfo.setName(hereThaPlaces.getPlacename());

                                            poiExportInfo.setHereId(poiSelect.getLinkid());
                                            poiExportInfo.setHereName(hereThaPlaces.getPlacename());
                                            poiExportInfo.setHereAddress(hereThaPlaces.getPlaceaddress());
                                            String geo = hereThaPlaces.getGeometry().replace("POINT(", "")
                                                    .replace(")", "").replace(" ", ", ");
                                            poiExportInfo.setHerePos(geo);
                                        }

                                    }

                                }
                            } else if ("manila".equals(poiSelect.getCity())) {
                                if (poiSelect.getSource().equals("poi")) {
                                    //HerePhaPoi herePhaPoi = herePhaPoiMapper.selectById(new QueryWrapper<>().eq("feat_id", poiSelect.getLinkid()));
//                                    HerePhaPoi herePhaPoi = herePhaPoiService.lambdaQuery().
//                                            eq(HerePhaPoi::getPoiEntityId, poiSelect.getLinkid()).list().get(0);
//                                    poiInfo.setId(herePhaPoi.getPoiEntityId().toString());
//                                    poiInfo.setAddress("");
//                                    poiInfo.setLon(herePhaPoi.getLongitude().toString());
//                                    poiInfo.setLat(herePhaPoi.getLatitude().toString());
//                                    poiInfo.setName(herePhaPoi.getPoiName());
//                                    selectedHereList.add(poiInfo);

//                                    poiExportInfo.setHereId(poiSelect.getLinkid());
//                                    poiExportInfo.setHereName(poiInfo.getName());
//                                    poiExportInfo.setHereAddress("");
//                                    poiExportInfo.setHerePos(poiInfo.getLon() + "," + poiInfo.getLat());

                                    List<HerePhaPointaddress> herePhaPointaddresses = herePhaPointaddressService.lambdaQuery()
                                            .eq(HerePhaPointaddress::getGid, Integer.parseInt(poiSelect.getLinkid())).list();
                                    if (herePhaPointaddresses.size() > 0) {
                                        HerePhaPointaddress herePhaPoi = herePhaPointaddresses.get(0);
                                        poiInfo.setId(herePhaPoi.getGid().toString());
                                        //HerePhaStreets herePhaStreets = herePhaStreetsMapper.selectById(Integer.parseInt(herePhaPoi.getGid()));
                                        HerePhaStreets herePhaStreets = herePhaStreetsService.lambdaQuery().eq(HerePhaStreets::getLinkId, Integer.parseInt(herePhaPoi.getLinkId())).list().get(0);
                                        poiInfo.setAddress(herePhaStreets.getStName() + "_" + herePhaPoi.getAddress());
                                        poiInfo.setLon(herePhaPoi.getDispLon().toString());
                                        poiInfo.setLat(herePhaPoi.getDispLat().toString());
                                        poiInfo.setPoiGeom(herePhaPoi.getGeom());
                                        poiInfo.setName(herePhaPoi.getAddress());


                                        poiExportInfo.setHereId(poiSelect.getLinkid());
                                        poiExportInfo.setHereName(poiInfo.getName());
                                        poiExportInfo.setHereAddress(poiInfo.getAddress());
                                        poiExportInfo.setHerePos(poiInfo.getLon() + "," + poiInfo.getLat());
                                    } else {
                                        List<HerePhaPlaces> herePhaPlacesList = herePhaPlacesService.lambdaQuery()
                                                .eq(HerePhaPlaces::getId, Integer.parseInt(poiSelect.getLinkid())).list();
                                        if (herePhaPlacesList.size() > 0) {
                                            HerePhaPlaces herePhaPlaces = herePhaPlacesList.get(0);
                                            poiInfo.setId(herePhaPlaces.getId().toString());
                                            poiInfo.setAddress(herePhaPlaces.getPlaceaddress());
                                            poiInfo.setLon(herePhaPlaces.getPlacelocationdisplay().split(",")[0]);
                                            poiInfo.setLat(herePhaPlaces.getPlacelocationdisplay().split(",")[1]);
//                                        poiInfo.setPoiGeom(herePhaPlaces.getGeometry());
                                            poiInfo.setName(herePhaPlaces.getPlacename());

                                            poiExportInfo.setHereId(poiSelect.getLinkid());
                                            poiExportInfo.setHereName(herePhaPlaces.getPlacename());
                                            poiExportInfo.setHereAddress(herePhaPlaces.getPlaceaddress());
                                            String geo = herePhaPlaces.getGeometry().replace("POINT(", "")
                                                    .replace(")", "").replace(" ", ", ");
                                            poiExportInfo.setHerePos(geo);
                                        }

                                    }
                                }
                            }
                        } else if (poiSelect.getMapType().equals("foursquare")) {
                            if ("mangu".equals(poiSelect.getCity())) {
                                if (poiSelect.getSource().equals("poi")) {
                                    //FoursquareThaPoi foursquareThaPoi = foursquareThaPoiMapper.selectById(new QueryWrapper<>().eq("feat_id", poiSelect.getLinkid()));
                                    FoursquareThaPoi foursquareThaPoi = foursquareThaPoiService.lambdaQuery().eq(FoursquareThaPoi::getFsqId, poiSelect.getLinkid()).list().get(0);
                                    poiInfo.setId(foursquareThaPoi.getFsqId());
                                    poiInfo.setAddress(foursquareThaPoi.getAddress());
                                    poiInfo.setLon(foursquareThaPoi.getLongitude());
                                    poiInfo.setLat(foursquareThaPoi.getLatitude());
                                    poiInfo.setName(foursquareThaPoi.getName());
                                    poiInfo.setFoursquareValid(foursquareThaPoi.getVenueRealityBucket());
                                    selectedSquareList.add(poiInfo);

                                    poiExportInfo.setFoursquareId(poiSelect.getLinkid());
                                    poiExportInfo.setFourSquareName(poiInfo.getName());
                                    poiExportInfo.setFourSquareAddress(poiInfo.getAddress());
                                    poiExportInfo.setFoursquarePos(poiInfo.getLon() + "," + poiInfo.getLat());
                                    poiExportInfo.setFoureaqurareValid(poiInfo.getFoursquareValid());
                                    poiExportInfo.setFoursquareexistence(foursquareThaPoi.getExistence());
                                }
                            } else if ("manila".equals(poiSelect.getCity())) {
                                if (poiSelect.getSource().equals("poi")) {
                                    //FoursquarePhaPoi foursquarePhaPoi = foursquarePhaPoiMapper.selectById(new QueryWrapper<>().eq("feat_id", poiSelect.getLinkid()));
                                    FoursquarePhaPoi foursquarePhaPoi = foursquarePhaPoiService.lambdaQuery().eq(FoursquarePhaPoi::getFsqId, poiSelect.getLinkid()).list().get(0);
                                    poiInfo.setId(foursquarePhaPoi.getFsqId());
                                    poiInfo.setAddress(foursquarePhaPoi.getAddress());
                                    poiInfo.setLon(foursquarePhaPoi.getLongitude());
                                    poiInfo.setLat(foursquarePhaPoi.getLatitude());
                                    poiInfo.setName(foursquarePhaPoi.getName());
                                    poiInfo.setFoursquareValid(foursquarePhaPoi.getVenueRealityBucket());
                                    selectedSquareList.add(poiInfo);

                                    poiExportInfo.setFoursquareId(poiSelect.getLinkid());
                                    poiExportInfo.setFourSquareName(poiInfo.getName());
                                    poiExportInfo.setFourSquareAddress(poiInfo.getAddress());
                                    poiExportInfo.setFoursquarePos(poiInfo.getLon() + "," + poiInfo.getLat());
                                    poiExportInfo.setFoureaqurareValid(foursquarePhaPoi.getVenueRealityBucket());
                                    poiExportInfo.setFoursquareexistence(foursquarePhaPoi.getExistence());
                                }
                            }
                        }
                    }
                }
                System.out.println(poiExportInfo.toString());
                poiExportInfos.add(poiExportInfo);
            }
        }
        //add sheet name
        sheetNames.add("poi_attributes");

        List<ExcelPage> paramPages = new ArrayList<>();
        ExcelPage excelPagePoiProperty = new ExcelPage();
        excelPagePoiProperty.setPageData(poiExportInfos);

        paramPages.add(excelPagePoiProperty);

        //读取模板
        InputStream in = this.getClass().getResourceAsStream("/templates/poiresults-template.xlsx");
        if (null == in) {
            log.error("can't find excel template by path:" + "/templates/poiresults-template.xlsx");
            throw new FileNotFoundException("找不到excel模板！,位置:" + "/templates/poiresults-template.xlsx");
        }

        OutputStream out = new FileOutputStream(filePath);
        Context context = new Context();
        context.putVar("pages", paramPages);
        context.putVar("sheetNames", sheetNames);
        JxlsHelper.getInstance().processTemplate(in, out, context);

        // 告诉浏览器用什么软件可以打开此文件
        response.setHeader("content-Type", "application/vnd.ms-excel");
        //返回导出数据文件
        response.setHeader("Content-Disposition", "attachment;filename=attributes.xlsx");
        // 读取文件并且输出
        FileInputStream fin = new FileInputStream(filePath);
        byte[] tempBytes = new byte[2048];
        while (fin.read(tempBytes) != -1) {
            response.getOutputStream().write(tempBytes);
        }
        response.getOutputStream().close();
//        HSSFWorkbook workBook = createWorkbook();
//        ServletOutputStream outputStream = response.getOutputStream();
//        workBook.write(outputStream);
//        outputStream.flush();
//        outputStream.close();
        //return ResponseResult.OK(out,true);
        //删除临时文件
//        File del_file = new File(filePath);
//        deleteDirectory(del_file);
    }

    public void deleteDirectory(File file) {
        if (file.isFile()) {// 表示该文件不是文件夹
            file.delete();
        } else {
            // 首先得到当前的路径
            String[] childFilePaths = file.list();
            for (String childFilePath : childFilePaths) {
                File childFile = new File(file.getAbsolutePath() + "/" + childFilePath);
                deleteDirectory(childFile);
            }
            file.delete();
        }
    }

    public void exportExcelTemplate(String sheetName, List<?> object, String templatePath, String outPutPath) throws IOException {
        //添加Sheets
        List<String> sheetNames = new ArrayList<String>();
        //add sheet name
        sheetNames.add(sheetName);

        List<ExcelPage> paramPages = new ArrayList<>();
        ExcelPage excelPageRoadProperty = new ExcelPage();
        excelPageRoadProperty.setPageData(object);

        paramPages.add(excelPageRoadProperty);

        //读取模板
        InputStream in = this.getClass().getResourceAsStream(templatePath);
        if (null == in) {
            log.error("can't find excel template by path:" + templatePath);
            throw new FileNotFoundException("找不到excel模板！,位置:" + templatePath);
        }

        OutputStream out = new FileOutputStream(outPutPath);
        Context context = new Context();
        context.putVar("pages", paramPages);
        context.putVar("sheetNames", sheetNames);
        JxlsHelper.getInstance().processTemplate(in, out, context);
    }
}