package com.hll.mapdataservice.business.api.road.controller;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.hll.mapdataservice.business.api.road.service.NodeEServiceImpl;
import com.hll.mapdataservice.business.api.road.service.OptimizedNodeMService;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.NodeE;
import com.hll.mapdataservice.common.entity.NodeM;
import com.hll.mapdataservice.common.service.INodeService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@RestController
@Slf4j
@RequestMapping("/common/node-e")
public class NodeEController {

    @Resource
    INodeService nodeService;
    @Resource
    NodeEServiceImpl nodeEService;

    @Resource
    OptimizedNodeMService optimizedNodeMService;

    @ApiOperation("map node_e diff column to node")
    @GetMapping("nodeE2node")
    public ResponseResult<Boolean> nodeE2node(@RequestParam(value = "step", required = false, defaultValue = "1") int step,
                                              @RequestParam(value = "area", required = false, defaultValue = "") String area,
                                              @RequestParam(value = "country", required = false, defaultValue = "") String country) throws InterruptedException {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        TimeInterval timer = DateUtil.timer();
        int nodeCount = nodeEService.lambdaQuery().ne(NodeE::getStatus, 1).count();
        int loop = nodeCount % step != 0 ? (nodeCount / step) + 1 : nodeCount / step;
        CountDownLatch countDownLatch = new CountDownLatch(loop);
        for (int i = 0; i < loop; i++) {
            List<NodeE> nodeList = nodeEService.lambdaQuery().ne(NodeE::getStatus, 1)
                    .orderByDesc(NodeE::getHllNodeid).last("limit " + step + " offset " + i * step).list();
            nodeEService.nodeE2node(area, country, countDownLatch, nodeList);
        }
        countDownLatch.await();
        log.info("map node_e diff column to node cost time is {}s", timer.intervalSecond());
        return ResponseResult.OK(true, true);
    }


}
