package com.hll.mapdataservice.business.api.poi.service;

import com.hll.mapdataservice.common.entity.HereThaPoiCopy1;
import com.hll.mapdataservice.common.mapper.HereThaPoiCopy1Mapper;
import com.hll.mapdataservice.common.service.IHereThaPoiCopy1Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.business.common.XmlFileUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-11
 */
@Service
//@DS("db3")
public class HereThaPoiCopy1ServiceImpl extends ServiceImpl<HereThaPoiCopy1Mapper, HereThaPoiCopy1> implements IHereThaPoiCopy1Service {

    @Async()
    public void saveHerePoi(File file){
        System.out.println("processing file:"+file.toString());
        long startTime = System.currentTimeMillis();
        List<HereThaPoiCopy1> hereThaPoiCopy1List = new XmlFileUtils().herePoiXmlReadCopy(file);
        this.saveBatch(hereThaPoiCopy1List);
        long endTime = System.currentTimeMillis();
        System.out.println("finished processing file:"+file.toString()+" cost "+(endTime-startTime)/1000+"s");
    }
}
