package com.hll.mapdataservice.business.api.road.service;

import com.hll.mapdataservice.business.common.GroupManager;
import com.hll.mapdataservice.common.entity.Zlevels;
import com.hll.mapdataservice.common.mapper.ZlevelsMapper;
import com.hll.mapdataservice.common.service.IZlevelsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-23
 */
@Service
public class ZlevelsServiceImpl extends ServiceImpl<ZlevelsMapper, Zlevels> implements IZlevelsService {

    @Resource
    private ZlevelsMapper zlevelsMapper;

    public List<Integer[]> findDuplicateGeomGroups(String area) {
        List<Map<String, Object>> result = zlevelsMapper.findDuplicateGeomWithDifferentZLevels(area);

        return result.stream()
                .map(map -> {
                    // PostgreSQL 返回的 array_agg 是 Long[] 类型
                    Integer[] gids = (Integer[]) map.get("gid_values");
                    return gids;
                })
                .collect(Collectors.toList());
    }

}
