package com.hll.mapdataservice.business.api.road.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.hll.mapdataservice.business.api.road.service.*;
import com.hll.mapdataservice.business.common.XmlFileUtils;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.mapper.RuleMMapper;
import com.hll.mapdataservice.common.utils.CommonUtils;
import com.linuxense.javadbf.DBFField;
import com.linuxense.javadbf.DBFWriter;
import com.vividsolutions.jts.io.ParseException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.CaseUtils;
import org.apache.xmlgraphics.image.loader.impl.PreloaderBMP;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

import static com.hll.mapdataservice.common.utils.DbfFileUtils.getFieldNameByTableName;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@RestController
@ResponseBody
@Api(tags = "road")
@Component
@Slf4j
@RequestMapping("/api/road/herelinkrule")
public class RuleController {
    @Resource
    CdmsServiceImpl cdmsService;
    @Resource
    RuleSw2021q133ServiceImpl ruleSw2021q133Service;
    @Resource
    RuleMServiceImpl ruleService;
    @Resource
    RuleMMapper ruleMapper;
    @Resource
    StreetsServiceImpl streetsService;
    @Resource
    LinkMServiceImpl linkMService;
    @Resource
    NodeMServiceImpl nodeMService;
    @Resource
    CdmsdtmodServiceImpl cdmsdtmodService;
    @Resource
    HerePhaStreetsServiceImpl herePhaStreetsService;
    @Resource
    RdmsServiceImpl rdmsService;

    @ApiOperation(value = "here link rule convert")
    @PostMapping("/convert")
    public ResponseResult<Boolean> hereLinkRuleConvert(@RequestParam(value = "step",
            required = false,
            defaultValue = "1") int step,
                                                       @RequestParam(value = "area",
                                                               required = false,
                                                               defaultValue = "") String area,
                                                       @RequestParam(value = "country",
                                                               required = false,
                                                               defaultValue = "") String country)
            throws SQLException, ParseException, InterruptedException {
        // MybatisPlusConfig.myTableName ="_"+area;
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }

        Set<String> nodeIdSet = Collections.synchronizedSet(new TreeSet<>());
        TimeInterval timer = DateUtil.timer();
        Integer listSize = cdmsService.lambdaQuery().count();
        log.info("The records to be transfered:" + listSize);
        int loop = listSize % step == 0 ? listSize/step : (listSize / step) + 1;
        CountDownLatch countDownLatch = new CountDownLatch(loop);

        for (int i = 0; i <loop; i++) {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<Cdms> cdmsList = cdmsService.lambdaQuery()
                    .orderByDesc(Cdms::getGid).last("limit " + step + " offset " + i * step).list();
            log.info("cdmsList size is:"+cdmsList.size());
            if (cdmsList.size() > 0) {
                cdmsService.ruleConvert(cdmsList, nodeIdSet, area, country, countDownLatch);
            }
        }
        countDownLatch.await();
        log.info("rule convert finished,country is {},area is {}", country, area);
        log.info("rule convert finished! cost time is{}s", timer.intervalSecond());

        return ResponseResult.OK(true, true);
    }

    @ApiOperation(value = "here link rule export")
    @PostMapping("/export")
    public ResponseResult<Boolean> hereLinkRuleExport(@RequestParam(value = "path",
            required = true) String path,
                                                      @RequestParam(value = "area",
                                                              required = false,
                                                              defaultValue = "") String area,
                                                      @RequestParam(value = "country",
                                                              required = false,
                                                              defaultValue = "") String country)
            throws SQLException, ParseException, InterruptedException {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        List<RuleSw2021q133> ruleSw2021q133List = ruleSw2021q133Service.list();
        log.info("rule to be exported:" + ruleSw2021q133List.size());
        // RuleSw2021q133 ruleSw2021q133List = ruleSw2021q133Service.list().get(0);
        DBFField[] fields = getFieldNameByTableName("rule");

        // 定义DBFWriter实例用来写DBF文件
        DBFWriter dbfWriter = new DBFWriter(new File(path + "/rule_" + LocalDateTime.now() + ".dbf"));

        dbfWriter.setFields(fields);

        for (RuleSw2021q133 rule : ruleSw2021q133List
        ) {
            Object[] data = new Object[fields.length];

            for (int i = 0; i < fields.length; i++) {
//                System.out.println("field name is:"+fields[i].getName()+",camel filed name is:"+
//                        CaseUtils.toCamelCase(String.valueOf(fields[i].getName()),false,'_'));
//                System.out.println("rule info is:"+JSONObject.parseObject(JSONObject.toJSONString(rule)));
                if (JSONObject.parseObject(JSONObject.toJSONString(rule))
                        .get(CaseUtils.toCamelCase(String.valueOf(fields[i].getName()), false, '_')) != null) {
                    data[i] = JSONObject.parseObject(JSONObject.toJSONString(rule))
                            .get(CaseUtils.toCamelCase(String.valueOf(fields[i].getName()), false, '_'));
                } else {
                    data[i] = null;
                }
            }
//            for (String datai:data
//                 ) {
//                System.out.println(datai);
//            }
            dbfWriter.addRecord(data);
        }

        dbfWriter.close();
        log.info("export rule finished");
        return ResponseResult.OK(true, true);
    }


    @ApiOperation("hande rule pass pass2")
    @GetMapping("handleRulePass")
    public ResponseResult<Boolean> handleRulePass(@RequestParam(value = "step", required = false, defaultValue = "1900") int step,
                                                  @RequestParam(value = "area", required = false, defaultValue = "") String area,
                                                  @RequestParam(value = "country", required = false, defaultValue = "") String country) throws InterruptedException {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        TimeInterval timer = DateUtil.timer();
        // int nodeCount = ruleService.lambdaQuery().last("where pass is not null or pass2 is not null").count();
        int nodeCount = ruleService.lambdaQuery().isNotNull(RuleM::getPass).or().isNotNull(RuleM::getPass2).count();
        int loop = nodeCount % step != 0 ? (nodeCount / step) + 1 : nodeCount / step;
        CountDownLatch countDownLatch = new CountDownLatch(loop);
        for (int i = 0; i < loop; i++) {
            // List<RuleM> ruleList = ruleMapper.selectUnhandleIds(step, i * step);
            List<RuleM> ruleList = ruleService.lambdaQuery().isNotNull(RuleM::getPass).or().isNotNull(RuleM::getPass2)
                                                           .orderByDesc(RuleM::getRuleId)
                                                           .last("limit " + step + " offset " + i * step).list();
            ruleService.handleId(area, country, countDownLatch, ruleList);
        }
        countDownLatch.await();
        log.info("handle node_rp mainnodeid subnodeid subnodeid2 to be inherited, cost time is {}s,country is {},area is {}", timer.intervalSecond(), country, area);
        return ResponseResult.OK(true, true);
    }


    @ApiOperation(value = "here link rule convert optimized - NEW OPTIMIZED VERSION")
    @PostMapping("/convert-optimized")
    public ResponseResult<Boolean> hereLinkRuleConvertOptimized(@RequestParam(value = "step",
            required = false,
            defaultValue = "0") int step,
                                                       @RequestParam(value = "area",
                                                               required = false,
                                                               defaultValue = "") String area,
                                                       @RequestParam(value = "country",
                                                               required = false,
                                                               defaultValue = "") String country)
            throws SQLException, ParseException, InterruptedException {

        log.info("Starting optimized here link rule convert process for area: {}, country: {}", area, country);
        TimeInterval timer = DateUtil.timer();

        // Configure database context
        configureDatabaseContext(area, country, true);

        // Log memory usage at start
        logMemoryUsage("Start of hereLinkRuleConvertOptimized");

        try {
            // Calculate total records and optimal batch size
            int totalRecords = cdmsService.lambdaQuery().in(Cdms::getCondType, CollUtil.newArrayList(7,39)).count();
            int optimizedStep = calculateOptimalBatchSizeForRules(totalRecords, step);

            log.info("Total CDMS records to process: {}, optimized step size: {}", totalRecords, optimizedStep);
            ConcurrentLinkedQueue<Exception> exceptions = new ConcurrentLinkedQueue<>();

            // Process data in optimized batches
            processRuleDataInOptimizedBatches(area, country, totalRecords, optimizedStep,exceptions);

            if (!exceptions.isEmpty()) {
                Exception firstException = exceptions.peek();
                log.error("Error in optimized rule convert", firstException);
                return ResponseResult.otherInfo("500", "handle " + country + " failed[rule convert]: " + firstException.getMessage(), false);
            }

            log.info("Optimized rule convert finished! cost time is {}s,country is {},area is {}", timer.intervalSecond(), country, area);
            logMemoryUsage("End of hereLinkRuleConvertOptimized");

            return ResponseResult.OK(true, true);

        } catch (Exception e) {
            log.error("Error in optimized rule convert process", e);
            return ResponseResult.OK(false, false);
        } finally {
            // Cleanup resources
            cleanupResources();
        }
    }

    /**
     * Calculate optimal batch size for rule processing based on available memory and data volume
     */
    private int calculateOptimalBatchSizeForRules(int totalRecords, int requestedStep) {
        if (requestedStep > 0) {
            return requestedStep; // Use user-specified step if provided
        }

        // Adaptive batch sizing based on data volume
        if (totalRecords <= 1000) {
            return Math.max(50, totalRecords / 10);
        } else if (totalRecords <= 10000) {
            return 200;
        } else if (totalRecords <= 100000) {
            return 500;
        } else {
            return 1000; // Large datasets
        }
    }

    /**
     * Process rule data in optimized batches with proper memory management
     */
    private void processRuleDataInOptimizedBatches(String area, String country, int totalRecords, int optimizedStep,ConcurrentLinkedQueue<Exception> exceptions)
            throws InterruptedException {

        Set<String> nodeIdSet = Collections.synchronizedSet(new TreeSet<>());
        int totalBatches = (totalRecords + optimizedStep - 1) / optimizedStep;
        CountDownLatch countDownLatch = new CountDownLatch(totalBatches);

        log.info("Processing {} total batches for rule conversion", totalBatches);

        log.info("preloading cache data......");
        configureDatabaseContext(area, country, true);
        Map<Long,HerePhaStreets> allStreetsCache = preloadStreetsData();
        Map<Integer,List<Rdms>> rdmsCache = preloadRdmsData();
        Map<Integer, List<Cdmsdtmod>> cdmsdtmodCache = preloadCdmsdtmodData();

        configureDatabaseContext(area, country, false);
        Map<String, LinkM> allLinkMCache = preloadLinkMData();
        Map<String, NodeM> allNodeMCache = preloadNodeMData();

        for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
            // Set database context for each batch
            configureDatabaseContext(area, country, true);

            int offset = batchIndex * optimizedStep;
            List<Cdms> cdmsList = cdmsService.lambdaQuery().in(Cdms::getCondType, CollUtil.newArrayList(7, 39))
                    .orderByDesc(Cdms::getGid)
                    .last("limit " + optimizedStep + " offset " + offset)
                    .list();

            log.info("Processing batch {}/{}, CDMS records: {}", batchIndex + 1, totalBatches, cdmsList.size());

            if (!cdmsList.isEmpty()) {
                // Use optimized service for better performance and memory management
                cdmsService.optimizedRuleConvert(cdmsList, nodeIdSet, area, country, countDownLatch,allStreetsCache,rdmsCache,cdmsdtmodCache,allLinkMCache,allNodeMCache,exceptions);
            } else {
                // No data in this batch, count down the latch
                countDownLatch.countDown();
            }

            // Periodic memory monitoring and cleanup
            // if (batchIndex % 10 == 0 && batchIndex > 0) {
            //     logMemoryUsage("After processing batch " + batchIndex);
            //     System.gc(); // Suggest garbage collection periodically
            // }

        }

        countDownLatch.await();

        if(allStreetsCache!=null) allStreetsCache.clear();
        if(rdmsCache!=null) rdmsCache.clear();
        if(cdmsdtmodCache!=null) cdmsdtmodCache.clear();
        if(allLinkMCache!=null) allLinkMCache.clear();
        if(allNodeMCache!=null) allNodeMCache.clear();
        log.info("All rule conversion batches completed");
    }


    /**
     * Log current memory usage for monitoring
     */
    private void logMemoryUsage(String phase) {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;

        log.info("Memory Usage - {}: Used={}MB, Free={}MB, Total={}MB, Max={}MB",
                phase,
                usedMemory / (1024 * 1024),
                freeMemory / (1024 * 1024),
                totalMemory / (1024 * 1024),
                maxMemory / (1024 * 1024));

        // Log memory usage percentage
        double memoryUsagePercent = (double) usedMemory / maxMemory * 100;
        log.info("Memory Usage Percentage - {}: {}%", phase, String.format("%.2f", memoryUsagePercent));

        // Warn if memory usage is high
        if (memoryUsagePercent > 80) {
            log.warn("High memory usage detected: {}% - Consider garbage collection", String.format("%.2f", memoryUsagePercent));
        }
    }

    /**
     * Cleanup resources and suggest garbage collection
     */
    private void cleanupResources() {
        // Clear any thread-local variables
        DynamicDataSourceContextHolder.clear();
        MybatisPlusConfig.myTableName.remove();

        // Suggest garbage collection
        System.gc();

        log.debug("Resources cleaned up and garbage collection suggested");
    }

    @ApiOperation("rule result check")
    @GetMapping("checkRuleResult")
    public ResponseResult<String> checkRuleResult(@RequestParam(value = "area", required = false, defaultValue = "") String area,
                                                  @RequestParam(value = "country", required = false, defaultValue = "") String country,
                                                  @RequestParam(value = "filePath", required = false, defaultValue = "") String filePath) throws IOException, ParseException {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        log.info("start check rule result...");
        // 1.查询rule convert编译流程的结果数量
        Integer firstConverRuleNum = cdmsService.lambdaQuery().eq(Cdms::getCondType, 7).or().eq(Cdms::getCondType, 39).count();
        log.info("firstConverRuleNum is {}", firstConverRuleNum);

        // 2.从condition_divider_rule中获取的结果
        List<RuleM> ruleList = new XmlFileUtils().dividerFileRead(filePath);
        Integer dividerRuleNum = ruleList.size();
        log.info("dividerRuleNum is {}", dividerRuleNum);

        // 3.二阶段rule编译
        List<Streets> streets = streetsService.lambdaQuery().select(Streets::getLinkId).list();
        List<Long> linkIds = streets.stream().map(Streets::getLinkId).collect(Collectors.toList());
        // ruleList为here本次street表中，进入link在condition_divider_rule中的rule
        ruleList = ruleList.stream().filter(s -> linkIds.contains(Long.parseLong(s.getInlinkId()))).collect(Collectors.toList());
        Integer needConvertRuleNum = ruleList.size();
        log.info("needConvertRuleNum is {}", needConvertRuleNum);
        Integer finalRuleNum = firstConverRuleNum + needConvertRuleNum;
        log.info("finalRuleNum is {}", finalRuleNum);

        // 4.查询编译结果
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        Integer ruleNum = ruleService.lambdaQuery().count();
        log.info("ruleNum is {}", ruleNum);
        log.info("第一阶段编译rule数量为："+firstConverRuleNum+"，第二阶段编译rule数量为："+needConvertRuleNum+"，总数量为："+finalRuleNum+"，实际编译结果数量："+ruleNum);
        return ResponseResult.OK("第一阶段编译rule数量为："+firstConverRuleNum+"，第二阶段编译数量为："+needConvertRuleNum+"，总数量为："+finalRuleNum+"，实际编译结果数量："+ruleNum, true);
    }


    /**
     * Configure database context for source or target operations
     */
    private void configureDatabaseContext(String area, String country, boolean isSource) {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, isSource));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
    }


    private Map<String, NodeM> preloadNodeMData() {
        List<NodeM> allNodeMList = nodeMService.lambdaQuery().list();
        return allNodeMList.stream()
                .collect(Collectors.toMap(NodeM::getNodeId, nodeM -> nodeM, (existing, replacement) -> existing));
    }

    private Map<String, LinkM> preloadLinkMData() {
        List<LinkM> allLinkMList = linkMService.lambdaQuery().list();
        return allLinkMList.stream()
                .collect(Collectors.toMap(LinkM::getLinkId, linkM -> linkM, (existing, replacement) -> existing));
    }


    private Map<Long, HerePhaStreets> preloadStreetsData() {
        List<HerePhaStreets> allStreetsList = herePhaStreetsService.lambdaQuery().list();
        return allStreetsList.stream()
                .collect(Collectors.toMap(HerePhaStreets::getLinkId, herePhaStreets -> herePhaStreets, (existing, replacement) -> existing));
    }

    private Map<Integer,List<Cdmsdtmod>> preloadCdmsdtmodData() {
        List<Cdmsdtmod> cdmsDtmodList = cdmsdtmodService.lambdaQuery().list();
        return cdmsDtmodList.stream()
                .collect(Collectors.groupingBy(Cdmsdtmod::getCondId));
    }

    private Map<Integer, List<Rdms>> preloadRdmsData() {

        // 1. 查询所有 Rdms 记录
        List<Rdms> rdmsList = rdmsService.lambdaQuery().list();

        // 2. 先按 condId 分组
        Map<Integer, List<Rdms>> result = rdmsList.stream()
                .collect(Collectors.groupingBy(Rdms::getCondId));

        // 3. 对每个分组内部根据 seqNumber 做升序排序
        result.values().forEach(
                list -> list.sort(Comparator.comparing(Rdms::getSeqNumber))
        );

        return result;
    }
}
