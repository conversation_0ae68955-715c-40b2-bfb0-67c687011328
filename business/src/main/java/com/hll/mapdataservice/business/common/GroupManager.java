package com.hll.mapdataservice.business.common;

import java.util.*;
import java.util.stream.Collectors;

public class GroupManager {
    // 组号到gid列表的映射
    private final Map<Integer, Set<Integer>> groupToGids = new HashMap<>();

    // gid到组号的快速查找映射
    private final Map<Integer, Integer> gidToGroup = new HashMap<>();

    private int nextGroupId = 1;

    /**
     * 添加一组gid并分配组号
     */
    public int addGroup(Integer[] gids) {
        int groupId = nextGroupId++;
        Set<Integer> gidSet = new HashSet<>(Arrays.asList(gids));

        groupToGids.put(groupId, gidSet);
        for (Integer gid : gids) {
            gidToGroup.put(gid, groupId);
        }

        return groupId;
    }

    /**
     * 根据gid查找组号
     */
    public Integer getGroupId(Integer gid) {
        return gidToGroup.get(gid);
    }

    /**
     * 获取指定组的所有gid
     */
    public Set<Integer> getGidsByGroup(Integer groupId) {
        return groupToGids.getOrDefault(groupId, Collections.emptySet());
    }

    /**
     * 获取所有分组信息
     */
    public Map<Integer, Set<Integer>> getAllGroups() {
        return new HashMap<>(groupToGids);
    }

    /**
     * 批量初始化分组数据
     */
    public void initGroups(List<Integer[]> duplicateGroups) {
        duplicateGroups.forEach(this::addGroup);
    }
}
