package com.hll.mapdataservice.business.api.road.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hll.mapdataservice.common.entity.RuleSw2021q133;
import com.hll.mapdataservice.common.mapper.RuleSw2021q133Mapper;
import com.hll.mapdataservice.common.service.IRuleSw2021q133Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-01
 */
@Service
//@DS("db8")
public class RuleSw2021q133ServiceImpl extends ServiceImpl<RuleSw2021q133Mapper, RuleSw2021q133> implements IRuleSw2021q133Service {

}
