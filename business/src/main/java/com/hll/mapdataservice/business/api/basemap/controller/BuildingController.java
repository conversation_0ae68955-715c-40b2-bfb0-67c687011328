package com.hll.mapdataservice.business.api.basemap.controller;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.hll.mapdataservice.business.api.basemap.service.BuildingServiceImpl;
import com.hll.mapdataservice.business.api.basemap.service.LandmarkServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.utils.CommonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-02
 */
@RestController
@ResponseBody
@Api(tags = "basemap")
@Component
@Slf4j
@RequestMapping("/api/basemap/building")
public class BuildingController {

    @Resource
    BuildingServiceImpl buildingService;
    @Resource
    LandmarkServiceImpl landmarkService;
    @ApiOperation(value = "here basemap building convert")
    @PostMapping("/convert")
    public ResponseResult<Boolean> hereBuildingConvert(@RequestParam(value = "step",
            required = false,
            defaultValue = "1") int step,
                                                       @RequestParam(value = "version",
                                                               required = false) String version,
                                                       @RequestParam(value = "iscompiletranseng",
                                                               required = false,
                                                               defaultValue = "false") boolean isCompileTransEng,
                                                       @RequestParam(value = "area",
                                                               required = false,
                                                               defaultValue = "") String area,
                                                       @RequestParam(value = "country",
                                                               required = false,
                                                               defaultValue = "") String country)
            throws InterruptedException, SQLException {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        //landmark
        TimeInterval timer = DateUtil.timer();

        Integer listSize = landmarkService.count();
        CountDownLatch countDownLatch = new CountDownLatch(listSize / step + 1);
        log.info("The landmark records to be transfered:" + listSize);
        for (int i = 0; i <= listSize / step; i++) {
//            if (i == 0) {
//                step = 100;
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<Landmark> landmarkList = landmarkService.lambdaQuery()
                    .orderByDesc(Landmark::getPolygonId).last("limit " + step + " offset " + i * step).list();
            log.info("process start limit " + step + " offset " + i * step);
            if (landmarkList.size() > 0) {
                // linkSw2021q133Service.linkConvert(streetsListi,nodeIdSet,nodeList);
                buildingService.buildingConvert(landmarkList,
                        isCompileTransEng, area, country, countDownLatch);
            }
//            }
        }
        log.info("here landmark convert to building cost time is {}s", timer.intervalSecond());
        countDownLatch.await();
        //log.info("here waterseg convert to herebpolygon cost time is {}s", timer.intervalSecond());

        return ResponseResult.OK(true, true);
    }

}
