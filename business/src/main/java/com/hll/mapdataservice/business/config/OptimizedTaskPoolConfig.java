package com.hll.mapdataservice.business.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * Optimized thread pool configuration for variable data volumes
 * Provides better performance for both small and large datasets
 * 
 * <AUTHOR> Version
 * @date 2024
 */
@Configuration
@Slf4j
public class OptimizedTaskPoolConfig {

    // System resource detection
    private static final int CPU_COUNT = Runtime.getRuntime().availableProcessors();
    private static final long MAX_MEMORY = Runtime.getRuntime().maxMemory();
    private static final long AVAILABLE_MEMORY = Runtime.getRuntime().freeMemory();
    
    /**
     * Optimized thread pool for I/O intensive operations (like database and file operations)
     * Uses higher thread count since these operations are mostly waiting
     */
    @Bean("optimizedAsyncTaskExecutor")
    public Executor getOptimizedAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // For I/O intensive operations, we can use more threads than CPU cores
        int corePoolSize = Math.max(4, CPU_COUNT);
        int maxPoolSize = Math.max(16, CPU_COUNT * 4); // Higher for I/O operations
        int queueCapacity = calculateOptimalQueueCapacity();
        
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setThreadNamePrefix("optimized-task-");
        executor.setKeepAliveSeconds(60); // Longer keep-alive for variable workloads
        
        // Use DiscardPolicy to prevent web server threads from executing heavy tasks
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());
        
        // Allow core threads to timeout when idle
        executor.setAllowCoreThreadTimeOut(true);
        
        executor.initialize();
        
        log.info("Optimized Thread Pool Configuration:");
        log.info("  CPU Count: {}", CPU_COUNT);
        log.info("  Max Memory: {} MB", MAX_MEMORY / (1024 * 1024));
        log.info("  Core Pool Size: {}", corePoolSize);
        log.info("  Max Pool Size: {}", maxPoolSize);
        log.info("  Queue Capacity: {}", queueCapacity);
        
        return executor;
    }
    
    /**
     * Adaptive thread pool that adjusts based on data volume
     * For processing large datasets with memory constraints
     */
    @Bean("adaptiveAsyncTaskExecutor")
    public Executor getAdaptiveAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // Conservative settings for memory-intensive operations
        int corePoolSize = Math.max(2, CPU_COUNT / 2);
        int maxPoolSize = Math.max(4, CPU_COUNT);
        int queueCapacity = 50; // Moderate queue to balance throughput and memory
        
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setThreadNamePrefix("adaptive-task-");
        executor.setKeepAliveSeconds(30);
        
        // Use AbortPolicy to fail fast when system is overloaded
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        
        executor.initialize();
        
        log.info("Adaptive Thread Pool Configuration:");
        log.info("  Core Pool Size: {}", corePoolSize);
        log.info("  Max Pool Size: {}", maxPoolSize);
        log.info("  Queue Capacity: {}", queueCapacity);
        
        return executor;
    }
    
    /**
     * Calculate optimal queue capacity based on available memory
     */
    private int calculateOptimalQueueCapacity() {
        long availableMemoryMB = AVAILABLE_MEMORY / (1024 * 1024);
        
        if (availableMemoryMB > 2048) { // > 2GB available
            return 500; // Increased for high-volume batch processing
        } else if (availableMemoryMB > 1024) { // > 1GB available
            return 50;
        } else if (availableMemoryMB > 512) { // > 512MB available
            return 25;
        } else {
            return 10; // Conservative for low memory
        }
    }
    
    /**
     * Monitor thread pool performance and adjust if needed
     */
    @Bean("threadPoolMonitor")
    public ThreadPoolMonitor getThreadPoolMonitor() {
        return new ThreadPoolMonitor();
    }
    
    /**
     * Simple thread pool monitoring utility
     */
    public static class ThreadPoolMonitor {
        
        public void logThreadPoolStats(ThreadPoolTaskExecutor executor, String poolName) {
            if (executor != null && executor.getThreadPoolExecutor() != null) {
                ThreadPoolExecutor tpe = executor.getThreadPoolExecutor();
                log.info("{} Thread Pool Stats:", poolName);
                log.info("  Active Threads: {}", tpe.getActiveCount());
                log.info("  Pool Size: {}", tpe.getPoolSize());
                log.info("  Queue Size: {}", tpe.getQueue().size());
                log.info("  Completed Tasks: {}", tpe.getCompletedTaskCount());
                log.info("  Total Tasks: {}", tpe.getTaskCount());
            }
        }
        
        // public boolean isThreadPoolHealthy(ThreadPoolTaskExecutor executor) {
        //     if (executor == null || executor.getThreadPoolExecutor() == null) {
        //         return false;
        //     }
        //
        //     ThreadPoolExecutor tpe = executor.getThreadPoolExecutor();
        //
        //     // Check if queue is not overwhelmed (< 80% capacity)
        //     double queueUtilization = (double) tpe.getQueue().size() / executor.getQueueCapacity();
        //
        //     // Check if thread pool is not maxed out
        //     double poolUtilization = (double) tpe.getPoolSize() / executor.getMaxPoolSize();
        //
        //     return queueUtilization < 0.8 && poolUtilization < 0.9;
        // }
    }
}
