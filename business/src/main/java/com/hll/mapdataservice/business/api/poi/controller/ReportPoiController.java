package com.hll.mapdataservice.business.api.poi.controller;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hll.mapdataservice.business.api.poi.service.ReportPoiServiceImpl;
import com.hll.mapdataservice.common.entity.ReportPoi;
import com.hll.mapdataservice.common.mapper.ReportPoiMapper;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-19
 */
@RestController
@ResponseBody
@Api(tags ="poi")
@RequestMapping("/api/poi")
public class ReportPoiController {

    @Resource
    private ReportPoiMapper reportPoiMapper;

    @Resource
    private ReportPoiServiceImpl reportPoiService;

    @GetMapping("/reportPoi")
    @ApiOperation(value = "reportPoi")
    //@DS("db1")
    public IPage<ReportPoi> listReportPoi() {
        IPage<ReportPoi> reportPoiPage = new Page<>(1, 3);//参数一是当前页，参数二是每页个数

        reportPoiPage = reportPoiMapper.selectPage(reportPoiPage, Wrappers.<ReportPoi>lambdaQuery().eq(ReportPoi::getCity, "深圳").orderByDesc(ReportPoi::getCreateTime));
        //reportPoiService.lambdaUpdate().set(ReportPoi::getCity,"").update();
        return reportPoiPage;
    }

//    public List<ReportPoi> listReportPoi() {
//        return reportPoiService.list();
//    }
}

