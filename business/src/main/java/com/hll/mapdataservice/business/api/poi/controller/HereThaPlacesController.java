package com.hll.mapdataservice.business.api.poi.controller;


import com.hll.mapdataservice.business.api.poi.service.HereThaPlacesServiceImpl;
import com.hll.mapdataservice.common.entity.HereThaPlaces;
import com.hll.mapdataservice.common.mapper.HereThaPlacesMapper;
import com.hll.mapdataservice.business.common.XmlFileUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-06
 */
@RestController
@ResponseBody
@Api(tags ="poi")
@RequestMapping("/api/poi/hereThaPlaces")
@Component
@Slf4j
public class HereThaPlacesController {

    @Resource
    HereThaPlacesMapper hereThaPlacesMapper;
    @Resource
    HereThaPlacesServiceImpl hereThaPlacesService;

    @GetMapping("/importPlaces")
    @ApiOperation(value = "importPlaces")
    //@DS("db3")
    public void importPlaces(@RequestParam String filePath) throws Exception{
        log.info("folder path is:"+filePath);
        System.out.println("folder path is:"+filePath);
        File filePaths = new File(filePath);
        File[] files = filePaths.listFiles(file->file.getName().toLowerCase().endsWith(".xml"));
        //按文件名排序
        List fileList = Arrays.asList(files);
        Collections.sort(fileList, new Comparator<File>() {
            @Override
            public int compare(File o1, File o2) {
                if (o1.isDirectory() && o2.isFile()) {
                    return -1;
                }
                if (o1.isFile() && o2.isDirectory()) {
                    return 1;
                }
                return o1.getName().compareTo(o2.getName());
            }
        });

        for(File file:files){
            //hereThaPoiService.saveHerePoi(file);
            //hereThaPoiListAll.addAll(new XmlFileUtils().herePoiXmlRead(file));
            System.out.println("processing file:"+file.toString());
            log.info("processing file:"+file.toString());
            long startTime = System.currentTimeMillis();
            List<HereThaPlaces> hereThaPlacesList = new XmlFileUtils().hereThaPlaceXmlRead(file.toString());
//        for (HereThaPlaces hereThaPlaces: hereThaPlacesList
//             ) {
//            System.out.println(hereThaPlaces.toString());
//        }
            hereThaPlacesService.saveBatch(hereThaPlacesList);
            long endTime = System.currentTimeMillis();
            System.out.println("finished processing file:"+file.toString()+" cost "+(endTime-startTime)/1000+"s");
            log.info("finished processing file:"+file.toString()+" cost "+(endTime-startTime)/1000+"s");
        }
    }
}