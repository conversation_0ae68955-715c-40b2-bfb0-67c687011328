// package com.hll.mapdataservice.business.config;
//
// import org.springframework.boot.CommandLineRunner;
// import org.springframework.stereotype.Component;
// import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;
//
// import java.util.Map;
//
// @Component
// public class UrlsPrinter implements CommandLineRunner {
//
//     private final RequestMappingHandlerMapping handlerMapping;
//
//     public UrlsPrinter(RequestMappingHandlerMapping handlerMapping) {
//         this.handlerMapping = handlerMapping;
//     }
//
//     @Override
//     public void run(String... args) throws Exception {
//         handlerMapping.getHandlerMethods()
//             .forEach((key, value) -> System.out.println(key.getPatternsCondition().getPatterns()));
//     }
// }
