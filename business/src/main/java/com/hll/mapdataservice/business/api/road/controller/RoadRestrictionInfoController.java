package com.hll.mapdataservice.business.api.road.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.RoadRestrictionInfo;
import com.hll.mapdataservice.common.service.IRoadRestrictionInfoService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: ares.chen
 * @Since: 2022/5/23
 */
@RestController
@RequestMapping("roadRestriction")
public class RoadRestrictionInfoController {

    @Resource
    IRoadRestrictionInfoService roadRestrictionInfoService;

    @GetMapping("supplement")
    public ResponseResult<String> supplementRoadRestriction(String country, String filePath) {

        // 1. 切换数据源
        DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        try {
            int res = roadRestrictionInfoService.supplementSegmentIds(filePath);
            if (res == 0) {
                return ResponseResult.otherInfo("500", "excel数据未全部处理完！", false);
            }
        } catch (Exception e) {
            return ResponseResult.otherInfo("500", e.getMessage(),false);
        }

        // 2. 导出结果数据至excel
        List<RoadRestrictionInfo> finalList = roadRestrictionInfoService.list();
        // excel 导出路径
        String resExportPath = StrUtil.sub(filePath,0,filePath.lastIndexOf("/")+1);
        String fileName = resExportPath+"road_restriction_info"+System.currentTimeMillis()+".xlsx";
        EasyExcel.write(fileName,RoadRestrictionInfo.class).sheet().doWrite(finalList);
        return ResponseResult.otherInfo("200", "excel数据处理完成！导出路径"+resExportPath,true);
    }
}
