package com.hll.mapdataservice.business.api.road.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.third.InheritIDService;
import com.hll.mapdataservice.business.third.dto.InheritIDDTO;
import com.hll.mapdataservice.common.entity.RuleM;
import com.hll.mapdataservice.common.mapper.RuleMMapper;
import com.hll.mapdataservice.common.service.IRuleMService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.common.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-28
 */
@Slf4j
@Service
public class RuleMServiceImpl extends ServiceImpl<RuleMMapper, RuleM> implements IRuleMService {

    @Resource
    RuleMMapper ruleMapper;
    @Resource
    InheritIDService inheritIDService;
    @Override
    @Async("asyncTaskExecutor")
    public void handleId(String area, String country, CountDownLatch countDownLatch, List<RuleM> ruleList) {
        try {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            int fieldNum = BeanUtil.beanToMap(new RuleM()).keySet().size();
            int batchSize = 32767 / fieldNum;
            log.info("batchInsert size is {}", batchSize);
            List<List<RuleM>> splitList = ListUtil.split(ruleList, batchSize);
            for (List<RuleM> rules : splitList) {
                List<RuleM> resRuleList = new ArrayList<>();
                for (RuleM rule : rules) {
                    // 处理 pass
                    if (StrUtil.isNotEmpty(rule.getPass())) {
                        if (rule.getPass().contains("|")) {
                            List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(rule.getPass().split("\\|"))));
                            String resPass = CollUtil.join(inheritID, "|");
                            rule.setPass(resPass);
                        } else if (rule.getPass().length() < 18) {
                            rule.setPass(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(rule.getPass()))).get(0)));
                        }
                    } else {
                        rule.setPass(rule.getPass());
                    }
                    // 处理 pass2
                    if (StrUtil.isNotEmpty(rule.getPass2())) {
                        if (rule.getPass2().contains("|")) {
                            List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(rule.getPass2().split("\\|"))));
                            String resPass2 = CollUtil.join(inheritID, "|");
                            rule.setPass2(resPass2);
                        } else if (rule.getPass2().length() < 18) {
                            rule.setPass2(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(rule.getPass2()))).get(0)));
                        }
                    } else {
                        rule.setPass2(rule.getPass2());
                    }

                    resRuleList.add(rule);
                }
                // log.info("insert datasource is {}", DynamicDataSourceContextHolder.peek());
                ruleMapper.mysqlInsertOrUpdateBath(resRuleList);
            }
        } catch (Exception e) {
            log.error("handle rule inherited id(pass,pass2) error,detail is {}", e.getMessage());
            throw e;
        } finally {
            countDownLatch.countDown();
        }
    }
}
