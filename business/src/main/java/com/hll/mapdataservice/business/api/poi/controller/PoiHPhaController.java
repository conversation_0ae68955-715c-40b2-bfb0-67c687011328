package com.hll.mapdataservice.business.api.poi.controller;


import com.hll.mapdataservice.business.api.poi.service.MtdareaServiceImpl;
import com.hll.mapdataservice.business.api.poi.service.PoiMServiceImpl;
import com.hll.mapdataservice.business.api.road.service.StreetsServiceImpl;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.mapper.PoiMMapper;
import com.hll.mapdataservice.business.common.XmlFileUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-31
 */
@RestController
@ResponseBody
@Api(tags ="poi")
@Component
@Slf4j
@RequestMapping("/api/poi/poiHPha")
public class PoiHPhaController {

    @Resource
    PoiMServiceImpl poiMService;
    @Resource
    PoiMMapper poiMMapper;
    @Resource
    MtdareaServiceImpl mtdareaService;
    @Resource
    StreetsServiceImpl streetsService;

    @PostMapping("/importHerePoi")
    @ApiOperation(value = "importHerePoi")
    //@DS("db3")
    public void importPlaces(@RequestParam String filePath,
                             @RequestParam(value = "step",
                                     required = false,
                                     defaultValue = "300") int step,
                             @RequestParam(value = "create",
                                     required = false,
                                     defaultValue = "true") boolean create) throws Exception{
        log.info("folder path is:"+filePath);
        System.out.println("folder path is:"+filePath);
        File filePaths = new File(filePath);
        File[] files = filePaths.listFiles(file->file.getName().toLowerCase().endsWith(".xml"));
        //按文件名排序
        List fileList = Arrays.asList(files);
        Collections.sort(fileList, new Comparator<File>() {
            @Override
            public int compare(File o1, File o2) {
                if (o1.isDirectory() && o2.isFile()) {
                    return -1;
                }
                if (o1.isFile() && o2.isDirectory()) {
                    return 1;
                }
                return o1.getName().compareTo(o2.getName());
            }
        });
        //mtdAreaList
        List<Mtdarea> mtdareaList = mtdareaService.lambdaQuery().eq(Mtdarea::getAreaType,"B").list();
        Map<String,String> sourceIdPoiIdMap= new HashMap<>();
        if(!create){
            //map poiId and sourceid
            List<PoiM> poiList = poiMService.list();
            log.info("poiHphaList size is:"+poiList.size());
            sourceIdPoiIdMap=poiList.stream().collect(Collectors.toMap(PoiM::getSourceId,PoiM::getPoiId));
            log.info("sourceIdPoiIdMap size is:"+sourceIdPoiIdMap.size());
        }
        //linkAreaMap
        Map<Long,Long> linkIdLAreaIdMap = new HashMap<>();
        Map<Long,Long> linkIdRAreaIdMap = new HashMap<>();
        List<Streets> streetsList =streetsService.lambdaQuery().select(Streets::getLinkId,Streets::getlAreaId,Streets::getrAreaId).list();
        linkIdLAreaIdMap = streetsList.stream().collect(Collectors.toMap(Streets::getLinkId,Streets::getlAreaId));
        linkIdRAreaIdMap = streetsList.stream().collect(Collectors.toMap(Streets::getLinkId,Streets::getrAreaId));
        for(File file:files){
            //hereThaPoiService.saveHerePoi(file);
            //hereThaPoiListAll.addAll(new XmlFileUtils().herePoiXmlRead(file));
            System.out.println("processing file:"+file.toString());
            log.info("processing file:"+file.toString());
            long startTime = System.currentTimeMillis();
            List<PoiM> poiList = new ArrayList<>();
            if(create){
                poiList = new XmlFileUtils().poiHphaXmlRead(file.toString(),mtdareaList,linkIdLAreaIdMap,linkIdRAreaIdMap,true,null,null);
            } else {
                poiList = new XmlFileUtils().poiHphaXmlRead(file.toString(),mtdareaList,linkIdLAreaIdMap,linkIdRAreaIdMap,false,sourceIdPoiIdMap,null);
            }

            log.info("read file {} finished;Start saving poiInfo,poiInfo size is:{}",file.toString(),poiList.size());
//        for (HereThaPlaces hereThaPlaces: hereThaPlacesList
//             ) {
//            System.out.println(hereThaPlaces.toString());
//        }
            //poiService.saveOrUpdateBatch(poiList);
            for(int i=0;i<=poiList.size()/step;i++){
                if(i == poiList.size()/step){
                    List<PoiM> poiListi = poiList.subList(i*step,poiList.size());
                    poiMMapper.mysqlInsertOrUpdateBath(poiListi);
                } else {
                    List<PoiM> poiListi = poiList.subList(i*step,(i+1)*step);
                    poiMMapper.mysqlInsertOrUpdateBath(poiListi);
                }
            }
            //poiHPhaMapper.mysqlInsertOrUpdateBath(poiList);
            long endTime = System.currentTimeMillis();
            System.out.println("finished processing file:"+file.toString()+" cost "+(endTime-startTime)/1000+"s");
            log.info("finished processing file:"+file.toString()+" cost "+(endTime-startTime)/1000+"s");
        }
    }
}
