package com.hll.mapdataservice.business.interceptor;

import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;

import java.sql.Connection;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CustomTableNameInterceptor implements InnerInterceptor {

    // 正则表达式用于匹配COALESCE函数中的表名
    private static final Pattern COALESCE_PATTERN = Pattern.compile(
            "(COALESCE\\s*\\(\\s*EXCLUDED\\.\\s*[^,]+\\s*,\\s*)([^.]+)(\\.\\s*[^\\s\\)]+\\s*\\))",
            Pattern.CASE_INSENSITIVE
    );

    @Override
    public void beforePrepare(StatementHandler sh, Connection connection, Integer transactionTimeout) {
        // 获取原始 SQL
        MetaObject metaObject = SystemMetaObject.forObject(sh);
        String originalSql = (String) metaObject.getValue("delegate.boundSql.sql");

        // 获取当前线程的表名后缀
        String suffix = MybatisPlusConfig.myTableName.get();

        // 如果后缀非空，则修改 SQL 以包含动态表名
        if (suffix != null && !suffix.isEmpty()) {
            // 修改 SQL 以包含动态表名
            String modifiedSql = modifyTableNameInSql(originalSql, suffix);

            // 使用反射设置修改后的 SQL
            metaObject.setValue("delegate.boundSql.sql", modifiedSql);
        }
    }

    private String modifyTableNameInSql(String sql, String suffix) {
        // 使用 Matcher 来查找和替换 COALESCE 函数内的表名
        Matcher matcher = COALESCE_PATTERN.matcher(sql);
        StringBuffer modifiedSql = new StringBuffer();

        while (matcher.find()) {
            String replacement = matcher.group(1) + matcher.group(2) + suffix + matcher.group(3);
            matcher.appendReplacement(modifiedSql, replacement);
        }
        matcher.appendTail(modifiedSql);

        return modifiedSql.toString();
    }
}
