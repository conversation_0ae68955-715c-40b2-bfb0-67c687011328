package com.hll.mapdataservice.business.api.common.controller;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.hll.mapdataservice.business.api.road.service.*;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.CountryAreaEnum;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/1/28
 */
@Slf4j
@RestController
@RequestMapping("/validate")
public class ValidateController {

    @Resource
    private StreetsServiceImpl streetsService;
    @Resource
    private LinkMServiceImpl linkMService;
    @Resource
    private LinkServiceImpl linkService;
    @Resource
    private NodeMServiceImpl nodeMService;
    @Resource
    private NodeServiceImpl nodeService;
    @Resource
    private RelationMServiceImpl relationService;
    @Resource
    private RuleMServiceImpl ruleService;

    /**
     * 验证路网编译，规格映射后的数据量是否一致
     * streets -> link -> link_rp
     * node -> node_rp
     *
     * @param country
     * @param singleFlag
     * @return
     */
    @GetMapping("/quantity")
    public ResponseResult<List<String>> quantityValidate(String country, Boolean singleFlag) {
        Integer streetsNum = 0;
        Integer linkNum = 0;
        Integer linkRpNum = 0;
        Integer nodeNum = 0;
        Integer nodeRpNum = 0;
        List<String> resList = new ArrayList<>();
        try {
            if (singleFlag) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
                MybatisPlusConfig.myTableName.set("");
                streetsNum = streetsService.count();
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                linkNum = linkMService.count();
                linkRpNum = linkService.count();
                nodeNum = nodeMService.count();
                nodeRpNum = nodeService.count();
            } else {
                List<String> areas = CountryAreaEnum.getAreaByCountry(country);
                for (String area : areas) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
                    streetsNum += streetsService.count();
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                    linkNum += linkMService.count();
                    linkRpNum += linkService.count();
                    nodeNum += nodeMService.count();
                    nodeRpNum += nodeService.count();
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return ResponseResult.otherInfo("500", e.getMessage(), false);
        }
        if (streetsNum.equals(linkNum)) {
            resList.add("street->link数量一致" + ",streetsNum:" + streetsNum + ",linkNum:" + linkNum);
            log.info(country + ":street->link数量一致" + ",streetsNum:" + streetsNum + ",linkNum:" + linkNum);
        }
        if (linkNum.equals(linkRpNum)) {
            resList.add("link->link_rp数量一致" + ",linkNum:" + linkNum + ",linkRpNum:" + linkRpNum);
            log.info(country + ":link->link_rp数量一致" + ",linkNum:" + linkNum + ",linkRpNum:" + linkRpNum);
        }
        if (nodeNum.equals(nodeRpNum)) {
            resList.add("node->node_rp数量一致" + ",nodeNum:" + nodeNum + ",nodeRpNum:" + nodeRpNum);
            log.info(country + ":node->node_rp数量一致" + ",nodeNum:" + nodeNum + ",nodeRpNum:" + nodeRpNum);
        }
        return ResponseResult.OK(resList, true);
    }


    /**
     * 校验relation，rule中业务id是否都已继承
     *
     * @param country
     * @param singleFlag
     * @return
     */
    @GetMapping("/length")
    public ResponseResult<List<String>> lengthValidate(String country, Boolean singleFlag) {
        Integer noRelationNum = 0;
        Integer noRuleNum = 0;
        List<String> resList = new ArrayList<>();
        try {
            if (singleFlag) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                MybatisPlusConfig.myTableName.set("");
                noRelationNum = relationService.lambdaQuery().last("where length(inlink_id)<18 or length(node_id)<18 or length(outlink_id)<18").count();
                noRuleNum = ruleService.lambdaQuery().last("where length(inlink_id)<18 or length(node_id)<18 or length(outlink_id)<18 or length(pass)<18 or length(pass2)<18").count();
            } else {
                List<String> areas = CountryAreaEnum.getAreaByCountry(country);
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                for (String area : areas) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                    noRelationNum += relationService.lambdaQuery().last("where length(inlink_id)<18 or length(node_id)<18 or length(outlink_id)<18").count();
                    noRuleNum += ruleService.lambdaQuery().last("where length(inlink_id)<18 or length(node_id)<18 or length(outlink_id)<18 or length(pass)<18 or length(pass2)<18").count();
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            return ResponseResult.otherInfo("500", e.getMessage(), false);
        }
        if (noRelationNum > 0) {
            resList.add("relation表中业务id存在长度不足18位的数据" + ",noRelationNum:" + noRelationNum);
            log.info(country + ":relation表中业务id存在长度不足18位的数据" + ",noRelationNum:" + noRelationNum);
        } else {
            resList.add("relation表中业务id均已继承");
            log.info(country + ":relation表中业务id均已继承");
        }
        if (noRuleNum > 0) {
            resList.add("rule表中业务id存在长度不足18位的数据" + ",noRuleNum:" + noRuleNum);
            log.info(country + "rule表中业务id存在长度不足18位的数据" + ",noRuleNum:" + noRuleNum);
        } else {
            resList.add("rule表中业务id均已继承");
            log.info(country + ":rule表中业务id均已继承");
        }
        return ResponseResult.OK(resList, true);
    }
}
