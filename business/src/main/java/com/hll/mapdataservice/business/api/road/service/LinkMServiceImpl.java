package com.hll.mapdataservice.business.api.road.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.hll.mapdataservice.business.api.poi.service.MtdareaServiceImpl;
import com.hll.mapdataservice.business.api.poi.service.PlanetOsmWaysServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.third.InheritIDService;
import com.hll.mapdataservice.business.third.dto.InheritIDDTO;
import com.hll.mapdataservice.common.constant.Const;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.mapper.*;
import com.hll.mapdataservice.common.service.ILinkMService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import com.vividsolutions.jts.geom.Geometry;
import com.vividsolutions.jts.io.ParseException;
import com.vividsolutions.jts.io.WKTReader;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.servlet.view.tiles3.TilesView;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@Service
//@DS("db9")
@Slf4j
public class LinkMServiceImpl extends ServiceImpl<LinkMMapper, LinkM> implements ILinkMService {

    @Resource
    HerePhaZlevelsServiceImpl herePhaZlevelsService;
    @Resource
    NodeMServiceImpl nodeMService;
    @Resource
    CdmsServiceImpl cdmsService;
    @Resource
    HerePhaAltstreetsServiceImpl herePhaAltstreetsService;

    @Resource
    LinkMMapper linkMMapper;
    @Resource
    NodeMMapper nodeMMapper;
    @Resource
    StreettransServiceImpl streettransService;
    @Resource
    ZlevelsServiceImpl zlevelsService;
    @Resource
    ZlevelsMapper zlevelsMapper;
    @Resource
    CalculationMapper calculationMapper;
    @Resource
    RoadServiceImpl roadService;
    @Resource
    RelationMServiceImpl relationService;
    @Resource
    RuleMServiceImpl ruleService;
    //@Autowired
    // IRoadMatchResService roadMatchResService;
    @Resource
    RoadMatchResMapper roadMatchResMapper;
    @Resource
    InheritIDService inheritIDService;
    @Resource
    RelationMMapper relationMapper;
    @Resource
    RuleMMapper ruleMapper;
    @Resource
    StreetsServiceImpl streetsService;

    @Resource
    MtdareaServiceImpl mtdareaService;

    @Resource
    MtddstServiceImpl mtddstService;
    @Resource
    PlanetOsmWaysServiceImpl planetOsmWaysService;
    @Resource
    TileViewMapper tileViewMapper;

    @Async("asyncTaskExecutor")
    //@Async()
    public void linkConvert(List<Streets> streetssList, Set<Integer> nodeIdSet, MultiValueMap<String, String> rdfCfLinkMap,
                            Map<String, String> rdfCfMap, Map<String, List<String>> rdfNavLinkMap, Boolean isCompileNode, Boolean isCompileTransEng, String area,
                            String country, CountDownLatch countDownLatch) throws SQLException, InterruptedException {

        // MybatisPlusConfig.myTableName.set("_"+area);
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            log.info("processing country:" + CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        log.info("tmp thread is:" + Thread.currentThread().getName());
        List<LinkM> linList = new ArrayList<>();
        List<NodeM> nodeList = new ArrayList<>();

        try {
            if (streetssList.size() > 0) {
                final int areaAssign = StrUtil.isNotBlank(area)
                        ? Integer.parseInt(area.replaceAll("\\D+", ""))
                        : 0;
                for (Streets streets : streetssList
                ) {
                    LinkM link = new LinkM();
                    NodeM node = new NodeM();
                    link.setArea(areaAssign);
                    link.setLinkId(streets.getLinkId().toString());
                    // link.setHllLinkid(UUID.randomUUID().toString());
                    // 使用id继承，2022-01-20
                    // List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(link.getLinkId())));
                    // link.setHllLinkid(String.valueOf(inheritID.get(0)));
                    link.setHllLinkid(link.getLinkId());
                    // 使用id继承，2022-01-20
                    // List<Long> inheritID1 = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(streets.getRefInId().toString())));
                    // link.setHllSNid(String.valueOf(inheritID1.get(0)));
                    link.setHllSNid(streets.getRefInId().toString());

                    // divider and dividerLeg
                    link.setDivider(streets.getDivider());
                    link.setDividerLeg(streets.getDividerleg());
                    // KIND
                    // add limitedAccess according to EMEA about freeway/motorway avoid
                    String limitedAccess = Optional.ofNullable(rdfNavLinkMap.get(streets.getLinkId().toString())).map(navLink -> navLink.get(0)).orElse("");
                    if ("Y".equals(streets.getContracc()) || "Y".equals(limitedAccess)) {
                        link.setKind("1");
                        //remove 人渡 type follow the feedback from rp 2023-12-12
                        //update 人渡 map rule 2025-05-27
                    } else if ("B".equals(streets.getFerryType()) && "N".equals(streets.getArAuto()) && "N".equals(streets.getArBus())
                            && "N".equals(streets.getArTaxis()) && "N".equals(streets.getArCarpool())
                            && "N".equals(streets.getArTrucks()) && "N".equals(streets.getArDeliv())
                            && "N".equals(streets.getArEmerveh()) && "N".equals(streets.getArMotor())
                            && "Y".equals(streets.getArPedest())) {
                        link.setKind("11");
                    } else if ("B".equals(streets.getFerryType())) {
                        link.setKind("13");
                    } else if ("N".equals(streets.getArAuto()) && "N".equals(streets.getArBus())
                            && "N".equals(streets.getArTaxis()) && "N".equals(streets.getArCarpool())
                            && "N".equals(streets.getArTrucks()) && "N".equals(streets.getArDeliv())
                            && "N".equals(streets.getArEmerveh()) && "N".equals(streets.getArMotor())
                            && "Y".equals(streets.getArPedest())) {
                        link.setKind("10");
                    } else {
                        link.setKind("8");
                    }
                    //tAdmin and timeZone
                    //step1: get the areaId info of streets.lAreaId(the smallest level)
                    List<Mtdarea> mtdareaList = mtdareaService.lambdaQuery().eq(Mtdarea::getAreaId, streets.getlAreaId()).list();
                    //step2: set the timeZone and tAdmin from areacode1 to areacode5
                    if (mtdareaList != null && mtdareaList.size() > 0) {
                        Mtdarea mtdarea = mtdareaList.get(0);
                        if (mtdarea != null) {
                            // 尝试设置时区和管理员
                            for (int i = 0; i < 5; i++) {
                                boolean isTimeZoneSet = setTimeZoneAndAdmin(link, mtdarea, i);
                                if (isTimeZoneSet) {
                                    break;
                                }
                            }
                        }
                        //areacode1
                        // if (mtdarea.getAreacode1() != null) {
                        //     List<Mtdarea> mtdarea1List = mtdareaService.lambdaQuery().eq(Mtdarea::getAreacode1, mtdarea.getAreacode1()).eq(Mtdarea::getAdminLvl, 1).list();
                        //     if (mtdarea1List != null && mtdarea1List.size() > 0) {
                        //         Mtdarea mtdarea1 = mtdarea1List.get(0);
                        //         //find mtddstArea according to areacode1
                        //         List<Mtddst> mtddstArea1List = mtddstService.lambdaQuery().eq(Mtddst::getAreaId, mtdarea1.getAreaId()).list();
                        //         if (mtddstArea1List != null && mtddstArea1List.size() > 0) {
                        //             Mtddst mtddstArea1 = mtddstArea1List.get(0);
                        //             if (mtddstArea1.getTimeZone() != null) {
                        //                 link.setTimeZone(mtddstArea1.getTimeZone());
                        //                 link.settAdmin(mtddstArea1.getAreaId().toString());
                        //             } else {
                        //                 //areaCode1 and areacode2
                        //                 List<Mtdarea> mtdarea2List = mtdareaService.lambdaQuery().eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                        //                         .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2()).eq(Mtdarea::getAdminLvl, 2).list();
                        //                 if (mtdarea2List != null && mtdarea2List.size() > 0) {
                        //                     Mtdarea mtdarea2 = mtdarea2List.get(0);
                        //                     List<Mtddst> mtddstArea2List = mtddstService.lambdaQuery().eq(Mtddst::getAreaId, mtdarea2.getAreaId()).list();
                        //                     if (mtddstArea2List != null && mtddstArea2List.size() > 0) {
                        //                         Mtddst mtddstArea2 = mtddstArea2List.get(0);
                        //                         if (mtddstArea2.getTimeZone() != null) {
                        //                             link.setTimeZone(mtddstArea2.getTimeZone());
                        //                             link.settAdmin(mtddstArea2.getAreaId().toString());
                        //                         } else {
                        //                             //areaCode1,areacode2,areacode3
                        //                             List<Mtdarea> mtdarea3List = mtdareaService.lambdaQuery().eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                        //                                     .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2()).eq(Mtdarea::getAreacode3, mtdarea.getAreacode3()).eq(Mtdarea::getAdminLvl, 3).list();
                        //                             if (mtdarea3List != null && mtdarea3List.size() > 0) {
                        //                                 Mtdarea mtdarea3 = mtdarea3List.get(0);
                        //                                 List<Mtddst> mtddstArea3List = mtddstService.lambdaQuery().eq(Mtddst::getAreaId, mtdarea3.getAreaId()).list();
                        //                                 if (mtddstArea3List != null && mtddstArea3List.size() > 0) {
                        //                                     Mtddst mtddstArea3 = mtddstArea3List.get(0);
                        //                                     if (mtddstArea3.getTimeZone() != null) {
                        //                                         link.setTimeZone(mtddstArea3.getTimeZone());
                        //                                         link.settAdmin(mtddstArea3.getAreaId().toString());
                        //                                     } else {
                        //                                         //areaCode1,areacode2,areacode3,areacode4
                        //                                         List<Mtdarea> mtdarea4List = mtdareaService.lambdaQuery().eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                        //                                                 .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2()).eq(Mtdarea::getAreacode3, mtdarea.getAreacode3())
                        //                                                 .eq(Mtdarea::getAreacode4, mtdarea.getAreacode4()).eq(Mtdarea::getAdminLvl, 4).list();
                        //                                         if (mtdarea4List != null && mtdarea4List.size() > 0) {
                        //                                             Mtdarea mtdarea4 = mtdarea4List.get(0);
                        //                                             List<Mtddst> mtddstArea4List = mtddstService.lambdaQuery().eq(Mtddst::getAreaId, mtdarea4.getAreaId()).list();
                        //                                             if (mtddstArea4List != null && mtddstArea4List.size() > 0) {
                        //                                                 Mtddst mtddstArea4 = mtddstArea4List.get(0);
                        //                                                 if (mtddstArea4.getTimeZone() != null) {
                        //                                                     link.setTimeZone(mtddstArea4.getTimeZone());
                        //                                                     link.settAdmin(mtddstArea4.getAreaId().toString());
                        //                                                 } else {
                        //                                                     //areaCode1,areacode2,areacode3,areacode4,areacode5
                        //                                                     List<Mtdarea> mtdarea5List = mtdareaService.lambdaQuery().eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                        //                                                             .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2()).eq(Mtdarea::getAreacode3, mtdarea.getAreacode3())
                        //                                                             .eq(Mtdarea::getAreacode4, mtdarea.getAreacode4()).eq(Mtdarea::getAreacode5, mtdarea.getAreacode5()).eq(Mtdarea::getAdminLvl, 5).list();
                        //                                                     if (mtdarea5List != null && mtdarea5List.size() > 0) {
                        //                                                         Mtdarea mtdarea5 = mtdarea5List.get(0);
                        //                                                         List<Mtddst> mtddstArea5List = mtddstService.lambdaQuery().eq(Mtddst::getAreaId, mtdarea5.getAreaId()).list();
                        //                                                         if (mtddstArea5List != null && mtddstArea5List.size() > 0) {
                        //                                                             Mtddst mtddstArea5 = mtddstArea5List.get(0);
                        //                                                             if (mtddstArea5.getTimeZone() != null) {
                        //                                                                 link.setTimeZone(mtddstArea5.getTimeZone());
                        //                                                                 link.settAdmin(mtddstArea5.getAreaId().toString());
                        //                                                             }
                        //                                                         }
                        //                                                     }
                        //                                                 }
                        //                                             }
                        //                                         }
                        //                                     }
                        //                                 }
                        //                             }
                        //                         }
                        //                     }
                        //                 }
                        //             }
                        //         }
                        //     }
                        // }
                    }
                    // FORMWAY
                    String formWay = "";
                    if ("Y".equals(streets.getInprocdata())) {
                        formWay += ",0";
                        // link.setFormway("0");
                    }
                    if ("Y".equals(streets.getRamp()) && "Y".equals(streets.getContracc())) {
                        formWay += ",11";
                        // link.setFormway("11");
                    }
                    if ("Y".equals(streets.getRamp()) && "N".equals(streets.getContracc())) {
                        formWay += ",15";
                        // link.setFormway("15");
                    }
                    if ("Y".equals(streets.getContracc()) && "N".equals(streets.getRamp())) {
                        formWay += ",14";
                        // link.setFormway("14");
                    }
                    String navLink = Optional.ofNullable(rdfNavLinkMap.get(streets.getLinkId().toString())).map(tmpNavLink -> tmpNavLink.get(1)).orElse("");
                    if ("1".equals(navLink)) {
                        formWay += ",16";
                    }
                    if ("2".equals(navLink)) {
                        formWay += ",17";
                    }
                    if ("Y".equals(streets.getPrivateInfo())) {
                        formWay += ",18";
                        // link.setFormway("18");
                    }
                    if ("N".equals(streets.getArAuto()) && "N".equals(streets.getArBus())
                            && "N".equals(streets.getArTaxis()) && "N".equals(streets.getArCarpool())
                            && "N".equals(streets.getArTrucks()) && "N".equals(streets.getArDeliv())
                            && "N".equals(streets.getArEmerveh()) && "N".equals(streets.getArMotor())
                            && "Y".equals(streets.getArPedest())) {
                        formWay += ",20";
                        // link.setFormway("20");
                    }
                    if ("N".equals(streets.getArAuto()) && "Y".equals(streets.getArBus())
                            && "N".equals(streets.getArTaxis()) && "N".equals(streets.getArCarpool())
                            && "N".equals(streets.getArTrucks()) && "N".equals(streets.getArDeliv())
                            && "N".equals(streets.getArMotor()) && "N".equals(streets.getArPedest())) {
                        formWay += ",22";
                        // link.setFormway("22");
                    }
                    if ("Y".equals(streets.getBridge())) {
                        formWay += ",30";
                        // link.setFormway("30");
                    }
                    if ("Y".equals(streets.getTunnel())) {
                        formWay += ",31";
                        // link.setFormway("31");
                    }
                    if ("Y".equals(streets.getRoundabout())) {
                        formWay += ",33";
                        // link.setFormway("33");
                    }
                    if ("Y".equals(streets.getFrontage())) {
                        formWay += ",34";
                        // link.setFormway("34");
                    }
//                if ("Y".equals(streets.getInterinter()) || "Y".equals(streets.getManoeuvre())
//                        || "Y".equals(streets.getIndescrib())) {
                    if ("Y".equals(streets.getManoeuvre())) {
                        formWay += ",90";
                        // link.setFormway("35");
                    }
                    if ("Y".equals(streets.getPoiaccess())) {
                        formWay += ",36";
                        // link.setFormway("36");
                    }
                    if ("Y".equals(streets.getScenicRt())) {
                        formWay += ",60";
                        // link.setFormway("60");
                    }
//                    if ("Y".equals(streets.getMultidigit())&&StrUtil.isNotEmpty(formWay)&&!formWay.contains("15")) {
                    if ("Y".equals(streets.getMultidigit())) {
                        formWay += ",81";
                    }
                    // process formay=50
                    if (rdfCfLinkMap.get(link.getLinkId()) != null && rdfCfLinkMap.get(link.getLinkId()).size() > 0) {
                        List<String> cfids = rdfCfLinkMap.get(link.getLinkId());
                        for (String cfid : cfids
                        ) {
                            if (rdfCfMap.get(cfid) != null
                                    && rdfCfMap.get(cfid).length() > 0) {
                                formWay += ",50";
                            }
                        }
                    }
                    if ("N".equals(streets.getPubAccess())) {
                        formWay += ",52";
                        // link.setFormway("36");
                    }
                    //arTraff
                    if ("N".equals(streets.getArTraff())) {
                        formWay += ",55";
                        // link.setFormway("36");
                    }
                    String forwayReplace = formWay.replaceFirst(",", "");
                    if (formWay.length() > 0) {
                        // log.info("formway is:"+forwayReplace);
                        link.setFormway(forwayReplace);
                    } else {
                        link.setFormway("1");
                    }

                    // 使用id继承，2022-01-20
                    // List<Long> inheritID2 = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(streets.getNrefInId().toString())));
                    // link.setHllENid(inheritID2.get(0).toString());
                    link.setHllENid(streets.getNrefInId().toString());

                    List<Cdms> cdmsList = cdmsService.lambdaQuery().eq(Cdms::getLinkId, streets.getLinkId()).list();
                    // 转换node信息
                    if (isCompileNode) {
                        List<Zlevels> herePhaZlevelsList = zlevelsService.lambdaQuery()
                                .eq(Zlevels::getNodeId, streets.getRefInId()).list();
//                    List<Zlevels> herePhaZlevelsList = zlevelsMapper.selectList(new QueryWrapper<Zlevels>()
//                            .lambda().eq(Zlevels::getNodeId, streets.getRefInId()));
//                    log.info("herePhaZlevelsList size is:"+herePhaZlevelsList.size());

                        // 判断nodeid是否已经存在
                        if (herePhaZlevelsList.size() > 0 && !nodeIdSet.contains(streets.getRefInId().intValue())) {
                            // NODE_ID
                            node.setNodeId(streets.getRefInId().toString());
                            // if (StrUtil.isNotEmpty(streets.getRefInId().toString())) {
                            //     node.setNodeId(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(streets.getRefInId().toString()))).get(0)));
                            // } else {
                            //     node.setNodeId(streets.getRefInId().toString());
                            //
                            // }
                            // HLL_NODEID
                            // 使用id继承，2022-01-20
                            // List<Long> inheritID3 = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(node.getNodeId())));
                            // node.setHllNodeid(inheritID3.get(0).toString());
                            node.setHllNodeid(node.getNodeId());
                            // GEOM
                            node.setGeometry(herePhaZlevelsList.get(0).getGeom());
                            // GEOMWKT
                            node.setGeomwkt(herePhaZlevelsList.get(0).getGeomwkt());
                            // UP_DATE
                            node.setUpDate(LocalDateTime.now());
                            // LIGHT
                            // if (cdmsList.size() > 0 && cdmsList.stream().map(Cdms::getCondType).collect(Collectors.toList()).contains(16)) {
                            //     node.setLight("1");
                            // } else {
                            //     node.setLight("0");
                            // }
                            // DATASOURCE
                            node.setDatasource("7");
                            // STATUS
                            node.setStatus(0);
                            node.setTileId(CommonUtils.getH3IndexByCentroid(node.getGeomwkt(), 7));
                            node.setTileType(Const.H3);
                            node.setArea(areaAssign);
                            nodeList.add(node);

                            nodeIdSet.add(streets.getRefInId().intValue());
                        }

                        NodeM nodeN = new NodeM();
                        herePhaZlevelsList = zlevelsService.lambdaQuery()
                                .eq(Zlevels::getNodeId, streets.getNrefInId()).list();
                        if (herePhaZlevelsList.size() > 0 && !nodeIdSet.contains(streets.getNrefInId().intValue())) {
                            // NODE_ID
                            nodeN.setNodeId(streets.getNrefInId().toString());
                            // if (StrUtil.isNotEmpty(streets.getNrefInId().toString())) {
                            //     nodeN.setNodeId(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(streets.getNrefInId().toString()))).get(0)));
                            // } else {
                            //     nodeN.setNodeId(streets.getNrefInId().toString());
                            //
                            // }
                            // HLL_NODEID
                            // 使用id继承，2022-01-20
                            // List<Long> inheritID3 = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(nodeN.getNodeId())));
                            // nodeN.setHllNodeid(inheritID3.get(0).toString());
                            nodeN.setHllNodeid(nodeN.getNodeId());
                            // GEOM
                            nodeN.setGeometry(herePhaZlevelsList.get(0).getGeom());
                            // GEOMWKT
                            nodeN.setGeomwkt(herePhaZlevelsList.get(0).getGeomwkt());
                            // UP_DATE
                            nodeN.setUpDate(LocalDateTime.now());
                            // LIGHT
                            // if (cdmsList.size() > 0 && cdmsList.stream().map(Cdms::getCondType).collect(Collectors.toList()).contains(16)) {
                            //     nodeN.setLight("1");
                            // } else {
                            //     nodeN.setLight("0");
                            // }
                            // DATASOURCE
                            nodeN.setDatasource("7");
                            // STATUS
                            nodeN.setStatus(0);
                            nodeN.setTileId(CommonUtils.getH3IndexByCentroid(nodeN.getGeomwkt(), 7));
                            nodeN.setTileType(Const.H3);
                            nodeN.setArea(areaAssign);
                            nodeList.add(nodeN);
                            nodeIdSet.add(streets.getNrefInId().intValue());
                        }
                    }
                    // DIR
                    switch (streets.getDirTravel()) {
                        case "B":
                            link.setDir("1");
                            break;
                        case "F":
                            link.setDir("2");
                            break;
                        case "T":
                            link.setDir("3");
                            break;
                        case "N":
                            link.setDir("4");
                            break;
                        default:
                            link.setDir("0");
                    }
                    // APP
                    if (cdmsList.size() > 0 && cdmsList.stream().map(Cdms::getCondType).collect(Collectors.toList()).contains(3)) {
                        link.setApp("4");
                    } else if ("Y".equals(streets.getArAuto()) || "Y".equals(streets.getArBus())
                            || "Y".equals(streets.getArTaxis()) || "Y".equals(streets.getArCarpool())
                            || "Y".equals(streets.getArTrucks()) || "Y".equals(streets.getArDeliv())
                            || "Y".equals(streets.getArEmerveh()) || "Y".equals(streets.getArMotor())
                            || "Y".equals(streets.getArPedest())) {
                        link.setApp("1");
                    } else if ("N".equals(streets.getArAuto()) && "N".equals(streets.getArBus())
                            && "N".equals(streets.getArTaxis()) && "N".equals(streets.getArCarpool())
                            && "N".equals(streets.getArTrucks()) && "N".equals(streets.getArDeliv())
                            && "N".equals(streets.getArEmerveh()) && "N".equals(streets.getArMotor())
                            && "N".equals(streets.getArPedest())) {
                        link.setApp("2");
                    }
                    // TOLL
                    if ("Y".equals(streets.getTollway())) {
                        link.setToll("1");
                    } else if ("N".equals(streets.getTollway())) {
                        link.setToll("2");
                    } else {
                        link.setToll("0");
                    }
                    // MD
                    if ("Y".equals(streets.getMultidigit())) {
                        link.setMd("1");
                    } else {
                        link.setMd("0");
                    }
                    // SPET
                    if ("Y".equals(streets.getSpectrfig())) {
                        link.setSpet("1");
                    } else {
                        link.setSpet("0");
                    }
                    // FUNCT
                    link.setFunct(streets.getFuncClass());
                    // URBAN
                    link.setFunct(streets.getFuncClass());
                    if ("Y".equals(streets.getUrban())) {
                        link.setUrban("1");
                    } else {
                        link.setUrban("0");
                    }
                    // PAVE
                    if ("Y".equals(streets.getPaved())) {
                        link.setPave("1");
                    } else {
                        link.setPave("0");
                    }
                    // LANE_N
                    link.setLaneN(streets.getPhysLanes());
                    // LANE_L
                    link.setLaneL(streets.getToLanes());
                    // LANE_R
                    link.setLaneR(streets.getFromLanes());
                    // LANE_C
                    link.setLaneC(streets.getLaneCat());
                    // L_ADMIN
                    link.setLAdmin(streets.getlAreaId().toString());
                    // R_ADMIN
                    link.setRAdmin(streets.getrAreaId().toString());

                    // 根据增加转换类型存储geometry
//                Geometry geometry = new PGgeometry(streets.getGeometry()).getGeometry();
//                geometry.setSrid(4326);
//                link.setGeometry(geometry.toString());
                    // 根据增加类型存储geometry
                    // link.setGeometry(streets.getGeometry());
                    // GEOMWKT
                    // link.setGeomwkt(streets.getGeometry());
                    // 处理geom字段，调整multiline为line
                    String srcLineGeom = streets.getGeometry();
                    String resLineGeom = StrUtil.subBetween(srcLineGeom, "((", "))");
                    link.setGeomwkt(StrUtil.wrap(resLineGeom, "LINESTRING(", ")"));
                    link.setGeometry("SRID=4326;" + link.getGeomwkt());
                    // LEN
                    // link.setLen(streets.getLen() * 111000);
                    link.setLen(streets.getLen());
                    // F_SPEED
                    String fSpeed = streets.getFrSpdLim().toString();
                    if ("0".equals(fSpeed)) {
                        link.setFSpeed(null);
                    } else {
                        if ("1".equals(streets.getSpdLmSrc())) {
                            fSpeed += ",1,";
                        } else if ("2".equals(streets.getSpdLmSrc())) {
                            fSpeed += ",5,";
                        } else if ("".equals(streets.getSpdLmSrc())) {
                            fSpeed += ",0,";
                        }
                        fSpeed += "1";
                        link.setFSpeed(fSpeed);
                    }
                    // T_SPEED
                    String tSpeed = streets.getToSpdLim().toString();
                    if ("0".equals(tSpeed)) {
                        link.setTSpeed(null);
                    } else {
                        if ("1".equals(streets.getSpdLmSrc())) {
                            tSpeed += ",1,";
                        } else if ("2".equals(streets.getSpdLmSrc())) {
                            tSpeed += ",5,";
                        } else if ("".equals(streets.getSpdLmSrc())) {
                            tSpeed += ",0,";
                        }
                        tSpeed += "1";
                        link.setTSpeed(tSpeed);
                    }

                    // SP_CLASS
                    link.setSpClass(streets.getSpeedCat());
                    // AR_VEH
                    char[] vehValue = {'0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0',
                            '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0'};
                    if ("Y".equals(streets.getArAuto())) {
                        vehValue[0] = '1';
                    }
                    if ("Y".equals(streets.getArTrucks())) {
                        vehValue[2] = '1';
                    }
                    if ("Y".equals(streets.getArPedest())) {
                        vehValue[3] = '1';
                    }
                    if ("Y".equals(streets.getArMotor())) {
                        vehValue[5] = '1';
                    }
                    if ("Y".equals(streets.getArEmerveh())) {
                        vehValue[7] = '1';
                    }
                    if ("Y".equals(streets.getArTaxis())) {
                        vehValue[8] = '1';
                    }
                    if ("Y".equals(streets.getArBus())) {
                        vehValue[9] = '1';
                    }
                    if ("Y".equals(streets.getArCarpool())) {
                        vehValue[13] = '1';
                    }
                    if ("Y".equals(streets.getArDeliv())) {
                        vehValue[26] = '1';
                    }
                    link.setArVeh(String.valueOf(vehValue));
                    // VERIFYFLAG
                    if ("N".equals(streets.getInprocdata())) {
                        link.setVerifyflag("0");
                    } else if ("Y".equals(streets.getInprocdata())) {
                        link.setVerifyflag("2");
                    }
                    if (streets.getNumStnmes() > 0) {
                        List<HerePhaAltstreets> herePhaAltstreetsList = herePhaAltstreetsService.lambdaQuery().eq(HerePhaAltstreets::getLinkId, streets.getLinkId())
                                .list();
                        // NAME_CH_O
                        String nameCho = "";
                        String nmChoLangcd = "";
                        JSONArray nameChoArray = new JSONArray();
                        if (streets.getNumStnmes() == 1) {
                            link.setNameChO(streets.getStName());
                            nmChoLangcd = streets.getStLangcd();
                            link.setNmChoLangcd(nmChoLangcd);
                        } else if (streets.getNumStnmes() > 1) {
                            if ("N".equals(streets.getStalename())) {
                                nameCho += streets.getStName();
                                nmChoLangcd += streets.getStLangcd();
                                if (herePhaAltstreetsList.size() > 0) {
                                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                    ) {
                                        if ("N".equals(herePhaAltstreets.getStalename())) {
                                            nameCho += "|" + herePhaAltstreets.getStName();
                                            nmChoLangcd += "|" + herePhaAltstreets.getStLangcd();
                                        }
                                    }
                                }
                            } else {
                                if (herePhaAltstreetsList.size() > 0) {
                                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                    ) {
                                        if ("N".equals(herePhaAltstreets.getStalename())) {
                                            nameCho += "|" + herePhaAltstreets.getStName();
                                            nmChoLangcd += "|" + herePhaAltstreets.getStLangcd();
                                        }
                                    }
                                }
                            }
                            if (nameCho.length() > 0) {
                                if ("|".equals(nameCho.substring(0, 1))) {
                                    nameCho = nameCho.replaceFirst("\\|", "");
                                    nmChoLangcd = nmChoLangcd.replaceFirst("\\|", "");
                                }
                                link.setNameChO(nameCho);
                                link.setNmChoLangcd(nmChoLangcd);
                                // log.info("nameCho is:" + nameCho + ",nmChoLangcd is:" + nmChoLangcd);
                                nameChoArray = nameConverterList(nameCho, nmChoLangcd);
                            } else {
                                link.setNameChO(null);
                                link.setNmChoLangcd(null);
                            }
                        }


                        // NAME_CH_A
                        String nameCha = "";
                        String nmChaLangcd = "";
                        JSONArray nameChaArray = new JSONArray();
                        if (streets.getNumStnmes() == 1) {
                            if ("Y".equals(streets.getVanityname())) {
                                nameCha += streets.getStName();
                                nmChaLangcd += streets.getStLangcd();
                                link.setNameChA(nameCha);
                                link.setNmChaLangcd(nmChaLangcd);
                            }
                        } else if (streets.getNumStnmes() > 1) {
                            if ("Y".equals(streets.getVanityname())) {
                                nameCha += streets.getStName();
                                nmChaLangcd += streets.getStLangcd();
                                if (herePhaAltstreetsList.size() > 0) {
                                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                    ) {
                                        if ("Y".equals(herePhaAltstreets.getVanityname())) {
                                            nameCha += "|" + herePhaAltstreets.getStName();
                                            nmChaLangcd += "|" + herePhaAltstreets.getStLangcd();
                                        }
                                    }
                                }
                            } else {
                                if (herePhaAltstreetsList.size() > 0) {
                                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                    ) {
                                        if ("Y".equals(herePhaAltstreets.getVanityname())) {
                                            nameCha += "|" + herePhaAltstreets.getStName();
                                            nmChaLangcd += "|" + herePhaAltstreets.getStLangcd();
                                        }
                                    }
                                }
                            }
                            if (nameCha.length() > 0) {
                                if ("|".equals(nameCha.substring(0, 1))) {
                                    nameCha = nameCha.replaceFirst("\\|", "");
                                    nmChaLangcd = nmChaLangcd.replaceFirst("\\|", "");
                                }
                                link.setNameChA(nameCha);
                                // log.info("nameCha is:" + nameCha + ",nmChaLangcd is:" + nmChaLangcd);
                                nameChaArray = nameConverterList(nameCha, nmChaLangcd);
                                link.setNmChaLangcd(nmChaLangcd);
                            } else {
                                link.setNameChA(null);
                                link.setNmChaLangcd(null);
                            }
                        }

                        // link.setNameChA(streets.getVanityname());
                        // NAME_CH_F
                        String nameChf = "";
                        String nmChfLangcd = "";
                        JSONArray nameChfArray = new JSONArray();
                        if (streets.getNumStnmes() == 1) {
                            if ("Y".equals(streets.getStalename())) {
                                nameChf += streets.getStName();
                                nmChfLangcd += streets.getStLangcd();
                                link.setNameChF(nameChf);
                                link.setNmChfLangcd(nmChfLangcd);
                            }
                        } else if (streets.getNumStnmes() > 1) {
                            if ("Y".equals(streets.getStalename())) {
                                nameChf += streets.getStName();
                                nmChfLangcd += streets.getStLangcd();
                                if (herePhaAltstreetsList.size() > 0) {
                                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                    ) {
                                        if ("Y".equals(herePhaAltstreets.getStalename())) {
                                            nameChf += "|" + herePhaAltstreets.getStName();
                                            nmChfLangcd += "|" + herePhaAltstreets.getStLangcd();
                                        }
                                    }
                                }
                            } else {
                                if (herePhaAltstreetsList.size() > 0) {
                                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                    ) {
                                        if ("Y".equals(herePhaAltstreets.getStalename())) {
                                            nameChf += "|" + herePhaAltstreets.getStName();
                                            nmChfLangcd += "|" + herePhaAltstreets.getStLangcd();
                                        }
                                    }
                                }
                            }
                            if (nameChf.length() > 0) {
                                if ("|".equals(nameChf.substring(0, 1))) {
                                    nameChf = nameChf.replaceFirst("\\|", "");
                                    nmChfLangcd = nmChfLangcd.replaceFirst("\\|", "");
                                }
                                link.setNameChF(nameChf);
                                // log.info("nameChf is:" + nameChf + ",nmChfLangcd is:" + nmChfLangcd);
                                nameChfArray = nameConverterList(nameChf, nmChfLangcd);
                                link.setNmChfLangcd(nmChfLangcd);
                            } else {
                                link.setNameChF(null);
                                link.setNmChfLangcd(null);
                            }
                            JSONObject nameJson = new JSONObject();
                            nameJson.put("nameChO", nameChoArray);
                            nameJson.put("nameChA", nameChaArray);
                            nameJson.put("nameChF", nameChfArray);
                            link.setName(nameJson.toMap());
                        }
                        if (isCompileTransEng) {
                            if (streets.getFeatId() > 0) {
                                List<Streettrans> streettransList = streettransService.lambdaQuery()
                                        .eq(Streettrans::getFeatureId, streets.getFeatId()).list();
                                if (streettransList.size() > 0) {
                                    // NAME_EN_O
                                    String nameEno = "";
                                    if (streets.getNumStnmes() == 1) {
                                        if (streettransList.size() > 0) {
                                            link.setNameEnO(streettransList.get(0).getStNameTr());
                                        }
                                    } else if (streets.getNumStnmes() > 1) {
                                        if ("N".equals(streets.getStalename())) {
                                            nameEno += streettransList.get(0).getStNameTr();
                                            if (herePhaAltstreetsList.size() > 0) {
                                                for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                                ) {
                                                    if ("N".equals(herePhaAltstreets.getStalename())) {
                                                        List<Streettrans> altStreettransList = streettransService.lambdaQuery()
                                                                .eq(Streettrans::getFeatureId, herePhaAltstreets.getFeatId()).list();
                                                        if (altStreettransList.size() > 0) {
                                                            nameEno += "|" + altStreettransList.get(0).getStNameTr();
                                                        }
                                                    }
                                                }
                                            }
                                        } else {
                                            if (herePhaAltstreetsList.size() > 0) {
                                                for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                                ) {
                                                    List<Streettrans> altStreettransList = streettransService.lambdaQuery()
                                                            .eq(Streettrans::getFeatureId, herePhaAltstreets.getFeatId()).list();
                                                    if (altStreettransList.size() > 0) {
                                                        nameEno += "|" + altStreettransList.get(0).getStNameTr();
                                                    }
                                                }
                                            }
                                        }
                                        if (nameEno.length() > 0) {
                                            if ("|".equals(nameEno.substring(0, 1))) {
                                                nameEno = nameEno.replaceFirst("\\|", "");
                                            }
                                            link.setNameEnO(nameEno);
                                        } else {
                                            link.setNameEnO(null);
                                        }
                                    }

                                    // NAME_EN_A
                                    String nameEha = "";

                                    if (streets.getNumStnmes() == 1) {
                                        if ("Y".equals(streets.getVanityname())) {
                                            if (streettransList.size() > 0) {
                                                nameEha += streettransList.get(0).getStNameTr();
                                                link.setNameEnA(nameEha);
                                            }
                                        }
                                    } else if (streets.getNumStnmes() > 1) {
                                        if ("Y".equals(streets.getVanityname())) {
                                            if (streettransList.size() > 0) {
                                                nameEha += streettransList.get(0).getStNameTr();
                                            }
                                            if (herePhaAltstreetsList.size() > 0) {
                                                for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                                ) {
                                                    if ("Y".equals(herePhaAltstreets.getVanityname())) {
                                                        List<Streettrans> altStreettransList = streettransService.lambdaQuery()
                                                                .eq(Streettrans::getFeatureId, herePhaAltstreets.getFeatId()).list();
                                                        if (altStreettransList.size() > 0) {
                                                            nameEha += "|" + altStreettransList.get(0).getStNameTr();
                                                        }
                                                    }
                                                }
                                            }
                                        } else {
                                            if (herePhaAltstreetsList.size() > 0) {
                                                for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                                ) {
                                                    if ("Y".equals(herePhaAltstreets.getVanityname())) {
                                                        List<Streettrans> altStreettransList = streettransService.lambdaQuery()
                                                                .eq(Streettrans::getFeatureId, herePhaAltstreets.getFeatId()).list();
                                                        if (altStreettransList.size() > 0) {
                                                            nameEha += "|" + altStreettransList.get(0).getStNameTr();
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        if (nameEha.length() > 0) {
                                            if ("|".equals(nameEha.substring(0, 1))) {
                                                nameEha = nameEha.replaceFirst("\\|", "");
                                            }
                                            link.setNameEnA(nameEha);
                                        } else {
                                            link.setNameEnA(null);
                                        }
                                    }

                                    // link.setNameChA(streets.getVanityname());
                                    // NAME_EH_F
                                    String nameEhf = "";
                                    if (streets.getNumStnmes() == 1) {
                                        if ("Y".equals(streets.getStalename())) {
                                            if (streettransList.size() > 0) {
                                                nameEhf += streettransList.get(0).getStNameTr();
                                                link.setNameEnF(nameEhf);
                                            }
                                        }
                                    } else if (streets.getNumStnmes() > 1) {
                                        if ("Y".equals(streets.getStalename())) {
                                            if (streettransList.size() > 0) {
                                                nameEhf += streettransList.get(0).getStNameTr();
                                            }
                                            if (herePhaAltstreetsList.size() > 0) {
                                                for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                                ) {
                                                    if ("Y".equals(herePhaAltstreets.getStalename())) {
                                                        List<Streettrans> altStreettransList = streettransService.lambdaQuery()
                                                                .eq(Streettrans::getFeatureId, herePhaAltstreets.getFeatId()).list();
                                                        if (altStreettransList.size() > 0) {
                                                            nameEhf += "|" + altStreettransList.get(0).getStNameTr();
                                                        }
                                                    }
                                                }
                                            }
                                        } else {
                                            if (herePhaAltstreetsList.size() > 0) {
                                                for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                                ) {
                                                    if ("Y".equals(herePhaAltstreets.getStalename())) {
                                                        List<Streettrans> altStreettransList = streettransService.lambdaQuery()
                                                                .eq(Streettrans::getFeatureId, herePhaAltstreets.getFeatId()).list();
                                                        if (altStreettransList.size() > 0) {
                                                            nameEhf += "|" + altStreettransList.get(0).getStNameTr();
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        if (nameEhf.length() > 0) {
                                            if ("|".equals(nameEhf.substring(0, 1))) {
                                                nameEhf = nameEhf.replaceFirst("\\|", "");
                                            }
                                            link.setNameEnF(nameEhf);
                                        } else {
                                            link.setNameEnF(null);
                                        }
                                    }
                                } else {
                                    log.info("cannot find the featureid,linkId is:" + streets.getLinkId() + ",featureid is:" + streets.getFeatId());
                                }
                            } else {
                                log.info("streets featureid =0,linkId is:" + streets.getLinkId() + ",featureid is:" + streets.getFeatId());
                            }
                        }
                    }
                    // link.setNameChF(streets.getStalename());
                    // CODE_TYPE
                    // link.setCodeType(streets.getRouteType() != null ? streets.getRouteType() : "0");
                    // NAME_TYPE
                    if ("Y".equals(streets.getVanityname())) {
                        link.setNameType("2");
                    } else if ("Y".equals(streets.getScenicNm())) {
                        link.setNameType("5");
                    } else {
                        link.setNameType("0");
                    }
                    // SRC_FLAG
                    if ("Y".equals(streets.getNameonrdsn())) {
                        link.setSrcFlag("0");
                    } else {
                        link.setSrcFlag("6");
                    }
                    // UP_DATE
                    link.setUpDate(LocalDateTime.now());
                    // STATUS
                    link.setStatus(0);
                    // DATASOURCE
                    link.setDatasource("7");
                    // PUB_ACCESS
                    link.setPubAccess(streets.getPubAccess());
                    link.setTileId(CommonUtils.getH3IndexByCentroid(link.getGeomwkt(), 7));
                    link.setTileType(Const.H3);
                    linList.add(link);

//                //转换RULE禁左信息（DIVIDER=1/2/N）
//                if("1".equals(streets.getDivider())){
//
//                }
                    // log.info(link.toString());
                }
                log.info("linklist size is:" + linList.size());
                // this.saveOrUpdateBatch(linList);
                List<List<LinkM>> linListPartition = Lists.partition(linList, 32767 / BeanUtil.beanToMap(new LinkM()).keySet().size());
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }

                for (List<LinkM> partitionList : linListPartition) {
                    // log.info("linkMapper threadlocal info is:" + Thread.currentThread().getName());
                    // linMapper.mysqlInsertOrUpdateBath(partitionList);
                    List<Long> linkIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(LinkM::getLinkId).collect(Collectors.toList())));
                    List<Long> sNodeIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(LinkM::getHllSNid).collect(Collectors.toList())));
                    List<Long> eNodeIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(LinkM::getHllENid).collect(Collectors.toList())));
                    for (int i = 0; i < partitionList.size(); i++) {
                        partitionList.get(i).setHllLinkid(String.valueOf(linkIds.get(i)));
                        partitionList.get(i).setHllSNid(String.valueOf(sNodeIds.get(i)));
                        partitionList.get(i).setHllENid(String.valueOf(eNodeIds.get(i)));
                    }
                    linkMMapper.mysqlInsertOrUpdateBath(partitionList);
                    // this.saveOrUpdateBatch(partitionList);
                }

                // log.info("node list size is:{},nodeSw2021 is:{}",nodeList.size(),nodeList.stream().map(p->p.getNodeId()).collect(Collectors.toList()));
                if (isCompileNode) {
                    log.info("node list size is:{}", nodeList.size());
                    // log.info("set size is:{},nodeIdSet is:{}",nodeIdSet.size(),nodeIdSet);
                    // nodeService.saveOrUpdateBatch(nodeList);
                    List<List<NodeM>> nodeListPartion = Lists.partition(nodeList, 32767 / BeanUtil.beanToMap(new NodeM()).keySet().size());
                    if (!country.isEmpty()) {
                        DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                    }
                    if (!area.isEmpty()) {
                        MybatisPlusConfig.myTableName.set("_" + area);
                    } else {
                        MybatisPlusConfig.myTableName.set("");
                    }
                    for (List<NodeM> partitionList : nodeListPartion
                    ) {
                        // log.info("nodeService threadlocal info is:"+Thread.currentThread().getName());
                        // log.info("nodeService threadlocal info is:" + Thread.currentThread().getName());
                        // nodeMapper.mysqlInsertOrUpdateBath(partitionList);
                        List<Long> nodeIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(NodeM::getNodeId).collect(Collectors.toList())));
                        for (int i = 0; i < partitionList.size(); i++) {
                            partitionList.get(i).setHllNodeid(String.valueOf(nodeIds.get(i)));
                        }
                        nodeMMapper.mysqlInsertOrUpdateBath(partitionList);
                        // nodeService.saveOrUpdateBatch(partitionList);
                    }
                }
            }
        } catch (Exception e) {
            log.error("link node convert error,msg is {}", e);
            throw new RuntimeException("link node convert error,msg is {}" + e.getMessage());
        } finally {
            countDownLatch.countDown();
        }
    }


    @Async("asyncTaskExecutor")
    //@Async()
    public void updateFormway(List<Streets> streetssList, MultiValueMap<String, String> rdfCfLinkMap,
                              Map<String, String> rdfCfMap, String area, String country) throws SQLException, InterruptedException {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        List<LinkM> linList = new ArrayList<>();

        if (streetssList.size() > 0) {
            for (Streets streets : streetssList
            ) {
                LinkM link = new LinkM();
                link.setLinkId(streets.getLinkId().toString());
                //deal with no-null column
                link.setHllSNid(streets.getRefInId().toString());
                link.setHllENid(streets.getNrefInId().toString());

                // L_ADMIN
                link.setLAdmin(streets.getlAreaId().toString());
                // R_ADMIN
                link.setRAdmin(streets.getrAreaId().toString());
                // FORMWAY
                String formWay = "";
                if ("Y".equals(streets.getInprocdata())) {
                    formWay += ",0";
                }

                if ("Y".equals(streets.getRamp()) && "Y".equals(streets.getContracc())) {
                    formWay += ",11";
                }
                if ("Y".equals(streets.getRamp()) && "N".equals(streets.getContracc())) {
                    formWay += ",15";
                }
                if ("Y".equals(streets.getContracc()) && "N".equals(streets.getRamp())) {
                    formWay += ",14";
                }
                if ("Y".equals(streets.getPrivateInfo())) {
                    formWay += ",18";
                }
                if ("N".equals(streets.getArAuto()) && "N".equals(streets.getArBus())
                        && "N".equals(streets.getArTaxis()) && "N".equals(streets.getArCarpool())
                        && "N".equals(streets.getArTrucks()) && "N".equals(streets.getArDeliv())
                        && "N".equals(streets.getArEmerveh()) && "N".equals(streets.getArMotor())
                        && "Y".equals(streets.getArPedest())) {
                    formWay += ",20";
                }
                if ("N".equals(streets.getArAuto()) && "Y".equals(streets.getArBus())
                        && "N".equals(streets.getArTaxis()) && "N".equals(streets.getArCarpool())
                        && "N".equals(streets.getArTrucks()) && "N".equals(streets.getArDeliv())
                        && "N".equals(streets.getArMotor()) && "N".equals(streets.getArPedest())) {
                    formWay += ",22";
                }
                if ("Y".equals(streets.getBridge())) {
                    formWay += ",30";
                }
                if ("Y".equals(streets.getTunnel())) {
                    formWay += ",31";
                }
                if ("Y".equals(streets.getRoundabout())) {
                    formWay += ",33";
                }
                if ("Y".equals(streets.getFrontage())) {
                    formWay += ",34";
                }
                if ("Y".equals(streets.getManoeuvre())) {
                    formWay += ",90";
                }
                if ("Y".equals(streets.getPoiaccess())) {
                    formWay += ",36";
                }
                if ("Y".equals(streets.getScenicRt())) {
                    formWay += ",60";
                }
                if ("Y".equals(streets.getMultidigit())) {
                    formWay += ",81";
                }
                // process formay=50
                if (rdfCfLinkMap.get(link.getLinkId()) != null && rdfCfLinkMap.get(link.getLinkId()).size() > 0) {
                    List<String> cfids = rdfCfLinkMap.get(link.getLinkId());
                    for (String cfid : cfids
                    ) {
                        if (rdfCfMap.get(cfid) != null
                                && rdfCfMap.get(cfid).length() > 0) {
                            formWay += ",50";
                        }
                    }
                }
                String forwayReplace = formWay.replaceFirst(",", "");
                if (formWay.length() > 0) {
                    link.setFormway(forwayReplace);
                } else {
                    link.setFormway("1");
                }
                // UP_DATE
                link.setUpDate(LocalDateTime.now());
                linList.add(link);
            }
            log.info("linklist size is:" + linList.size());
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            List<List<LinkM>> linListPartition = Lists.partition(linList, 32767 / BeanUtil.beanToMap(new LinkM()).keySet().size());
            for (List<LinkM> partitionList : linListPartition
            ) {
                List<Long> linkIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(LinkM::getLinkId).collect(Collectors.toList())));
                List<Long> sNodeIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(LinkM::getHllSNid).collect(Collectors.toList())));
                List<Long> eNodeIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(LinkM::getHllENid).collect(Collectors.toList())));
                for (int i = 0; i < partitionList.size(); i++) {
                    partitionList.get(i).setHllLinkid(String.valueOf(linkIds.get(i)));
                    partitionList.get(i).setHllSNid(String.valueOf(sNodeIds.get(i)));
                    partitionList.get(i).setHllENid(String.valueOf(eNodeIds.get(i)));
                }
//                    this.saveOrUpdateBatch(partitionList);
                linkMMapper.mysqlInsertOrUpdateBath(partitionList);
            }
        }
    }


    @Async("asyncTaskExecutor")
    public void upateArVeh(List<Streets> streetsList, String area, String country, CountDownLatch countDownLatch) {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            log.info("processing country:" + CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        log.info("tmp thread is:" + Thread.currentThread().getName());
        List<LinkM> linkList = new ArrayList<>();

        try {
            if (streetsList.size() > 0) {
                for (Streets streets : streetsList
                ) {
                    LinkM link = new LinkM();
                    link.setLinkId(streets.getLinkId().toString());
                    //deal with no-null column
                    link.setHllSNid(streets.getRefInId().toString());
                    link.setHllENid(streets.getNrefInId().toString());

                    // L_ADMIN
                    link.setLAdmin(streets.getlAreaId().toString());
                    // R_ADMIN
                    link.setRAdmin(streets.getrAreaId().toString());

                    // AR_VEH
                    char[] vehValue = {'0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0',
                            '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0'};
                    if ("Y".equals(streets.getArAuto())) {
                        vehValue[0] = '1';
                    }
                    if ("Y".equals(streets.getArTrucks())) {
                        vehValue[2] = '1';
                    }
                    if ("Y".equals(streets.getArMotor())) {
                        vehValue[5] = '1';
                    }
                    if ("Y".equals(streets.getArEmerveh())) {
                        vehValue[7] = '1';
                    }
                    if ("Y".equals(streets.getArTaxis())) {
                        vehValue[8] = '1';
                    }
                    if ("Y".equals(streets.getArBus())) {
                        vehValue[9] = '1';
                    }
                    if ("Y".equals(streets.getArCarpool())) {
                        vehValue[13] = '1';
                    }
                    if ("Y".equals(streets.getArDeliv())) {
                        vehValue[26] = '1';
                    }
                    link.setArVeh(String.valueOf(vehValue));

                    // UP_DATE
                    link.setUpDate(LocalDateTime.now());
                    linkList.add(link);
                }
                log.info("update ar_veh linklist size is:" + linkList.size());
                List<List<LinkM>> linListPartition = Lists.partition(linkList, 32767 / BeanUtil.beanToMap(new LinkM()).keySet().size());
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }

                for (List<LinkM> partitionList : linListPartition
                ) {
                    log.info("linkMapper threadlocal info is:" + Thread.currentThread().getName());
                    List<Long> linkIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(LinkM::getLinkId).collect(Collectors.toList())));
                    List<Long> sNodeIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(LinkM::getHllSNid).collect(Collectors.toList())));
                    List<Long> eNodeIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(LinkM::getHllENid).collect(Collectors.toList())));
                    for (int i = 0; i < partitionList.size(); i++) {
                        partitionList.get(i).setHllLinkid(String.valueOf(linkIds.get(i)));
                        partitionList.get(i).setHllSNid(String.valueOf(sNodeIds.get(i)));
                        partitionList.get(i).setHllENid(String.valueOf(eNodeIds.get(i)));
                    }
//                    this.saveOrUpdateBatch(partitionList);
                    linkMMapper.mysqlInsertOrUpdateBath(partitionList);
                }
            }
        } finally {
            countDownLatch.countDown();
        }
    }

    private Mtdarea getMtdareaByStreet(Streets street) {
        List<Mtdarea> mtdareaList = mtdareaService.lambdaQuery()
                .eq(Mtdarea::getAreaId, street.getlAreaId())
                .list();
        return mtdareaList.isEmpty() ? null : mtdareaList.get(0);
    }

    private boolean setTimeZoneAndAdmin(LinkM link, Mtdarea mtdarea, int i) {
        if (i == 0) {
            List<Mtdarea> list = mtdareaService.lambdaQuery().eq(Mtdarea::getAreacode1, mtdarea.getAreacode1()).eq(Mtdarea::getAdminLvl, i + 1).list();
            if (CollUtil.isNotEmpty(list)) {
                List<Mtddst> mtddstList1 = mtddstService.lambdaQuery().eq(Mtddst::getAreaId, list.get(0).getAreaId()).list();
                if (CollUtil.isNotEmpty(mtddstList1)) {
                    Mtddst mtddst = mtddstList1.get(0);
                    if (mtddst.getTimeZone() != null) {
                        link.setTimeZone(mtddst.getTimeZone());
                        link.setTAdmin(mtddst.getAreaId().toString());
                        return true;
                    }
                }
            }
        } else if (i == 1) {
            List<Mtdarea> list = mtdareaService.lambdaQuery().eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                    .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2()).eq(Mtdarea::getAdminLvl, i + 1).list();
            if (CollUtil.isNotEmpty(list)) {
                List<Mtddst> mtddstList1 = mtddstService.lambdaQuery().eq(Mtddst::getAreaId, list.get(0).getAreaId()).list();
                if (CollUtil.isNotEmpty(mtddstList1)) {
                    Mtddst mtddst = mtddstList1.get(0);
                    if (mtddst.getTimeZone() != null) {
                        link.setTimeZone(mtddst.getTimeZone());
                        link.setTAdmin(mtddst.getAreaId().toString());
                        return true;
                    }
                }
            }
        } else if (i == 2) {
            List<Mtdarea> list = mtdareaService.lambdaQuery().eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                    .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2())
                    .eq(Mtdarea::getAreacode3, mtdarea.getAreacode3())
                    .eq(Mtdarea::getAdminLvl, i + 1).list();
            if (CollUtil.isNotEmpty(list)) {
                List<Mtddst> mtddstList1 = mtddstService.lambdaQuery().eq(Mtddst::getAreaId, list.get(0).getAreaId()).list();
                if (CollUtil.isNotEmpty(mtddstList1)) {
                    Mtddst mtddst = mtddstList1.get(0);
                    if (mtddst.getTimeZone() != null) {
                        link.setTimeZone(mtddst.getTimeZone());
                        link.setTAdmin(mtddst.getAreaId().toString());
                        return true;
                    }
                }
            }
        } else if (i == 3) {
            List<Mtdarea> list = mtdareaService.lambdaQuery().eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                    .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2())
                    .eq(Mtdarea::getAreacode3, mtdarea.getAreacode3())
                    .eq(Mtdarea::getAreacode4, mtdarea.getAreacode4())
                    .eq(Mtdarea::getAdminLvl, i + 1).list();
            if (CollUtil.isNotEmpty(list)) {
                List<Mtddst> mtddstList1 = mtddstService.lambdaQuery().eq(Mtddst::getAreaId, list.get(0).getAreaId()).list();
                if (CollUtil.isNotEmpty(mtddstList1)) {
                    Mtddst mtddst = mtddstList1.get(0);
                    if (mtddst.getTimeZone() != null) {
                        link.setTimeZone(mtddst.getTimeZone());
                        link.setTAdmin(mtddst.getAreaId().toString());
                        return true;
                    }
                }
            }
        } else if (i == 4) {
            List<Mtdarea> list = mtdareaService.lambdaQuery().eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                    .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2())
                    .eq(Mtdarea::getAreacode3, mtdarea.getAreacode3())
                    .eq(Mtdarea::getAreacode4, mtdarea.getAreacode4())
                    .eq(Mtdarea::getAreacode5, mtdarea.getAreacode5())
                    .eq(Mtdarea::getAdminLvl, i + 1).list();
            if (CollUtil.isNotEmpty(list)) {
                List<Mtddst> mtddstList1 = mtddstService.lambdaQuery().eq(Mtddst::getAreaId, list.get(0).getAreaId()).list();
                if (CollUtil.isNotEmpty(mtddstList1)) {
                    Mtddst mtddst = mtddstList1.get(0);
                    if (mtddst.getTimeZone() != null) {
                        link.setTimeZone(mtddst.getTimeZone());
                        link.setTAdmin(mtddst.getAreaId().toString());
                        return true;
                    }
                }
            }
        }
        return false;
    }

    @Async("asyncTaskExecutor")
    public void upateTimeZone(List<Streets> streetsList, String area, String country, CountDownLatch countDownLatch) {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            log.info("processing country:" + CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        log.info("tmp thread is:" + Thread.currentThread().getName());
        List<LinkM> linkList = new ArrayList<>();

        try {
            if (streetsList.size() > 0) {

                for (Streets street : streetsList) {
                    LinkM link = new LinkM();
                    link.setLinkId(street.getLinkId().toString());
                    //deal with no-null column
                    link.setHllSNid(street.getRefInId().toString());
                    link.setHllENid(street.getNrefInId().toString());

                    link.setLAdmin(street.getlAreaId().toString());
                    link.setRAdmin(street.getrAreaId().toString());
                    // 获取街道对应的区域信息
                    Mtdarea mtdarea = getMtdareaByStreet(street);
                    if (mtdarea != null) {
                        // 尝试设置时区和管理员
                        for (int i = 0; i < 5; i++) {
                            boolean isTimeZoneSet = setTimeZoneAndAdmin(link, mtdarea, i);
                            if (isTimeZoneSet) {
                                break;
                            }
                        }
                        link.setUpDate(LocalDateTime.now());
                        linkList.add(link);
                    }
                }
                log.info("update timeZone linklist size is:" + linkList.size());
                List<List<LinkM>> linListPartition = Lists.partition(linkList, 32767 / BeanUtil.beanToMap(new LinkM()).keySet().size());
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }

                for (List<LinkM> partitionList : linListPartition
                ) {
                    log.info("linkMapper threadlocal info is:" + Thread.currentThread().getName());
                    List<Long> linkIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(LinkM::getLinkId).collect(Collectors.toList())));
                    List<Long> sNodeIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(LinkM::getHllSNid).collect(Collectors.toList())));
                    List<Long> eNodeIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(LinkM::getHllENid).collect(Collectors.toList())));
                    for (int i = 0; i < partitionList.size(); i++) {
                        partitionList.get(i).setHllLinkid(String.valueOf(linkIds.get(i)));
                        partitionList.get(i).setHllSNid(String.valueOf(sNodeIds.get(i)));
                        partitionList.get(i).setHllENid(String.valueOf(eNodeIds.get(i)));
                    }
//                    this.saveOrUpdateBatch(partitionList);
                    linkMMapper.mysqlInsertOrUpdateBath(partitionList);
                }
            }
        } finally {
            countDownLatch.countDown();
        }
    }

    @Async("asyncTaskExecutor")
    public void updateNameLangcd(List<Streets> streetsList, String area, String country, CountDownLatch countDownLatch) {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            log.info("processing country:" + CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        log.info("tmp thread is:" + Thread.currentThread().getName());
        List<LinkM> linkList = new ArrayList<>();

        try {
            if (streetsList.size() > 0) {
                for (Streets streets : streetsList
                ) {
                    if (streets.getNumStnmes() > 0) {
                        LinkM link = new LinkM();
                        link.setLinkId(streets.getLinkId().toString());
                        //deal with no-null column
                        link.setHllSNid(streets.getRefInId().toString());
                        link.setHllENid(streets.getNrefInId().toString());

                        // L_ADMIN
                        link.setLAdmin(streets.getlAreaId().toString());
                        // R_ADMIN
                        link.setRAdmin(streets.getrAreaId().toString());

                        List<HerePhaAltstreets> herePhaAltstreetsList = herePhaAltstreetsService.lambdaQuery().eq(HerePhaAltstreets::getLinkId, streets.getLinkId())
                                .list();
                        // NAME_CH_O
                        String nameCho = "";
                        String nmChoLangcd = "";
                        JSONArray nameChoArray = new JSONArray();
                        if (streets.getNumStnmes() == 1) {
                            link.setNameChO(streets.getStName());
                            nmChoLangcd = streets.getStLangcd();
                            link.setNmChoLangcd(nmChoLangcd);
                        } else if (streets.getNumStnmes() > 1) {
                            if ("N".equals(streets.getStalename())) {
                                nameCho += streets.getStName();
                                nmChoLangcd += streets.getStLangcd();
                                if (herePhaAltstreetsList.size() > 0) {
                                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                    ) {
                                        if ("N".equals(herePhaAltstreets.getStalename())) {
                                            nameCho += "|" + herePhaAltstreets.getStName();
                                            nmChoLangcd += "|" + herePhaAltstreets.getStLangcd();
                                        }
                                    }
                                }
                            } else {
                                if (herePhaAltstreetsList.size() > 0) {
                                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                    ) {
                                        if ("N".equals(herePhaAltstreets.getStalename())) {
                                            nameCho += "|" + herePhaAltstreets.getStName();
                                            nmChoLangcd += "|" + herePhaAltstreets.getStLangcd();
                                        }
                                    }
                                }
                            }
                            if (nameCho.length() > 0) {
                                if ("|".equals(nameCho.substring(0, 1))) {
                                    nameCho = nameCho.replaceFirst("\\|", "");
                                    nmChoLangcd = nmChoLangcd.replaceFirst("\\|", "");
                                }
                                link.setNameChO(nameCho);
                                link.setNmChoLangcd(nmChoLangcd);
                                nameChoArray = nameConverterList(nameCho, nmChoLangcd);
                            } else {
                                link.setNameChO(null);
                                link.setNmChoLangcd(null);
                            }
                        }


                        // NAME_CH_A
                        String nameCha = "";
                        String nmChaLangcd = "";
                        JSONArray nameChaArray = new JSONArray();
                        if (streets.getNumStnmes() == 1) {
                            if ("Y".equals(streets.getVanityname())) {
                                nameCha += streets.getStName();
                                nmChaLangcd += streets.getStLangcd();
                                link.setNameChA(nameCha);
                                link.setNmChaLangcd(nmChaLangcd);
                            }
                        } else if (streets.getNumStnmes() > 1) {
                            if ("Y".equals(streets.getVanityname())) {
                                nameCha += streets.getStName();
                                nmChaLangcd += streets.getStLangcd();
                                if (herePhaAltstreetsList.size() > 0) {
                                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                    ) {
                                        if ("Y".equals(herePhaAltstreets.getVanityname())) {
                                            nameCha += "|" + herePhaAltstreets.getStName();
                                            nmChaLangcd += "|" + herePhaAltstreets.getStLangcd();
                                        }
                                    }
                                }
                            } else {
                                if (herePhaAltstreetsList.size() > 0) {
                                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                    ) {
                                        if ("Y".equals(herePhaAltstreets.getVanityname())) {
                                            nameCha += "|" + herePhaAltstreets.getStName();
                                            nmChaLangcd += "|" + herePhaAltstreets.getStLangcd();
                                        }
                                    }
                                }
                            }
                            if (nameCha.length() > 0) {
                                if ("|".equals(nameCha.substring(0, 1))) {
                                    nameCha = nameCha.replaceFirst("\\|", "");
                                    nmChaLangcd = nmChaLangcd.replaceFirst("\\|", "");
                                }
                                link.setNameChA(nameCha);
                                nameChaArray = nameConverterList(nameCha, nmChaLangcd);
                                link.setNmChaLangcd(nmChaLangcd);
                            } else {
                                link.setNameChA(null);
                                link.setNmChaLangcd(null);
                            }
                        }

                        // link.setNameChA(streets.getVanityname());
                        // NAME_CH_F
                        String nameChf = "";
                        String nmChfLangcd = "";
                        JSONArray nameChfArray = new JSONArray();
                        if (streets.getNumStnmes() == 1) {
                            if ("Y".equals(streets.getStalename())) {
                                nameChf += streets.getStName();
                                nmChfLangcd += streets.getStLangcd();
                                link.setNameChF(nameChf);
                                link.setNmChfLangcd(nmChfLangcd);
                            }
                        } else if (streets.getNumStnmes() > 1) {
                            if ("Y".equals(streets.getStalename())) {
                                nameChf += streets.getStName();
                                nmChfLangcd += streets.getStLangcd();
                                if (herePhaAltstreetsList.size() > 0) {
                                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                    ) {
                                        if ("Y".equals(herePhaAltstreets.getStalename())) {
                                            nameChf += "|" + herePhaAltstreets.getStName();
                                            nmChfLangcd += "|" + herePhaAltstreets.getStLangcd();
                                        }
                                    }
                                }
                            } else {
                                if (herePhaAltstreetsList.size() > 0) {
                                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                    ) {
                                        if ("Y".equals(herePhaAltstreets.getStalename())) {
                                            nameChf += "|" + herePhaAltstreets.getStName();
                                            nmChfLangcd += "|" + herePhaAltstreets.getStLangcd();
                                        }
                                    }
                                }
                            }
                            if (nameChf.length() > 0) {
                                if ("|".equals(nameChf.substring(0, 1))) {
                                    nameChf = nameChf.replaceFirst("\\|", "");
                                    nmChfLangcd = nmChfLangcd.replaceFirst("\\|", "");
                                }
                                link.setNameChF(nameChf);
                                nameChfArray = nameConverterList(nameChf, nmChfLangcd);
                                link.setNmChfLangcd(nmChfLangcd);
                            } else {
                                link.setNameChF(null);
                                link.setNmChfLangcd(null);
                            }
                            JSONObject nameJson = new JSONObject();
                            nameJson.put("nameChO", nameChoArray);
                            nameJson.put("nameChA", nameChaArray);
                            nameJson.put("nameChF", nameChfArray);
                            link.setName(nameJson.toMap());
                        }

                        // UP_DATE
                        link.setUpDate(LocalDateTime.now());
                        // if nameChoArray.isEmpty() && nameChaArray.isEmpty() && nameChfArray.isEmpty() no need to update
                        if (!nameChoArray.isEmpty() || !nameChaArray.isEmpty() || !nameChfArray.isEmpty()) {
                            linkList.add(link);
//                        log.info("linkId is:" + link.getLinkId() + ",nameChO is:" + link.getNameChO() + ",nameChA is:" + link.getNameChA() + ",nameChF is:" + link.getNameChF());
                        }
                    }
                }
                log.info("update name_langcd linklist size is:" + linkList.size());
                List<List<LinkM>> linListPartition = Lists.partition(linkList, 32767 / BeanUtil.beanToMap(new LinkM()).keySet().size());
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }

                for (List<LinkM> partitionList : linListPartition
                ) {
                    log.info("linkMapper threadlocal info is:" + Thread.currentThread().getName());
                    List<Long> linkIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(LinkM::getLinkId).collect(Collectors.toList())));
                    List<Long> sNodeIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(LinkM::getHllSNid).collect(Collectors.toList())));
                    List<Long> eNodeIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(LinkM::getHllENid).collect(Collectors.toList())));
                    for (int i = 0; i < partitionList.size(); i++) {
                        partitionList.get(i).setHllLinkid(String.valueOf(linkIds.get(i)));
                        partitionList.get(i).setHllSNid(String.valueOf(sNodeIds.get(i)));
                        partitionList.get(i).setHllENid(String.valueOf(eNodeIds.get(i)));
                    }
//                    this.saveOrUpdateBatch(partitionList);
                    linkMMapper.mysqlInsertOrUpdateBath(partitionList);
                    log.info("linkMapper insertorupdate finished:" + partitionList.size());
                }
            }
        } finally {
            log.info("finally update name_langcd thread is:" + Thread.currentThread().getName());
            countDownLatch.countDown();
        }
    }

    private static List<NodeM> removeDuplicateOrder(List<NodeM> orderList) {
        Set<NodeM> set = new TreeSet<>(Comparator.comparing(NodeM::getNodeId));
        set.addAll(orderList);
        return new ArrayList<>(set);
    }


    @Async("asyncTaskExecutor")
    @Override
    public void aloneRoadMerge(List<RoadMatchRes> batchList, CountDownLatch downLatch, String country, String
            area) throws ParseException {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }

        try {
            List<LinkM> insertLinkList = new ArrayList<>();
            List<NodeM> insertNodeList = new ArrayList<>();
            int count = 0;
            // 融合操作处理
            for (RoadMatchRes roadMatchRes : batchList) {
                log.info("正在处理第{}条", ++count);
                // 1.判断两条道路是否相交
                // 考虑到新增的支路，如果过长，有打断的可能，因此使用osm_id获取osm完整geom
                List<Road> roadList = roadService.lambdaQuery().eq(Road::getOsmId, roadMatchRes.getOsmId()).list();
                Geometry hereGeom = new WKTReader().read(roadMatchRes.getLinkGeomWkt());
                Geometry osmGeom = new WKTReader().read(roadList.get(0).getGeomWkt());
                Geometry intersectPn = hereGeom.intersection(osmGeom);

                // 将link(here)道路打断，为获取交点所在区间做准备
                String linkGeomWkt = roadMatchRes.getLinkGeomWkt();
                List<String> breakLine = breakHandle(linkGeomWkt);
                if (intersectPn.toString().contains("EMPTY")) {
                    // 2. 新增osm道路与here不相交情况
                    // 优先处理此类情况

                    // 过滤掉一些不符合孤立新增的场景
                    String osmPoint = calculationMapper.calClosetPoint(roadList.get(0).getGeom(), roadMatchRes.getLinkGeom());
                    List<String> osmFTPoints = extractCoord(roadList.get(0).getGeomWkt()).stream().map(s -> {
                        String s1 = s.getLongitude() + " " + s.getLatitude();
                        return s1;
                    }).collect(Collectors.toList());
                    if (!osmFTPoints.contains(removeBracket(osmPoint))) {
                        continue;
                    }

                    // 2.1 获取osm道路距离here最近的点
                    String targetPoint = calculationMapper.calClosetPoint(roadMatchRes.getLinkGeom(), roadList.get(0).getGeom());

                    String roadGeom = roadList.get(0).getGeomWkt();
                    String newOsmGeomWkt = generateNewLineGeomWkt(targetPoint, roadGeom);

                    commonBusinessHandle(insertLinkList, insertNodeList, roadMatchRes, breakLine, targetPoint, newOsmGeomWkt);

                } else {
                    // 3. 新增osm道路与here相交情况
                    // String osmGeomWkt = roadList.get(0).getGeomWkt();
                    // List<String> osmBreakLine = breakHandle(osmGeomWkt);
                    //// 判断交点在osm的区间
                    // String osmSectionLine = null;
                    // for (String lineGeom : osmBreakLine) {
                    //    Geometry secGeom = new WKTReader().read(lineGeom);
                    //    if (intersectPn.intersects(secGeom.buffer(0.000001))) {
                    //        osmSectionLine = lineGeom;
                    //        break;
                    //    }
                    //}
                    //// 获得新增osm道路geom
                    // String newOsmGeomWkt = generateNewGeomOfIntersects(osmSectionLine, osmGeomWkt, intersectPn.toString());
                    //
                    // commonBusinessHandle(insertLinkList, insertNodeList, roadMatchRes, breakLine, intersectPn.toString(), newOsmGeomWkt);

                }
            }

            // 数据入库
            int linkFieldNum = BeanUtil.beanToMap(new LinkM()).keySet().size();
            int linkBatchSize = 32767 / linkFieldNum;
            List<List<LinkM>> splitLinkList = CollUtil.splitList(insertLinkList, linkBatchSize);
            for (List<LinkM> links : splitLinkList) {
                linkMMapper.saveBatch(links);
            }
            int nodeFieldNum = BeanUtil.beanToMap(new NodeM()).keySet().size();
            int nodeBatchSize = 32767 / nodeFieldNum;
            List<List<NodeM>> splitNodeList = CollUtil.splitList(insertNodeList, nodeBatchSize);
            for (List<NodeM> nodes : splitNodeList) {
                nodeMMapper.saveBatch(nodes);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            downLatch.countDown();
        }
    }

    @NotNull
    private String generateNewLineGeomWkt(String targetPoint, String roadGeom) throws ParseException {
        String newOsmGeomWkt;
        List<Location> locations = CommonUtils.extractCoord(roadGeom, false);
        String point1 = locations.get(0).getLongitude() + " " + locations.get(0).getLatitude();
        String point2 = locations.get(1).getLongitude() + " " + locations.get(1).getLatitude();

        // 2.2 为新增的osm道路创建geom
        Geometry targetGeom = new WKTReader().read(targetPoint);
        double distance1 = targetGeom.distance(new WKTReader().read(StrUtil.wrap(point1, "POINT(", ")")));
        double distance2 = targetGeom.distance(new WKTReader().read(StrUtil.wrap(point2, "POINT(", ")")));

        if (distance1 > distance2) {
            // 尾点后插入交点
            newOsmGeomWkt = generateNewGeom(roadGeom, targetPoint, true);
        } else {
            // 首点前插入交点
            newOsmGeomWkt = generateNewGeom(roadGeom, targetPoint, false);
        }
        return newOsmGeomWkt;
    }

    @Async("asyncTaskExecutor")
    @Override
    public void branchRoadMerge(List<String> resList, String country, String area, CountDownLatch downLatch) throws
            ParseException {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }

        WKTReader wktReader = new WKTReader();
        try {


            List<LinkM> insertLinkList = new ArrayList<>();
            List<NodeM> insertNodeList = new ArrayList<>();
            for (String osmId : resList) {
                // List<RoadMatchRes> matchResList = roadMatchResService.lambdaQuery().eq(RoadMatchRes::getOsmId, osmId).list();
                LambdaQueryWrapper<RoadMatchRes> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(RoadMatchRes::getOsmId, osmId);
                List<RoadMatchRes> matchResList = roadMatchResMapper.selectList(wrapper);
                List<RoadMatchRes> changeRoadList = matchResList.stream().filter(s -> s.getMatchRes().doubleValue() != 0).collect(Collectors.toList());

                LambdaQueryWrapper<Road> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Road::getOsmId, osmId);
                Road road = roadService.getOne(queryWrapper);
                Geometry linkGeom = wktReader.read(changeRoadList.get(0).getLinkGeomWkt());
                Geometry osmGeom = wktReader.read(road.getGeomWkt());

                // 1.过滤掉变化支路与不相似的两条here路都相交的情况
                List<RoadMatchRes> unlikeLinkList = matchResList.stream().filter(s -> s.getMatchRes().doubleValue() == 0).collect(Collectors.toList());
                if (CollUtil.isEmpty(unlikeLinkList) || unlikeLinkList.size() == 1) {
                    continue;
                }
                String unLikeLink1 = unlikeLinkList.get(0).getLinkGeomWkt();
                String unLikeLink2 = unlikeLinkList.get(1).getLinkGeomWkt();
                if (osmGeom.intersects(wktReader.read(unLikeLink1)) && osmGeom.intersects(wktReader.read(unLikeLink2))) {
                    continue;
                }

                // 2.判断支路是正向变化，还是反向新增
                // osm道路建立5m缓冲区，判断是否包含相似的link道路，如果包含则走支路变更逻辑，否则走录入逻辑
                boolean isContain = calculationMapper.calContains(road.getGeom(), changeRoadList.get(0).getLinkGeom());

                if (isContain) {
                    // 判断支路长度是否有加长，变短的数据过滤掉
                    if (osmGeom.getLength() < linkGeom.getLength()) {
                        continue;
                    }

                    // 3.变化支路与2条here不相交的情况
                    if (!osmGeom.intersects(wktReader.read(unLikeLink1)) && !osmGeom.intersects(wktReader.read(unLikeLink2))) {
                        // 3.1 判断变化支路，与哪条here近
                        Geometry unLikeLink1Geom = wktReader.read(unLikeLink1);
                        Geometry unLikeLink2Geom = wktReader.read(unLikeLink2);
                        TreeMap<Double, RoadMatchRes> treeMap = new TreeMap<>();
                        treeMap.put(osmGeom.distance(unLikeLink1Geom), unlikeLinkList.get(0));
                        treeMap.put(osmGeom.distance(unLikeLink2Geom), unlikeLinkList.get(1));
                        // 为之后被截断link道路属性赋值做准备
                        RoadMatchRes roadMatchRes = treeMap.get(treeMap.firstKey());
                        RoadMatchRes roadMatchRes1 = treeMap.get(treeMap.lastKey());
                        LinkM minusLink = this.getOne(Wrappers.<LinkM>lambdaQuery().eq(LinkM::getLinkId, roadMatchRes.getLinkId()));
                        LinkM plusLink = this.getOne(Wrappers.<LinkM>lambdaQuery().eq(LinkM::getLinkId, roadMatchRes1.getLinkId()));
                        String targetLine = roadMatchRes.getLinkGeom();
                        // 3.2 获取osm与目标线最近的点
                        String targetPoint = calculationMapper.calClosetPoint(targetLine, road.getGeom());
                        String newLineGeomWkt = generateNewLineGeomWkt(targetPoint, road.getGeomWkt());

                        String handledLinkGeomWkt = roadMatchRes.getLinkGeomWkt();
                        List<String> linkLines = breakHandle(handledLinkGeomWkt);
                        // 判断交点在哪个区间
                        Geometry geometry = wktReader.read(targetPoint);
                        String sectionLine = null;
                        for (String lineGeom : linkLines) {
                            Geometry secGeom = new WKTReader().read(lineGeom);
                            if (geometry.intersects(secGeom.buffer(0.000001))) {
                                sectionLine = lineGeom;
                                break;
                            }
                        }
                        String closerPn = getSplitGeom(handledLinkGeomWkt, sectionLine);
                        String truncatedLine1 = truncateGeom(handledLinkGeomWkt, closerPn, targetPoint, true);
                        String truncatedLine2 = truncateGeom(handledLinkGeomWkt, closerPn, targetPoint, false);
                        String needExtendLinkGeomWkt = treeMap.get(treeMap.lastKey()).getLinkGeomWkt();
                        // 判断被截断的link道路，哪段是需要延长的
                        String intersectPn = wktReader.read(unLikeLink1).intersection(wktReader.read(unLikeLink2)).toString();
                        String pureIntersectPn = removeBracket(intersectPn);
                        String extendLine = StrUtil.containsAny(truncatedLine1, removeBracket(intersectPn)) ? truncatedLine1 : truncatedLine2;
                        String pureExtendLine = removeBracket(extendLine);
                        String minusNewSubLineGeomWkt = StrUtil.containsAny(truncatedLine1, removeBracket(intersectPn)) ? truncatedLine2 : truncatedLine1;
                        // 将原始不相似link延伸
                        String plusNewSubLineGeomWkt;
                        // 如果交点为起点
                        if (extendLine.indexOf(pureIntersectPn) == 17) {
                            if (needExtendLinkGeomWkt.indexOf(pureIntersectPn) == 17) {
                                // 扩展线段反向插入
                                String reverseStr = StrUtil.reverse(pureExtendLine);
                                plusNewSubLineGeomWkt = needExtendLinkGeomWkt.replace(pureIntersectPn, reverseStr);
                            } else {
                                // 扩展线段正向插入
                                plusNewSubLineGeomWkt = needExtendLinkGeomWkt.replace(pureIntersectPn, pureExtendLine);
                            }
                        } else {
                            if (needExtendLinkGeomWkt.indexOf(pureIntersectPn) == 17) {
                                // 扩展线段正向插入
                                plusNewSubLineGeomWkt = needExtendLinkGeomWkt.replace(pureIntersectPn, pureExtendLine);
                            } else {
                                // 扩展线段反向插入
                                String reverseStr = StrUtil.reverse(pureExtendLine);
                                plusNewSubLineGeomWkt = needExtendLinkGeomWkt.replace(pureIntersectPn, reverseStr);
                            }
                        }
                        // 入库顺序，node、way、relation、rule
                        // a）删除原始两个node，新增两个node
                        NodeM startNode = new NodeM();
                        NodeM endNode = new NodeM();
                        List<Coordinate> newLinePoint = extractCoord(newLineGeomWkt);
                        assembleNode(startNode, endNode, newLinePoint);

                        // b）删除way，新增3个way
                        // 新增link属性赋值
                        LinkM link = new LinkM();
                        link.setLinkId(changeRoadList.get(0).getOsmId());
                        link.setHllLinkid(UUID.randomUUID().toString());
                        link.setHllSNid(startNode.getNodeId());
                        link.setHllENid(endNode.getNodeId());
                        link.setDir(changeRoadList.get(0).getRoadDir());
                        link.setGeomwkt(newLineGeomWkt);
                        link.setLen(new WKTReader().read(newLineGeomWkt).getLength() * 111000);
                        link.setDatasource("OSM");
                        link.setUpDate(LocalDateTime.now());
                        link.setStatus(0);
                        link.setRAdmin("");
                        link.setLAdmin("");

                        // 删除原先三条路，原始交点，新增3条link，2个node
                        this.lambdaUpdate().set(LinkM::getStatus, 1).set(LinkM::getMemo, "CHANGED").eq(LinkM::getLinkId, changeRoadList.get(0).getLinkId()).update();
                        this.lambdaUpdate().set(LinkM::getStatus, 1).eq(LinkM::getLinkId, roadMatchRes.getLinkId()).update();
                        this.lambdaUpdate().set(LinkM::getStatus, 1).eq(LinkM::getLinkId, roadMatchRes1.getLinkId()).update();
                        List<String> nodeIdList = CollUtil.newArrayList(minusLink.getHllSNid(), minusLink.getHllENid(), plusLink.getHllSNid(), plusLink.getHllENid());
                        List<String> repeatElement = CommonUtils.getRepeatElement(nodeIdList);
                        nodeMService.lambdaUpdate().set(NodeM::getStatus, 1).eq(NodeM::getNodeId, repeatElement.get(0)).update();
                        // 原始here道路打断模型赋值
                        String filterPoint = removeBracket(targetPoint);
                        List<NodeM> filterNode = Stream.of(startNode, endNode).filter(s -> s.getGeomwkt().contains(filterPoint)).collect(Collectors.toList());
                        LinkM subLink1 = new LinkM();
                        LinkM subLink2 = new LinkM();
                        BeanUtil.copyProperties(minusLink, subLink1);
                        BeanUtil.copyProperties(plusLink, subLink2);
                        subLink1.setHllLinkid(UUID.randomUUID().toString());
                        subLink1.setLinkId(subLink1.getHllLinkid());
                        subLink1.setGeomwkt(minusNewSubLineGeomWkt);
                        subLink1.setLen(wktReader.read(subLink1.getGeomwkt()).getLength() * 111000);
                        subLink1.setGeometry(null);
                        subLink1.setStatus(0);
                        NodeM sNode1 = nodeMService.getOne(Wrappers.<NodeM>lambdaQuery().eq(NodeM::getNodeId, subLink1.getHllSNid()));
                        if (sNode1.getGeomwkt().contains(removeBracket(intersectPn))) {
                            subLink1.setHllSNid(filterNode.get(0).getNodeId());
                        } else {
                            subLink1.setHllENid(filterNode.get(0).getNodeId());
                        }

                        subLink2.setHllLinkid(UUID.randomUUID().toString());
                        subLink2.setLinkId(subLink2.getHllLinkid());
                        subLink2.setGeomwkt(plusNewSubLineGeomWkt);
                        subLink2.setLen(wktReader.read(subLink2.getGeomwkt()).getLength() * 111000);
                        subLink2.setGeometry(null);
                        subLink2.setStatus(0);
                        NodeM sNode2 = nodeMService.getOne(Wrappers.<NodeM>lambdaQuery().eq(NodeM::getNodeId, subLink2.getHllSNid()));
                        if (sNode2.getGeomwkt().contains(removeBracket(intersectPn))) {
                            subLink2.setHllSNid(filterNode.get(0).getNodeId());
                        } else {
                            subLink2.setHllENid(filterNode.get(0).getNodeId());
                        }
                        insertLinkList.add(link);
                        insertLinkList.add(subLink1);
                        insertLinkList.add(subLink2);
                        insertNodeList.add(startNode);
                        insertNodeList.add(endNode);

                        // 更新relation rule
                        // sublink1 -> minusLink  sublink2 -> plusLink
                        Map<String, String> linkIdMap = new HashMap<>(4);
                        linkIdMap.put(minusLink.getLinkId(), subLink1.getLinkId());
                        linkIdMap.put(plusLink.getLinkId(), subLink2.getLinkId());
                        updateRelatedInfoOfBranchChange(changeRoadList.get(0).getLinkId(), link, filterNode, linkIdMap);

                    } else {
                        // 3.2变化支路与其中一条不相似的here相交的情况

                    }
                } else {

                }


            }

            // 数据入库
            int linkFieldNum = BeanUtil.beanToMap(new LinkM()).keySet().size();
            int linkBatchSize = 32767 / linkFieldNum;
            List<List<LinkM>> splitLinkList = CollUtil.splitList(insertLinkList, linkBatchSize);
            for (List<LinkM> links : splitLinkList) {
                linkMMapper.saveBatch(links);
            }
            int nodeFieldNum = BeanUtil.beanToMap(new NodeM()).keySet().size();
            int nodeBatchSize = 32767 / nodeFieldNum;
            List<List<NodeM>> splitNodeList = CollUtil.splitList(insertNodeList, nodeBatchSize);
            for (List<NodeM> nodes : splitNodeList) {
                nodeMMapper.saveBatch(nodes);
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            downLatch.countDown();
        }
    }

    @Async("asyncTaskExecutor")
    @Override
    public void branchLinkMergeOfMove(List<String> resList, String country, String area, String
            countryOsm, CountDownLatch downLatch) {

        // if (!country.isEmpty()) {
        //    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        //}
        // if (!area.isEmpty()) {
        //    MybatisPlusConfig.myTableName.set("_" + area);
        //} else {
        //    MybatisPlusConfig.myTableName.set("");
        //}

        WKTReader wktReader = new WKTReader();
        try {
            List<LinkM> updateLinkList = new ArrayList<>();
            List<NodeM> updateNodeList = new ArrayList<>();
            for (String osmId : resList) {
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
                // List<RoadMatchRes> matchResList = roadMatchResMapper.selectList(Wrappers.<RoadMatchRes>lambdaQuery().eq(RoadMatchRes::getOsmId, osmId));
                List<RoadMatchRes> matchResList = roadMatchResMapper.selectListByOsmId(osmId);
                List<RoadMatchRes> changeRoadList = matchResList.stream().filter(s -> s.getMatchRes().doubleValue() != 0).collect(Collectors.toList());

                List<RoadMatchRes> unlikeLinkList = matchResList.stream().filter(s -> s.getMatchRes().doubleValue() == 0).collect(Collectors.toList());
                if (CollUtil.isEmpty(unlikeLinkList) || unlikeLinkList.size() == 1) {
                    continue;
                }

                if (!countryOsm.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(countryOsm, false));
                }
                // Road road = roadService.getOne(Wrappers.<Road>lambdaQuery().eq(Road::getOsmId, osmId));
                LinkM osmLink = this.getOne(Wrappers.<LinkM>lambdaQuery().eq(LinkM::getLinkId, osmId));
                Geometry linkGeom = wktReader.read(changeRoadList.get(0).getLinkGeomWkt());
                Geometry osmGeom = wktReader.read(osmLink.getGeomwkt());

                // 1.过滤掉变化支路与不相似的两条here路都相交的情况
                String unLikeLink1 = unlikeLinkList.get(0).getLinkGeomWkt();
                String unLikeLink2 = unlikeLinkList.get(1).getLinkGeomWkt();
                // log.info("osmId:{}",osmId);
                // log.info("osmGeom:{}",osmGeom);
                // log.info("unLikeLink1:{}",unLikeLink1);
                // log.info("unLikeLink2:{}",unLikeLink2);
                // 考虑到不相似的两条路,link_geom字段有为null的场景，因此需要过滤掉，否则后面流程方法会出现空指针异常
                // if (StrUtil.isEmpty(unLikeLink1)||StrUtil.isEmpty(unLikeLink2)) {
                //    continue;
                //}
                if (osmGeom.intersects(wktReader.read(unLikeLink1)) && osmGeom.intersects(wktReader.read(unLikeLink2))) {
                    continue;
                }

                // 2.判断支路是否是正向变化
                // osm道路建立5m缓冲区，判断是否包含相似的link道路，如果包含则走支路变更逻辑
                boolean isContain = calculationMapper.calContains(osmLink.getGeometry(), changeRoadList.get(0).getLinkGeom());

                if (isContain) {
                    // 切换至here数据源
                    if (!country.isEmpty()) {
                        DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                    }
                    if (!area.isEmpty()) {
                        MybatisPlusConfig.myTableName.set("_" + area);
                    } else {
                        MybatisPlusConfig.myTableName.set("");
                    }
                    // 判断支路长度是否有加长，变短的数据过滤掉
                    if (osmGeom.getLength() < linkGeom.getLength()) {
                        continue;
                    }
                    // 3.变更道路相关属性
                    LinkM srcLink = linkMMapper.selectOne(Wrappers.<LinkM>lambdaQuery().eq(LinkM::getLinkId, changeRoadList.get(0).getLinkId()));

                    // 3.1 确定link的交点，然后确定osm距离此交点最近的点
                    log.info("linkId:{}", changeRoadList.get(0).getLinkId());
                    log.info("srcLink:{}", srcLink.getGeomwkt());
                    log.info("unLikeLink1:{}", unLikeLink1);
                    log.info("unLikeLink2:{}", unLikeLink2);
                    Geometry calLinkPn = wktReader.read(srcLink.getGeomwkt()).intersection(wktReader.read(unLikeLink1));
                    Geometry calLinkPn2 = wktReader.read(srcLink.getGeomwkt()).intersection(wktReader.read(unLikeLink2));
                    if (!calLinkPn.toString().equals(calLinkPn2.toString())) {
                        continue;
                    }

                    List<Coordinate> osmSEPn = extractCoord(osmGeom.toString());
                    String point1 = StrUtil.wrap(osmSEPn.get(0).getLongitude() + " " + osmSEPn.get(0).getLatitude(), "POINT(", ")");
                    String point2 = StrUtil.wrap(osmSEPn.get(1).getLongitude() + " " + osmSEPn.get(1).getLatitude(), "POINT(", ")");
                    TreeMap<Double, String> treeMap = new TreeMap<>();
                    treeMap.put(calLinkPn.distance(wktReader.read(point1)), point1);
                    treeMap.put(calLinkPn.distance(wktReader.read(point2)), point2);

                    // 同步修改node相关信息
                    NodeM sNode = nodeMMapper.selectOne(Wrappers.<NodeM>lambdaQuery().eq(NodeM::getNodeId, srcLink.getHllSNid()));
                    NodeM eNode = nodeMMapper.selectOne(Wrappers.<NodeM>lambdaQuery().eq(NodeM::getNodeId, srcLink.getHllENid()));
                    if (sNode.getGeomwkt().equals(calLinkPn)) {
                        eNode.setGeomwkt(treeMap.get(treeMap.lastKey()));
                        eNode.setGeometry("SRID=4326;" + eNode.getGeomwkt());
                        eNode.setUpDate(LocalDateTime.now());
                        eNode.setMemo("CHANGED");
                        updateNodeList.add(eNode);
                    } else {
                        sNode.setGeomwkt(treeMap.get(treeMap.lastKey()));
                        sNode.setGeometry("SRID=4326;" + eNode.getGeomwkt());
                        sNode.setUpDate(LocalDateTime.now());
                        sNode.setMemo("CHANGED");
                        updateNodeList.add(sNode);
                    }

                    // 记录原始道路长度，方便后期计算道路长度变化
                    srcLink.setMemo(String.valueOf(srcLink.getLen()));
                    srcLink.setGeomwkt(parallelMoveLine(osmLink.getGeomwkt(), calLinkPn.toString(), treeMap.get(treeMap.firstKey())));
                    srcLink.setGeometry("SRID=4326;" + srcLink.getGeomwkt());
                    srcLink.setLen(wktReader.read(srcLink.getGeomwkt()).getLength() * 111000);
                    srcLink.setUpDate(LocalDateTime.now());
                    updateLinkList.add(srcLink);
                }
            }
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            // 批量更新道路坐标
            int linkFieldNum = BeanUtil.beanToMap(new LinkM()).keySet().size();
            int linkBatchSize = 32767 / linkFieldNum;
            log.info("the number of updated link is {}", updateLinkList.size());
            List<List<LinkM>> splitLinkList = CollUtil.splitList(updateLinkList, linkBatchSize);
            for (List<LinkM> updateLinks : splitLinkList) {
                linkMMapper.mysqlUpdateBath(updateLinks);
            }

            int nodeFieldNum = BeanUtil.beanToMap(new NodeM()).keySet().size();
            int nodeBatchSize = 32767 / nodeFieldNum;
            log.info("the number of updated node is {}", updateNodeList.size());
            List<List<NodeM>> splitNodeList = CollUtil.splitList(updateNodeList, nodeBatchSize);
            for (List<NodeM> updateNodes : splitNodeList) {
                nodeMMapper.mysqlUpdateBath(updateNodes);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            downLatch.countDown();
        }
    }

    @Async("asyncTaskExecutor")
    @Override
    public void aloneLinkMerge(List<RoadMatchRes> matchResList, CountDownLatch downLatch, String
            countryHere, String area, String countryOsm) {
        try {
            List<LinkM> insertLinkList = new ArrayList<>();
            List<NodeM> insertNodeList = new ArrayList<>();
            int count = 0;
            // 融合操作处理
            for (RoadMatchRes roadMatchRes : matchResList) {
                log.info("正在处理第{}条", ++count);
                // 1.判断两条道路是否相交
                // 考虑到新增的支路，如果过长，有打断的可能，因此使用osm_id获取osm完整geom
                // List<Road> roadList = roadService.lambdaQuery().eq(Road::getOsmId, roadMatchRes.getOsmId()).list();
                if (!countryOsm.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(countryOsm, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
                LinkM osmLink = this.getOne(Wrappers.<LinkM>lambdaQuery().eq(LinkM::getLinkId, roadMatchRes.getOsmId()));
                Geometry hereGeom = new WKTReader().read(roadMatchRes.getLinkGeomWkt());
                Geometry osmGeom = new WKTReader().read(osmLink.getGeomwkt());
                Geometry intersectPn = hereGeom.intersection(osmGeom);

                // 将link(here)道路打断，为获取交点所在区间做准备
                String linkGeomWkt = roadMatchRes.getLinkGeomWkt();
                List<String> breakLine = breakHandle(linkGeomWkt);
                if (intersectPn.toString().contains("EMPTY")) {
                    // 2. 新增osm道路与here不相交情况
                    // 优先处理此类情况

                    // 过滤掉一些不符合孤立新增的场景
                    String osmPoint = calculationMapper.calClosetPoint(osmLink.getGeometry(), roadMatchRes.getLinkGeom());
                    List<String> osmFTPoints = extractCoord(osmLink.getGeomwkt()).stream().map(s -> {
                        String s1 = s.getLongitude() + " " + s.getLatitude();
                        return s1;
                    }).collect(Collectors.toList());
                    if (!osmFTPoints.contains(removeBracket(osmPoint))) {
                        continue;
                    }

                    // 2.1 获取osm道路距离here最近的点
                    String targetPoint = calculationMapper.calClosetPoint(roadMatchRes.getLinkGeom(), osmLink.getGeometry());

                    String roadGeom = osmLink.getGeomwkt();
                    String newOsmGeomWkt = generateNewLineGeomWkt(targetPoint, roadGeom);

                    if (!countryHere.isEmpty()) {
                        DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(countryHere, false));
                    }
                    if (!area.isEmpty()) {
                        MybatisPlusConfig.myTableName.set("_" + area);
                    } else {
                        MybatisPlusConfig.myTableName.set("");
                    }
                    commonBusinessHandle(insertLinkList, insertNodeList, roadMatchRes, breakLine, targetPoint, newOsmGeomWkt);

                }
            }

            if (!countryHere.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(countryHere, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            // 数据入库
            int linkFieldNum = BeanUtil.beanToMap(new LinkM()).keySet().size();
            int linkBatchSize = 32767 / linkFieldNum;
            List<List<LinkM>> splitLinkList = CollUtil.splitList(insertLinkList, linkBatchSize);
            for (List<LinkM> links : splitLinkList) {
                linkMMapper.saveBatch(links);
            }
            int nodeFieldNum = BeanUtil.beanToMap(new NodeM()).keySet().size();
            int nodeBatchSize = 32767 / nodeFieldNum;
            List<List<NodeM>> splitNodeList = CollUtil.splitList(insertNodeList, nodeBatchSize);
            for (List<NodeM> nodes : splitNodeList) {
                nodeMMapper.saveBatch(nodes);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            downLatch.countDown();
        }
    }

    private synchronized void updateRelatedInfoOfBranchChange(String changeSrcLinkId, LinkM
            link, List<NodeM> filterNode, Map<String, String> linkIdMap) {
        // 更新变化支路的关系
        List<RelationM> relationList1 = relationService.lambdaQuery().eq(RelationM::getStatus, 0).and(wrappers -> wrappers.eq(RelationM::getInlinkId, changeSrcLinkId)
                .or().eq(RelationM::getOutlinkId, changeSrcLinkId)).list();
        for (RelationM relation : relationList1) {
            if (changeSrcLinkId.equals(relation.getInlinkId())) {
                RelationM newRelation = new RelationM();
                BeanUtil.copyProperties(relation, newRelation);
                newRelation.setInlinkId(link.getLinkId());
                newRelation.setNodeId(filterNode.get(0).getNodeId());
                newRelation.setOutlinkId(linkIdMap.get(relation.getOutlinkId()));
                newRelation.setUpDate(LocalDateTime.now());
                newRelation.setRelationid(UUID.randomUUID().toString());
                relationService.lambdaUpdate().set(RelationM::getStatus, 1).eq(RelationM::getRelationid, relation.getRelationid()).update();
                relationService.save(newRelation);
            }
            if (changeSrcLinkId.equals(relation.getOutlinkId())) {
                RelationM newRelation = new RelationM();
                BeanUtil.copyProperties(relation, newRelation);
                newRelation.setOutlinkId(link.getLinkId());
                newRelation.setNodeId(filterNode.get(0).getNodeId());
                newRelation.setInlinkId(linkIdMap.get(relation.getInlinkId()));
                newRelation.setUpDate(LocalDateTime.now());
                newRelation.setRelationid(UUID.randomUUID().toString());
                relationService.lambdaUpdate().set(RelationM::getStatus, 1).eq(RelationM::getRelationid, relation.getRelationid()).update();
                relationService.save(newRelation);
            }
        }
        // 更新非变化支路的关系
        for (Map.Entry<String, String> entry : linkIdMap.entrySet()) {
            List<RelationM> relationList2 = relationService.lambdaQuery().eq(RelationM::getStatus, 0).and(wrappers -> wrappers.eq(RelationM::getInlinkId, entry.getKey())
                    .or().eq(RelationM::getOutlinkId, entry.getKey())).list();
            for (RelationM relation : relationList2) {
                if (relation.getInlinkId().equals(entry.getKey())) {
                    RelationM newRelation = new RelationM();
                    BeanUtil.copyProperties(relation, newRelation);
                    newRelation.setInlinkId(entry.getValue());
                    newRelation.setUpDate(LocalDateTime.now());
                    newRelation.setRelationid(UUID.randomUUID().toString());
                    relationService.lambdaUpdate().set(RelationM::getStatus, 1).eq(RelationM::getRelationid, relation.getRelationid()).update();
                    relationService.save(newRelation);
                }
                if (relation.getOutlinkId().equals(entry.getKey())) {
                    RelationM newRelation = new RelationM();
                    BeanUtil.copyProperties(relation, newRelation);
                    newRelation.setOutlinkId(entry.getValue());
                    newRelation.setUpDate(LocalDateTime.now());
                    newRelation.setRelationid(UUID.randomUUID().toString());
                    relationService.lambdaUpdate().set(RelationM::getStatus, 1).eq(RelationM::getRelationid, relation.getRelationid()).update();
                    relationService.save(newRelation);
                }
            }
        }
        // 更新rule信息
        // 更新变化支路的关系
        List<RuleM> ruleList1 = ruleService.lambdaQuery().eq(RuleM::getStatus, 0)
                .and(wrappers -> wrappers.eq(RuleM::getInlinkId, changeSrcLinkId).or().eq(RuleM::getOutlinkId, changeSrcLinkId)).list();
        for (RuleM rule : ruleList1) {
            if (changeSrcLinkId.equals(rule.getInlinkId())) {
                RuleM newRule = new RuleM();
                BeanUtil.copyProperties(rule, newRule);
                newRule.setInlinkId(link.getLinkId());
                newRule.setNodeId(filterNode.get(0).getNodeId());
                newRule.setOutlinkId(linkIdMap.get(rule.getOutlinkId()));
                newRule.setUpDate(LocalDateTime.now());
                newRule.setRuleId(UUID.randomUUID().toString());
                ruleService.lambdaUpdate().set(RuleM::getStatus, 1).eq(RuleM::getRuleId, rule.getRuleId()).update();
                ruleService.save(newRule);
            }
            if (changeSrcLinkId.equals(rule.getOutlinkId())) {
                RuleM newRule = new RuleM();
                BeanUtil.copyProperties(rule, newRule);
                newRule.setOutlinkId(link.getLinkId());
                newRule.setNodeId(filterNode.get(0).getNodeId());
                newRule.setInlinkId(linkIdMap.get(rule.getInlinkId()));
                newRule.setUpDate(LocalDateTime.now());
                newRule.setRuleId(UUID.randomUUID().toString());
                ruleService.lambdaUpdate().set(RuleM::getStatus, 1).eq(RuleM::getRuleId, rule.getRuleId()).update();
                ruleService.save(newRule);
            }
        }
        // 更新非变化支路的关系
        for (Map.Entry<String, String> entry : linkIdMap.entrySet()) {
            List<RuleM> ruleList2 = ruleService.lambdaQuery().eq(RuleM::getStatus, 0)
                    .and(wrappers -> wrappers.eq(RuleM::getInlinkId, entry.getKey()).or().eq(RuleM::getOutlinkId, entry.getKey())).list();
            for (RuleM rule : ruleList2) {
                if (rule.getInlinkId().equals(entry.getKey())) {
                    RuleM newRule = new RuleM();
                    BeanUtil.copyProperties(rule, newRule);
                    newRule.setInlinkId(entry.getValue());
                    newRule.setUpDate(LocalDateTime.now());
                    newRule.setRuleId(UUID.randomUUID().toString());
                    ruleService.lambdaUpdate().set(RuleM::getStatus, 1).eq(RuleM::getRuleId, rule.getRuleId()).update();
                    ruleService.save(newRule);
                }
                if (rule.getOutlinkId().equals(entry.getKey())) {
                    RuleM newRule = new RuleM();
                    BeanUtil.copyProperties(rule, newRule);
                    newRule.setOutlinkId(entry.getValue());
                    newRule.setUpDate(LocalDateTime.now());
                    newRule.setRuleId(UUID.randomUUID().toString());
                    ruleService.lambdaUpdate().set(RuleM::getStatus, 1).eq(RuleM::getRuleId, rule.getRuleId()).update();
                    ruleService.save(newRule);
                }
            }
        }
    }

    private void commonBusinessHandle(List<LinkM> insertLinkList, List<NodeM> insertNodeList, RoadMatchRes
            roadMatchRes, List<String> breakLine, String targetPoint, String newOsmGeom) throws ParseException {
        // 入库顺序 node link relation rule
        // 1 新增node属性赋值
        NodeM startNode = new NodeM();
        NodeM endNode = new NodeM();
        List<Coordinate> newLinePoint = extractCoord(newOsmGeom);
        assembleNode(startNode, endNode, newLinePoint);

        // 2 新增link属性赋值
        LinkM link = new LinkM();
        link.setLinkId(roadMatchRes.getOsmId());
        link.setHllLinkid(UUID.randomUUID().toString());
        link.setHllSNid(startNode.getNodeId());
        link.setHllENid(endNode.getNodeId());
        link.setDir(roadMatchRes.getRoadDir());
        link.setGeomwkt(newOsmGeom);
        link.setLen(new WKTReader().read(newOsmGeom).getLength() * 111000);
        link.setDatasource("OSM");
        link.setUpDate(LocalDateTime.now());
        link.setStatus(0);
        link.setRAdmin("");
        link.setLAdmin("");

        // 3 原始here道路打断模型赋值
        // 删除原始道路，变为两条路
        this.lambdaUpdate().set(LinkM::getStatus, 1).eq(LinkM::getLinkId, roadMatchRes.getLinkId()).update();

        LambdaQueryWrapper<LinkM> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LinkM::getLinkId, roadMatchRes.getLinkId());
        LinkM srcLink = this.getOne(queryWrapper);
        // 判断交点在哪个区间
        Geometry geometry = new WKTReader().read(targetPoint);
        String sectionLine = null;
        for (String lineGeom : breakLine) {
            Geometry secGeom = new WKTReader().read(lineGeom);
            if (geometry.intersects(secGeom.buffer(0.000001))) {
                sectionLine = lineGeom;
                break;
            }
        }
        String filterPoint = removeBracket(targetPoint);
        List<NodeM> filterNode = Stream.of(startNode, endNode).filter(s -> s.getGeomwkt().contains(filterPoint)).collect(Collectors.toList());

        // 返回离起点最近的 经纬坐标点
        String linkGeomWkt = roadMatchRes.getLinkGeomWkt();
        String splitGeom = getSplitGeom(linkGeomWkt, sectionLine);
        LinkM subLink1 = new LinkM();
        LinkM subLink2 = new LinkM();
        BeanUtil.copyProperties(srcLink, subLink1);
        BeanUtil.copyProperties(srcLink, subLink2);
        // 封装截断后的两条道路属性
        assembleTruncatedLink(srcLink, subLink1, subLink2, splitGeom, filterNode, targetPoint);

        // 4 更新relation,rule信息
        updateRelatedInfo(roadMatchRes.getLinkId(), subLink1, subLink2);

        // 一条新增道路，对应录入3条link，2个node
        insertLinkList.add(link);
        insertLinkList.add(subLink1);
        insertLinkList.add(subLink2);
        insertNodeList.add(startNode);
        insertNodeList.add(endNode);
    }

    /**
     * 基于新增的osm与link相交的场景，生成geom
     *
     * @param sectionLine
     * @param osmGeomWkt
     * @param intersectPn
     * @return
     * @throws ParseException
     */
    private String generateNewGeomOfIntersects(String sectionLine, String osmGeomWkt, String intersectPn) throws
            ParseException {
        String sectionStr = removeBracket(sectionLine);
        String pointStr = removeBracket(intersectPn);
        String[] split = sectionStr.split(",");
        List<String> jointList = CollUtil.newArrayList(split[0], pointStr, split[1]);
        String tmpStr = osmGeomWkt.replace(sectionStr, CollUtil.join(jointList, ","));
        String preStr = tmpStr.substring(0, tmpStr.indexOf(pointStr) + pointStr.length());
        String suffixStr = tmpStr.substring(tmpStr.indexOf(pointStr));
        String preGeomWkt = preStr + "))";
        String suffixGeomWkt = "MULTILINESTRING((" + suffixStr;
        WKTReader wktReader = new WKTReader();
        double preLength = wktReader.read(preGeomWkt).getLength();
        double suffixLength = wktReader.read(suffixGeomWkt).getLength();
        return preLength > suffixLength ? preGeomWkt : suffixGeomWkt;
    }

    /**
     * 提取geomWkt，首尾点
     *
     * @param newOsmGeom
     * @return
     */
    private List<Coordinate> extractCoord(String newOsmGeom) {
        List<Coordinate> resList = new ArrayList<>();
        String subStr = StrUtil.subBetween(newOsmGeom, "(", ")");
        if (subStr.contains("(") || subStr.contains(")")) {
            subStr = StrUtil.removeAny(subStr, "(", ")");
        }
        String[] splitCoord = subStr.split(",");
        String beginStr = splitCoord[0];
        String endStr = splitCoord[splitCoord.length - 1];
        Coordinate startCoordinate = new Coordinate();
        startCoordinate.setLongitude(beginStr.split(" ")[0]);
        startCoordinate.setLatitude(beginStr.split(" ")[1]);
        Coordinate endCoordinate = new Coordinate();
        endCoordinate.setLongitude(StrUtil.trim(endStr).split(" ")[0]);
        endCoordinate.setLatitude(StrUtil.trim(endStr).split(" ")[1]);
        resList.add(startCoordinate);
        resList.add(endCoordinate);
        return resList;
    }

    private synchronized void updateRelatedInfo(String srcLinkId, LinkM subLink1, LinkM subLink2) {
        // 更新relation信息
        List<RelationM> srcRelationList = relationService.lambdaQuery().eq(RelationM::getStatus, 0).and(wrapper -> wrapper.eq(RelationM::getInlinkId, srcLinkId)
                .or().eq(RelationM::getOutlinkId, srcLinkId)).list();
        Map<String, List<String>> subLinkMap = new HashMap<>();
        subLinkMap.put(subLink1.getLinkId(), CollUtil.newArrayList(subLink1.getHllSNid(), subLink1.getHllENid()));
        subLinkMap.put(subLink2.getLinkId(), CollUtil.newArrayList(subLink2.getHllSNid(), subLink2.getHllENid()));
        for (RelationM relation : srcRelationList) {
            if (srcLinkId.equals(relation.getInlinkId())) {
                RelationM newRelation = new RelationM();
                BeanUtil.copyProperties(relation, newRelation);
                for (Map.Entry<String, List<String>> entry : subLinkMap.entrySet()) {
                    if (entry.getValue().contains(relation.getNodeId())) {
                        newRelation.setInlinkId(entry.getKey());
                    }
                }
                relationService.lambdaUpdate().set(RelationM::getStatus, 1).eq(RelationM::getRelationid, relation.getRelationid()).update();
                newRelation.setUpDate(LocalDateTime.now());
                newRelation.setRelationid(UUID.randomUUID().toString());
                relationService.save(newRelation);
            }
            if (srcLinkId.equals(relation.getOutlinkId())) {
                RelationM newRelation = new RelationM();
                BeanUtil.copyProperties(relation, newRelation);
                for (Map.Entry<String, List<String>> entry : subLinkMap.entrySet()) {
                    if (entry.getValue().contains(relation.getNodeId())) {
                        newRelation.setOutlinkId(entry.getKey());
                    }
                }
                relationService.lambdaUpdate().set(RelationM::getStatus, 1).eq(RelationM::getRelationid, relation.getRelationid()).update();
                newRelation.setUpDate(LocalDateTime.now());
                newRelation.setRelationid(UUID.randomUUID().toString());
                relationService.save(newRelation);
            }
        }
        // 更新rule信息
        List<RuleM> srcRuleList = ruleService.lambdaQuery().eq(RuleM::getStatus, 0)
                .and(wrapper -> wrapper.eq(RuleM::getInlinkId, srcLinkId).or().eq(RuleM::getOutlinkId, srcLinkId)).list();
        for (RuleM rule : srcRuleList) {
            if (srcLinkId.equals(rule.getInlinkId())) {
                RuleM newRule = new RuleM();
                BeanUtil.copyProperties(rule, newRule);
                for (Map.Entry<String, List<String>> entry : subLinkMap.entrySet()) {
                    if (entry.getValue().contains(rule.getNodeId())) {
                        newRule.setInlinkId(entry.getKey());
                    }
                }
                ruleService.lambdaUpdate().set(RuleM::getStatus, 1).eq(RuleM::getRuleId, rule.getRuleId()).update();
                newRule.setUpDate(LocalDateTime.now());
                newRule.setRuleId(UUID.randomUUID().toString());
                ruleService.save(newRule);
            }
            if (srcLinkId.equals(rule.getOutlinkId())) {
                RuleM newRule = new RuleM();
                BeanUtil.copyProperties(rule, newRule);
                for (Map.Entry<String, List<String>> entry : subLinkMap.entrySet()) {
                    if (entry.getValue().contains(rule.getNodeId())) {
                        newRule.setOutlinkId(entry.getKey());
                    }
                }
                ruleService.lambdaUpdate().set(RuleM::getStatus, 1).eq(RuleM::getRuleId, rule.getRuleId()).update();
                newRule.setUpDate(LocalDateTime.now());
                newRule.setRuleId(UUID.randomUUID().toString());
                ruleService.save(newRule);
            }
        }
    }

    private void assembleTruncatedLink(LinkM srcLink, LinkM subLink1, LinkM subLink2, String
            splitGeom, List<NodeM> filterNode, String targetPoint) throws ParseException {
        subLink1.setHllLinkid(UUID.randomUUID().toString());
        subLink1.setLinkId(subLink1.getHllLinkid());
        subLink1.setHllENid(filterNode.get(0).getNodeId());
        subLink1.setGeomwkt(truncateGeom(srcLink.getGeomwkt(), splitGeom, targetPoint, true));
        subLink1.setLen(new WKTReader().read(subLink1.getGeomwkt()).getLength() * 111000);
        subLink1.setGeometry(null);
        subLink1.setStatus(0);

        subLink2.setHllLinkid(UUID.randomUUID().toString());
        subLink2.setLinkId(subLink2.getHllLinkid());
        subLink2.setHllSNid(filterNode.get(0).getNodeId());
        subLink2.setGeomwkt(truncateGeom(srcLink.getGeomwkt(), splitGeom, targetPoint, false));
        subLink2.setLen(new WKTReader().read(subLink2.getGeomwkt()).getLength() * 111000);
        subLink2.setGeometry(null);
        subLink2.setStatus(0);
    }

    private void assembleNode(NodeM startNode, NodeM endNode, List<Coordinate> newLinePoint) {
        startNode.setNodeId(UUID.randomUUID().toString());
        startNode.setHllNodeid(startNode.getNodeId());
        startNode.setGeomwkt(StrUtil.wrap(newLinePoint.get(0).getLongitude() + " " + newLinePoint.get(0).getLatitude(), "POINT(", ")"));
        startNode.setDatasource("OSM");
        startNode.setNodeId(startNode.getHllNodeid());
        startNode.setUpDate(LocalDateTime.now());
        startNode.setStatus(0);

        endNode.setNodeId(UUID.randomUUID().toString());
        endNode.setHllNodeid(endNode.getNodeId());
        endNode.setGeomwkt(StrUtil.wrap(newLinePoint.get(1).getLongitude() + " " + newLinePoint.get(1).getLatitude(), "POINT(", ")"));
        endNode.setDatasource("OSM");
        endNode.setNodeId(endNode.getHllNodeid());
        endNode.setUpDate(LocalDateTime.now());
        endNode.setStatus(0);
    }

    /**
     * 原始link坐标根据插入点，生产新的geomWkt
     *
     * @param geomwkt
     * @param splitGeom
     * @param insertPoint
     * @param isBefore
     * @return
     */
    private String truncateGeom(String geomwkt, String splitGeom, String insertPoint, boolean isBefore) {
        int index = geomwkt.indexOf(splitGeom) + splitGeom.length();
        insertPoint = removeBracket(insertPoint);
        if (isBefore) {
            return geomwkt.substring(0, index) + "," + insertPoint + "))";
        } else {
            return "MULTILINESTRING((" + insertPoint + geomwkt.substring(index);
        }
    }

    /**
     * 根据插入点，生产新的geomWkt
     *
     * @param roadGeom
     * @param point
     * @param isLast
     * @return
     */
    private String generateNewGeom(String roadGeom, String point, boolean isLast) {
        // MULTILINESTRING((108.19109 12.30391,108.19136 12.30389,108.19178 12.30428))
        String purePoint = removeBracket(point);
        String[] split = purePoint.split(" ");
        // BigDecimal newLongitude = new BigDecimal(split[0]).setScale(5, BigDecimal.ROUND_HALF_UP);
        // BigDecimal newLatitude = new BigDecimal(split[1]).setScale(5, BigDecimal.ROUND_HALF_UP);
        // String combinePoint = newLongitude.toString() + " " + newLatitude.toString();
        String combinePoint = split[0] + " " + split[1];
        if (isLast) {
            String tmpLine = StrUtil.subBefore(roadGeom, "))", true);
            String res = tmpLine + "," + combinePoint + "))";
            return res;
        } else {
            String tmpLine = StrUtil.subAfter(roadGeom, "((", true);
            String res = "MULTILINESTRING((" + combinePoint + "," + tmpLine;
            return res;
        }
    }

    /**
     * 获取区间两点，离起点最近的点
     *
     * @param linkGeomWkt
     * @param sectionLine
     * @return
     */
    private String getSplitGeom(String linkGeomWkt, String sectionLine) throws ParseException {
        String linkGeomStr = removeBracket(linkGeomWkt);
        String sectionStr = removeBracket(sectionLine);
        // 1.获取首点
        String firstPoint = StrUtil.subBefore(linkGeomStr, ",", false);
        String[] split = sectionStr.split(",");
        String firstPointWkt = StrUtil.wrap(firstPoint, "POINT(", ")");
        String node1 = StrUtil.wrap(split[0], "POINT(", ")");
        String node2 = StrUtil.wrap(split[1], "POINT(", ")");
        Geometry firstPointGeom = new WKTReader().read(firstPointWkt);
        Geometry node1Geom = new WKTReader().read(node1);
        Geometry node2Geom = new WKTReader().read(node2);
        double distance1 = firstPointGeom.distance(node1Geom);
        double distance2 = firstPointGeom.distance(node2Geom);
        TreeMap<Double, String> treeMap = new TreeMap<>();
        treeMap.put(distance1, split[0]);
        treeMap.put(distance2, split[1]);
        return treeMap.get(treeMap.firstKey());
    }

    /**
     * wkt道路做打断处理
     *
     * @param linkGeomWkt
     * @return 集合形式返回打断道路
     */
    private List<String> breakHandle(String linkGeomWkt) {
        List<String> resList = new ArrayList<>();
        String subStr = removeBracket(linkGeomWkt);
        // MULTILINESTRING((108.19109 12.30391,108.19136 12.30389,108.19178 12.30428))
        String[] coordList = subStr.split(",");
        for (int i = 0; i < coordList.length; i++) {
            if (i == coordList.length - 1) {
                break;
            }
            String start = coordList[i];
            String end = coordList[i + 1];
            String geoWkt = "MULTILINESTRING((" + start.split(" ")[0] + " " + start.split(" ")[1] +
                    "," + end.split(" ")[0] + " " + end.split(" ")[1] + "))";
            resList.add(geoWkt);
        }
        return resList;
    }

    /**
     * wkt格式去除两边括号
     *
     * @param wktStr
     * @return
     */
    private String removeBracket(String wktStr) {
        String subStr = StrUtil.subBetween(wktStr, "(", ")");
        if (subStr.contains("(") || subStr.contains(")")) {
            subStr = StrUtil.removeAny(subStr, "(", ")");
        }
        return subStr;
    }

    public void prepareLinkstoMerge(List<LinkM> linkList, String area,
                                    String country) throws SQLException, InterruptedException {


        if (linkList.size() > 0) {
            List<LinkM> linkAddList = new ArrayList<>();
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry("vnm-r", false));
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            for (LinkM link : linkList
            ) {
                String preLaunch = link.getPreLaunch();
                if (preLaunch == null || preLaunch.isEmpty()) {
                    continue;
                }
                List<String> connectedLinklistSource = Arrays.asList(preLaunch.replace(" ", "").replace("[", "").replace("]", "").split(","));
                List<String> connectedLinklist = new ArrayList<>();
                connectedLinklist.addAll(connectedLinklistSource);
                if (connectedLinklist.size() > 4) {
                    connectedLinklist.remove(link.getLinkId());
                    connectedLinklist.remove(0);
                    connectedLinklist.remove(connectedLinklist.size() - 1);
                    if (connectedLinklist.size() > 1) {
                        LinkM linkUpdate = new LinkM();
                        List<String> needAddList = new ArrayList<>();
                        for (String linkId : connectedLinklist
                        ) {
                            QueryWrapper<RoadMatchRes> queryWrapper = new QueryWrapper();
                            List<RoadMatchRes> matchResList = roadMatchResMapper.selectList(queryWrapper.eq("osm_id", linkId));
                            // 如果有多条，跳过
                            if (matchResList.size() > 0) {
                                Double sumRes = 0.0;
                                for (RoadMatchRes roadMatchRes : matchResList
                                ) {
                                    sumRes = sumRes + roadMatchRes.getMatchRes().doubleValue();
                                }
                                // 如果一条的匹配度大于0，跳过
                                if (sumRes > 0.2) {
                                    continue;
                                }
                            }

                            needAddList.add(linkId);
                        }
                        if (needAddList.size() > 0) {
                            linkUpdate.setLinkId(link.getLinkId());
                            linkUpdate.setMeshId(needAddList.toString());
                            linkUpdate.setUpDate(LocalDateTime.now());
                            linkAddList.add(linkUpdate);
                        }
                    }
                }
            }
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry("vnm-o", false));
            log.info("processing country:" + CommonUtils.getDsbyCountry("vnm-o", false));
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            if (linkAddList.size() > 0) {
                // update connected link
                List<List<LinkM>> listUpdatePatitions = Lists.partition(linkAddList, 500);
                for (int i = 0; i < listUpdatePatitions.size(); i++) {
                    linkMMapper.mysqlUpdateBath(listUpdatePatitions.get(i));
                }
            }
        }
    }

    @Async("asyncTaskExecutor")
    public void branchRoadMergeOfMove(List<String> resList, String country, String area, CountDownLatch
            downLatch) {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }

        WKTReader wktReader = new WKTReader();
        try {
            List<LinkM> updateLinkList = new ArrayList<>();
            List<NodeM> updateNodeList = new ArrayList<>();
            for (String osmId : resList) {
                // List<RoadMatchRes> matchResList = roadMatchResMapper.selectList(Wrappers.<RoadMatchRes>lambdaQuery().eq(RoadMatchRes::getOsmId, osmId));
                List<RoadMatchRes> matchResList = roadMatchResMapper.selectListByOsmId(osmId);
                List<RoadMatchRes> changeRoadList = matchResList.stream().filter(s -> s.getMatchRes().doubleValue() != 0).collect(Collectors.toList());

                List<RoadMatchRes> unlikeLinkList = matchResList.stream().filter(s -> s.getMatchRes().doubleValue() == 0).collect(Collectors.toList());
                if (CollUtil.isEmpty(unlikeLinkList) || unlikeLinkList.size() == 1) {
                    continue;
                }

                Road road = roadService.getOne(Wrappers.<Road>lambdaQuery().eq(Road::getOsmId, osmId));
                Geometry linkGeom = wktReader.read(changeRoadList.get(0).getLinkGeomWkt());
                Geometry osmGeom = wktReader.read(road.getGeomWkt());

                // 1.过滤掉变化支路与不相似的两条here路都相交的情况
                String unLikeLink1 = unlikeLinkList.get(0).getLinkGeomWkt();
                String unLikeLink2 = unlikeLinkList.get(1).getLinkGeomWkt();
                if (osmGeom.intersects(wktReader.read(unLikeLink1)) && osmGeom.intersects(wktReader.read(unLikeLink2))) {
                    continue;
                }

                // 2.判断支路是否是正向变化
                // osm道路建立5m缓冲区，判断是否包含相似的link道路，如果包含则走支路变更逻辑
                boolean isContain = calculationMapper.calContains(road.getGeom(), changeRoadList.get(0).getLinkGeom());

                if (isContain) {
                    // 判断支路长度是否有加长，变短的数据过滤掉
                    if (osmGeom.getLength() < linkGeom.getLength()) {
                        continue;
                    }
                    // 3.变更道路相关属性
                    LinkM srcLink = linkMMapper.selectOne(Wrappers.<LinkM>lambdaQuery().eq(LinkM::getLinkId, changeRoadList.get(0).getLinkId()));

                    // 3.1 确定link的交点，然后确定osm距离此交点最近的点
                    Geometry calLinkPn = wktReader.read(srcLink.getGeomwkt()).intersection(wktReader.read(unLikeLink1));
                    Geometry calLinkPn2 = wktReader.read(srcLink.getGeomwkt()).intersection(wktReader.read(unLikeLink2));
                    if (!calLinkPn.toString().equals(calLinkPn2.toString())) {
                        continue;
                    }

                    List<Coordinate> osmSEPn = extractCoord(osmGeom.toString());
                    String point1 = StrUtil.wrap(osmSEPn.get(0).getLongitude() + " " + osmSEPn.get(0).getLatitude(), "POINT(", ")");
                    String point2 = StrUtil.wrap(osmSEPn.get(1).getLongitude() + " " + osmSEPn.get(1).getLatitude(), "POINT(", ")");
                    TreeMap<Double, String> treeMap = new TreeMap<>();
                    treeMap.put(calLinkPn.distance(wktReader.read(point1)), point1);
                    treeMap.put(calLinkPn.distance(wktReader.read(point2)), point2);

                    // 同步修改node相关信息
                    NodeM sNode = nodeMMapper.selectOne(Wrappers.<NodeM>lambdaQuery().eq(NodeM::getNodeId, srcLink.getHllSNid()));
                    NodeM eNode = nodeMMapper.selectOne(Wrappers.<NodeM>lambdaQuery().eq(NodeM::getNodeId, srcLink.getHllENid()));
                    if (sNode.getGeomwkt().equals(calLinkPn)) {
                        eNode.setGeomwkt(treeMap.get(treeMap.lastKey()));
                        eNode.setGeometry("SRID=4326;" + eNode.getGeomwkt());
                        eNode.setUpDate(LocalDateTime.now());
                        eNode.setMemo("CHANGED");
                        updateNodeList.add(eNode);
                    } else {
                        sNode.setGeomwkt(treeMap.get(treeMap.lastKey()));
                        sNode.setGeometry("SRID=4326;" + eNode.getGeomwkt());
                        sNode.setUpDate(LocalDateTime.now());
                        sNode.setMemo("CHANGED");
                        updateNodeList.add(sNode);
                    }

                    // 记录原始道路长度，方便后期计算道路长度变化
                    srcLink.setMemo(String.valueOf(srcLink.getLen()));
                    srcLink.setGeomwkt(parallelMoveLine(road.getGeomWkt(), calLinkPn.toString(), treeMap.get(treeMap.firstKey())));
                    srcLink.setGeometry("SRID=4326;" + srcLink.getGeomwkt());
                    srcLink.setLen(wktReader.read(srcLink.getGeomwkt()).getLength() * 111000);
                    srcLink.setUpDate(LocalDateTime.now());
                    updateLinkList.add(srcLink);
                }
            }
            // 批量更新道路坐标
            int linkFieldNum = BeanUtil.beanToMap(new LinkM()).keySet().size();
            int linkBatchSize = 32767 / linkFieldNum;
            log.info("the number of updated link is {}", updateLinkList.size());
            List<List<LinkM>> splitLinkList = CollUtil.splitList(updateLinkList, linkBatchSize);
            for (List<LinkM> updateLinks : splitLinkList) {
                linkMMapper.mysqlUpdateBath(updateLinks);
            }

            int nodeFieldNum = BeanUtil.beanToMap(new NodeM()).keySet().size();
            int nodeBatchSize = 32767 / nodeFieldNum;
            log.info("the number of updated node is {}", updateNodeList.size());
            List<List<NodeM>> splitNodeList = CollUtil.splitList(updateNodeList, nodeBatchSize);
            for (List<NodeM> updateNodes : splitNodeList) {
                nodeMMapper.mysqlUpdateBath(updateNodes);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            downLatch.countDown();
        }
    }

    /**
     * 将目标线移动至原始线，替换原始线，返回最新的geom
     *
     * @param targetLine 目标线，即osm道路
     * @return
     */
    public String parallelMoveLine(String targetLine, String calLinkPn, String calOsmPn) {
        // 1.获取计算偏移量计算的两个端点坐标
        // 2.计算偏移量
        String[] minuend = removeBracket(calOsmPn).split(" ");
        String[] subtractor = removeBracket(calLinkPn).split(" ");
        double diffX = Double.parseDouble(minuend[0]) - Double.parseDouble(subtractor[0]);
        double diffY = Double.parseDouble(minuend[1]) - Double.parseDouble(subtractor[1]);

        // 3.目标线偏移处理
        String[] targetLineArray = removeBracket(targetLine).split(",");
        List<String> list = new ArrayList<>();
        for (String coordStr : targetLineArray) {
            String[] coord = coordStr.split(" ");
            double newX = Double.parseDouble(coord[0]) - diffX;
            double newY = Double.parseDouble(coord[1]) - diffY;
            list.add(newX + " " + newY);
        }
        return StrUtil.wrap(CollUtil.join(list, ","), "MULTILINESTRING((", "))");
    }

    @Async("asyncTaskExecutor")
    public void treeBranchMerge(List<RoadMatchRes> resList, String countryHere, String countryOsm, String
            area, CountDownLatch downLatch) {
        List<LinkM> insertLinkList = new ArrayList<>();
        List<NodeM> insertNodeList = new ArrayList<>();
        List<NodeM> updateNodeList = new ArrayList<>();
        WKTReader wktReader = new WKTReader();
        try {
            for (RoadMatchRes roadMatchRes : resList) {
                String osmId = roadMatchRes.getOsmId();
                // 筛选出来的osmId，会存在多条匹配记录，其中有一条匹配结果为0，但其他结果不为0，需要把此类数据过滤掉
                if (!countryHere.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(countryHere, false));
                }
                List<RoadMatchRes> matchRes = roadMatchResMapper.selectList(Wrappers.<RoadMatchRes>lambdaQuery().eq(RoadMatchRes::getOsmId, osmId));
                matchRes = matchRes.stream().filter(s -> s.getMatchRes().compareTo(new BigDecimal(0.1)) > 0).collect(Collectors.toList());
                if (matchRes.size() > 0) {
                    continue;
                }
                if (!countryOsm.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(countryOsm, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
                // 1.去osm库获取编译后的osm数据，判断该数据是否有叶子支路
                LinkM osmLink = linkMMapper.selectOne(Wrappers.<LinkM>lambdaQuery().eq(LinkM::getLinkId, osmId));
                // 对支路主干link，赋值相关联原始link
                osmLink.setAdopt(roadMatchRes.getLinkId());

                String osmGeomWkt = osmLink.getGeomwkt();
                String linkGeomWkt = roadMatchRes.getLinkGeomWkt();
                Geometry osmGeom = wktReader.read(osmGeomWkt);
                Geometry linkGeom = wktReader.read(linkGeomWkt);


                String connectIds = osmLink.getPreLaunch();
                // 过滤掉了osmId，但是之后的融合入库，该osmId也需添加进来
                List<String> connectIdList = handleConnectIds(connectIds, "", osmId);
                if (CollUtil.isEmpty(connectIdList)) {
                    continue;
                }
                List<LinkM> firstInsertLinkList = linkMMapper.selectList(Wrappers.<LinkM>lambdaQuery().in(LinkM::getLinkId, connectIdList));

                // 2.判断挂接id，是否相似度都为0
                // 切换至here数据源
                if (!countryHere.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(countryHere, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
                List<RoadMatchRes> treeResList = roadMatchResMapper.selectList(Wrappers.<RoadMatchRes>lambdaQuery().in(RoadMatchRes::getOsmId, connectIdList));
                List<RoadMatchRes> collect = treeResList.stream().filter(s -> s.getMatchRes().compareTo(new BigDecimal(0)) > 0).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(collect)) {
                    continue;
                }
                // 处理node
                List<String> nodeIdList = new ArrayList<>();
                for (LinkM link : firstInsertLinkList) {
                    nodeIdList.add(link.getHllSNid());
                    nodeIdList.add(link.getHllENid());
                }
                nodeIdList.add(osmLink.getHllSNid());
                nodeIdList.add(osmLink.getHllENid());

                if (!countryOsm.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(countryOsm, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
                // 同步修改geom坐标，node信息
                // ------
                if (osmGeom.intersects(linkGeom)) {
                    Geometry targetPoint = osmGeom.intersection(linkGeom);
                    List<String> osmBreakLine = breakHandle(osmGeomWkt);
                    // 判断交点在osm的区间
                    String osmSectionLine = null;
                    for (String lineGeom : osmBreakLine) {
                        Geometry secGeom = new WKTReader().read(lineGeom);
                        // log.info("交点坐标：{}",targetPoint.toString());
                        // log.info("打断坐标：{}",secGeom.toString());
                        if (targetPoint.intersects(secGeom.buffer(0.000001))) {
                            osmSectionLine = lineGeom;
                            break;
                        }
                    }
                    String newOsmGeomWkt = generateNewGeomOfIntersects(osmSectionLine, osmGeomWkt, targetPoint.toString());
                    List<NodeM> seNodes = nodeMService.lambdaQuery().in(NodeM::getHllNodeid, CollUtil.newArrayList(osmLink.getHllSNid(), osmLink.getHllENid())).list();
                    NodeM updateNode = getUpdatedNode(seNodes, targetPoint.toString(), wktReader);
                    osmLink.setGeometry("SRID=4326;" + newOsmGeomWkt);
                    osmLink.setGeomwkt(newOsmGeomWkt);
                    updateNodeList.add(updateNode);
                } else {
                    String targetPoint = calculationMapper.calClosetPoint(roadMatchRes.getLinkGeom(), osmLink.getGeometry());
                    // 不相交场景下的新道路geom
                    String newOsmGeomWkt = generateNewLineGeomWkt(targetPoint, osmGeomWkt);
                    List<NodeM> seNodes = nodeMService.lambdaQuery().in(NodeM::getHllNodeid, CollUtil.newArrayList(osmLink.getHllSNid(), osmLink.getHllENid())).list();
                    NodeM updateNode = getUpdatedNode(seNodes, targetPoint, wktReader);
                    osmLink.setGeometry("SRID=4326;" + newOsmGeomWkt);
                    osmLink.setGeomwkt(newOsmGeomWkt);
                    updateNodeList.add(updateNode);
                }
                // ------
                // 将osm挂接新生成的node入库，走到这，躯干osmlink的node需要变更node信息
                nodeMMapper.mysqlUpdateBath(updateNodeList);

                // 3.寻找二级支路
                List<LinkM> secondInsertLinkList = new ArrayList<>();
                for (LinkM link : firstInsertLinkList) {
                    String preLaunch = link.getPreLaunch();
                    // 切换至osm库
                    List<String> strings = handleConnectIds(preLaunch, osmId, link.getLinkId());
                    if (CollUtil.isEmpty(strings)) {
                        continue;
                    }
                    if (!countryOsm.isEmpty()) {
                        DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(countryOsm, false));
                    }
                    if (!area.isEmpty()) {
                        MybatisPlusConfig.myTableName.set("_" + area);
                    } else {
                        MybatisPlusConfig.myTableName.set("");
                    }
                    List<LinkM> tmpInsertList = linkMMapper.selectList(Wrappers.<LinkM>lambdaQuery().in(LinkM::getLinkId, strings));
                    // 切换至here库
                    if (!countryHere.isEmpty()) {
                        DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(countryHere, false));
                    }
                    if (!area.isEmpty()) {
                        MybatisPlusConfig.myTableName.set("_" + area);
                    } else {
                        MybatisPlusConfig.myTableName.set("");
                    }
                    List<RoadMatchRes> treeResList2 = roadMatchResMapper.selectList(Wrappers.<RoadMatchRes>lambdaQuery().in(RoadMatchRes::getOsmId, strings));
                    List<RoadMatchRes> collect2 = treeResList2.stream().filter(s -> s.getMatchRes().compareTo(new BigDecimal(0)) > 0).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(collect2)) {
                        continue;
                    }
                    secondInsertLinkList.addAll(tmpInsertList);
                }

                for (LinkM link : secondInsertLinkList) {
                    nodeIdList.add(link.getHllSNid());
                    nodeIdList.add(link.getHllENid());
                }
                if (!countryOsm.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(countryOsm, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
                // nodeIdList = nodeIdList.stream().distinct().collect(Collectors.toList());
                List<NodeM> nodes = nodeMMapper.selectList(Wrappers.<NodeM>lambdaQuery().in(NodeM::getHllNodeid, nodeIdList));

                firstInsertLinkList.add(osmLink);
                firstInsertLinkList.addAll(secondInsertLinkList);
                insertLinkList.addAll(firstInsertLinkList);
                insertNodeList.addAll(nodes);
            }

            if (!countryHere.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(countryHere, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }

            insertLinkList = insertLinkList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(LinkM::getHllLinkid))), ArrayList::new));
            // insertLinkList = insertLinkList.stream().filter(s -> s.getGeometry().contains("MULTILINESTRING")).collect(Collectors.toList());
            insertNodeList = insertNodeList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(NodeM::getHllNodeid))), ArrayList::new));

            insertLinkList.forEach(s -> {
                s.setSpet("TREE");
                s.setUpDate(LocalDateTime.now());
                s.setLAdmin(Optional.ofNullable(s.getLAdmin()).orElse(""));
                s.setRAdmin(Optional.ofNullable(s.getRAdmin()).orElse(""));
                s.setStatus(0);
                try {
                    s.setLen(wktReader.read(s.getGeomwkt()).getLength() * 111000);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            });

            insertNodeList.forEach(s -> {
                s.setMemo("TREE");
                s.setUpDate(LocalDateTime.now());
                s.setStatus(0);
            });

            // 入库操作
            int linkFieldNum = BeanUtil.beanToMap(new LinkM()).keySet().size();
            int linkBatchSize = 32767 / linkFieldNum;
            log.info("the number of insert link is {}", insertLinkList.size());
            List<List<LinkM>> splitLinkList = CollUtil.splitList(insertLinkList, linkBatchSize);
            for (List<LinkM> insertLinks : splitLinkList) {
                linkMMapper.mysqlInsertOrUpdateBath(insertLinks);
            }

            int nodeFieldNum = BeanUtil.beanToMap(new NodeM()).keySet().size();
            int nodeBatchSize = 32767 / nodeFieldNum;
            log.info("the number of insert node is {}", insertNodeList.size());
            List<List<NodeM>> splitNodeList = CollUtil.splitList(insertNodeList, nodeBatchSize);
            for (List<NodeM> insertNodes : splitNodeList) {
                nodeMMapper.mysqlInsertOrUpdateBath(insertNodes);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("树形融合入库出现异常:" + e.getMessage());
        } finally {
            downLatch.countDown();
        }
    }

    /**
     * 处理挂接id
     * 去除重复的id，可选是否去除上一层id
     *
     * @param connectIds
     * @param parentId
     * @param repeatId
     * @return
     */
    private List<String> handleConnectIds(String connectIds, String parentId, String repeatId) {
        if (StrUtil.isEmpty(connectIds)) {
            return new ArrayList<>();
        }
        String subBetween = StrUtil.subBetween(connectIds, "[", "]");
        String[] split = subBetween.split(", ");
        List<String> resList = new ArrayList<>();
        for (String s : split) {
            resList.add(s);
        }
        // return CollUtil.subtractToList(resList, CommonUtils.getRepeatElement(resList));
        resList.removeIf(s -> s.equals(parentId));
        resList.removeIf(s -> s.equals(repeatId));
        return resList;
    }

    @Async("asyncTaskExecutor")
    public void roadAdjoinBreak(List<String> ids, Map<String, List<LinkM>> listMap, String country, String
            area, CountDownLatch downLatch) throws ParseException {
        // 1.各线程批量处理，原始link所关联的，融合录入的osm躯干道路
        WKTReader wktReader = new WKTReader();
        try {
            for (String id : ids) {
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
                List<LinkM> osmLinks = listMap.get(id);
                // 获取被操作的原始link
                LinkM srcLink = this.getOne(Wrappers.<LinkM>lambdaQuery().eq(LinkM::getHllLinkid, id));
                // 获取原始link，起点
                NodeM startNode = nodeMMapper.selectOne(Wrappers.<NodeM>lambdaQuery().eq(NodeM::getHllNodeid, srcLink.getHllSNid()));
                String srcStartPn = startNode.getGeomwkt();
                Geometry srcStartPnGeom = wktReader.read(srcStartPn);
                // 处理osm躯干与原始link的交点，然后根据交点排序
                TreeMap<Double, String> linkTreeMap = new TreeMap<>();
                String srcLinkGeomWkt = srcLink.getGeomwkt();
                Geometry srcLinkGeom = wktReader.read(srcLinkGeomWkt);
                // 获取所有交点
                List<String> seIdList = new ArrayList<>();
                for (LinkM osmLink : osmLinks) {
                    seIdList.add(osmLink.getHllSNid());
                    seIdList.add(osmLink.getHllENid());
                }
                List<NodeM> seNodes = nodeMService.lambdaQuery().in(NodeM::getHllNodeid, seIdList).list();
                List<String> seNodeWkts = seNodes.stream().map(NodeM::getGeomwkt).collect(Collectors.toList());
                // 获取在被操作link上的osm交点
                List<String> onlineNodes = new ArrayList<>();
                for (String nodeWkt : seNodeWkts) {
                    if (wktReader.read(nodeWkt).intersects(srcLinkGeom.buffer(0.000001))) {
                        onlineNodes.add(nodeWkt);
                    }
                }
                for (String onlineNode : onlineNodes) {
                    linkTreeMap.put(srcStartPnGeom.distance(wktReader.read(onlineNode)), onlineNode);
                }

                Collection<String> values = linkTreeMap.values();
                List<String> nodes = new ArrayList<>(values);
                // 获取node的id属性，为之后截断link属性赋值
                if (CollUtil.isEmpty(nodes)) {
                    continue;
                }
                List<NodeM> selectNodes = nodeMService.lambdaQuery().select(NodeM::getHllNodeid).eq(NodeM::getMemo, "TREE").in(NodeM::getGeomwkt, nodes).list();
                List<String> nodeIds = selectNodes.stream().map(NodeM::getHllNodeid).collect(Collectors.toList());
                List<String> breakLines = breakLine(srcLinkGeomWkt, nodes);
                List<LinkM> breakInsertLine = new ArrayList<>();

                for (int i = 0; i < breakLines.size(); i++) {
                    LinkM link = new LinkM();
                    BeanUtil.copyProperties(srcLink, link);
                    // if (i == breakLines.size() - 1) {
                    //    i = breakLines.size() - 1 - 1;
                    //}
                    // for (int j = i; j < nodeIds.size(); j++) {
                    //    if (i == 0) {
                    //        link.setHllENid(nodeIds.get(j));
                    //        break;
                    //    } else if (i == nodeIds.size()) {
                    //        link.setHllSNid(nodeIds.get(j));
                    //        break;
                    //    } else {
                    //        link.setHllSNid(nodeIds.get(j - 1));
                    //        link.setHllENid(nodeIds.get(j));
                    //        break;
                    //    }
                    //
                    //}
                    if (i == 0) {
                        link.setHllENid(nodeIds.get(i));
                    } else if (i == breakLines.size() - 1) {
                        link.setHllSNid(nodeIds.get(i - 1));
                    } else {
                        link.setHllSNid(nodeIds.get(i - 1));
                        link.setHllENid(nodeIds.get(i));
                    }
                    // TODO id规格的设计
                    // link.setHllLinkid(UUID.randomUUID().toString());
                    link.setHllLinkid(String.valueOf(inheritIDService.createID(new InheritIDDTO(12L, 1L)).get(0)));
                    link.setLinkId(link.getHllLinkid());
                    link.setGeomwkt(breakLines.get(i));
                    link.setGeometry("SRID=4326;" + breakLines.get(i));
                    // link.setLen(wktReader.read(breakLines.get(i)).getLength() * 111000);
                    link.setLen(wktReader.read(link.getGeomwkt()).getLength() * 111000);
                    link.setUpDate(LocalDateTime.now());
                    link.setStatus(0);
                    link.setMemo("BREAK;" + srcLink.getHllLinkid());
                    breakInsertLine.add(link);
                }
                if (CollUtil.isEmpty(breakInsertLine)) {
                    continue;
                }
                // 删除被打断的原始link
                this.lambdaUpdate().set(LinkM::getStatus, 1).set(LinkM::getMemo, "BREAKED").eq(LinkM::getHllLinkid, srcLink.getHllLinkid()).update();
                linkMMapper.mysqlInsertOrUpdateBath(breakInsertLine);

                // for (String breakLine : breakLines) {
                //    LinkM link = new LinkM();
                //    BeanUtil.copyProperties(srcLink,link);
                //    link.setHllLinkid(UUID.randomUUID().toString());
                //    link.setLinkId(link.getHllLinkid());
                //    link.setGeomwkt(breakLine);
                //    link.setGeometry("SRID=4326;"+breakLine);
                //    link.setLen(wktReader.read(breakLine).getLength() * 111000);
                //    link.setUpDate(LocalDateTime.now());
                //    link.setStatus(0);
                //}

                LinkM firstLine = breakInsertLine.get(0);
                NodeM firstNode = nodeMService.getOne(Wrappers.<NodeM>lambdaQuery().eq(NodeM::getHllNodeid, firstLine.getHllSNid()));
                LinkM endLine = breakInsertLine.get(breakInsertLine.size() - 1);

                // 更新relation rule
                List<RelationM> relationList = relationService.lambdaQuery().eq(RelationM::getStatus, 0).and(wrappers -> wrappers.eq(RelationM::getInlinkId, srcLink.getHllLinkid())
                        .or().eq(RelationM::getOutlinkId, srcLink.getHllLinkid())).list();

                for (RelationM relation : relationList) {
                    NodeM node = nodeMService.getOne(Wrappers.<NodeM>lambdaQuery().eq(NodeM::getHllNodeid, relation.getNodeId()));
                    String nodePnWkt = node.getGeomwkt();
                    if (srcLink.getHllLinkid().equals(relation.getInlinkId())) {
                        if (firstNode.getGeomwkt().equals(nodePnWkt)) {
                            relation.setInlinkId(firstLine.getHllLinkid());
                        } else {
                            relation.setInlinkId(endLine.getHllLinkid());
                        }
                        relation.setUpDate(LocalDateTime.now());
                        relationMapper.updateById(relation);
                    }
                    if (srcLink.getHllLinkid().equals(relation.getOutlinkId())) {
                        if (firstNode.getGeomwkt().equals(nodePnWkt)) {
                            relation.setOutlinkId(firstLine.getHllLinkid());
                        } else {
                            relation.setOutlinkId(endLine.getHllLinkid());
                        }
                        relation.setUpDate(LocalDateTime.now());
                        relationMapper.updateById(relation);
                    }
                }

                List<RuleM> ruleList = ruleService.lambdaQuery().eq(RuleM::getStatus, 0)
                        .and(wrappers -> wrappers.eq(RuleM::getInlinkId, srcLink.getHllLinkid()).or().eq(RuleM::getOutlinkId, srcLink.getHllLinkid())).list();
                for (RuleM rule : ruleList) {
                    NodeM node = nodeMService.getOne(Wrappers.<NodeM>lambdaQuery().eq(NodeM::getHllNodeid, rule.getNodeId()));
                    String nodePnWkt = node.getGeomwkt();
                    if (srcLink.getHllLinkid().equals(rule.getInlinkId())) {
                        if (firstNode.getGeomwkt().equals(nodePnWkt)) {
                            rule.setInlinkId(firstLine.getHllLinkid());
                        } else {
                            rule.setInlinkId(endLine.getHllLinkid());
                        }
                        rule.setUpDate(LocalDateTime.now());
                        ruleMapper.updateById(rule);
                    }
                    if (srcLink.getHllLinkid().equals(rule.getOutlinkId())) {
                        if (firstNode.getGeomwkt().equals(nodePnWkt)) {
                            rule.setOutlinkId(firstLine.getHllLinkid());
                        } else {
                            rule.setOutlinkId(endLine.getHllLinkid());
                        }
                        rule.setUpDate(LocalDateTime.now());
                        ruleMapper.updateById(rule);
                    }
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            downLatch.countDown();
        }

    }

    private NodeM getUpdatedNode(List<NodeM> seNodes, String targetPoint, WKTReader wktReader) throws
            ParseException {
        TreeMap<Double, NodeM> nodeTreeMap = new TreeMap<>();
        for (NodeM node : seNodes) {
            nodeTreeMap.put(wktReader.read(targetPoint).distance(wktReader.read(node.getGeomwkt())), node);
        }
        NodeM updateNode = nodeTreeMap.get(nodeTreeMap.firstKey());
        updateNode.setGeometry("SRID=4326;" + targetPoint);
        updateNode.setGeomwkt(targetPoint);
        return updateNode;
    }

    /**
     * 根据传入node集合，对目标line，进行打断处理，返回所有打断的线
     * eg：一条line，3个node，返回4条线
     *
     * @param line
     * @param nodes node点需按离line起点顺序排序
     * @return
     * @throws ParseException
     */
    private List<String> breakLine(String line, List<String> nodes) throws ParseException {
        WKTReader wktReader = new WKTReader();
        String pureLine = removeBracket(line);
        List<String> resList = new ArrayList<>();
        List<String> lines = breakHandle(line);
        String tmpLine = null;
        List<String> sectionList = new ArrayList<>();
        for (int i = 0; i < nodes.size(); i++) {
            Geometry pnGeom = wktReader.read(nodes.get(i));
            if (i == 0) {
                tmpLine = pureLine;
            }
            String osmSectionLine = "";
            for (String subLine : lines) {
                Geometry secGeom = wktReader.read(subLine);
                if (pnGeom.intersects(secGeom.buffer(0.000001))) {
                    osmSectionLine = subLine;
                    sectionList.add(osmSectionLine);
                    break;
                }
            }
            if (i == 0 || !sectionList.contains(osmSectionLine) || tmpLine.contains(removeBracket(osmSectionLine))) {


                String pureSection = removeBracket(osmSectionLine);
                String[] splitStrs = pureSection.split(",");
                String pureNode = removeBracket(nodes.get(i));
                String insertLine = tmpLine.replace(pureSection, splitStrs[0] + "," + pureNode + "," + splitStrs[1]);
                String[] split = insertLine.split(pureNode);
                String line1 = split[0] + pureNode;
                tmpLine = pureNode + split[1];
                resList.add(line1);
            } else {
                String line1 = removeBracket(nodes.get(i - 1)) + "," + removeBracket(nodes.get(i));
                tmpLine = tmpLine.replace(removeBracket(nodes.get(i - 1)), removeBracket(nodes.get(i)));
                resList.add(line1);
            }
            if (i == nodes.size() - 1) {
                resList.add(tmpLine);
            }
        }
        List<String> finalList = new ArrayList<>();
        resList.forEach(s -> {
            if (s.endsWith(",") || s.indexOf(",") == -1) {
                // s = StrUtil.removeSuffix(s, ",");
                return;
            }
            finalList.add(StrUtil.wrap(s, "MULTILINESTRING((", "))"));
        });

        return finalList;
    }


    @Async("asyncTaskExecutor")
    public void processFormway(String country, String area, CountDownLatch
            countDownLatch, List<LinkM> batchLinkMList) {

        try {
            for (LinkM linkM : batchLinkMList) {

                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
                Streets street = streetsService.lambdaQuery().eq(Streets::getLinkId, Long.parseLong(linkM.getLinkId())).one();
                if ("Y".equals(street.getMultidigit())) {
                    if ("1".equals(linkM.getFormway())) {
                        linkM.setFormway("81");
                    } else {
                        linkM.setFormway(linkM.getFormway() + ",81");
                    }
                }
            }

            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            linkMMapper.mysqlInsertOrUpdateBath(batchLinkMList);
            countDownLatch.countDown();
        } catch (Exception e) {
            log.error("更新formway异常,{}", e.getMessage());
        }
    }

    @Async("asyncTaskExecutor")
    //@Async()
    public void linkConvertTT(List<PlanetOsmLine> planetOsmLineList, Boolean isCompileNode, Boolean isCompileTransEng, String area,
                              String country, CountDownLatch countDownLatch) throws SQLException, InterruptedException {

        // MybatisPlusConfig.myTableName.set("_"+area);
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            log.info("processing country:" + CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        log.info("tmp thread is:" + Thread.currentThread().getName());
        List<LinkM> linList = new ArrayList<>();
        List<NodeM> nodeList = new ArrayList<>();

        try {
            if (planetOsmLineList.size() > 0) {
                for (PlanetOsmLine planetOsmLine : planetOsmLineList
                ) {
                    LinkM link = new LinkM();
                    NodeM node = new NodeM();
                    link.setLinkId(planetOsmLine.getOsmId().toString());
                    // link.setHllLinkid(UUID.randomUUID().toString());
                    // 使用id继承，2022-01-20
                    // List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(link.getLinkId())));
                    // link.setHllLinkid(String.valueOf(inheritID.get(0)));
                    link.setHllLinkid(link.getLinkId());
                    // 使用id继承，2022-01-20
                    // List<Long> inheritID1 = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(streets.getRefInId().toString())));
                    // link.setHllSNid(String.valueOf(inheritID1.get(0)));
                    PlanetOsmWays ways = planetOsmWaysService.lambdaQuery().eq(PlanetOsmWays::getId, planetOsmLine.getOsmId()).one();
                    link.setHllSNid(ways.getNodes()[0].toString());
                    link.setHllENid(ways.getNodes()[ways.getNodes().length - 1].toString());

                    // KIND
                    if ("motorway".equals(planetOsmLine.getHighway()) || "motorway_link".equals(planetOsmLine.getHighway())) {
                        link.setKind("2");
                    } else if ("trunk".equals(planetOsmLine.getHighway()) || "trunk_link".equals(planetOsmLine.getHighway())) {
                        link.setKind("3");
                    } else if ("primary".equals(planetOsmLine.getHighway()) || "primary_link".equals(planetOsmLine.getHighway())) {
                        link.setKind("4");
                    } else if ("secondary".equals(planetOsmLine.getHighway()) || "secondary_link".equals(planetOsmLine.getHighway())) {
                        link.setKind("6");
                    } else if ("tertiary".equals(planetOsmLine.getHighway()) || "tertiary_link".equals(planetOsmLine.getHighway()) || "residential".equals(planetOsmLine.getHighway())) {
                        link.setKind("7");
                    } else if ("unclassified".equals(planetOsmLine.getHighway()) || "service".equals(planetOsmLine.getHighway()) || "living_street".equals(planetOsmLine.getHighway()) || "road" == planetOsmLine.getHighway() || "track" == planetOsmLine.getHighway()) {
                        link.setKind("8");
                    } else if ("pedestrian".equals(planetOsmLine.getHighway()) || "footway".equals(planetOsmLine.getHighway()) || "path".equals(planetOsmLine.getHighway()) || "steps".equals(planetOsmLine.getHighway()) || "cycleway".equals(planetOsmLine.getHighway())) {
                        link.setKind("10");
                    } else if ("footway".equals(planetOsmLine.getHighway()) && "ferry".equals(planetOsmLine.getRoute())) {
                        link.setKind("11");
                    } else if ("ferry".equals(planetOsmLine.getRoute())) {
                        link.setKind("13");
                    } else if ("cycleway".equals(planetOsmLine.getHighway())) {
                        link.setKind("14");
                    } else {
                        link.setKind("8");
                    }
                    // FORMWAY
                    String formWay = "";
//                    if ("Y".equals(streets.getInprocdata())) {
//                        formWay += ",0";
//                        // link.setFormway("0");
//                    }
                    if ("motorway_link".equals(planetOsmLine.getHighway())) {
                        formWay += ",11";
                        // link.setFormway("11");
                    }
                    if ("service_area".equals(planetOsmLine.getAmenity())) {
                        formWay += ",12";
                        // link.setFormway("15");
                    }
                    if ("parking".equals(planetOsmLine.getAmenity())) {
                        formWay += ",13";
                        // link.setFormway("15");
                    }
                    if ("motorway".equals(planetOsmLine.getHighway())) {
                        formWay += ",14";
                        // link.setFormway("14");
                    }

                    if ("private".equals(planetOsmLine.getAccess())) {
                        formWay += ",18";
                        // link.setFormway("18");
                    }
                    if ("pedestrian".equals(planetOsmLine.getHighway())) {
                        formWay += ",20";
                        // link.setFormway("20");
                    }
                    if ("busway".equals(planetOsmLine.getHighway())) {
                        formWay += ",22";
                        // link.setFormway("22");
                    }
                    if ("yes".equals(planetOsmLine.getBridge())) {
                        formWay += ",30";
                        // link.setFormway("30");
                    }
                    if ("yes".equals(planetOsmLine.getTunnel())) {
                        formWay += ",31";
                        // link.setFormway("31");
                    }
                    if ("roundabout".equals(planetOsmLine.getJunction())) {
                        formWay += ",33";
                        // link.setFormway("33");
                    }
                    String forwayReplace = formWay.replaceFirst(",", "");
                    if (formWay.length() > 0) {
                        // log.info("formway is:"+forwayReplace);
                        link.setFormway(forwayReplace);
                    } else {
                        link.setFormway("1");
                    }
                    // DIR
                    if (planetOsmLine.getOneway() == null) {
                        link.setDir("0");
                    } else {
                        switch (planetOsmLine.getOneway()) {
                            case "no":
                                link.setDir("0");
                                break;
                            case "yes":
                                link.setDir("2");
                                break;
                            case "-1":
                                link.setDir("3");
                                break;
                            default:
                                link.setDir("0");
                        }
                    }

                    // APP
                    if (planetOsmLine.getAccess() == null) {
                        link.setApp("1");
                    } else {
                        if ("no".equals(planetOsmLine.getAccess())) {
                            link.setApp("2");
                        } else {
                            link.setApp("1");
                        }
                    }
                    // TOLL
                    if ("yes".equals(planetOsmLine.getToll())) {
                        link.setToll("1");
                    } else if ("no".equals(planetOsmLine.getToll())) {
                        link.setToll("2");
                    } else {
                        link.setToll("0");
                    }
                    // MD
                    if ("yes".equals(planetOsmLine.getOneway()) && planetOsmLine.getHighway() != null) {
                        link.setMd("1");
                    } else {
                        link.setMd("0");
                    }
                    // SPET
                    // FUNCT
                    if (planetOsmLine.getHighway() == null) {
                        link.setFunct("0");
                    } else {
                        switch (planetOsmLine.getHighway()) {
                            case "motorway":
                            case "motorway_link":
                                link.setFunct("1");
                                break;
                            case "trunk":
                            case "trunk_link":
                                link.setFunct("2");
                                break;
                            case "primary":
                            case "primary_link":
                                link.setFunct("3");
                                break;
                            case "secondary":
                            case "secondary_link":
                                link.setFunct("4");
                                break;
                            case "tertiary":
                            case "tertiary_link":
                                link.setFunct("5");
                                break;
                            default:
                                link.setFunct("0");
                        }
                    }
                    // URBAN
                    // PAVE
                    //paved,asphalt,chipseal,concrete,concrete:lanes,concrete:plates,paving_stones,paving_stones:lanes
                    //,grass_paver,sett,unhewn_cobblestone,cobblestone,bricks,metal,metal_grid,wood,stepping_stones
                    // ,rubber,tiles,fibre_reinforced_polymer_grate
                    if ("paved".equals(planetOsmLine.getSurface()) || "asphalt".equals(planetOsmLine.getSurface())
                            || "chipseal".equals(planetOsmLine.getSurface()) || "concrete".equals(planetOsmLine.getSurface())
                            || "concrete:lanes".equals(planetOsmLine.getSurface()) || "concrete:plates".equals(planetOsmLine.getSurface())
                            || "paving_stones".equals(planetOsmLine.getSurface()) || "paving_stones:lanes".equals(planetOsmLine.getSurface())
                            || "grass_paver".equals(planetOsmLine.getSurface()) || "sett".equals(planetOsmLine.getSurface())
                            || "unhewn_cobblestone".equals(planetOsmLine.getSurface()) || "cobblestone".equals(planetOsmLine.getSurface())
                            || "bricks".equals(planetOsmLine.getSurface()) || "metal".equals(planetOsmLine.getSurface())
                            || "metal_grid".equals(planetOsmLine.getSurface()) || "wood".equals(planetOsmLine.getSurface())
                            || "stepping_stones".equals(planetOsmLine.getSurface()) || "rubber".equals(planetOsmLine.getSurface())
                            || "tiles".equals(planetOsmLine.getSurface()) || "fibre_reinforced_polymer_grate".equals(planetOsmLine.getSurface())) {
                        link.setPave("1");
                    } else {
                        link.setPave("0");
                    }
                    // LANE_N
                    // LANE_L
                    // LANE_R
                    // LANE_C
                    // L_ADMIN
                    // R_ADMIN
                    // geom
                    link.setGeometry(planetOsmLine.getWay());
                    // LEN
                    // link.setLen(streets.getLen() * 111000);
                    // F_SPEED
                    // T_SPEED
                    // SP_CLASS
                    // AR_VEH
                    //1	客车
                    //2	配送卡车
                    //3	运输卡车
                    //4	步行者
                    //5	自行车
                    //6	摩托车
                    //7	机动脚踏两用车
                    //8	急救车
                    //9	出租车
                    //10	公交车
                    //11	工程车
                    //12	本地车辆
                    //13	自用车辆
                    //14	多人乘坐车辆
                    //15	军车
                    //16	有拖车的车
                    //17	私营公共汽车
                    //18	农用车
                    //19	载有易爆品的车辆
                    //20	载有水污染品的车辆
                    //21	载有其它危险品的车辆
                    //22	电车
                    //23	轻轨
                    //24	校车
                    //25	四轮驱动车
                    //26	装有防雪链的车
                    //27	邮政车
                    //28	槽罐车
                    //29	残疾人车
                    // NAME_CH_O
                    if (planetOsmLine.getName() != null) {
                        link.setNameChO(planetOsmLine.getName());
                    }
                    // NAME_CH_A
                    // NAME_CH_F
                    // NAME_EN_A
                    // NAME_EH_F
                    // CODE_TYPE
                    // NAME_TYPE
                    // SRC_FLAG
                    // UP_DATE
                    link.setUpDate(LocalDateTime.now());
                    // STATUS
                    link.setStatus(0);
                    // DATASOURCE
                    link.setDatasource("7");
                    // PUB_ACCESS
                    linList.add(link);
                }
                log.info("linklist size is:" + linList.size());
                // this.saveOrUpdateBatch(linList);
                List<List<LinkM>> linListPartition = Lists.partition(linList, 32767 / BeanUtil.beanToMap(new LinkM()).keySet().size());
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }

                for (List<LinkM> partitionList : linListPartition) {
                    // log.info("linkMapper threadlocal info is:" + Thread.currentThread().getName());
                    // linMapper.mysqlInsertOrUpdateBath(partitionList);
                    //TODO: 正式生产时需要打开进行id赋值，测试时注释掉
//                    List<Long> linkIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(LinkM::getLinkId).collect(Collectors.toList())));
//                    List<Long> sNodeIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(LinkM::getHllSNid).collect(Collectors.toList())));
//                    List<Long> eNodeIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(LinkM::getHllENid).collect(Collectors.toList())));
//                    for (int i = 0; i < partitionList.size(); i++) {
//                        partitionList.get(i).setHllLinkid(String.valueOf(linkIds.get(i)));
//                        partitionList.get(i).setHllSNid(String.valueOf(sNodeIds.get(i)));
//                        partitionList.get(i).setHllENid(String.valueOf(eNodeIds.get(i)));
//                    }
                    linkMMapper.mysqlInsertOrUpdateBath(partitionList);
                    // this.saveOrUpdateBatch(partitionList);
                }

                // log.info("node list size is:{},nodeSw2021 is:{}",nodeList.size(),nodeList.stream().map(p->p.getNodeId()).collect(Collectors.toList()));
                if (isCompileNode) {
                    log.info("node list size is:{}", nodeList.size());
                    // log.info("set size is:{},nodeIdSet is:{}",nodeIdSet.size(),nodeIdSet);
                    // nodeService.saveOrUpdateBatch(nodeList);
                    List<List<NodeM>> nodeListPartion = Lists.partition(nodeList, 32767 / BeanUtil.beanToMap(new NodeM()).keySet().size());
                    if (!country.isEmpty()) {
                        DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                    }
                    if (!area.isEmpty()) {
                        MybatisPlusConfig.myTableName.set("_" + area);
                    } else {
                        MybatisPlusConfig.myTableName.set("");
                    }
                    for (List<NodeM> partitionList : nodeListPartion
                    ) {
                        // log.info("nodeService threadlocal info is:"+Thread.currentThread().getName());
                        // log.info("nodeService threadlocal info is:" + Thread.currentThread().getName());
                        // nodeMapper.mysqlInsertOrUpdateBath(partitionList);
                        List<Long> nodeIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(NodeM::getNodeId).collect(Collectors.toList())));
                        for (int i = 0; i < partitionList.size(); i++) {
                            partitionList.get(i).setHllNodeid(String.valueOf(nodeIds.get(i)));
                        }
                        nodeMMapper.mysqlInsertOrUpdateBath(partitionList);
                        // nodeService.saveOrUpdateBatch(partitionList);
                    }
                }
            }
        } catch (Exception e) {
            log.error("link node convert error,msg is {}", e);
            throw new RuntimeException("link node convert error,msg is {}" + e.getMessage());
        } finally {
            countDownLatch.countDown();
        }
    }

    @Async("asyncTaskExecutor")
    public void handleTileWkt(String country, String area, List<String> tileIds, CountDownLatch countDownLatch, ConcurrentLinkedQueue<Exception> exceptions) {
        try {

            List<TileView> resList = new ArrayList<>();
            for (String tileId : tileIds) {
                TileView tileView = new TileView();
                tileView.setTileId(tileId);
                tileView.setTileWkt(CommonUtils.h3IndexToWKT(tileId));
                resList.add(tileView);
            }
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            tileViewMapper.mysqlInsertOrUpdateBath(resList);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("handleTileWkt error, msg is {}", e.getMessage());
            // throw new RuntimeException("handleTileWkt error, msg is {}" + e.getMessage());
            exceptions.offer(e);
        } finally {
            countDownLatch.countDown();
        }
    }


    public JSONObject nameConverter(String nameType, String nameValue, String nmLangcd) {

        String[] names = nameValue.split("\\|");
        String[] langs = nmLangcd.split("\\|");

        JSONArray variantsArray = new JSONArray();
        for (int i = 0; i < names.length; i++) {
            JSONObject variant = new JSONObject();
            variant.put(langs[i], names[i]);
            variantsArray.add(variant);
        }

        JSONObject result = new JSONObject();
        result.put(nameType, variantsArray);

        return result;
    }

    public JSONArray nameConverterList(String nameValue, String nmLangcd) {

        String[] names = nameValue.split("\\|");
        String[] langs = nmLangcd.split("\\|");

        JSONArray variantsArray = new JSONArray();
        for (int i = 0; i < names.length; i++) {
            JSONObject variant = new JSONObject();
            variant.put(langs[i], names[i]);
            variantsArray.add(variant);
        }

        return variantsArray;
    }

    private void setLinkNames(LinkM link, Streets streets, List<HerePhaAltstreets> herePhaAltstreetsList) {
        // NAME_CH_O
        String nameCho = "";
        String nmChoLangcd = "";
        JSONArray nameChoArray = new JSONArray();
        if (streets.getNumStnmes() == 1) {
            link.setNameChO(streets.getStName());
            nmChoLangcd = streets.getStLangcd();
            link.setNmChoLangcd(nmChoLangcd);
        } else if (streets.getNumStnmes() > 1) {
            if ("N".equals(streets.getStalename())) {
                nameCho += streets.getStName();
                nmChoLangcd += streets.getStLangcd();
                if (herePhaAltstreetsList.size() > 0) {
                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList) {
                        if ("N".equals(herePhaAltstreets.getStalename())) {
                            nameCho += "|" + herePhaAltstreets.getStName();
                            nmChoLangcd += "|" + herePhaAltstreets.getStLangcd();
                        }
                    }
                }
            } else {
                if (herePhaAltstreetsList.size() > 0) {
                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList) {
                        if ("N".equals(herePhaAltstreets.getStalename())) {
                            nameCho += "|" + herePhaAltstreets.getStName();
                            nmChoLangcd += "|" + herePhaAltstreets.getStLangcd();
                        }
                    }
                }
            }
            if (nameCho.length() > 0) {
                if ("|".equals(nameCho.substring(0, 1))) {
                    nameCho = nameCho.replaceFirst("\\|", "");
                    nmChoLangcd = nmChoLangcd.replaceFirst("\\|", "");
                }
                link.setNameChO(nameCho);
                link.setNmChoLangcd(nmChoLangcd);
                nameChoArray = nameConverterList(nameCho, nmChoLangcd);
            } else {
                link.setNameChO(null);
                link.setNmChoLangcd(null);
            }
        }

        // NAME_CH_A
        String nameCha = "";
        String nmChaLangcd = "";
        JSONArray nameChaArray = new JSONArray();
        if (streets.getNumStnmes() == 1) {
            if ("Y".equals(streets.getVanityname())) {
                nameCha += streets.getStName();
                nmChaLangcd += streets.getStLangcd();
                link.setNameChA(nameCha);
                link.setNmChaLangcd(nmChaLangcd);
            }
        } else if (streets.getNumStnmes() > 1) {
            if ("Y".equals(streets.getVanityname())) {
                nameCha += streets.getStName();
                nmChaLangcd += streets.getStLangcd();
                if (herePhaAltstreetsList.size() > 0) {
                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList) {
                        if ("Y".equals(herePhaAltstreets.getVanityname())) {
                            nameCha += "|" + herePhaAltstreets.getStName();
                            nmChaLangcd += "|" + herePhaAltstreets.getStLangcd();
                        }
                    }
                }
            } else {
                if (herePhaAltstreetsList.size() > 0) {
                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList) {
                        if ("Y".equals(herePhaAltstreets.getVanityname())) {
                            nameCha += "|" + herePhaAltstreets.getStName();
                            nmChaLangcd += "|" + herePhaAltstreets.getStLangcd();
                        }
                    }
                }
            }
            if (nameCha.length() > 0) {
                if ("|".equals(nameCha.substring(0, 1))) {
                    nameCha = nameCha.replaceFirst("\\|", "");
                    nmChaLangcd = nmChaLangcd.replaceFirst("\\|", "");
                }
                link.setNameChA(nameCha);
                nameChaArray = nameConverterList(nameCha, nmChaLangcd);
                link.setNmChaLangcd(nmChaLangcd);
            } else {
                link.setNameChA(null);
                link.setNmChaLangcd(null);
            }
        }

        // NAME_CH_F
        String nameChf = "";
        String nmChfLangcd = "";
        JSONArray nameChfArray = new JSONArray();
        if (streets.getNumStnmes() == 1) {
            if ("Y".equals(streets.getStalename())) {
                nameChf += streets.getStName();
                nmChfLangcd += streets.getStLangcd();
                link.setNameChF(nameChf);
                link.setNmChfLangcd(nmChfLangcd);
            }
        } else if (streets.getNumStnmes() > 1) {
            if ("Y".equals(streets.getStalename())) {
                nameChf += streets.getStName();
                nmChfLangcd += streets.getStLangcd();
                if (herePhaAltstreetsList.size() > 0) {
                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList) {
                        if ("Y".equals(herePhaAltstreets.getStalename())) {
                            nameChf += "|" + herePhaAltstreets.getStName();
                            nmChfLangcd += "|" + herePhaAltstreets.getStLangcd();
                        }
                    }
                }
            } else {
                if (herePhaAltstreetsList.size() > 0) {
                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList) {
                        if ("Y".equals(herePhaAltstreets.getStalename())) {
                            nameChf += "|" + herePhaAltstreets.getStName();
                            nmChfLangcd += "|" + herePhaAltstreets.getStLangcd();
                        }
                    }
                }
            }
            if (nameChf.length() > 0) {
                if ("|".equals(nameChf.substring(0, 1))) {
                    nameChf = nameChf.replaceFirst("\\|", "");
                    nmChfLangcd = nmChfLangcd.replaceFirst("\\|", "");
                }
                link.setNameChF(nameChf);
                nameChfArray = nameConverterList(nameChf, nmChfLangcd);
                link.setNmChfLangcd(nmChfLangcd);
            } else {
                link.setNameChF(null);
                link.setNmChfLangcd(null);
            }
            JSONObject nameJson = new JSONObject();
            nameJson.put("nameChO", nameChoArray);
            nameJson.put("nameChA", nameChaArray);
            nameJson.put("nameChF", nameChfArray);
            link.setName(nameJson.toMap());
        }
    }


    /**
     * Process a batch of CF IDs to extract node relationships
     * This method runs asynchronously in the optimized thread pool
     */
    @Async("optimizedAsyncTaskExecutor")
    public void processCfidBatchAsync(List<String> cfidBatch, Map<String, String> rdfCfMap,
                                      MultiValueMap<String, String> rdfCfLinkMap,
                                      MultiValueMap<String, String> rdfCfNodeMap,
                                      MultiValueMap<String, Integer> cfidNodesMap,
                                      List<NodeM> nodeList, String area, String country,
                                      CountDownLatch countDownLatch,ConcurrentLinkedQueue<Exception> exceptions) {

        String currentThread = Thread.currentThread().getName();
        log.info("Processing CF ID batch on thread: {} - Batch size: {}", currentThread, cfidBatch.size());

        try {
            for (String cfid : cfidBatch) {
                List<String> nodeIdlist = rdfCfNodeMap.get(cfid);

                // Process type "2" nodes
                if (nodeIdlist != null) {
                    for (String nodeId : nodeIdlist) {
                        NodeM node = new NodeM();
                        node.setNodeId(nodeId);
                        List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(nodeId)));
                        node.setHllNodeid(inheritID.toString());
                        node.setType("2");
                        node.setUpDate(LocalDateTime.now());

                        // Add area assignment for type "2" nodes
                        final int areaAssign = StrUtil.isNotBlank(area)
                                ? Integer.parseInt(area.replaceAll("\\D+", ""))
                                : 0;
                        node.setArea(areaAssign);

                        // Thread-safe addition to the shared list
                        // synchronized (nodeList) {
                        nodeList.add(node);
                        // }
                    }
                }
            }

            log.info("Completed CF ID batch processing on thread: {} - Processed {} CFIDs",
                    currentThread, cfidBatch.size());

        } catch (Exception e) {
            log.error("Error processing CF ID batch on thread: {}", currentThread, e);
            exceptions.offer(e);
        } finally {
            countDownLatch.countDown();
            log.debug("CountDownLatch decremented by thread: {}", currentThread);
        }
    }
}

