package com.hll.mapdataservice.business.api.poi.controller;


import com.hll.mapdataservice.business.api.poi.service.HerePhaPoiServiceImpl;
import com.hll.mapdataservice.business.api.poi.service.HereThaPoiCopy1ServiceImpl;
import com.hll.mapdataservice.business.api.poi.service.HereThaPoiServiceImpl;
import com.hll.mapdataservice.business.api.poi.service.ReportPoiServiceImpl;
import com.hll.mapdataservice.business.common.XmlFileUtils;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.mapper.HereThaPoiMapper;
import com.hll.mapdataservice.common.mapper.ReportPoiMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-19
 */
@RestController
@ResponseBody
@RequestMapping("/api/poi/herethapoi")
@Api(tags ="poi")
@Component
public class HereThaPoiController {

    @Resource
    private ReportPoiMapper reportPoiMapper;

    @Resource
    private ReportPoiServiceImpl reportPoiService;

    @Resource
    private HereThaPoiServiceImpl hereThaPoiService;

    @Resource
    private HereThaPoiMapper hereThaPoiMapper;

    @Resource
    private HereThaPoiCopy1ServiceImpl hereThaPoiCopy1Service;

    @Resource
    private HerePhaPoiServiceImpl herePhaPoiService;

    @GetMapping("getById")
    @ApiOperation(value = "getById")
    public HereThaPoiCopy1 getPoiById(@RequestParam(value = "featId") String featId) {
        //return mnrNetwGeoLinkService.listByMap();
        //return mnrNetwGeoLinkMapper.selectById(UUID.fromString("00005448-**************-000000f09a71"));
        return hereThaPoiCopy1Service.lambdaQuery().eq(HereThaPoiCopy1::getPoiEntityId, featId).list().get(0);
        //return mnrNetwGeoLinkService.getById(UUID.fromString("00005448-**************-000000f09a71"));
    }

    @GetMapping("/importPoi")
    @ApiOperation(value = "importPoi")
    public void importPoi(@RequestParam String filePath){

        File filePaths = new File(filePath);
        File[] files = filePaths.listFiles(file->file.getName().toLowerCase().endsWith(".xml"));
        System.out.println(files.toString());
        List<HereThaPoiCopy1> hereThaPoiListAll = new ArrayList<>();

        for(File file:files){
           //hereThaPoiService.saveHerePoi(file);
            //hereThaPoiListAll.addAll(new XmlFileUtils().herePoiXmlRead(file));
            System.out.println("processing file:"+file.toString());
            hereThaPoiService.saveBatch(new XmlFileUtils().herePoiXmlRead(file));
            System.out.println("finished processing file:"+file.toString());
        }
        //List<HereThaPoi> hereThaPoiList = new XmlFileUtils().herePoiXmlReadBatch(filePath);
        //hereThaPoiService.saveBatch(hereThaPoiListAll);
    }
    @GetMapping("/importPoiBatch")
    @ApiOperation(value = "importPoiBatch")
    public void importPoiBatch(@RequestParam String filePath){

        File filePaths = new File(filePath);
        File[] files = filePaths.listFiles(file->file.getName().toLowerCase().endsWith(".xml"));
        //按文件名排序
        List fileList = Arrays.asList(files);
        Collections.sort(fileList, new Comparator<File>() {
            @Override
            public int compare(File o1, File o2) {
                if (o1.isDirectory() && o2.isFile())
                    return -1;
                if (o1.isFile() && o2.isDirectory())
                    return 1;
                return o1.getName().compareTo(o2.getName());
            }
        });

        for(File file:files){
            //hereThaPoiService.saveHerePoi(file);
            //hereThaPoiListAll.addAll(new XmlFileUtils().herePoiXmlRead(file));
            System.out.println("processing file:"+file.toString());
            long startTime = System.currentTimeMillis();
            List<HereThaPoiCopy1> hereThaPoiCopy1List = new XmlFileUtils().herePoiXmlReadCopy(file);
            hereThaPoiCopy1Service.saveBatch(hereThaPoiCopy1List);
            long endTime = System.currentTimeMillis();
            System.out.println("finished processing file:"+file.toString()+" cost "+(endTime-startTime)/1000+"s");
        }
        //List<HereThaPoi> hereThaPoiList = new XmlFileUtils().herePoiXmlReadBatch(filePath);
        //hereThaPoiService.saveBatch(hereThaPoiListAll);
    }
    @GetMapping("/importHerePhaPoiBatch")
    @ApiOperation(value = "importHerePhaPoiBatch")
    //@DS("db4")
    public void importHerePhaPoiBatch(@RequestParam String filePath){

        File filePaths = new File(filePath);
        File[] files = filePaths.listFiles(file->file.getName().toLowerCase().endsWith(".xml"));
        //按文件名排序
        List fileList = Arrays.asList(files);
        Collections.sort(fileList, new Comparator<File>() {
            @Override
            public int compare(File o1, File o2) {
                if (o1.isDirectory() && o2.isFile())
                    return -1;
                if (o1.isFile() && o2.isDirectory())
                    return 1;
                return o1.getName().compareTo(o2.getName());
            }
        });

        for(File file:files){
            //hereThaPoiService.saveHerePoi(file);
            //hereThaPoiListAll.addAll(new XmlFileUtils().herePoiXmlRead(file));
            System.out.println("processing file:"+file.toString());
            long startTime = System.currentTimeMillis();
            List<HerePhaPoi> hereThaPoiCopy1List = new XmlFileUtils().herePhaPoiXmlRead(file);
            herePhaPoiService.saveBatch(hereThaPoiCopy1List);
            long endTime = System.currentTimeMillis();
            System.out.println("finished processing file:"+file.toString()+" cost "+(endTime-startTime)/1000+"s");
        }
        //List<HereThaPoi> hereThaPoiList = new XmlFileUtils().herePoiXmlReadBatch(filePath);
        //hereThaPoiService.saveBatch(hereThaPoiListAll);
    }
}

