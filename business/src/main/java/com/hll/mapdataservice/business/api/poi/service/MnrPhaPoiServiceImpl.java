package com.hll.mapdataservice.business.api.poi.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hll.mapdataservice.common.entity.MnrPhaPoi;
import com.hll.mapdataservice.common.mapper.MnrPhaPoiMapper;
import com.hll.mapdataservice.common.service.IMnrPhaPoiService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-19
 */
@Service
@DS("db6")
public class MnrPhaPoiServiceImpl extends ServiceImpl<MnrPhaPoiMapper, MnrPhaPoi> implements IMnrPhaPoiService {

}
