package com.hll.mapdataservice.business.common;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component
public class Spring<PERSON>ontainerGetter implements ApplicationContextAware {

    private static ApplicationContext applicationContext = null;

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        if (SpringContainerGetter.applicationContext == null)
            SpringContainerGetter.applicationContext = applicationContext;
    }

    public static String getProperty(String key) {
        return SpringContainerGetter.applicationContext.getEnvironment().getProperty(key);
    }

    public static Object getBean(Class<?> clazz) {
        return SpringContainerGetter.applicationContext.getBean(clazz);
    }
}
