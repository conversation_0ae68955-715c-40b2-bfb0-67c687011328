package com.hll.mapdataservice.business.api.poi.service.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
@Data
@Getter
@Setter
public class PoiExportInfo {

    private String id;

    private String linkid;

    private String source;

    private String orderId;

    private String mapType;

    private String trueId;

    private String city;

    private String tomtomId;

    private String hereId;

    private String foursquareId;

    private String tomtomName;

    private String hereName;

    private String fourSquareName;

    private String tomtomAddress;

    private String tomtomAddress1;

    private String hereAddress;

    private String fourSquareAddress;

    private String tomtomPos;

    private String herePos;

    private String foursquarePos;

    private String foureaqurareValid;

    private String trueAddress;

    private String truePos;

    private String foursquareexistence;
}

