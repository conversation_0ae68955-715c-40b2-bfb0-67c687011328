package com.hll.mapdataservice.business.api.road.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.third.InheritIDService;
import com.hll.mapdataservice.business.third.dto.InheritIDDTO;
import com.hll.mapdataservice.common.entity.LinkM;
import com.hll.mapdataservice.common.entity.Metadst;
import com.hll.mapdataservice.common.entity.Mtddst;
import com.hll.mapdataservice.common.mapper.MetadstMapper;
import com.hll.mapdataservice.common.service.IMetadstService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/3/19
 */
@Service
public class MetadstServiceImpl extends ServiceImpl<MetadstMapper, Metadst> implements IMetadstService {
    @Resource
    MetadstMapper metadstMapper;
    @Resource
    InheritIDService inheritIDService;

    @Override
    public Integer convert(String country, String area, Map<String, Mtddst> srcMap, List<LinkM> linkMList, int step) {
        Integer resNum = 0;
        try {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<Metadst> finalList = new ArrayList<>();
            for (LinkM linkM : linkMList) {
                if (srcMap.containsKey(linkM.getTAdmin())) {
                    Metadst metadst = new Metadst();
                    Mtddst mtddst = srcMap.get(linkM.getTAdmin());
                    metadst.setTimeZone(mtddst.getTimeZone());
                    metadst.setDstExist(mtddst.getDstExist());
                    metadst.setDstStday(mtddst.getDstStday());
                    metadst.setDstStwk(mtddst.getDstStwk());
                    metadst.setDstStmnth(mtddst.getDstStmnth());
                    metadst.setDstSttime(mtddst.getDstSttime());
                    metadst.setDstEnday(mtddst.getDstEnday());
                    metadst.setDstEnwk(mtddst.getDstEnwk());
                    metadst.setDstEnmnth(mtddst.getDstEnmnth());
                    metadst.setDstEntime(mtddst.getDstEntime());

                    metadst.setAreaId(new BigDecimal(linkM.getLAdmin()));
                    finalList.add(metadst);
                }
            }
            List<List<Metadst>> split = CollUtil.split(finalList, BeanUtil.beanToMap(new Metadst()).keySet().size());
            for (int i = 0; i < split.size(); i++) {
                List<Metadst> metadsts = split.get(i);
                List<Long> ids = inheritIDService.inheritID(new InheritIDDTO(12L, metadsts.stream().map(Metadst::getAreaId).map(BigDecimal::toString).collect(Collectors.toList())));
                for (int j = 0; j < metadsts.size(); j++) {
                    metadsts.get(j).setId(ids.get(j));
                }
                resNum += metadstMapper.mysqlInsertOrUpdateBath(metadsts);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return resNum;
    }
}
