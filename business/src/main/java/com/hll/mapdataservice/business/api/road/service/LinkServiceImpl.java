package com.hll.mapdataservice.business.api.road.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.entity.LinkM;
import com.hll.mapdataservice.common.mapper.LinkMMapper;
import com.hll.mapdataservice.common.mapper.LinkMapper;
import com.hll.mapdataservice.common.service.ILinkService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * @Author: ares.chen
 * @Since: 2021/8/16
 */
@Service
@Slf4j
//@Transactional(rollbackFor = Exception.class)
public class LinkServiceImpl extends ServiceImpl<LinkMapper, Link> implements ILinkService {

    @Resource
    LinkMapper linkMapper;

    @Resource
    LinkMMapper linkMMapper;
    @Resource
    CdmsServiceImpl cdmsService;
    @Resource
    StreetsServiceImpl streetsService;

    @Async("asyncTaskExecutor")
    public void convert2rp(String area, String country, CountDownLatch countDownLatch, List<LinkM> linkList) {
        try {
//            if (!country.isEmpty()) {
//                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
//            }
//            if (!area.isEmpty()) {
//                MybatisPlusConfig.myTableName.set("_" + area);
//            } else {
//                MybatisPlusConfig.myTableName.set("");
//            }
            log.info("area is {},country is{},db is{}", area, country, DynamicDataSourceContextHolder.peek());
            int fieldNum = BeanUtil.beanToMap(new LinkM()).keySet().size();
            int batchSize = 32767 / fieldNum;
            log.info("batchInsert size is {}", batchSize);
            List<List<LinkM>> splitList = ListUtil.split(linkList, batchSize);
            for (List<LinkM> links : splitList) {
                //Link linkRp;
                List<Link> rpList = new ArrayList<>();
                for (LinkM linkM : links) {
                    Link link = new Link();
                    BeanUtil.copyProperties(linkM, link);
                    //差异字段对应赋值
                    link.setId(linkM.getLinkId());
                    link.setDirection(linkM.getDir());
                    link.setConstSt(linkM.getApp());
                    link.setDetailcity(linkM.getDevs());
                    link.setSpecial(linkM.getSpet());
                    link.setFuncclass(linkM.getFunct());
                    link.setUflag(linkM.getUrban());
                    link.setRoadCond(linkM.getPave());
                    link.setLanenumsum(linkM.getLaneN());
                    link.setLanenums2e(linkM.getLaneL());
                    link.setLanenume2s(linkM.getLaneR());
                    link.setLanenumc(linkM.getLaneC());
                    link.setElevated(linkM.getViad());
                    link.setAdmincodel(linkM.getLAdmin());
                    link.setAdmincoder(linkM.getRAdmin());
                    link.setSpdlmts2e(linkM.getFSpeed());
                    link.setSpdlmte2s(linkM.getTSpeed());
                    link.setSpeedclass(linkM.getSpClass());
                    link.setDcType(linkM.getDiciType());
                    link.setTAdmin(linkM.getTAdmin());
                    link.setTimeZone(linkM.getTimeZone());
                    rpList.add(link);
                }
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
                log.info("insert db is:" + DynamicDataSourceContextHolder.peek());
                // linkMapper.insertBatch(rpList);
                linkMapper.mysqlInsertOrUpdateBath(rpList);
                //this.saveBatch(rpList);

            }
        } catch (Exception e) {
            log.error("sync to link_rp error,detail is {}", e.getMessage());
            throw e;
        } finally {
            //DynamicDataSourceContextHolder.poll();
            countDownLatch.countDown();
        }
    }

    @Async("optimizedAsyncTaskExecutor")
    public void convert2rpOptimized(String area, String country, CountDownLatch countDownLatch, List<LinkM> linkList, ConcurrentLinkedQueue<Exception> exceptions) {
        try {
            log.info("Starting optimized convert2rp for area: {}, country: {}, records: {}", area, country, linkList.size());

            // Calculate optimized batch size based on field count and PostgreSQL parameter limit
            int fieldNum = BeanUtil.beanToMap(new LinkM()).keySet().size();
            int batchSize = 65535 / fieldNum;
            log.info("Optimized batchInsert size: {}", batchSize);

            // Split into optimized batches
            List<List<LinkM>> splitList = ListUtil.split(linkList, batchSize);

            for (List<LinkM> linkBatch : splitList) {
                // Process conversion for this batch
                List<Link> rpList = convertLinkMToLinkOptimized(linkBatch);

                // Configure database context for target data writing
                configureDatabaseContextForWrite(area, country);

                // Insert batch with optimized method
                log.info("Inserting batch of {} converted links to database: {}", rpList.size(), DynamicDataSourceContextHolder.peek());
                linkMapper.mysqlInsertOrUpdateBath(rpList);

                // Clear batch to help memory management
                rpList.clear();
            }

            log.info("Optimized convert2rp completed for {} records", linkList.size());

        } catch (Exception e) {
            log.error("Error in optimized convert2rp for area: {}, country: {}, detail: {}", area, country, e.getMessage(), e);
            exceptions.offer(e);
            throw e;
        } finally {
            countDownLatch.countDown();
        }
    }

    /**
     * Optimized conversion from LinkM to Link with all field mappings preserved
     */
    private List<Link> convertLinkMToLinkOptimized(List<LinkM> linkMList) {
        List<Link> rpList = new ArrayList<>(linkMList.size());

        for (LinkM linkM : linkMList) {
            Link link = new Link();

            // Copy common properties using BeanUtil for efficiency
            BeanUtil.copyProperties(linkM, link);

            // Apply all specific field mappings (100% functional parity with original)
            link.setId(linkM.getLinkId());                    // id <- linkId
            link.setDirection(linkM.getDir());                // direction <- dir
            link.setConstSt(linkM.getApp());                  // constSt <- app
            link.setDetailcity(linkM.getDevs());              // detailcity <- devs
            link.setSpecial(linkM.getSpet());                 // special <- spet
            link.setFuncclass(linkM.getFunct());              // funcclass <- funct
            link.setUflag(linkM.getUrban());                  // uflag <- urban
            link.setRoadCond(linkM.getPave());                // roadCond <- pave
            link.setLanenumsum(linkM.getLaneN());             // lanenumsum <- laneN
            link.setLanenums2e(linkM.getLaneL());             // lanenums2e <- laneL
            link.setLanenume2s(linkM.getLaneR());             // lanenume2s <- laneR
            link.setLanenumc(linkM.getLaneC());               // lanenumc <- laneC
            link.setElevated(linkM.getViad());                // elevated <- viad
            link.setAdmincodel(linkM.getLAdmin());            // admincodel <- lAdmin
            link.setAdmincoder(linkM.getRAdmin());            // admincoder <- rAdmin
            link.setSpdlmts2e(linkM.getFSpeed());             // spdlmts2e <- fSpeed
            link.setSpdlmte2s(linkM.getTSpeed());             // spdlmte2s <- tSpeed
            link.setSpeedclass(linkM.getSpClass());           // speedclass <- spClass
            link.setDcType(linkM.getDiciType());              // dcType <- diciType
            link.setTAdmin(linkM.getTAdmin());                // tAdmin <- tAdmin
            link.setTimeZone(linkM.getTimeZone());            // timeZone <- timeZone

            rpList.add(link);
        }

        return rpList;
    }

    /**
     * Configure database context for write operations
     */
    private void configureDatabaseContextForWrite(String area, String country) {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
    }

    private void handleData(List<LinkM> linkList, int batchSize, String area, String country) {
        List<List<LinkM>> splitList = ListUtil.split(linkList, batchSize);
        for (List<LinkM> links : splitList) {
            Link linkRp;
            List<Link> rpList = new ArrayList<>();
            for (LinkM link : links) {
                linkRp = new Link();
                BeanUtil.copyProperties(link, linkRp);
                //差异字段对应赋值
                linkRp.setId(link.getLinkId());
                linkRp.setDirection(link.getDir());
                linkRp.setConstSt(link.getApp());
                linkRp.setDetailcity(link.getDevs());
                linkRp.setSpecial(link.getSpet());
                linkRp.setFuncclass(link.getFunct());
                linkRp.setUflag(link.getUrban());
                linkRp.setRoadCond(link.getPave());
                linkRp.setLanenumsum(link.getLaneN());
                linkRp.setLanenums2e(link.getLaneL());
                linkRp.setLanenume2s(link.getLaneR());
                linkRp.setLanenumc(link.getLaneC());
                linkRp.setElevated(link.getViad());
                linkRp.setAdmincodel(link.getLAdmin());
                linkRp.setAdmincoder(link.getRAdmin());
                linkRp.setSpdlmts2e(link.getFSpeed());
                linkRp.setSpdlmte2s(link.getTSpeed());
                linkRp.setSpeedclass(link.getSpClass());
                linkRp.setDcType(link.getDiciType());
                rpList.add(linkRp);
            }
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            //linkRpMapper.insertBatch(rpList);
        }
    }

    /**
     * 递归分批，批量录入数据
     *
     * @param list
     * @param start
     * @param limit
     */
    private void transferData(List<Link> list, long start, long limit) {
        List<Link> collect = list.stream().skip(start).limit(limit).collect(Collectors.toList());
        if (CollUtil.isEmpty(collect)) {
            return;
        }
        //linkRpMapper.insertBatch(collect);
        transferData(list, start + limit, limit);
    }

    @Override
    @Async("asyncTaskExecutor")
    public void updateLinkConstSt(String area, String country, CountDownLatch countDownLatch, List<Link> linkList) {
        try {
            for (Link link : linkList) {
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
                List<Cdms> cdmsList = cdmsService.lambdaQuery().eq(Cdms::getLinkId, link.getId()).list();
                Streets streets = streetsService.lambdaQuery().eq(Streets::getLinkId, link.getId()).one();
                if (cdmsList.size() > 0 && cdmsList.get(0).getCondType() == 3) {
                    link.setConstSt("4");
                } else if ("Y".equals(streets.getArAuto()) || "Y".equals(streets.getArBus())
                        || "Y".equals(streets.getArTaxis()) || "Y".equals(streets.getArCarpool())
                        || "Y".equals(streets.getArTrucks()) || "Y".equals(streets.getArDeliv())
                        || "Y".equals(streets.getArEmerveh()) || "Y".equals(streets.getArMotor())
                        || "Y".equals(streets.getArPedest())) {
                    link.setConstSt("1");
                } else if ("N".equals(streets.getArAuto()) && "N".equals(streets.getArBus())
                        && "N".equals(streets.getArTaxis()) && "N".equals(streets.getArCarpool())
                        && "N".equals(streets.getArTrucks()) && "N".equals(streets.getArDeliv())
                        && "N".equals(streets.getArEmerveh()) && "N".equals(streets.getArMotor())
                        && "N".equals(streets.getArPedest())) {
                    link.setConstSt("2");
                }
            }
            linkMapper.mysqlInsertOrUpdateBath(linkList);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            countDownLatch.countDown();
        }

    }
}
