package com.hll.mapdataservice.business.api.poi.controller;


import com.hll.mapdataservice.business.api.poi.service.FoursquarePhaPoiServiceImpl;
import com.hll.mapdataservice.business.api.poi.service.FoursquareThaPoiServiceImpl;
import com.hll.mapdataservice.common.entity.FoursquarePhaPoi;
import com.hll.mapdataservice.common.entity.FoursquareThaPoi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import org.springframework.stereotype.Controller;

import javax.annotation.Resource;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-19
 */
@RestController
@ResponseBody
@Api(tags ="poi")
@RequestMapping("/api/poi/foursquareThaPoi")
public class FoursquareThaPoiController {

    @Resource
    private FoursquareThaPoiServiceImpl foursquareThaPoiService;

    @GetMapping("getById")
    @ApiOperation(value = "getById")
    public FoursquareThaPoi getPoiById(@RequestParam(value = "featId") String featId) {
        //return mnrNetwGeoLinkService.listByMap();
        //return mnrNetwGeoLinkMapper.selectById(UUID.fromString("00005448-**************-000000f09a71"));
        return foursquareThaPoiService.lambdaQuery().eq(FoursquareThaPoi::getFsqId, featId).list().get(0);
        //return mnrNetwGeoLinkService.getById(UUID.fromString("00005448-**************-000000f09a71"));
    }


}
