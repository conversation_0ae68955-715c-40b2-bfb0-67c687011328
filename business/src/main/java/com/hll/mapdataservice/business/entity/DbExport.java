package com.hll.mapdataservice.business.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/1/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DbExport {
    private String url;
    private String db;
    private String port;
    private String user;
    private String pwd;
    private String fileOutputDir;
    private String fileName;
    private String fileDesc;
    private String version;
    /**
     * 导出类型，1 html,2 word,3 md
     */
    private String exportType;
    private List<String> ignoreTables;
    private List<String> ignoreSuffixes;
    private List<String> ignorePrefixes;
}
