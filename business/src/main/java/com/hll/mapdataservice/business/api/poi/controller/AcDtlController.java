package com.hll.mapdataservice.business.api.poi.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.AcDtlMatch;
import com.hll.mapdataservice.common.mapper.AcDtlMapper;
import com.hll.mapdataservice.common.service.IAcDtlService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: ares.chen
 * @Since: 2022/4/6
 */
@RestController
@RequestMapping("acDtl")
@Slf4j
public class AcDtlController {

    @Resource
    AcDtlMapper acDtlMapper;
    @Resource
    IAcDtlService acDtlService;

    @GetMapping("match")
    public ResponseResult<String> acDtlMatch(String country, String fileName) {
        try {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            AtomicInteger sourceNum = new AtomicInteger();
            AtomicInteger handleNum = new AtomicInteger();
            // 1.读取excel数据
            EasyExcel.read(fileName, AcDtlMatch.class, new PageReadListener<AcDtlMatch>(dataList -> {
                sourceNum.addAndGet(dataList.size());
                handleNum.addAndGet(acDtlMatchHandle(dataList));
            })).sheet().doRead();
            // 2.判断是否全部处理完excel数据
            return sourceNum.get() == handleNum.get() ? ResponseResult.otherInfo("200", "excel数据全部匹配完成！",true) :
                    ResponseResult.otherInfo("400", "excel数据未全部匹配",false);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseResult.otherInfo("500", e.getMessage(),false);
        }
    }

    private int acDtlMatchHandle(List<AcDtlMatch> dataList) {
        // 1.计算name相似度
        dataList.forEach(s->{
            s.setAcDtlNameMatch((double) CommonUtils.levenshtein(s.getAcName(), s.getDtlName()));
            // s.setAcDtlAddrMatch((double) CommonUtils.levenshtein(s.getAcAddr(), s.getDtlAddr()));
            // s.setAcDtlNameAddrMatch((double) CommonUtils.levenshtein(s.getAcName()+s.getAcAddr(), s.getDtlName()+s.getDtlAddr()));
            // s.setAcGcAddrMatch((double) CommonUtils.levenshtein(s.getAcAddr(),s.getGcAddr()));
            // s.setDtlGcAddrMatch((double) CommonUtils.levenshtein(s.getDtlAddr(),s.getGcAddr()));
        });
        // 2.结果数据入库
        return acDtlMapper.insertBatch(dataList);
        //return acDtlService.saveBatch(dataList) ? dataList.size() : 0;
    }
}
