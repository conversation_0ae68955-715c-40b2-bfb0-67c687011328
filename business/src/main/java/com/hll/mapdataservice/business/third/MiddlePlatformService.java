package com.hll.mapdataservice.business.third;

import com.hll.mapdataservice.business.config.FeignConfig;
import feign.Headers;
import feign.RequestLine;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

@FeignClient(name = "middlePlatformService", configuration = FeignConfig.class, url = "${third.middlePlatformService.url}")
public interface MiddlePlatformService {

//    @Headers("Content-Type: text/plain")
    @GetMapping("/map/api?mapType=google&apiName=google_ac&appId=global_algo&otherParam=async%3Dfalse&tid=test_111&sign=1")
    String getGoogleAc(String requestData);

//    @Headers("Content-Type: text/plain")
    @GetMapping("/map/api?mapType=here&apiName=here_autosuggest&appId=global_algo&otherParam=async%3Dfalse&tid=test_111&sign=1")
    String getHereAs(String requestData);

    @GetMapping("/map/api?mapType=google&apiName=google_detail&appId=map-global-trade-crp-svc&otherParam=async%3Dfalse&tid=testwangaimin11111111&sign=1")
    String getGoogleDetail(String requestData);
}
