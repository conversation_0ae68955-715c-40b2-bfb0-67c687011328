package com.hll.mapdataservice.business.api.poi.controller;


import com.hll.mapdataservice.business.api.poi.service.MnrPoiServiceImpl;
import com.hll.mapdataservice.common.entity.HereThaPoiCopy1;
import com.hll.mapdataservice.common.entity.MnrPoi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import org.springframework.stereotype.Controller;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-19
 */
@RestController
@ResponseBody
@Api(tags ="poi")
@RequestMapping("/api/poi/mnrPoi")
public class MnrPoiController {
    @Resource
    private MnrPoiServiceImpl mnrPoiService;

    @GetMapping("getById")
    @ApiOperation(value = "getById")
    public MnrPoi getPoiById(@RequestParam(value = "featId") String featId) {
        //return mnrNetwGeoLinkService.listByMap();
        //return mnrNetwGeoLinkMapper.selectById(UUID.fromString("00005448-**************-000000f09a71"));
        return mnrPoiService.lambdaQuery().eq(MnrPoi::getFeatId, UUID.fromString(featId)).list().get(0);
        //return mnrNetwGeoLinkService.getById(UUID.fromString("00005448-**************-000000f09a71"));
    }
}
