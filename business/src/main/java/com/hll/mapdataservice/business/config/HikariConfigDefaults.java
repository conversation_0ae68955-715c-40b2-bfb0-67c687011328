// package com.hll.mapdataservice.business.config;
//
// import com.zaxxer.hikari.HikariConfig;
// import com.zaxxer.hikari.HikariDataSource;
// import org.springframework.boot.context.properties.ConfigurationProperties;
// import org.springframework.context.annotation.Configuration;
//
// @Configuration
// public class HikariConfigDefaults {
//
//     public static HikariConfig createDefaultHikariConfig() {
//         HikariConfig config = new HikariConfig();
//         config.setAutoCommit(true);
//         config.setIdleTimeout(600000);
//         config.setConnectionTimeout(600000);
//         config.setMaxLifetime(1500000);
//         config.setMinimumIdle(20);
//         config.setMaximumPoolSize(20);
//         config.setConnectionInitSql("SELECT 1");
//         config.setValidationTimeout(5000);
//         return config;
//     }
//
//     public static HikariDataSource createDataSource(String jdbcUrl, String username, String password) {
//         HikariConfig config = createDefaultHikariConfig();
//         config.setJdbcUrl(jdbcUrl);
//         config.setUsername(username);
//         config.setPassword(password);
//         config.setDriverClassName("org.postgresql.Driver");
//         return new HikariDataSource(config);
//     }
// }