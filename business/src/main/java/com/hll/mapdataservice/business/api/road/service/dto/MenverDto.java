package com.hll.mapdataservice.business.api.road.service.dto;

import com.hll.mapdataservice.common.vo.ManeuverVo;
import com.hll.mapdataservice.common.vo.RoadProperty;

import java.util.List;

/*
 * <AUTHOR>
 * @date  3/15/21 9:56 PM
 * @Email:<EMAIL>
 */
public class MenverDto {
    /**
     * tomtom数据列表
     */
    private List<ManeuverVo> tomtomList;


    /**
     * here道路数据
     */
    private List<ManeuverVo> hereList;

    /**
     * osm道路数据
     */
    private List<ManeuverVo> osmList;

    public List<ManeuverVo> getTomtomList() {
        return tomtomList;
    }

    public void setTomtomList(List<ManeuverVo> tomtomList) {
        this.tomtomList = tomtomList;
    }

    public List<ManeuverVo> getHereList() {
        return hereList;
    }

    public void setHereList(List<ManeuverVo> hereList) {
        this.hereList = hereList;
    }

    public List<ManeuverVo> getOsmList() {
        return osmList;
    }

    public void setOsmList(List<ManeuverVo> osmList) {
        this.osmList = osmList;
    }

}
