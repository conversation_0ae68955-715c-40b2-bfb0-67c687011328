package com.hll.mapdataservice.business.base;

import com.hll.mapdataservice.common.entity.MeshLinkTemplate;
import com.hll.mapdataservice.common.entity.Road;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

class Graph {
    // 邻接表，用于存储每个节点及其相邻节点的关系
    private Map<String, List<MeshLinkTemplate>> adjacencyList;

    public Graph() {
        this.adjacencyList = new HashMap<>();
    }

    public void addEdge(MeshLinkTemplate meshLinkTemplate) {
        // 首先检查road对象是否为null
        if (meshLinkTemplate == null) {
            return; // 如果road为null，直接返回以避免进一步的空指针风险
        }

        // 获取起始和结束节点的ID，同时检查这些ID是否为null
        String sNodeId = meshLinkTemplate.getHllSNid() != null ? String.valueOf(meshLinkTemplate.getHllSNid()) : null;
        String eNodeId = meshLinkTemplate.getHllENid() != null ? String.valueOf(meshLinkTemplate.getHllENid()) : null;

        // 为起始节点添加边，确保sNodeId非空
        if (sNodeId != null) {
            adjacencyList.putIfAbsent(sNodeId, new ArrayList<>());
            adjacencyList.get(sNodeId).add(meshLinkTemplate);
        }

        // 为结束节点添加边，确保eNodeId非空
        if (eNodeId != null && !eNodeId.equals(sNodeId)) { // 还要确保起始节点和结束节点不相同
            adjacencyList.putIfAbsent(eNodeId, new ArrayList<>());
            adjacencyList.get(eNodeId).add(meshLinkTemplate);
        }
    }

    public List<MeshLinkTemplate> getAdjacencyList(String nodeId) {
        // 返回给定节点的所有相邻道路
        return adjacencyList.getOrDefault(nodeId, new ArrayList<>());
    }
}
