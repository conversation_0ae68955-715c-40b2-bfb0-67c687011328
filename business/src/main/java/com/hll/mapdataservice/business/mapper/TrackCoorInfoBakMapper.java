package com.hll.mapdataservice.business.mapper;

import com.hll.mapdataservice.business.entity.TrackCoorInfoBak;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-05
 */
public interface TrackCoorInfoBakMapper extends BaseMapper<TrackCoorInfoBak> {
    @Select("select distinct(order_id) from track_coor_info_bak order by order_id")
    List<String> getOrderId();
}
