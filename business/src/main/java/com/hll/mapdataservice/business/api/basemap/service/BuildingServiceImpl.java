package com.hll.mapdataservice.business.api.basemap.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.google.common.collect.Lists;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.third.InheritIDService;
import com.hll.mapdataservice.business.third.dto.InheritIDDTO;
import com.hll.mapdataservice.common.entity.Building;
import com.hll.mapdataservice.common.entity.Landmark;
import com.hll.mapdataservice.common.mapper.BuildingMapper;
import com.hll.mapdataservice.common.service.IBuildingService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.common.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-02
 */
@Service
@Slf4j
public class BuildingServiceImpl extends ServiceImpl<BuildingMapper, Building> implements IBuildingService {
    @Resource
    InheritIDService inheritIDService;
    @Resource
    BuildingMapper buildingMapper;
    @Async("asyncTaskExecutor")
    //@Async()
    public void buildingConvert(List<Landmark> landmarkList, Boolean isCompileTransEng, String area,
                                String country, CountDownLatch countDownLatch) {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            log.info("processing country:" + CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        log.info("tmp thread is:" + Thread.currentThread().getName());
        List<Building> buildingList = new ArrayList<>();

        try {
            if (landmarkList.size() > 0) {
                for (Landmark landmark : landmarkList
                ) {
                    Building building = new Building();
                    //id
                    building.setId(landmark.getPolygonId().toString());
                    //scource_id
                    building.setSourceId(landmark.getPolygonId().toString());
                    //class_code
                    building.setClassCode(landmark.getFeatCod().toString());
                    //show
                    building.setShow(null);
                    //height
                    building.setHeight(landmark.getHeight().toString());
                    //carto_id
                    building.setCartoId(null);
                    //name
                    building.setName(landmark.getPolygonNm());
                    //nm_langcd
                    building.setNmLangcd(landmark.getNmLangcd());
                    //nm_tr
                    building.setNmTr(landmark.getPolyNmTr());
                    //trans_type
                    building.setTransType(landmark.getTransType());
                    //geometry
                    building.setGeometry(landmark.getGeom());
                    // UP_DATE
                    building.setUpDate(LocalDateTime.now());
                    // STATUS
                    building.setStatus(0);
                    // DATASOURCE
                    building.setDatasource("7");

                    buildingList.add(building);
                }
                log.info("buildingList size is:" + buildingList.size());
                // this.saveOrUpdateBatch(linList);
                List<List<Building>> bbuildingListPartition = Lists.partition(buildingList, 32767/BeanUtil.beanToMap(new Building()).keySet().size());
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }

                for (List<Building> partitionList : bbuildingListPartition) {
                    // log.info("linkMapper threadlocal info is:" + Thread.currentThread().getName());
                    // linMapper.mysqlInsertOrUpdateBath(partitionList);
                    List<Long> bbuildingIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(Building::getId).collect(Collectors.toList())));
                    for (int i = 0; i < partitionList.size(); i++) {
                        partitionList.get(i).setId(String.valueOf(bbuildingIds.get(i)));
                    }
                    buildingMapper.mysqlInsertOrUpdateBath(partitionList);
                    // this.saveOrUpdateBatch(partitionList);
                }
            }
        } catch (Exception e) {
            log.error("building convert error,msg is {}", e);
            throw new RuntimeException("building convert error,msg is {}" + e.getMessage());
        } finally {
            countDownLatch.countDown();
        }
    }

}
