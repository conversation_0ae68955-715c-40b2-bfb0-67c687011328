package com.hll.mapdataservice.business.api.basemap.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.google.common.collect.Lists;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.third.InheritIDService;
import com.hll.mapdataservice.business.third.dto.InheritIDDTO;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.mapper.BlineMapper;
import com.hll.mapdataservice.common.service.IBlineService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.common.utils.CommonUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-02
 */
@Service
@Slf4j
public class BlineServiceImpl extends ServiceImpl<BlineMapper, Bline> implements IBlineService {
    @Resource
    BlineMapper blineMapper;

    @Resource
    InheritIDService inheritIDService;

    @Async("asyncTaskExecutor")
    //@Async()
    public void blineConvertRailrds(List<Railrds> railrdsList, Boolean isCompileTransEng, String area,
                            String country, CountDownLatch countDownLatch) throws SQLException, InterruptedException {

        // MybatisPlusConfig.myTableName.set("_"+area);
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            log.info("processing country:" + CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        log.info("tmp thread is:" + Thread.currentThread().getName());
        List<Bline> blineList = new ArrayList<>();

        try {
            if (railrdsList.size() > 0) {
                for (Railrds railrds : railrdsList
                ) {
                    Bline bline = new Bline();
                    //id
                    bline.setId(railrds.getLinkId().toString());
                    //sourceId
                    bline.setSourceId(railrds.getLinkId().toString());
                    //source
                    bline.setSource("railrds");
                    //area_id
                    bline.setAreaId(null);
                    //id_source_area
                    bline.setIdSourceArea(railrds.getLinkId().toString() + "_railrds_" + railrds.getGid());
                    //kind
                    bline.setKind("1800201");
                    //name
                    bline.setName(railrds.getRailwayNm());
                    //nm_langcd
                    bline.setNmLangcd(railrds.getLangCode());
                    //nm_tr
                    bline.setNmTr(railrds.getRailNmTr());
                    //trans_type
                    bline.setTransType(railrds.getTransType());
                    //geometry
                    bline.setGeometry(railrds.getGeom());
                    //line_cntry
                    bline.setLineCntry(null);
                    //claimed_by
                    bline.setClaimedBy(null);
                    //control_by
                    bline.setControlBy(null);
                    //disp_class
                    bline.setDispClass(null);
                    // UP_DATE
                    bline.setUpDate(LocalDateTime.now());
                    // STATUS
                    bline.setStatus(0);
                    // DATASOURCE
                    bline.setDatasource("7");
;
                    blineList.add(bline);
                }
                log.info("linklist size is:" + blineList.size());
                // this.saveOrUpdateBatch(linList);
                List<List<Bline>> blineListPartition = Lists.partition(blineList, 32767/BeanUtil.beanToMap(new Bline()).keySet().size());
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }

                for (List<Bline> partitionList : blineListPartition) {
                    // log.info("linkMapper threadlocal info is:" + Thread.currentThread().getName());
                    // linMapper.mysqlInsertOrUpdateBath(partitionList);
                    List<Long> blineIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(Bline::getId).collect(Collectors.toList())));
                    for (int i = 0; i < partitionList.size(); i++) {
                        partitionList.get(i).setId(String.valueOf(blineIds.get(i)));
                    }
                    blineMapper.mysqlInsertOrUpdateBath(partitionList);
                    // this.saveOrUpdateBatch(partitionList);
                }
            }
        } catch (Exception e) {
            log.error("link node convert error,msg is {}", e);
            throw new RuntimeException("link node convert error,msg is {}" + e.getMessage());
        } finally {
            countDownLatch.countDown();
        }
    }


    @Async("asyncTaskExecutor")
    public void blineConvertAdminline1(List<Adminline1> adminline1List, Boolean isCompileTransEng, String area,
                             String country, CountDownLatch countDownLatch) throws SQLException, InterruptedException {

        // MybatisPlusConfig.myTableName.set("_"+area);
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            log.info("processing country:" + CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        log.info("tmp thread is:" + Thread.currentThread().getName());
        List<Bline> blineList = new ArrayList<>();

        try {
            if (adminline1List.size() > 0) {
                for (Adminline1 adminline1 : adminline1List
                ) {
                    Bline bline = new Bline();
                    //id
                    bline.setId(adminline1.getLinkId().toString());
                    //snode_id
                    bline.setSourceId(adminline1.getLinkId().toString());
                    //source
                    bline.setSource("adminline1");
                    //area_id
                    bline.setAreaId(adminline1.getAreaId().toString());
                    //id_source_area
                    bline.setIdSourceArea(adminline1.getLinkId().toString() + "_adminline1_" + adminline1.getGid() + "_" + adminline1.getAreaId().toString());
                    //kind
                    bline.setKind(adminline1.getFeatCod().toString());
                    //name
                    bline.setName(adminline1.getAdminNm());
                    //nm_langcd
                    bline.setNmLangcd(adminline1.getNmLangcd());
                    //nm_tr
                    bline.setNmTr(null);
                    //trans_type
                    bline.setTransType(null);
                    //geometry
                    bline.setGeometry(adminline1.getGeom());
                    //line_cntry
                    bline.setLineCntry(adminline1.getLineCntrl());
                    //claimed_by
                    bline.setClaimedBy(adminline1.getClaimedBy());
                    //control_by
                    bline.setControlBy(adminline1.getControlBy());
                    //disp_class
                    bline.setDispClass(null);
                    // UP_DATE
                    bline.setUpDate(LocalDateTime.now());
                    // STATUS
                    bline.setStatus(0);
                    // DATASOURCE
                    bline.setDatasource("7");
                    ;
                    blineList.add(bline);
                }
                log.info("linklist size is:" + blineList.size());
                // this.saveOrUpdateBatch(linList);
                List<List<Bline>> blineListPartition = Lists.partition(blineList, BeanUtil.beanToMap(new Bline()).keySet().size());
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }

                for (List<Bline> partitionList : blineListPartition) {
                    // log.info("linkMapper threadlocal info is:" + Thread.currentThread().getName());
                    // linMapper.mysqlInsertOrUpdateBath(partitionList);
                    List<Long> blineIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(Bline::getId).collect(Collectors.toList())));
                    for (int i = 0; i < partitionList.size(); i++) {
                        partitionList.get(i).setId(String.valueOf(blineIds.get(i)));
                    }
                    blineMapper.mysqlInsertOrUpdateBath(partitionList);
                    // this.saveOrUpdateBatch(partitionList);
                }
            }
        } catch (Exception e) {
            log.error("link node convert error,msg is {}", e);
            throw new RuntimeException("link node convert error,msg is {}" + e.getMessage());
        } finally {
            countDownLatch.countDown();
        }
    }

    @Async("asyncTaskExecutor")
    public void blineConvertAdminline2(List<Adminline2> adminline2List, Boolean isCompileTransEng, String area,
                                       String country, CountDownLatch countDownLatch) throws SQLException, InterruptedException {

        // MybatisPlusConfig.myTableName.set("_"+area);
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            log.info("processing country:" + CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        log.info("tmp thread is:" + Thread.currentThread().getName());
        List<Bline> blineList = new ArrayList<>();

        try {
            if (adminline2List.size() > 0) {
                for (Adminline2 adminline2 : adminline2List
                ) {
                    Bline bline = new Bline();
                    //id
                    bline.setId(adminline2.getLinkId().toString());
                    //snode_id
                    bline.setSourceId(adminline2.getLinkId().toString());
                    //source
                    bline.setSource("adminline2");
                    //area_id
                    bline.setAreaId(adminline2.getAreaId().toString());
                    //id_source_area
                    bline.setIdSourceArea(adminline2.getLinkId().toString() + "_adminline2_" + adminline2.getGid() + "_" +adminline2.getAreaId().toString());
                    //kind
                    bline.setKind(adminline2.getFeatCod().toString());
                    //name
                    bline.setName(adminline2.getAdminNm());
                    //nm_langcd
                    bline.setNmLangcd(adminline2.getNmLangcd());
                    //nm_tr
                    bline.setNmTr(null);
                    //trans_type
                    bline.setTransType(null);
                    //geometry
                    bline.setGeometry(adminline2.getGeom());
                    //line_cntry
                    bline.setLineCntry(adminline2.getLineCntrl());
                    //claimed_by
                    bline.setClaimedBy(adminline2.getClaimedBy());
                    //control_by
                    bline.setControlBy(adminline2.getControlBy());
                    //disp_class
                    bline.setDispClass(null);
                    // UP_DATE
                    bline.setUpDate(LocalDateTime.now());
                    // STATUS
                    bline.setStatus(0);
                    // DATASOURCE
                    bline.setDatasource("7");
                    ;
                    blineList.add(bline);
                }
                log.info("linklist size is:" + blineList.size());
                // this.saveOrUpdateBatch(linList);
                List<List<Bline>> blineListPartition = Lists.partition(blineList, BeanUtil.beanToMap(new Bline()).keySet().size());
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }

                for (List<Bline> partitionList : blineListPartition) {
                    // log.info("linkMapper threadlocal info is:" + Thread.currentThread().getName());
                    // linMapper.mysqlInsertOrUpdateBath(partitionList);
                    List<Long> blineIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(Bline::getId).collect(Collectors.toList())));
                    for (int i = 0; i < partitionList.size(); i++) {
                        partitionList.get(i).setId(String.valueOf(blineIds.get(i)));
                    }
                    blineMapper.mysqlInsertOrUpdateBath(partitionList);
                    // this.saveOrUpdateBatch(partitionList);
                }
            }
        } catch (Exception e) {
            log.error("link node convert error,msg is {}", e);
            throw new RuntimeException("link node convert error,msg is {}" + e.getMessage());
        } finally {
            countDownLatch.countDown();
        }
    }

    @Async("asyncTaskExecutor")
    public void blineConvertWaterseg(List<Waterseg> watersegList, Boolean isCompileTransEng, String area,
                                       String country, CountDownLatch countDownLatch) throws SQLException, InterruptedException {

        // MybatisPlusConfig.myTableName.set("_"+area);
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            log.info("processing country:" + CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        log.info("tmp thread is:" + Thread.currentThread().getName());
        List<Bline> blineList = new ArrayList<>();

        try {
            if (watersegList.size() > 0) {
                for (Waterseg waterseg : watersegList
                ) {
                    Bline bline = new Bline();
                    //id
                    bline.setId(waterseg.getLinkId().toString());
                    //snode_id
                    bline.setSourceId(waterseg.getLinkId().toString());
                    //source
                    bline.setSource("waterseg");
                    //area_id
                    bline.setAreaId(null);
                    //kind
                    bline.setKind(waterseg.getFeatCod().toString());
                    //id_source_area
                    bline.setIdSourceArea(waterseg.getLinkId().toString() + "_waterseg_" + waterseg.getGid());
                    //name
                    bline.setName(waterseg.getPolygonNm());
                    //nm_langcd
                    bline.setNmLangcd(waterseg.getNmLangcd());
                    //nm_tr
                    bline.setNmTr(waterseg.getPolyNmTr());
                    //trans_type
                    bline.setTransType(waterseg.getTransType());
                    //geometry
                    bline.setGeometry(waterseg.getGeom());
                    //line_cntry
                    bline.setLineCntry(null);
                    //claimed_by
                    bline.setClaimedBy(null);
                    //control_by
                    bline.setControlBy(null);
                    //disp_class
                    bline.setDispClass(waterseg.getDispClass());
                    // UP_DATE
                    bline.setUpDate(LocalDateTime.now());
                    // STATUS
                    bline.setStatus(0);
                    // DATASOURCE
                    bline.setDatasource("7");
                    blineList.add(bline);
                }
                log.info("linklist size is:" + blineList.size());
                // this.saveOrUpdateBatch(linList);
                List<List<Bline>> blineListPartition = Lists.partition(blineList, BeanUtil.beanToMap(new Bline()).keySet().size());
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }

                for (List<Bline> partitionList : blineListPartition) {
                    // log.info("linkMapper threadlocal info is:" + Thread.currentThread().getName());
                    // linMapper.mysqlInsertOrUpdateBath(partitionList);
                    List<Long> blineIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(Bline::getId).collect(Collectors.toList())));
                    for (int i = 0; i < partitionList.size(); i++) {
                        partitionList.get(i).setId(String.valueOf(blineIds.get(i)));
                    }
                    blineMapper.mysqlInsertOrUpdateBath(partitionList);
                    // this.saveOrUpdateBatch(partitionList);
                }
            }
        } catch (Exception e) {
            log.error("link node convert error,msg is {}", e);
            throw new RuntimeException("link node convert error,msg is {}" + e.getMessage());
        } finally {
            countDownLatch.countDown();
        }
    }

}
