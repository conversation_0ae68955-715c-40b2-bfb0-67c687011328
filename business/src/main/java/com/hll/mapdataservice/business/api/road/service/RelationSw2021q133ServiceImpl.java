package com.hll.mapdataservice.business.api.road.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hll.mapdataservice.common.entity.RelationSw2021q133;
import com.hll.mapdataservice.common.mapper.RelationSw2021q133Mapper;
import com.hll.mapdataservice.common.service.IRelationSw2021q133Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-01
 */
@Service
//@DS("db8")
public class RelationSw2021q133ServiceImpl extends ServiceImpl<RelationSw2021q133Mapper, RelationSw2021q133> implements IRelationSw2021q133Service {

}
