package com.hll.mapdataservice.business.base;

import com.hll.mapdataservice.common.entity.MeshLinkTemplate;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2024/3/29
 */
public class MergeUtil {
    public static Map<String, List<MeshLinkTemplate>> identifyLinkNetworks(List<MeshLinkTemplate> links) {
        Map<String, List<MeshLinkTemplate>> roadMap = new HashMap<>();
        Graph graph = buildGraph(links);
        Set<String> visited = new HashSet<>();

        for (MeshLinkTemplate link : links) {
            String nodeId;
            if ("3".equals(link.getDirection())) {
                nodeId = String.valueOf(link.getHllENid());
            }else {
                nodeId = String.valueOf(link.getHllSNid());
            }
            if (!visited.contains(String.valueOf(nodeId))) {
                List<MeshLinkTemplate> component = new ArrayList<>();
                Set<String> visitedLinks = new HashSet<>();  // 用于存储已访问过的linkid
                dfs(graph, String.valueOf(nodeId), visited, component, visitedLinks);
                if (component.size() == 1) {
                    // 孤立的道路
                    roadMap.put(String.valueOf(component.get(0).getHllLinkid()), component);
                } else {
                    // 网状道路
                    roadMap.put(String.valueOf(component.get(0).getHllLinkid()), component);  // 使用第一条道路的linkId作为键
                }
            }
        }
        return roadMap;
    }

    static void dfs(Graph graph, String node, Set<String> visited, List<MeshLinkTemplate> component, Set<String> visitedLinks) {
        visited.add(node);
        for (MeshLinkTemplate link : graph.getAdjacencyList(node)) {
            // 检查是否已经访问过这条边
            if (!visitedLinks.contains(String.valueOf(link.getHllLinkid()))) {
                visitedLinks.add(String.valueOf(link.getHllLinkid()));

                // 确定下一个访问节点
                String nextNode = null;
                if ("1".equals(link.getDirection()) || "2".equals(link.getDirection())) {
                    // 方向为1或2，以snode为起点
                    if (link.getHllSNid().equals(node)) {
                        nextNode = String.valueOf(link.getHllENid()); // 下一个节点是enode
                        component.add(link); // 当前节点是起点，可以添加到连接组件中
                    }
                } else if ("3".equals(link.getDirection())) {
                    // 方向为3，代表反向，以enode为起点
                    if (link.getHllENid().equals(node)) {
                        nextNode = String.valueOf(link.getHllSNid()); // 下一个节点是snode
                        component.add(link); // 当前节点是起点，可以添加到连接组件中
                    }
                }

                if (nextNode != null && !visited.contains(nextNode)) {
                    dfs(graph, nextNode, visited, component, visitedLinks);
                }
            }
        }
    }
    static Graph buildGraph(List<MeshLinkTemplate> links) {
        Graph graph = new Graph();
        for (MeshLinkTemplate meshLinkTemplate : links) {
            graph.addEdge(meshLinkTemplate);
        }
        return graph;
    }
}
