package com.hll.mapdataservice.business.api.poi.service;

import com.hll.mapdataservice.common.entity.HereThaPoi;
import com.hll.mapdataservice.common.mapper.HereThaPoiMapper;
import com.hll.mapdataservice.common.service.IHereThaPoiService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.business.common.XmlFileUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
@Service
//@DS("db3")
public class HereThaPoiServiceImpl extends ServiceImpl<HereThaPoiMapper, HereThaPoi> implements IHereThaPoiService {

    @Async()
    public void saveHerePoi(File file){
        System.out.println("processing file:"+file.toString());
        long startTime = System.currentTimeMillis();
        this.saveBatch(new XmlFileUtils().herePoiXmlRead(file));
        long endTime = System.currentTimeMillis();
        System.out.println("finished processing file:"+file.toString()+" cost "+(endTime-startTime)/1000+"s");

    }
}
