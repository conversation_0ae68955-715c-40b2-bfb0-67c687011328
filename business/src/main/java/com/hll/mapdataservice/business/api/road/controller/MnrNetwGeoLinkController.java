package com.hll.mapdataservice.business.api.road.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hll.mapdataservice.business.api.road.service.HereThaStreetsServiceImpl;
import com.hll.mapdataservice.business.api.road.service.MnrNetwGeoLinkServiceImpl;
import com.hll.mapdataservice.business.api.road.service.dto.LinkData;
import com.hll.mapdataservice.business.api.road.service.dto.MenverDto;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.mapper.PhlTrackCoorInfoBakMapper;
import com.hll.mapdataservice.business.mapper.TrackCoorInfoMapper;
import com.hll.mapdataservice.business.service.PhlTrackCoorInfoBakServiceImpl;
import com.hll.mapdataservice.business.service.TrackCoorInfoServiceImpl;
import com.hll.mapdataservice.business.third.RoadMatchService;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.mapper.HerePhaStreetsMapper;
import com.hll.mapdataservice.common.mapper.HereThaStreetsMapper;
import com.hll.mapdataservice.common.mapper.MnrNetwGeoLinkMapper;
import com.hll.mapdataservice.common.mapper.MnrPhaNetwGeoLinkMapper;
import com.hll.mapdataservice.common.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-24
 */
@RestController
@ResponseBody
@Api(tags ="road")
@RequestMapping("/api/road/mnrNetwGeoLink")
@Component
public class MnrNetwGeoLinkController {

    @Resource
    private MnrNetwGeoLinkServiceImpl mnrNetwGeoLinkService;
    @Resource
    private MnrNetwGeoLinkMapper mnrNetwGeoLinkMapper;
    @Resource
    private RoadMatchService roadMatchService;
//    @Resource
//    private TrackCoorInfoBakMapper trackCoorInfoBakMapper;
//    @Resource
//    private TrackCoorInfoBakServiceImpl trackCoorInfoBakService;
    @Resource
    private TrackCoorInfoMapper trackCoorInfoMapper;
    @Resource
    private TrackCoorInfoServiceImpl trackCoorInfoService;

    @Resource
    private PhlTrackCoorInfoBakMapper phlTrackCoorInfoBakMapper;

    @Resource
    private PhlTrackCoorInfoBakServiceImpl phlTrackCoorInfoBakService;

    @Resource
    private HereThaStreetsMapper hereThaStreetsMapper;

    @Resource
    private HereThaStreetsServiceImpl hereThaStreetsService;

    @Resource
    private MnrPhaNetwGeoLinkMapper mnrPhaNetwGeoLinkMapper;

    @Resource
    private HerePhaStreetsMapper herePhaStreetsMapper;

    @GetMapping("/listByFeatid")
    @ApiOperation(value = "listByFeatid")
    public MnrNetwGeoLink listReportPoi(@RequestParam(value = "featid") String featid) {
        //return mnrNetwGeoLinkService.listByMap();
        //return mnrNetwGeoLinkMapper.selectById(UUID.fromString("00005448-**************-000000f09a71"));
        return mnrNetwGeoLinkService.lambdaQuery().eq(MnrNetwGeoLink::getFeatId, UUID.fromString(featid)).list().get(0);
        //return mnrNetwGeoLinkService.getById(UUID.fromString("00005448-**************-000000f09a71"));
    }

    @GetMapping("/linkByRoadid")
    @ApiOperation(value="根据linkid，获得路的信息")
//    @DS("db2")
    public ResponseResult<LinkData>  getLinkInfoByRoadid(@RequestParam(value = "ids") String ids,
                                                         @RequestParam(value = "mapType") String mapType,
                                                         @RequestParam(value = "city") String city) {
        LinkData linkData = new LinkData();

        List<RoadProperty> mnrList = new ArrayList<>();
        List<RoadProperty> hereList = new ArrayList<>();

        String[] idList = ids.split(",");
        List<String> typeList = new ArrayList<>();
        for (String id: idList) {

//            scenicFileQueryWrapper.eq("n.feat_id", UUID.fromString(id));

            if(mapType.equals("tomtom")){
                QueryWrapper<RoadProperty> scenicFileQueryWrapper = new QueryWrapper<>();
                scenicFileQueryWrapper.eq("n.feat_id", UUID.fromString(id));
                if("manila".equals(city)){
                    RoadProperty rp = mnrPhaNetwGeoLinkMapper.getRoadInfo(scenicFileQueryWrapper);
                    mnrList.add(rp);
                }
                else if("mangu".equals(city)){
                    RoadProperty rp = mnrNetwGeoLinkMapper.getRoadInfo(scenicFileQueryWrapper);
                    mnrList.add(rp);
                }

            }
            else if(mapType.equals("here")){
                if("manila".equals(city)){
                    MybatisPlusConfig.myTableName.set("");
                    QueryWrapper<HerePhaStreets> scenicFileQueryWrapper = new QueryWrapper<>();
                    scenicFileQueryWrapper.eq("link_id",Integer.parseInt(id));
                    HerePhaStreets herePhaStreets = herePhaStreetsMapper.selectOne(scenicFileQueryWrapper);
                    //HereThaStreets hereThaStreets = hereThaStreetsService.lambdaQuery().eq(HereThaStreets::getLinkId,Integer.parseInt(id)).list().get(0);

                    //RoadPropertyHere rp = new RoadPropertyHere();
                    RoadProperty rp = new RoadProperty();

                    rp.setFeatId(herePhaStreets.getLinkId().toString());
                    rp.setGeom(herePhaStreets.getGeometry());
                    rp.setName(herePhaStreets.getStName());
                    rp.setSpeedmaxPos(herePhaStreets.getFrSpdLim().toString());
                    rp.setRoadCondition(herePhaStreets.getPaved());
                    rp.setRoutingClass(herePhaStreets.getFuncClass());
                    rp.setNumOfLanes(herePhaStreets.getPhysLanes().toString());
                    rp.setRamp(herePhaStreets.getRamp());
                    rp.setSpeedmaxNeg(herePhaStreets.getToSpdLim().toString());
                    rp.setSimpleTrafficDirection(herePhaStreets.getDirTravel());

                    QueryWrapper<HerePhaStreets> scenicFileQueryWrapper1 = new QueryWrapper<>();
                    scenicFileQueryWrapper1.eq("link_id",Integer.parseInt(id));
                    scenicFileQueryWrapper1.eq("cond_type", 7);

                    List<ManeuverVo> maneuverVoList = herePhaStreetsMapper.getHereManeuver(scenicFileQueryWrapper1);

                    for (ManeuverVo maneu:maneuverVoList) {
                        QueryWrapper<HerePhaStreets> hereThaStreetsQueryWrapper = new QueryWrapper<>();
                        hereThaStreetsQueryWrapper.eq("r.link_id", Integer.parseInt(id));
                        hereThaStreetsQueryWrapper.eq("r.cond_id", Integer.parseInt(maneu.getId()));

                        List<LinkMainVo> linkMainVos = new ArrayList<>();
                        linkMainVos = herePhaStreetsMapper.getHereManeuverLink(hereThaStreetsQueryWrapper);

                        maneu.setLinkInfo(linkMainVos);
                    }

                    rp.setHereManeuverVoList(maneuverVoList);
                    hereList.add(rp);
                }
                else if("mangu".equals(city)){
                    QueryWrapper<HereThaStreets> scenicFileQueryWrapper = new QueryWrapper<>();
                    scenicFileQueryWrapper.eq("\"LINK_ID\"",Integer.parseInt(id));
                    HereThaStreets hereThaStreets = hereThaStreetsMapper.selectOne(scenicFileQueryWrapper);
                    //HereThaStreets hereThaStreets = hereThaStreetsService.lambdaQuery().eq(HereThaStreets::getLinkId,Integer.parseInt(id)).list().get(0);

                    //RoadPropertyHere rp = new RoadPropertyHere();
                    RoadProperty rp = new RoadProperty();

                    rp.setFeatId(hereThaStreets.getLinkId().toString());
                    rp.setGeom(hereThaStreets.getGeometry());
                    rp.setName(hereThaStreets.getStName());
                    rp.setSpeedmaxPos(hereThaStreets.getFrSpdLim());
                    rp.setRoadCondition(hereThaStreets.getPaved());
                    rp.setRoutingClass(hereThaStreets.getFuncClass());
                    rp.setNumOfLanes(hereThaStreets.getPhysLanes());
                    rp.setRamp(hereThaStreets.getRamp());
                    rp.setSpeedmaxNeg(hereThaStreets.getToSpdLim());
                    rp.setSimpleTrafficDirection(hereThaStreets.getDirTravel());

                    QueryWrapper<HereThaStreets> scenicFileQueryWrapper1 = new QueryWrapper<>();
                    scenicFileQueryWrapper1.eq("link_id",Integer.parseInt(id));
                    scenicFileQueryWrapper1.eq("cond_type", 7);

                    MybatisPlusConfig.myTableName.set("");
                    List<ManeuverVo> maneuverVoList = hereThaStreetsMapper.getHereManeuver(scenicFileQueryWrapper1);

                    for (ManeuverVo maneu:maneuverVoList) {
                        QueryWrapper<HereThaStreets> hereThaStreetsQueryWrapper = new QueryWrapper<>();
                        hereThaStreetsQueryWrapper.eq("r.link_id", Integer.parseInt(id));
                        hereThaStreetsQueryWrapper.eq("r.cond_id", Integer.parseInt(maneu.getId()));

                        List<LinkMainVo> linkMainVos = new ArrayList<>();
                        linkMainVos = hereThaStreetsMapper.getHereManeuverLink(hereThaStreetsQueryWrapper);

                        maneu.setLinkInfo(linkMainVos);
                    }

                    rp.setHereManeuverVoList(maneuverVoList);
                    hereList.add(rp);
                }

            }
        }


        linkData.setTomtomList(mnrList);
        List<RoadProperty> osmList = new ArrayList<>();
        linkData.setOsmList(osmList);

        linkData.setHereList(hereList);
        return  ResponseResult.OK(linkData, true);
    }

    @GetMapping("/linkByManeuver")
    @ApiOperation(value = "根据交限点获得路的信息")
    //@DS("db2")
    public  ResponseResult<MenverDto> getLinkInfoByManeuverid(@RequestParam(value = "ids") String ids,
                                                             @RequestParam(value = "mapType") String mapType,
                                                              @RequestParam(value = "city") String city){
        MenverDto menverDto = new MenverDto();

        List<ManeuverVo> mnrList = new ArrayList<>();
        List<ManeuverVo> osmList = new ArrayList<>();
        List<ManeuverVo> hereList = new ArrayList<>();

        String[] idList = ids.split(",");
        List<String> typeList = new ArrayList<>();
        for (String id: idList) {
            ManeuverVo maneuverVo = new ManeuverVo();
            QueryWrapper<RoadProperty> scenicFileQueryWrapper = new QueryWrapper<>();
            scenicFileQueryWrapper.eq("n.feat_id", UUID.fromString(id));
            if("manila".equals(city)){


                List<LinkMainVo> linkMainVos = new ArrayList<>();
                scenicFileQueryWrapper.eq("n.feat_id", UUID.fromString(id));
                Maneuver maneuver = mnrPhaNetwGeoLinkMapper.getManeuverBase(scenicFileQueryWrapper);
                maneuverVo.setId(maneuver.getFeatId());
                maneuverVo.setFeatType(maneuver.getFeatType());
                linkMainVos = mnrPhaNetwGeoLinkMapper.getManeuverLink(scenicFileQueryWrapper);
//            RoadProperty rp = mnrNetwGeoLinkMapper.getManeuver(scenicFileQueryWrapper);
                maneuverVo.setLinkInfo(linkMainVos);
                maneuverVo.setLinkSeqString(linkMainVos.stream().map(LinkMainVo::getManeuverSeq).collect(Collectors.joining(","))+":"+linkMainVos.stream().map(LinkMainVo::getFeatId).collect(Collectors.joining(",")));

            }
            else if("mangu".equals(city)){

                List<LinkMainVo> linkMainVos = new ArrayList<>();

                Maneuver maneuver = mnrNetwGeoLinkMapper.getManeuverBase(scenicFileQueryWrapper);
                maneuverVo.setId(maneuver.getFeatId());
                maneuverVo.setFeatType(maneuver.getFeatType());
                linkMainVos = mnrNetwGeoLinkMapper.getManeuverLink(scenicFileQueryWrapper);
//            RoadProperty rp = mnrNetwGeoLinkMapper.getManeuver(scenicFileQueryWrapper);
                maneuverVo.setLinkInfo(linkMainVos);
                maneuverVo.setLinkSeqString(linkMainVos.stream().map(LinkMainVo::getManeuverSeq).collect(Collectors.joining(","))+":"+linkMainVos.stream().map(LinkMainVo::getFeatId).collect(Collectors.joining(",")));

            }

            if(mapType.equals("tomtom")){
                mnrList.add(maneuverVo);
            }
            else if(mapType.equals("here")){
                hereList.add(maneuverVo);
            } else if (mapType.equals("osm")){
                osmList.add(maneuverVo);
            } else {
                return ResponseResult.OK(menverDto,false);
            }

        }

        menverDto.setTomtomList(mnrList);

        menverDto.setOsmList(osmList);

        menverDto.setHereList(hereList);
        return  ResponseResult.OK(menverDto, true);
    }
    public ResponseResult<LinkData> getLinkInfoByMulCoor(@RequestParam(value = "mulCoors") String mulCoors){
        LinkData linkData = new LinkData();

        return ResponseResult.OK(linkData, true);
    }


    @PostMapping(value = "/trackMatch")
    @ApiOperation(value = "trackMatch")
    public String trackMatch(@RequestBody String trackInfo) {
        return roadMatchService.getMatchResult(trackInfo);
    }

    @GetMapping(value = "/trackMatchBatch")
    @ApiOperation(value = "trackMatchBatch")
    public ResponseResult<String> trackMatchBatch(@RequestParam String dataSource,@RequestParam String city) throws ParseException, InterruptedException {
        //获取所有orderId
        List<String> orderIdList = new ArrayList<>();
        if(city.compareTo("mangu") == 0) {
            //orderIdList= trackCoorInfoMapper.getOrderId();
            if(dataSource.compareTo("here") == 0){
                orderIdList = trackCoorInfoMapper.getUnprocessedHereId();
            } else if(dataSource.compareTo("tomtom") == 0){
                orderIdList = trackCoorInfoMapper.getUnprocessedTomtomId();
            } else {
                return ResponseResult.OK("no such city:"+city+",please check it!",false);
            }
            //根据oderId拼接请求对象
            for(String orderId : orderIdList){
                mnrNetwGeoLinkService.getTrackMatch(orderId,dataSource);
            }
        } else if(city.compareTo("manila") == 0) {
            //orderIdList = phlTrackCoorInfoBakMapper.getOrderId();
            if(dataSource.compareTo("here") == 0){
                orderIdList = phlTrackCoorInfoBakMapper.getUnprocessedHereId();
            } else if(dataSource.compareTo("tomtom") == 0){
                orderIdList = phlTrackCoorInfoBakMapper.getUnprocessedTomtomId();
            } else {
                return ResponseResult.OK("no such city:"+city+",please check it!",false);
            }
            //根据oderId拼接请求对象
            for(String orderId : orderIdList){
                mnrNetwGeoLinkService.getPhlTrackMatch(orderId,dataSource);
            }
        } else {
            return ResponseResult.OK("no such city:"+city+",please check it!",false);
        }

        return ResponseResult.OK(true);
    }
    @GetMapping(value = "/getReleatedInfo")
    @ApiOperation(value = "getReleatedInfo")
//    public List<TestVo> getReleatedInfo(){
//        return mnrNetwGeoLinkMapper.getReleatedInfo();
//    }

    public ResponseResult<List<TestVo>> getReleatedInfo(){
        return ResponseResult.OK(mnrNetwGeoLinkMapper.getReleatedInfo(),true);
    }
}