package com.hll.mapdataservice.business.api.road.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.hll.mapdataservice.business.api.road.service.*;
import com.hll.mapdataservice.business.common.XmlFileUtils;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.mapper.RuleMMapper;
import com.hll.mapdataservice.common.utils.CommonUtils;
import com.linuxense.javadbf.DBFField;
import com.linuxense.javadbf.DBFWriter;
import com.vividsolutions.jts.io.ParseException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.CaseUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

import static com.hll.mapdataservice.common.utils.DbfFileUtils.getFieldNameByTableName;

@RestController
@ResponseBody
@Slf4j
@RequestMapping("/api/road/rule_e")
public class RuleEController {
    @Resource
    private RuleEServiceImpl ruleEService;

    @ApiOperation("批量刷新rule_info")
    @GetMapping("/handle_rule_info")
    public ResponseResult<String> handleRuleInfo(String country) {
        return ResponseResult.OK(ruleEService.handleRuleInfo(country));
    }
}
