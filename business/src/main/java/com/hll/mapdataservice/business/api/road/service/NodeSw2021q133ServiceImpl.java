package com.hll.mapdataservice.business.api.road.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hll.mapdataservice.common.entity.NodeSw2021q133;
import com.hll.mapdataservice.common.mapper.NodeSw2021q133Mapper;
import com.hll.mapdataservice.common.service.INodeSw2021q133Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
//@DS("db8")
@Service
public class NodeSw2021q133ServiceImpl extends ServiceImpl<NodeSw2021q133Mapper, NodeSw2021q133> implements INodeSw2021q133Service {

}
