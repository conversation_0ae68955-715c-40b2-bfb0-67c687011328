package com.hll.mapdataservice.business.api.poi.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hll.mapdataservice.common.entity.MnrPoi;
import com.hll.mapdataservice.common.mapper.MnrPoiMapper;
import com.hll.mapdataservice.common.service.IMnrPoiService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-19
 */
@Service
//@DS("db2")
public class MnrPoiServiceImpl extends ServiceImpl<MnrPoiMapper, MnrPoi> implements IMnrPoiService {

}
