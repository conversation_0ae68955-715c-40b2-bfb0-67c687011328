package com.hll.mapdataservice.business.vo;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.postgis.Geometry;

import java.io.Serializable;

/*
 * <AUTHOR>
 * @date  3/17/21 16:17
 * @Email:<EMAIL>
 * @remark:
 */
@Data
@Getter
@Setter
public class RoadPropertyHere implements Serializable {

    private String featId;

    private String funcClass;

    private String physLanes;

    private String travelDirection;

    private String ramp;

    private String frSpdLim;

    private String toSpdLim;

    private String stName;

    private String paved;

    private String geom;
}
