package com.hll.mapdataservice.business.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-12
 */
@ApiModel(value="PhlTrackCoorInfoBak对象", description="")
@TableName(value = "phl_track_coor_info_bak")
public class PhlTrackCoorInfoBak extends Model<PhlTrackCoorInfoBak> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String orderId;

    private String latitude;

    private String longitude;

    private String idvandriver;

    private String bearing;

    private String locTime;

    private String ttLink;

    private String osmLink;

    private String hereLink;

    private String geom;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }
    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }
    public String getIdvandriver() {
        return idvandriver;
    }

    public void setIdvandriver(String idvandriver) {
        this.idvandriver = idvandriver;
    }
    public String getBearing() {
        return bearing;
    }

    public void setBearing(String bearing) {
        this.bearing = bearing;
    }
    public String getLocTime() {
        return locTime;
    }

    public void setLocTime(String locTime) {
        this.locTime = locTime;
    }
    public String getTtLink() {
        return ttLink;
    }

    public void setTtLink(String ttLink) {
        this.ttLink = ttLink;
    }
    public String getOsmLink() {
        return osmLink;
    }

    public void setOsmLink(String osmLink) {
        this.osmLink = osmLink;
    }
    public String getHereLink() {
        return hereLink;
    }

    public void setHereLink(String hereLink) {
        this.hereLink = hereLink;
    }
    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "PhlTrackCoorInfoBak{" +
            "id=" + id +
            ", orderId=" + orderId +
            ", latitude=" + latitude +
            ", longitude=" + longitude +
            ", idvandriver=" + idvandriver +
            ", bearing=" + bearing +
            ", locTime=" + locTime +
            ", ttLink=" + ttLink +
            ", osmLink=" + osmLink +
            ", hereLink=" + hereLink +
            ", geom=" + geom +
        "}";
    }
}
