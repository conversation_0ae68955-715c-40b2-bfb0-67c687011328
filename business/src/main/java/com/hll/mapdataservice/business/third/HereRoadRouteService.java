package com.hll.mapdataservice.business.third;

import com.hll.mapdataservice.business.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * @Author: ares.chen
 * @Since: 2022/5/23
 */
@FeignClient(name = "hereRoadRouteService",configuration = FeignConfig.class,url = "${third.hereRoadRoute.url}")
public interface HereRoadRouteService {

    @GetMapping("/v8/routes")
    String getRoutesInfo(@RequestParam Map<String, Object> paramMap);

    @PostMapping("/v8/routes")
    String getRoutesInfoPost(@RequestBody Map<String, Object> paramMap);
}
