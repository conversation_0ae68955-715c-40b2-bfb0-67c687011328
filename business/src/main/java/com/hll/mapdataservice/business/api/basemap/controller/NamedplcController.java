package com.hll.mapdataservice.business.api.basemap.controller;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.hll.mapdataservice.business.api.basemap.service.NamedplcHServiceImpl;
import com.hll.mapdataservice.business.api.basemap.service.NamedplcServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.NamedplcH;
import com.hll.mapdataservice.common.utils.CommonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-02
 */
@RestController
@ResponseBody
@Api(tags = "basemap")
@Component
@Slf4j
@RequestMapping("/api/basemap/namedplc")
public class NamedplcController {

    @Resource
    NamedplcServiceImpl namedplcService;
    @Resource
    NamedplcHServiceImpl namedplcHService;

    @ApiOperation(value = "here basemap namedplc convert")
    @PostMapping("/convert")
    public ResponseResult<Boolean> hereNamedConvert(@RequestParam(value = "step",
            required = false,
            defaultValue = "1") int step,
                                                       @RequestParam(value = "version",
                                                               required = false) String version,
                                                       @RequestParam(value = "iscompiletranseng",
                                                               required = false,
                                                               defaultValue = "false") boolean isCompileTransEng,
                                                       @RequestParam(value = "area",
                                                               required = false,
                                                               defaultValue = "") String area,
                                                       @RequestParam(value = "country",
                                                               required = false,
                                                               defaultValue = "") String country)
            throws InterruptedException, SQLException {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        //namedplcH
        TimeInterval timer = DateUtil.timer();

        List<NamedplcH> namedplcHMultiList = new ArrayList<>();
        Integer listSize = namedplcHService.count();
        CountDownLatch countDownLatch = new CountDownLatch(listSize / step + 1);
        log.info("The namedplc records to be transfered:" + listSize);
        for (int i = 0; i <= listSize / step; i++) {
//            if (i == 0) {
//                step = 100;
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<NamedplcH> namedplcHList = namedplcHService.lambdaQuery()
                    .orderByDesc(NamedplcH::getPoiId).last("limit " + step + " offset " + i * step).list();

            Map<Long, Long> idCounts = namedplcHList.stream()
                    .collect(Collectors.groupingBy(NamedplcH::getPoiId, Collectors.counting()));

            List<NamedplcH> uniqueList = namedplcHList.stream()
                    .filter(namedplcH -> idCounts.get(namedplcH.getPoiId()) == 1)
                    .collect(Collectors.toList());
            log.info("The namedplc uniqueList records to be transfered:" + uniqueList.size());

            if(uniqueList.size() == namedplcHList.size()){
                log.info("process start limit " + step + " offset " + i * step);
                namedplcService.namedplcConvert(namedplcHList,
                        isCompileTransEng, area, country, countDownLatch, false);
            } else {
                log.info("process uniqueList size:" + step + " offset " + i * step);
                namedplcService.namedplcConvert(uniqueList,
                        isCompileTransEng, area, country, countDownLatch, false);
                log.info("process duplicatedList size:" + step + " offset " + i * step);
                List<NamedplcH> duplicatedList = namedplcHList.stream()
                        .filter(namedplcH -> idCounts.get(namedplcH.getPoiId()) > 1)
                        .collect(Collectors.toList());
                log.info("process duplicatedList size:" + duplicatedList.size());
                namedplcHMultiList.addAll(duplicatedList);
            }

//            log.info("process start limit " + step + " offset " + i * step);
//            if (namedplcHList.size() > 0) {
//                // linkSw2021q133Service.linkConvert(streetsListi,nodeIdSet,nodeList);
//                namedplcService.namedplcConvert(namedplcHList,
//                        isCompileTransEng, area, country, countDownLatch);
//            }
//            }
        }
        if(namedplcHMultiList.size() > 0){
            log.info("process namedplcHMultiList size:" + namedplcHMultiList.size());
            namedplcService.namedplcConvert(namedplcHMultiList,
                    isCompileTransEng, area, country, countDownLatch, true);
        }
        log.info("here landmark convert to building cost time is {}s", timer.intervalSecond());
        countDownLatch.await();
        //log.info("here waterseg convert to herebpolygon cost time is {}s", timer.intervalSecond());

        return ResponseResult.OK(true, true);
    }


}
