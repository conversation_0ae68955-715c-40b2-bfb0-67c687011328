package com.hll.mapdataservice.business.mapper;

import com.hll.mapdataservice.business.entity.TrackCoorInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-05
 */
@Mapper
public interface TrackCoorInfoMapper extends BaseMapper<TrackCoorInfo> {

    @Select("select distinct(order_id) from track_coor_info order by order_id")
    List<String> getOrderId();

    @Select("select distinct(order_id) from track_coor_info where here_link is null")
    List<String> getUnprocessedHereId();

    @Select("select distinct(order_id) from track_coor_info where tt_link is null")
    List<String> getUnprocessedTomtomId();
}
