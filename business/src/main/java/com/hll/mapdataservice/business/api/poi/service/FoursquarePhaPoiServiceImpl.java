package com.hll.mapdataservice.business.api.poi.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hll.mapdataservice.common.entity.FoursquarePhaPoi;
import com.hll.mapdataservice.common.mapper.FoursquarePhaPoiMapper;
import com.hll.mapdataservice.common.service.IFoursquarePhaPoiService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-19
 */
@Service
@DS("db8")
public class FoursquarePhaPoiServiceImpl extends ServiceImpl<FoursquarePhaPoiMapper, FoursquarePhaPoi> implements IFoursquarePhaPoiService {

}
