package com.hll.mapdataservice.business.api.road.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.third.InheritIDService;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.mapper.LinkEMapper;
import com.hll.mapdataservice.common.mapper.LinkMapper;
import com.hll.mapdataservice.common.mapper.NodeEMapper;
import com.hll.mapdataservice.common.mapper.NodeMapper;
import com.hll.mapdataservice.common.service.ILinkEService;
import com.hll.mapdataservice.common.service.INodeEService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import com.hll.mapdataservice.common.utils.StatusUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-28
 */
@Slf4j
@Service
public class LinkEServiceImpl extends ServiceImpl<LinkEMapper, LinkE> implements ILinkEService {

    @Resource
    private LinkMapper linkMapper;

    @Async("optimizedAsyncTaskExecutor")
    public void linkE2link(String area, String country, CountDownLatch countDownLatch, List<LinkE> linkEList) {
        try {
            log.info("area is {},country is{},db is{}", area, country, DynamicDataSourceContextHolder.peek());
            int fieldNum = BeanUtil.beanToMap(new Link()).keySet().size();
            int batchSize = 65535 / fieldNum;
            log.info("batchInsert size is {}", batchSize);
            List<List<LinkE>> splitList = ListUtil.split(linkEList, batchSize);
            for (List<LinkE> links : splitList) {
                //Link linkRp;
                List<Link> rpList = new ArrayList<>();
                for (LinkE linkE : links) {
                    Link link = new Link();
                    BeanUtil.copyProperties(linkE, link);
                    //差异字段对应赋值
                    link.setId(linkE.getLinkId());
                    link.setDirection(linkE.getDir());
                    link.setConstSt(linkE.getApp());
                    link.setDetailcity(linkE.getDevs());
                    link.setSpecial(linkE.getSpet());
                    link.setFuncclass(linkE.getFunct());
                    link.setUflag(linkE.getUrban());
                    link.setRoadCond(linkE.getPave());
                    link.setLanenumsum(linkE.getLaneN());
                    link.setLanenums2e(linkE.getLaneL());
                    link.setLanenume2s(linkE.getLaneR());
                    link.setLanenumc(linkE.getLaneC());
                    link.setElevated(linkE.getViad());
                    link.setAdmincodel(linkE.getLAdmin());
                    link.setAdmincoder(linkE.getRAdmin());
                    link.setSpdlmts2e(linkE.getFSpeed());
                    link.setSpdlmte2s(linkE.getTSpeed());
                    link.setSpeedclass(linkE.getSpClass());
                    link.setDcType(linkE.getDiciType());
                    link.setTAdmin(linkE.getTAdmin());
                    link.setTimeZone(linkE.getTimeZone());
                    link.setStatus(0);
                    rpList.add(link);
                }
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
                log.info("insert db is:" + DynamicDataSourceContextHolder.peek());
                // linkMapper.insertBatch(rpList);
                linkMapper.mysqlInsertOrUpdateBath(rpList);
                //this.saveBatch(rpList);

            }
        } catch (Exception e) {
            log.error("sync to link error,detail is {}", e.getMessage());
            throw e;
        } finally {
            //DynamicDataSourceContextHolder.poll();
            countDownLatch.countDown();
        }
    }


    public List<LinkE> queryByExtent(String extent, String status) {
        return this.lambdaQuery().in(LinkE::getStatus, StatusUtil.status(status))
                .apply("ST_Intersects(ST_GeomFromText({0}, 4326), geometry)", extent).list();
    }

    public LinkE queryById(String hllLinkid, String status) {
        return this.lambdaQuery().eq(LinkE::getHllLinkid, hllLinkid).in(LinkE::getStatus, StatusUtil.status(status)).one();
    }
}
