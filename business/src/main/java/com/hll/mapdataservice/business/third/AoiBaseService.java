package com.hll.mapdataservice.business.third;

import com.alibaba.fastjson.JSON;
import com.hll.mapdataservice.business.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "aoibaseservice", configuration = FeignConfig.class, url = "${third.aoibaseservice.url}")
public interface AoiBaseService {
    /**
     * http://192.168.106.46:8089/huolala/display/count/queryinfo
     */
    @GetMapping(value = "/huolala/display/count/queryinfo")
    String getQueryAoi();
}