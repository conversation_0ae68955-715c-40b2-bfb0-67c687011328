package com.hll.mapdataservice.business.api.road.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hll.mapdataservice.business.api.road.service.StreetsServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.mapper.CalculationMapper;
import com.hll.mapdataservice.common.mapper.LinkMapper;
import com.hll.mapdataservice.common.service.ILinkService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/9/22
 */
@Slf4j
@RequestMapping("linkRp")
@RestController
public class LinkController {

    @Resource
    private ILinkService linkService;
    @Resource
    private CalculationMapper calculationMapper;

    @Resource
    private StreetsServiceImpl streetsService;

    @Resource
    private LinkMapper linkMapper;

    @GetMapping("spellLink")
    public ResponseResult<List<String>> spellLink(String linkName, String country) {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        MybatisPlusConfig.myTableName.set("");

        // 1.筛选数据源
        List<Link> linkRpList = linkService.lambdaQuery().like(Link::getNameChO, linkName).list();

        // 2.同向拼接link
        // 2.1 获取两条拼接路的首尾段
        List<String> nodeIdList = new ArrayList<>();
        for (Link linkRp : linkRpList) {
            nodeIdList.add(linkRp.getHllSNid());
            nodeIdList.add(linkRp.getHllENid());
        }
        List<String> singleNodes = CommonUtils.removeDuplicateElement(nodeIdList);
        List<Link> seLinks = linkRpList.stream().filter(s -> singleNodes.contains(s.getHllSNid()) || singleNodes.contains(s.getHllENid())).collect(Collectors.toList());

        // 2.2 拼接link
        String tmpLinkId = "";
        List<String> resList = new ArrayList<>();
        Set<String> tmpSet = new HashSet<>();
        for (Link seLink : seLinks) {
            if (tmpSet.contains(seLink.getId())) {
                continue;
            }
            StringBuilder line1Sb = new StringBuilder();
            Link tmpLink = seLink;
            line1Sb.append(seLink.getId()).append(",");
            for (int i = 0; i < linkRpList.size(); i++) {
                if (tmpLink == null) {
                    break;
                }
                tmpLink = recursiveFind(i, tmpLink, line1Sb, linkRpList);
            }
            String spellLine = StrUtil.removeSuffix(line1Sb, ",");
            resList.add(spellLine);
            tmpSet.add(seLink.getId());
            tmpSet.add(getLastElement(spellLine));
        }
        // // 2.3 拼接第一条路
        // Link sLinkRp = seLinks.get(0);
        // Link tmpLink = sLinkRp;
        // line1Sb.append(sLinkRp.getId()).append(",");
        // for (int i = 0; i < linkRpList.size(); i++) {
        //     if (tmpLink == null) {
        //         break;
        //     }
        //     tmpLink = recursiveFind(i,tmpLink, line1Sb, linkRpList);
        // }

        // System.out.println(resList);
        // String join = CollUtil.join(tmpList, ";");
        ResponseResult<List<String>> result = new ResponseResult<>();
        result.setCode("200");
        result.setData(resList);
        result.setSuccess(true);
        return result;
    }

    /**
     * 香港收费隧道依次查找处理
     *
     * @param linkName
     * @param country
     * @return
     */
    @GetMapping("spellLinkNew")
    public ResponseResult<List<String>> spellLinkNew(String startLink1,
                                                     String startLink2,
                                                     String linkName,
                                                     @RequestParam(defaultValue = "") String linkName2,
                                                     String country) {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        MybatisPlusConfig.myTableName.set("");

        // 1.筛选数据源
        List<Link> linkRpList = linkService.lambdaQuery().like(Link::getNameChO, linkName).or().like(StrUtil.isNotEmpty(linkName2), Link::getNameChO, linkName2).list();

        List<String> startLinks = CollUtil.newArrayList(startLink1, startLink2);

        List<String> resList = new ArrayList<>();
        // 2.拼接处理
        for (String startLink : startLinks) {

            List<Link> startLinkIdentities = linkRpList.stream().filter(s -> s.getId().equals(startLink)).collect(Collectors.toList());
            Link linkRp = startLinkIdentities.get(0);
            if ("2".equals(linkRp.getDirection())) {
                StringBuilder line1Sb = new StringBuilder();
                Link tmpLink = linkRp;
                line1Sb.append(linkRp.getId()).append(",");
                for (int i = 0; i < linkRpList.size(); i++) {
                    if (tmpLink == null) {
                        break;
                    }
                    tmpLink = recursiveFindNew(i, tmpLink, line1Sb, linkRpList);
                }
                String spellLine = StrUtil.removeSuffix(line1Sb, ",");
                resList.add(spellLine);
            }
            if ("3".equals(linkRp.getDirection())) {
                StringBuilder line1Sb = new StringBuilder();
                Link tmpLink = linkRp;
                line1Sb.append(linkRp.getId()).append(",");
                for (int i = 0; i < linkRpList.size(); i++) {
                    if (tmpLink == null) {
                        break;
                    }
                    tmpLink = recursiveFindNew(i, tmpLink, line1Sb, linkRpList);
                }
                String spellLine = StrUtil.removeSuffix(line1Sb, ",");
                List<String> reverse = CollUtil.reverse(Arrays.asList(spellLine.split(",")));
                String reverseLine = CollUtil.join(reverse, ",");
                resList.add(reverseLine);
            }
        }

        ResponseResult<List<String>> result = new ResponseResult<>();
        result.setCode("200");
        result.setData(resList);
        result.setSuccess(true);
        return result;
    }

    private String getLastElement(String spellLine) {
        return StrUtil.sub(spellLine, spellLine.lastIndexOf(",") + 1, spellLine.length());
    }


    private Link recursiveFind(int i, Link linkRp, StringBuilder line1Sb, List<Link> linkRpList) {
        if (i == 0) {
            List<Link> findLinks = linkRpList.stream().filter(s -> (!s.getId().equals(linkRp.getId()))
                    && (s.getHllSNid().equals(linkRp.getHllSNid()) || s.getHllENid().equals(linkRp.getHllSNid()) ||
                    s.getHllSNid().equals(linkRp.getHllENid()) || s.getHllENid().equals(linkRp.getHllENid()))).collect(Collectors.toList());
            if (findLinks.size() == 1) {
                line1Sb.append(findLinks.get(0).getId()).append(",");
                Link nextLinkRp = findLinks.get(0);
                List<String> tmpNodeList = CollUtil.newArrayList(linkRp.getHllSNid(), linkRp.getHllENid(), nextLinkRp.getHllSNid(), nextLinkRp.getHllENid());
                nextLinkRp.setMemo(CommonUtils.getRepeatElement(tmpNodeList).get(0));
                return nextLinkRp;
            }
        } else {
            if (linkRp == null) {
                return null;
            }
            String findNodeId;
            if (linkRp.getHllSNid().equals(linkRp.getMemo())) {
                findNodeId = linkRp.getHllENid();
            } else {
                findNodeId = linkRp.getHllSNid();
            }
            List<Link> findLinks = linkRpList.stream().filter(s -> (!s.getId().equals(linkRp.getId()))
                    && (s.getHllSNid().equals(findNodeId) || s.getHllENid().equals(findNodeId) ||
                    s.getHllSNid().equals(findNodeId) || s.getHllENid().equals(findNodeId))).collect(Collectors.toList());
            if (findLinks.size() == 1) {
                line1Sb.append(findLinks.get(0).getId()).append(",");
                Link nextLinkRp = findLinks.get(0);
                List<String> tmpNodeList = CollUtil.newArrayList(linkRp.getHllSNid(), linkRp.getHllENid(), nextLinkRp.getHllSNid(), nextLinkRp.getHllENid());
                nextLinkRp.setMemo(CommonUtils.getRepeatElement(tmpNodeList).get(0));
                return nextLinkRp;
            }
        }
        return null;
    }

    private Link recursiveFindNew(int i, Link linkRp, StringBuilder line1Sb, List<Link> linkRpList) {
        // 1.过滤匝道
        List<String> filterList = CollUtil.newArrayList("14", "31");
        if (i == 0) {
            // 2.决定筛选方向
            // List<Link> findLinks = new ArrayList<>();
            // if ("2".equals(linkRp.getDirection())) {
            //     findLinks = linkRpList.stream().filter(s -> (!s.getId().equals(linkRp.getId()))
            //             && (s.getHllSNid().equals(linkRp.getHllENid()) || s.getHllENid().equals(linkRp.getHllENid()))).collect(Collectors.toList());
            // }
            // if("3".equals(linkRp.getDirection())) {
            //     findLinks = linkRpList.stream().filter(s -> (!s.getId().equals(linkRp.getId()))
            //             && (s.getHllSNid().equals(linkRp.getHllSNid()) || s.getHllENid().equals(linkRp.getHllSNid()))).collect(Collectors.toList());
            // }
            //----------------------
            // List<Link> findLinks = linkRpList.stream().filter(s -> (!s.getId().equals(linkRp.getId()))
            //         && (s.getHllSNid().equals(linkRp.getHllSNid()) || s.getHllENid().equals(linkRp.getHllSNid()) ||
            //         s.getHllSNid().equals(linkRp.getHllENid()) || s.getHllENid().equals(linkRp.getHllENid()))).collect(Collectors.toList());
            //---------------------
            List<Link> findLinks = linkRpList.stream().filter(s -> (!s.getId().equals(linkRp.getId()))
                    && (s.getHllSNid().equals(linkRp.getHllENid()) || s.getHllENid().equals(linkRp.getHllENid()))).collect(Collectors.toList());
            if (findLinks.size() == 1) {
                line1Sb.append(findLinks.get(0).getId()).append(",");
                Link nextLinkRp = findLinks.get(0);
                List<String> tmpNodeList = CollUtil.newArrayList(linkRp.getHllSNid(), linkRp.getHllENid(), nextLinkRp.getHllSNid(), nextLinkRp.getHllENid());
                nextLinkRp.setMemo(CommonUtils.getRepeatElement(tmpNodeList).get(0));
                return nextLinkRp;
            } else {
                // 1.1考虑有匝道汇合的场景
                List<Link> filterLinks = findLinks.stream().filter(s -> filterList.contains(s.getFormway())).collect(Collectors.toList());
                if (filterLinks.size() == 1) {
                    line1Sb.append(filterLinks.get(0).getId()).append(",");
                    Link nextLinkRp = filterLinks.get(0);
                    List<String> tmpNodeList = CollUtil.newArrayList(linkRp.getHllSNid(), linkRp.getHllENid(), nextLinkRp.getHllSNid(), nextLinkRp.getHllENid());
                    nextLinkRp.setMemo(CommonUtils.getRepeatElement(tmpNodeList).get(0));
                    return nextLinkRp;
                }
            }
        } else {
            if (linkRp == null) {
                return null;
            }
            String findNodeId;
            if (linkRp.getHllSNid().equals(linkRp.getMemo())) {
                findNodeId = linkRp.getHllENid();
            } else {
                findNodeId = linkRp.getHllSNid();
            }
            List<Link> findLinks = linkRpList.stream().filter(s -> (!s.getId().equals(linkRp.getId()))
                    && (s.getHllSNid().equals(findNodeId) || s.getHllENid().equals(findNodeId) ||
                    s.getHllSNid().equals(findNodeId) || s.getHllENid().equals(findNodeId))).collect(Collectors.toList());
            if (findLinks.size() == 1) {
                line1Sb.append(findLinks.get(0).getId()).append(",");
                Link nextLinkRp = findLinks.get(0);
                List<String> tmpNodeList = CollUtil.newArrayList(linkRp.getHllSNid(), linkRp.getHllENid(), nextLinkRp.getHllSNid(), nextLinkRp.getHllENid());
                nextLinkRp.setMemo(CommonUtils.getRepeatElement(tmpNodeList).get(0));
                return nextLinkRp;
            } else {
                // 1.1考虑有匝道汇合的场景
                List<Link> filterLinks = findLinks.stream().filter(s -> filterList.contains(s.getFormway())).collect(Collectors.toList());
                if (filterLinks.size() == 1) {
                    line1Sb.append(filterLinks.get(0).getId()).append(",");
                    Link nextLinkRp = filterLinks.get(0);
                    List<String> tmpNodeList = CollUtil.newArrayList(linkRp.getHllSNid(), linkRp.getHllENid(), nextLinkRp.getHllSNid(), nextLinkRp.getHllENid());
                    nextLinkRp.setMemo(CommonUtils.getRepeatElement(tmpNodeList).get(0));
                    return nextLinkRp;
                }
            }
        }
        return null;
    }

    private void addFirstAndLastPoint(String geomwkt, List<String> pointList) {
        String[] split = StrUtil.subBetween(geomwkt, "((", "))").split(",");
        String firstPoint = StrUtil.trim(split[0]);
        String lastPoint = StrUtil.trim(split[split.length - 1]);
        pointList.add(firstPoint);
        pointList.add(lastPoint);
    }

    @GetMapping("/validate")
    public ResponseResult<String> validateLink(String country, String area, String firstLinkId, String lastLinkId) {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        String finalRes = findAndSpellByFirstLinkEndLink(country, area, new StringBuilder(), firstLinkId, lastLinkId);

        return ResponseResult.OK(finalRes, true);
    }

    @GetMapping("/getLinkByHllLinkId")
    public ResponseResult<Link> getLinkByHllLinkId(String country, String area, String hllLinkId) {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        return ResponseResult.OK(linkService.getOne(Wrappers.<Link>lambdaQuery().eq(Link::getHllLinkid,hllLinkId)), true);
    }

    volatile boolean flag = true;

    public String findAndSpellByFirstLinkEndLink(String country, String area, StringBuilder sb, String firstLinkId, String lastLinkId) {

        Link firstLink = linkService.lambdaQuery().eq(Link::getId, firstLinkId).one();

        CountDownLatch countDownLatch = new CountDownLatch(2);
        if ("1".equals(firstLink.getDirection())) {
            // 双向
            CompletableFuture<String> future1 = CompletableFuture.supplyAsync(() -> {
                StringBuilder stringBuilder1 = new StringBuilder();
                stringBuilder1.append(firstLink.getId()).append(",");
                String nodeId = firstLink.getHllENid();
                while (flag) {
                    extractedFindThread(country, area, stringBuilder1, lastLinkId, firstLink, nodeId);
                }
                countDownLatch.countDown();
                return stringBuilder1.toString();
            });

            CompletableFuture<String> future2 = CompletableFuture.supplyAsync(() -> {
                StringBuilder stringBuilder2 = new StringBuilder();
                stringBuilder2.append(firstLink.getId()).append(",");
                String nodeId = firstLink.getHllSNid();
                while (flag) {
                    extractedFindThread(country, area, stringBuilder2, lastLinkId, firstLink, nodeId);
                }
                countDownLatch.countDown();
                return stringBuilder2.toString();
            });

            try {
                countDownLatch.await();
                return !future1.get().contains(lastLinkId) ? future2.get() : future1.get();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } catch (ExecutionException e) {
                throw new RuntimeException(e);
            }
        } else if ("2".equals(firstLink.getDirection())) {
            // 顺向，通过尾点查找
            sb.append(firstLink.getId()).append(",");
            String nodeId = firstLink.getHllENid();
            extractedFind(sb, lastLinkId, firstLink, nodeId);

        } else if ("3".equals(firstLink.getDirection())) {
            // 逆向
            sb.append(firstLink.getId()).append(",");
            String nodeId = firstLink.getHllSNid();
            extractedFind(sb, lastLinkId, firstLink, nodeId);
        }
        return StrUtil.removeSuffix(sb.toString(), ",");
    }

    private void extractedFind(StringBuilder sb, String lastLinkId, Link firstLink, String nodeId) {
        List<Link> secondLinkList = linkService.lambdaQuery().ne(Link::getId, firstLink.getId()).and(s -> s.eq(Link::getHllSNid, nodeId).or()
                .eq(Link::getHllENid, nodeId)).list();
        if (secondLinkList.size() == 1) {
            Link linkRp2 = secondLinkList.get(0);
            sb.append(linkRp2.getId()).append(",");
            List<String> tmpNodes = CollUtil.newArrayList(linkRp2.getHllSNid(), linkRp2.getHllENid());
            tmpNodes.removeIf(s -> s.equals(nodeId));
            linkRp2.setMemo(tmpNodes.get(0));
            Link tmpLinkRp = linkRp2;
            while (!sb.toString().contains(lastLinkId)) {
                tmpLinkRp = orderFind(sb, tmpLinkRp);
                if (tmpLinkRp == null) {
                    String linkIds = sb.toString();
                    linkIds = StrUtil.removeSuffix(linkIds, ",");
                    String id = StrUtil.sub(linkIds, linkIds.lastIndexOf(",") + 1, linkIds.length());
                    log.warn("链路出现分叉，分叉路id：{}", id);
                    break;
                }
            }

        }
    }

    private void extractedFindThread(String country, String area, StringBuilder sb, String lastLinkId, Link firstLink, String nodeId) {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        List<Link> secondLinkList = linkService.lambdaQuery().ne(Link::getId, firstLink.getId()).and(s -> s.eq(Link::getHllSNid, nodeId).or()
                .eq(Link::getHllENid, nodeId)).list();
        if (secondLinkList.size() == 1) {
            Link linkRp2 = secondLinkList.get(0);
            sb.append(linkRp2.getId()).append(",");
            List<String> tmpNodes = CollUtil.newArrayList(linkRp2.getHllSNid(), linkRp2.getHllENid());
            tmpNodes.removeIf(s -> s.equals(nodeId));
            linkRp2.setMemo(tmpNodes.get(0));
            Link tmpLinkRp = linkRp2;
            while (!sb.toString().contains(lastLinkId)) {
                tmpLinkRp = orderFind(sb, tmpLinkRp);
                if (tmpLinkRp == null) {
                    String linkIds = sb.toString();
                    linkIds = StrUtil.removeSuffix(linkIds, ",");
                    String id = StrUtil.sub(linkIds, linkIds.lastIndexOf(",") + 1, linkIds.length());
                    log.warn("链路出现分叉，分叉路id：{}", id);
                    break;
                }
            }
            flag = false;
        }
    }

    public Link orderFind(StringBuilder sb, Link linkRp) {
        List<Link> linkRpList = linkService.lambdaQuery().ne(Link::getId, linkRp.getId()).and(s -> s.eq(Link::getHllSNid, linkRp.getMemo()).or()
                .eq(Link::getHllENid, linkRp.getMemo())).list();
        // linkRpList = linkRpList.stream().filter(s -> CollUtil.intersection(CollUtil.newArrayList("14", "31"), Arrays.asList(s.getFormway().split(","))).size() > 0).collect(Collectors.toList());
        if (linkRpList.size() == 1) {
            Link tmpLinkRp = linkRpList.get(0);
            List<String> tmpNodes = CollUtil.newArrayList(tmpLinkRp.getHllSNid(), tmpLinkRp.getHllENid());
            tmpNodes.removeIf(s -> s.equals(linkRp.getMemo()));
            tmpLinkRp.setMemo(tmpNodes.get(0));
            sb.append(tmpLinkRp.getId()).append(",");
            return tmpLinkRp;
        }
        return null;
    }

    public Link orderFind2(StringBuilder sb, Link linkRp, Link lastLinkRp) {
        List<Link> linkRpList = linkService.lambdaQuery().ne(Link::getId, linkRp.getId()).and(s -> s.eq(Link::getHllSNid, linkRp.getMemo()).or()
                .eq(Link::getHllENid, linkRp.getMemo())).list();
        if (linkRpList.size() == 1) {
            Link tmpLinkRp = linkRpList.get(0);
            List<String> tmpNodes = CollUtil.newArrayList(tmpLinkRp.getHllSNid(), tmpLinkRp.getHllENid());
            tmpNodes.removeIf(s -> s.equals(linkRp.getMemo()));
            tmpLinkRp.setMemo(tmpNodes.get(0));
            sb.append(tmpLinkRp.getId()).append(",");
            return tmpLinkRp;
        } else {
            Link linkRp1 = calculateMinAngleLinkRp(linkRpList, lastLinkRp);
            List<String> tmpNodes = CollUtil.newArrayList(linkRp1.getHllSNid(), linkRp1.getHllENid());
            tmpNodes.removeIf(s -> s.equals(linkRp.getMemo()));
            linkRp1.setMemo(tmpNodes.get(0));
            sb.append(linkRp1.getId()).append(",");
            return linkRp1;
        }
    }

    @GetMapping("/lineCalculate")
    public ResponseResult<String> lineCalculate(String firstLinkId, String nodeId, String lastLinkId, String country, String area) {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }

        Link firstLink = linkService.lambdaQuery().eq(Link::getId, firstLinkId).one();
        Link lastLink = linkService.lambdaQuery().eq(Link::getId, lastLinkId).one();

        // 依次查找
        List<String> firstLinkNodes = CollUtil.newArrayList(firstLink.getHllSNid(), firstLink.getHllENid());
        firstLinkNodes.removeIf(s -> s.equals(nodeId));
        String secondNodeId = firstLinkNodes.get(0);
        List<Link> secondLinkList = linkService.lambdaQuery().ne(Link::getId, firstLink.getId()).and(s -> s.eq(Link::getHllSNid, secondNodeId).or()
                .eq(Link::getHllENid, secondNodeId)).list();
        StringBuilder sb = new StringBuilder(firstLinkId).append(",");

        if (secondLinkList.size() == 1) {
            Link linkRp2 = secondLinkList.get(0);
            sb.append(linkRp2.getId()).append(",");
            List<String> tmpNodes = CollUtil.newArrayList(linkRp2.getHllSNid(), linkRp2.getHllENid());
            tmpNodes.removeIf(s -> s.equals(secondNodeId));
            linkRp2.setMemo(tmpNodes.get(0));
            Link tmpLinkRp = linkRp2;
            for (int i = 0; i < 100; i++) {
                tmpLinkRp = orderFind2(sb, tmpLinkRp, lastLink);
                if (sb.toString().contains(lastLinkId)) {
                    log.info("匹配完整路线：{}", sb);
                    break;
                }
            }
        } else {
            // 计算夹角，取与最后一条linkrp夹角最小的
            Link linkRp = calculateMinAngleLinkRp(secondLinkList, lastLink);
            sb.append(linkRp.getId()).append(",");
            List<String> tmpNodes = CollUtil.newArrayList(linkRp.getHllSNid(), linkRp.getHllENid());
            tmpNodes.removeIf(s -> s.equals(secondNodeId));
            linkRp.setMemo(tmpNodes.get(0));
            Link tmpLinkRp = linkRp;
            for (int i = 0; i < 100; i++) {
                tmpLinkRp = orderFind2(sb, tmpLinkRp, lastLink);
                if (sb.toString().contains(lastLinkId)) {
                    log.info("匹配完整路线：{}", sb);
                    break;
                }
            }
        }
        log.info("匹配路线：{}", sb);
        return ResponseResult.OK(sb.toString(), true);
    }

    private Link calculateMinAngleLinkRp(List<Link> linkRpList, Link lastLinkRp) {
        // 计算夹角，取与最后一条linkrp夹角最小的
        TreeMap<Double, Link> treeMap = new TreeMap<>();
        // String swapLine = StrUtil.subBetween(lastLinkRp.getGeomwkt(), "MULTILINESTRING((", "))");
        for (Link linkRp : linkRpList) {
            // String swapLine2 = StrUtil.subBetween(linkRp.getGeomwkt(), "MULTILINESTRING((", "))");
            // Double angle = calculationMapper.calAngleLine(StrUtil.wrap(swapLine, "LINESTRING(", ")"),
            //         StrUtil.wrap(swapLine2, "LINESTRING(", ")"));
            Double angle = calculationMapper.calAngleLine(lastLinkRp.getGeomwkt(), linkRp.getGeomwkt());
            angle = angle > 180 ? 360 - angle : angle;
            // angle = angle>90?180-angle:angle;
            if (angle < 0) {
                log.warn("计算夹角出现负数，夹角：{},linkId:{},{}", angle, linkRp.getId(), lastLinkRp.getId());
                angle = Math.abs(angle);
            }
            treeMap.put(angle, linkRp);
        }
        return treeMap.get(treeMap.firstKey());
    }


    @GetMapping("/updateLinkConstSt")
    public ResponseResult<Boolean> updateLinkConstSt(String country,String area) throws InterruptedException {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        TimeInterval timer = DateUtil.timer();
        // calculate loop times
        int linkCount = linkService.count();
        int step =32767/ BeanUtil.beanToMap(new Link()).keySet().size();
        int loop = linkCount % step != 0 ? (linkCount / step) + 1 : linkCount / step;
        CountDownLatch countDownLatch = new CountDownLatch(loop);
        for (int i = 0; i < loop; i++) {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            log.info("query link db is:" + DynamicDataSourceContextHolder.peek());
            List<Link> linkList = linkService.lambdaQuery()
                    .orderByDesc(Link::getHllLinkid).last("limit " + step + " offset " + i * step).list();
            log.info("convert link db is:" + DynamicDataSourceContextHolder.peek());
            linkService.updateLinkConstSt(area, country, countDownLatch, linkList);
        }
        countDownLatch.await();
        log.info("map link diff column to link_rp cost time is {}s", timer.intervalSecond());
        return ResponseResult.OK(true, true);
    }
    @GetMapping("/lineCalculateDfs")
    public ResponseResult<String> lineCalculateDfs(String country, String area, String srcFilePath, String tmpFilePath) {
        try {
            // 1. 原文件读取,当前路与下一条路
            List<LineExport> lineExportList = new ArrayList<>();
            EasyExcel.read(tmpFilePath, LineExport.class, new PageReadListener(data -> {
                lineExportList.addAll((Collection<? extends LineExport>) data);
            })).sheet().doRead();

            // 2.获取linkId集中的所有首尾link
            List<LineDO> lineDOList = new ArrayList<>();
            EasyExcel.read(srcFilePath, LineDO.class, new PageReadListener(data -> {
                lineDOList.addAll((Collection<? extends LineDO>) data);
            })).sheet().doRead();
            List<String> linkIdList = lineDOList.stream().map(s -> s.getLine()).collect(Collectors.toList());
            lineDOList.removeIf(s -> StrUtil.isEmpty(s.getLine()));
            log.info("读取文件完成，共{}条数据", lineDOList.size());

            // List<String> singleLinkIdList = new ArrayList<>();
            List<String> singleLinkIdList = Collections.synchronizedList(new ArrayList<>());
            List<List<String>> split = CollUtil.split(linkIdList, 1000);
            CountDownLatch countDownLatch = new CountDownLatch(split.size());
            for (List<String> strings : split) {
                new Thread(() -> {
                    try {
                        for (String id : strings) {
                            if (!country.isEmpty()) {
                                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                            }
                            if (!area.isEmpty()) {
                                MybatisPlusConfig.myTableName.set("_" + area);
                            } else {
                                MybatisPlusConfig.myTableName.set("");
                            }
                            Link link = linkService.lambdaQuery().select(Link::getHllSNid, Link::getHllENid).eq(Link::getId, id).one();
                            // 2.获取他周边的link
                            // log.info("搜索linkId:{}", id);
                            List<Link> surroundLinkList = linkService.lambdaQuery().select(Link::getId).eq(Link::getHllSNid, link.getHllSNid()).or().eq(Link::getHllENid, link.getHllSNid()).or()
                                    .eq(Link::getHllENid, link.getHllENid()).or().eq(Link::getHllSNid, link.getHllENid()).list();
                            surroundLinkList = surroundLinkList.stream().filter(s -> !s.getId().equals(id)).collect(Collectors.toList());
                            List<String> surroundLinkIds = surroundLinkList.stream().map(s -> s.getId()).collect(Collectors.toList());
                            if (CollUtil.intersection(linkIdList, surroundLinkIds).size() == 1) {
                                singleLinkIdList.add(id);
                            }
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    } finally {
                        countDownLatch.countDown();
                    }
                }).start();
            }
            countDownLatch.await();
            // 3.获取所有首尾link,参与dfs运算
            log.info("单向linkId数量：{}", singleLinkIdList.size());
            log.info("单向linkId：{}", singleLinkIdList);
            List<List<String>> tmpList = new ArrayList<>();
            for (LineExport lineExport : lineExportList) {
                if (lineDOList.contains(lineExport.getId())) {
                    List<String> list = new ArrayList<>();
                    list.add(lineExport.getId());
                    list.add(lineExport.getOutLinkId());
                    tmpList.add(list);
                }
            }
            DFSLinkConnection dfsLinkConnection = new DFSLinkConnection();
            // tmpList转二维数组
            String[][] tmpArray = new String[tmpList.size()][2];
            for (int i = 0; i < tmpList.size(); i++) {
                tmpArray[i] = tmpList.get(i).toArray(new String[2]);
            }
            List<List<String>> resultList = dfsLinkConnection.connectRoads(tmpArray);
            for (List<String> strings : resultList) {
                System.out.println(strings);
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return ResponseResult.OK("1", true);
    }

    @GetMapping("/lineCalculate2")
    @ApiOperation("根据linkId数据集,串联所有道路")
    public ResponseResult<String> lineCalculate2(String country, String area, String filePath) {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }

        // 1.读取文件
        // List<String> linkIdList = FileUtil.readUtf8Lines(filePath);

        try {
            List<LineDO> lineDOList = new ArrayList<>();
            EasyExcel.read(filePath, LineDO.class, new PageReadListener(data -> {
                lineDOList.addAll((Collection<? extends LineDO>) data);
            })).sheet().doRead();
            List<String> linkIdList = lineDOList.stream().map(s -> s.getLine()).collect(Collectors.toList());
            lineDOList.removeIf(s -> StrUtil.isEmpty(s.getLine()));
            log.info("读取文件完成，共{}条数据", lineDOList.size());

            // List<String> singleLinkIdList = new ArrayList<>();
            List<String> singleLinkIdList = Collections.synchronizedList(new ArrayList<>());
            List<List<String>> split = CollUtil.split(linkIdList, 1000);
            CountDownLatch countDownLatch = new CountDownLatch(split.size());
            for (List<String> strings : split) {
                new Thread(() -> {
                    try {
                        for (String id : strings) {
                            if (!country.isEmpty()) {
                                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                            }
                            if (!area.isEmpty()) {
                                MybatisPlusConfig.myTableName.set("_" + area);
                            } else {
                                MybatisPlusConfig.myTableName.set("");
                            }
                            Link link = linkService.lambdaQuery().select(Link::getHllSNid, Link::getHllENid).eq(Link::getId, id).one();
                            // 2.获取他周边的link
                            // log.info("搜索linkId:{}", id);
                            List<Link> surroundLinkList = linkService.lambdaQuery().select(Link::getId).eq(Link::getHllSNid, link.getHllSNid()).or().eq(Link::getHllENid, link.getHllSNid()).or()
                                    .eq(Link::getHllENid, link.getHllENid()).or().eq(Link::getHllSNid, link.getHllENid()).list();
                            surroundLinkList = surroundLinkList.stream().filter(s -> !s.getId().equals(id)).collect(Collectors.toList());
                            List<String> surroundLinkIds = surroundLinkList.stream().map(s -> s.getId()).collect(Collectors.toList());
                            if (CollUtil.intersection(linkIdList, surroundLinkIds).size() == 1) {
                                singleLinkIdList.add(id);
                            }
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    } finally {
                        countDownLatch.countDown();
                    }
                }).start();
            }
            countDownLatch.await();
            log.info("单独linkId数量：{}", singleLinkIdList.size());
            log.info("单独linkId：{}", singleLinkIdList);
            // PathCalculator pathCalculator = new PathCalculator("809071945","1311059208",linkIdList);
            // pathCalculator.ruleComplete();

            List<List<String>> resList = new ArrayList<>();
            // 3.根据linkId查找link
            for (String sOreId : singleLinkIdList) {
                List<String> appendList = new ArrayList<>();
                appendList.add(sOreId);

                // 筛选只从首段link向下查找
                String nodeId = "";
                Link startOrEndLink = linkService.lambdaQuery().select(Link::getId, Link::getDirection, Link::getHllSNid, Link::getHllENid).eq(Link::getId, sOreId).one();
                if ("2".equals(startOrEndLink.getDirection())) {
                    List<Link> nextLinkList = linkService.lambdaQuery().select(Link::getId).eq(Link::getHllSNid, startOrEndLink.getHllENid()).or().eq(Link::getHllENid, startOrEndLink.getHllENid()).list();
                    List<String> nextLinkIds = nextLinkList.stream().map(s -> s.getId()).filter(s -> !sOreId.equals(s)).collect(Collectors.toList());
                    if (CollUtil.intersection(nextLinkIds, linkIdList).size() == 0) {
                        continue;
                    } else if (CollUtil.intersection(nextLinkIds, linkIdList).size() == 1) {
                        List<String> intersection = (List<String>) CollUtil.intersection(nextLinkIds, linkIdList);
                        Link nextLink = linkService.lambdaQuery().select(Link::getHllSNid, Link::getHllENid, Link::getDirection).eq(Link::getId, intersection.get(0)).one();
                        if ("2".equals(nextLink.getDirection())) {
                            nodeId = nextLink.getHllENid();
                        }
                        if ("3".equals(nextLink.getDirection())) {
                            nodeId = nextLink.getHllSNid();
                        }
                        //递归查找
                        Map<String, Object> map = new HashMap<>();
                        map.put("linkIds", appendList);
                        map.put("flag", false);
                        map.put("searchNodeId", nodeId);

                        while (true) {
                            Map<String, Object> resMap = recursiveFindLink(map, linkIdList, resList);
                            Boolean endFlag = (Boolean) resMap.get("flag");
                            if (endFlag) {
                                break;
                            }
                        }
                    } else {
                        // 存在多岔路
                        List<String> intersection = (List<String>) CollUtil.intersection(nextLinkIds, linkIdList);
                        intersection = intersection.stream().filter(s -> linkIdList.contains(s)).filter(s -> !sOreId.equals(s)).collect(Collectors.toList());
                        // 获取上条路的enodeId
                        resList.add(appendList);
                        Link lastLink = linkService.lambdaQuery().select(Link::getId, Link::getHllSNid, Link::getHllENid, Link::getDirection).eq(Link::getId, appendList.get(0)).one();
                        for (String forkLinkId : intersection) {
                            log.info("forkLinkId:{}", forkLinkId);
                            Link forkLink = linkService.lambdaQuery().select(Link::getId, Link::getHllSNid, Link::getHllENid, Link::getDirection).eq(Link::getId, forkLinkId).one();
                            if (judgeIsSameDirection(lastLink, forkLink)) {
                                Map<String, Object> map1 = new HashMap<>();
                                map1.put("linkIds", CollUtil.newArrayList(forkLink.getId()));
                                String searchNodeId = "";
                                if ("2".equals(forkLink.getDirection())) {
                                    searchNodeId = forkLink.getHllENid();
                                }
                                if ("3".equals(forkLink.getDirection())) {
                                    searchNodeId = forkLink.getHllSNid();
                                }
                                map1.put("searchNodeId", searchNodeId);
                                while (true) {
                                    Map<String, Object> resMap = recursiveFindLink(map1, linkIdList, resList);
                                    Boolean breakFlag = (Boolean) resMap.get("flag");
                                    if (breakFlag) {
                                        break;
                                    }
                                }
                            }
                        }
                    }

                }
                if ("3".equals(startOrEndLink.getDirection())) {
                    List<Link> nextLinkList = linkService.lambdaQuery().select(Link::getId).eq(Link::getHllSNid, startOrEndLink.getHllSNid()).or().eq(Link::getHllENid, startOrEndLink.getHllSNid()).list();
                    List<String> nextLinkIds = nextLinkList.stream().map(s -> s.getId()).filter(s -> !sOreId.equals(s)).collect(Collectors.toList());
                    if (CollUtil.intersection(nextLinkIds, linkIdList).size() == 0) {
                        continue;
                    } else if (CollUtil.intersection(nextLinkIds, linkIdList).size() == 1) {
                        List<String> intersection = (List<String>) CollUtil.intersection(nextLinkIds, linkIdList);
                        Link nextLink = linkService.lambdaQuery().select(Link::getDirection, Link::getHllSNid, Link::getHllENid).eq(Link::getId, intersection.get(0)).one();
                        if ("2".equals(nextLink.getDirection())) {
                            nodeId = nextLink.getHllENid();
                        }
                        if ("3".equals(nextLink.getDirection())) {
                            nodeId = nextLink.getHllSNid();
                        }
                        //递归查找
                        Map<String, Object> map = new HashMap<>();
                        map.put("linkIds", appendList);
                        map.put("flag", false);
                        map.put("searchNodeId", nodeId);

                        while (true) {
                            Map<String, Object> resMap = recursiveFindLink(map, linkIdList, resList);
                            Boolean endFlag = (Boolean) resMap.get("flag");
                            if (endFlag) {
                                break;
                            }
                        }
                    } else {
                        // 存在多岔路
                        List<String> intersection = (List<String>) CollUtil.intersection(nextLinkIds, linkIdList);
                        intersection = intersection.stream().filter(s -> linkIdList.contains(s)).filter(s -> !sOreId.equals(s)).collect(Collectors.toList());
                        // 获取上条路的enodeId
                        resList.add(appendList);
                        Link lastLink = linkService.lambdaQuery().select(Link::getId, Link::getHllSNid, Link::getHllENid, Link::getDirection).eq(Link::getId, appendList.get(0)).one();
                        for (String forkLinkId : intersection) {
                            log.info("forkLinkId:{}", forkLinkId);
                            Link forkLink = linkService.lambdaQuery().select(Link::getId, Link::getHllSNid, Link::getHllENid, Link::getDirection).eq(Link::getId, forkLinkId).one();
                            if (judgeIsSameDirection(lastLink, forkLink)) {
                                Map<String, Object> map1 = new HashMap<>();
                                map1.put("linkIds", CollUtil.newArrayList(forkLink.getId()));
                                String searchNodeId = "";
                                if ("2".equals(forkLink.getDirection())) {
                                    searchNodeId = forkLink.getHllENid();
                                }
                                if ("3".equals(forkLink.getDirection())) {
                                    searchNodeId = forkLink.getHllSNid();
                                }
                                map1.put("searchNodeId", searchNodeId);
                                while (true) {
                                    Map<String, Object> resMap = recursiveFindLink(map1, linkIdList, resList);
                                    Boolean breakFlag = (Boolean) resMap.get("flag");
                                    if (breakFlag) {
                                        break;
                                    }
                                }
                            }
                        }
                    }

                }
            }
            String resFilePath = StrUtil.sub(filePath, 0, filePath.lastIndexOf("/"));
            String fileName = resFilePath.endsWith("/") ? resFilePath + "_result_" + System.currentTimeMillis() + ".xlsx" :
                    resFilePath + "/" + "_result_" + System.currentTimeMillis() + ".xlsx";
            for (List<String> strings : resList) {
                // System.out.println("complete link id:" + strings);
                writeToFile(strings, fileName);
            }

        } catch (Exception e) {
            log.error("出现异常:", e);
            throw new RuntimeException(e);
        }
        return ResponseResult.OK("1", true);
    }

    public static void writeToFile(List<String> strings, String filename) {
        BufferedWriter bw = null;
        try {
            bw = new BufferedWriter(new FileWriter(filename));
            for (String s : strings) {
                bw.write(s);
                bw.newLine();  // 为每个字符串后添加新行
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (bw != null) {
                try {
                    bw.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private Map<String, Object> recursiveFindLink(Map<String, Object> map, List<String> linkIdList, List<List<String>> resList) {
        List<String> linkIds = (List) map.get("linkIds");
        String nodeId = (String) map.get("searchNodeId");
        // 选取末路节点周边的link
        List<Link> surroundLinkList2 = linkService.lambdaQuery().select(Link::getId).eq(Link::getHllSNid, nodeId).or().eq(Link::getHllENid, nodeId).list();
        List<String> surroundLinkIds2 = surroundLinkList2.stream().map(s -> s.getId()).collect(Collectors.toList());
        // 过滤掉本身的末路
        List<String> tmpLinkIds = CollUtil.subtractToList(surroundLinkIds2, linkIds);
        if (CollUtil.intersection(linkIdList, tmpLinkIds).size() == 1) {
            String nextLinkId = (String) ((List) CollUtil.intersection(linkIdList, tmpLinkIds)).get(0);
            linkIds.add(nextLinkId);
            Link nextLink = linkService.lambdaQuery().select(Link::getHllSNid, Link::getHllENid).eq(Link::getId, nextLinkId).one();
            List<String> nextNodeIds = CollUtil.newArrayList(nextLink.getHllSNid(), nextLink.getHllENid());
            map.put("linkIds", linkIds);
            map.put("searchNodeId", CollUtil.subtractToList(nextNodeIds, CollUtil.newArrayList(nodeId)).get(0));
            map.put("flag", false);
            return map;
        } else if (CollUtil.intersection(linkIdList, tmpLinkIds).size() == 0) {
            resList.add(linkIds);
            map.put("flag", true);
            return map;
        } else {
            // 存在多岔路
            List<String> intersection = (List<String>) CollUtil.intersection(linkIdList, tmpLinkIds);
            intersection = intersection.stream().filter(s -> linkIdList.contains(s)).collect(Collectors.toList());
            // 获取上条路的enodeId
            log.info("linkIds:{}", linkIds);
            resList.add(linkIds);
            Link lastLink = linkService.lambdaQuery().select(Link::getId, Link::getHllSNid, Link::getHllENid, Link::getDirection).eq(Link::getId, linkIds.get(linkIds.size() - 1)).one();
            for (String forkLinkId : intersection) {
                log.info("forkLinkId:{}", forkLinkId);
                Link forkLink = linkService.lambdaQuery().select(Link::getId, Link::getHllSNid, Link::getHllENid, Link::getDirection).eq(Link::getId, forkLinkId).one();
                if (judgeIsSameDirection(lastLink, forkLink)) {
                    Map<String, Object> map1 = new HashMap<>();
                    map1.put("linkIds", CollUtil.newArrayList(forkLink.getId()));
                    String searchNodeId = "";
                    if ("2".equals(forkLink.getDirection())) {
                        searchNodeId = forkLink.getHllENid();
                    }
                    if ("3".equals(forkLink.getDirection())) {
                        searchNodeId = forkLink.getHllSNid();
                    }
                    map1.put("searchNodeId", searchNodeId);
                    while (true) {
                        Map<String, Object> resMap = recursiveFindLink(map1, linkIdList, resList);
                        Boolean breakFlag = (Boolean) resMap.get("flag");
                        if (breakFlag) {
                            break;
                        }
                    }
                }
            }
        }
        map.put("flag", true);
        return map;
    }


    private boolean judgeIsSameDirection(Link lastLink, Link forkLink) {
        // 判断是否是顺方向
        if ("2".equals(lastLink.getDirection()) && "2".equals(forkLink.getDirection())) {
            if (lastLink.getHllENid().equals(forkLink.getHllSNid())) {
                return true;
            }
            if (lastLink.getHllENid().equals(forkLink.getHllENid())) {
                return false;
            }
        }
        if ("3".equals(lastLink.getDirection()) && "3".equals(forkLink.getDirection())) {
            if (lastLink.getHllSNid().equals(forkLink.getHllENid())) {
                return true;
            }
            if (lastLink.getHllSNid().equals(forkLink.getHllSNid())) {
                return false;
            }
        }
        if ("2".equals(lastLink.getDirection()) && "3".equals(forkLink.getDirection())) {
            if (lastLink.getHllENid().equals(forkLink.getHllENid())) {
                return true;
            }
            if (lastLink.getHllENid().equals(forkLink.getHllSNid())) {
                return false;
            }
        }
        if ("3".equals(lastLink.getDirection()) && "2".equals(forkLink.getDirection())) {
            if (lastLink.getHllSNid().equals(forkLink.getHllSNid())) {
                return true;
            }
            if (lastLink.getHllSNid().equals(forkLink.getHllENid())) {
                return false;
            }
        }
        return false;
    }


    @GetMapping("/lineCalculateStartAndEnd")
    @ApiOperation("根据linkId数据集,串联所有道路")
    public ResponseResult<String> lineCalculateStartAndEnd(String country, String area, String filePath) {
        try {
            TimeInterval timer = DateUtil.timer();
            List<LineDO> lineDOList = new ArrayList<>();
            EasyExcel.read(filePath, LineDO.class, new PageReadListener(data -> {
                lineDOList.addAll((Collection<? extends LineDO>) data);
            })).sheet().doRead();
            List<String> linkIdList = lineDOList.stream().map(s -> s.getLine()).collect(Collectors.toList());
            lineDOList.removeIf(s -> StrUtil.isEmpty(s.getLine()));
            log.info("读取文件完成，共{}条数据", lineDOList.size());

            List<LineExport> lineExportList = Collections.synchronizedList(new ArrayList<>());
            List<List<String>> split = CollUtil.split(linkIdList, 1000);
            CountDownLatch countDownLatch = new CountDownLatch(split.size());
            for (List<String> strings : split) {
                new Thread(() -> {
                    try {
                        for (String id : strings) {
                            if (!country.isEmpty()) {
                                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                            }
                            if (!area.isEmpty()) {
                                MybatisPlusConfig.myTableName.set("_" + area);
                            } else {
                                MybatisPlusConfig.myTableName.set("");
                            }
                            Link link = linkService.lambdaQuery().select(Link::getGeomwkt, Link::getDirection, Link::getHllSNid, Link::getHllENid).eq(Link::getId, id).one();
                            log.info("handle id is {}", id);
                            if ("2".equals(link.getDirection())) {
                                // 查找inlink
                                List<Link> preLinkList = linkService.lambdaQuery().select(Link::getId).eq(Link::getHllENid, link.getHllSNid()).eq(Link::getDirection, "2").or()
                                        .eq(Link::getHllSNid, link.getHllSNid()).eq(Link::getDirection, "3").list();
                                // 查找outlink
                                List<Link> postLinkList = linkService.lambdaQuery().select(Link::getId).eq(Link::getHllSNid, link.getHllENid()).eq(Link::getDirection, "2").or()
                                        .eq(Link::getHllENid, link.getHllENid()).eq(Link::getDirection, "3").list();

                                for (Link preLink : preLinkList) {
                                    for (Link postLink : postLinkList) {
                                        LineExport lineExport = new LineExport();
                                        lineExport.setInLinkId(preLink.getId());
                                        lineExport.setId(id);
                                        lineExport.setOutLinkId(postLink.getId());
                                        lineExport.setDirection(link.getDirection());
                                        if ("2".equals(link.getDirection())) {
                                            lineExport.setLineWkt(link.getGeomwkt());
                                        }
                                        if ("3".equals(link.getDirection())) {
                                            String tmpStr = StrUtil.unWrap(link.getGeomwkt(), "LINESTRING(", ")");
                                            String[] split1 = tmpStr.split(",");
                                            List<String> reverse = CollUtil.reverse(CollUtil.newArrayList(split1));
                                            lineExport.setLineWkt(StrUtil.wrap(CollUtil.join(reverse, ","), "LINESTRING(", ")"));
                                        }
                                        lineExportList.add(lineExport);
                                    }
                                }
                            }
                            if ("3".equals(link.getDirection())) {
                                // 查找inlink
                                List<Link> preLinkList = linkService.lambdaQuery().select(Link::getId).eq(Link::getHllENid, link.getHllENid()).eq(Link::getDirection, "2").or()
                                        .eq(Link::getHllSNid, link.getHllENid()).eq(Link::getDirection, "3").list();
                                // 查找outlink
                                List<Link> postLinkList = linkService.lambdaQuery().select(Link::getId).eq(Link::getHllSNid, link.getHllSNid()).eq(Link::getDirection, "2").or()
                                        .eq(Link::getHllENid, link.getHllSNid()).eq(Link::getDirection, "3").list();
                                for (Link preLink : preLinkList) {
                                    for (Link postLink : postLinkList) {
                                        LineExport lineExport = new LineExport();
                                        lineExport.setInLinkId(preLink.getId());
                                        lineExport.setId(id);
                                        lineExport.setOutLinkId(postLink.getId());
                                        lineExport.setDirection(link.getDirection());
                                        if ("2".equals(link.getDirection())) {
                                            lineExport.setLineWkt(link.getGeomwkt());
                                        }
                                        if ("3".equals(link.getDirection())) {
                                            String tmpStr = StrUtil.unWrap(link.getGeomwkt(), "LINESTRING(", ")");
                                            String[] split1 = tmpStr.split(",");
                                            List<String> reverse = CollUtil.reverse(CollUtil.newArrayList(split1));
                                            lineExport.setLineWkt(StrUtil.wrap(CollUtil.join(reverse, ","), "LINESTRING(", ")"));
                                        }
                                        lineExportList.add(lineExport);
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    } finally {
                        countDownLatch.countDown();
                    }
                }).start();
            }
            countDownLatch.await();

            String resFilePath = StrUtil.sub(filePath, 0, filePath.lastIndexOf("/"));
            resFilePath = resFilePath.endsWith("/") ? resFilePath + "_result_" + System.currentTimeMillis() + ".xlsx" :
                    resFilePath + "/" + "_result_" + System.currentTimeMillis() + ".xlsx";
            EasyExcel.write(resFilePath, LineExport.class).sheet().doWrite(lineExportList);

            log.info("算路完成,耗时{}s,导出路径为：{}", timer.intervalSecond(), resFilePath);
            return ResponseResult.OK("算路完成,导出路径为：" + resFilePath, true);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

    }
    @GetMapping("updatePubAccess")
    public ResponseResult<String> updatePubAccess(@RequestParam(value = "area", required = false, defaultValue = "") String area,
                                                  @RequestParam(value = "country", required = false, defaultValue = "") String country) {

        Integer updateNum = 0;
        try {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            // 1. 获取streets linkid,pubAccess的数据
            List<Streets> streetsList = streetsService.lambdaQuery().select(Streets::getLinkId, Streets::getPubAccess,Streets::getArTraff).list();
            log.info("需要升级的pubAccess size is {}", streetsList.size());
            // 2.分批处理linkIds
            List<List<Streets>> batchStreets = CollUtil.splitList(streetsList, 32767 / BeanUtil.beanToMap(new Link()).keySet().size());
            for (List<Streets> batchStreet : batchStreets) {
                log.info("process sublist size is {}", batchStreet.size());
//                Map<Long,String> linkIdMap = batchStreet.stream().collect(Collectors.toMap(Streets::getLinkId,Streets::getPubAccess));
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
                List<Long> linkIds = batchStreet.stream().map(Streets::getLinkId).collect(Collectors.toList());
                List<Link> linkList = linkService.lambdaQuery().in(Link::getId, linkIds.stream().map(String::valueOf).collect(Collectors.toList())).list();
                for (Streets streets : batchStreet) {
                    for (Link link : linkList) {
                        if (streets.getLinkId().equals(Long.parseLong(link.getId()))) {
                            link.setPubAccess(streets.getPubAccess());
                            if("N".equals(streets.getPubAccess())){
                                if("1".equals(link.getFormway())){
                                    link.setFormway("52");
                                } else {
                                    link.setFormway(link.getFormway()+",52");
                                }
                            }
                            if("N".equals(streets.getArTraff())){
                                if("1".equals(link.getFormway())){
                                    link.setFormway("55");
                                } else {
                                    link.setFormway(link.getFormway()+",55");
                                }
                            }
                        }
                    }
                }
                // 3.更新linkM表中的pubAccess
                updateNum += linkMapper.mysqlInsertOrUpdateBath(linkList);
                // log.info("update link pubAccess success,update num is {}", updateNum);
            }
            log.info("update link pubAccess success,update num is {}", updateNum);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
        return ResponseResult.OK("更新Link->pubAccess成功，更新数量:" + updateNum, true);
    }
}