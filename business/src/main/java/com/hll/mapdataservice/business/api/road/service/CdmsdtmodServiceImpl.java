package com.hll.mapdataservice.business.api.road.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hll.mapdataservice.common.entity.Cdmsdtmod;
import com.hll.mapdataservice.common.mapper.CdmsdtmodMapper;
import com.hll.mapdataservice.common.service.ICdmsdtmodService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-01
 */
@Service
//@DS("db4")
public class CdmsdtmodServiceImpl extends ServiceImpl<CdmsdtmodMapper, Cdmsdtmod> implements ICdmsdtmodService {

}
