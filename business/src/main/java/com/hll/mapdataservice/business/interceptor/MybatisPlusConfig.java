package com.hll.mapdataservice.business.interceptor;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TableNameHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.DynamicTableNameInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;

@Configuration
@Slf4j
public class MybatisPlusConfig {

    //public static String myTableName ="";
    public static ThreadLocal<String> myTableName = new ThreadLocal<>();
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.POSTGRE_SQL));
        DynamicTableNameInnerInterceptor dynamicTableNameInnerInterceptor = new DynamicTableNameInnerInterceptor();
//        HashMap<String, TableNameHandler> map = new HashMap<String, TableNameHandler>() {{
//            put("zlevels",(sql, tableName) -> "zlevels"+proFileEntity.getName());
//            put("streets",(sql, tableName) -> "streets"+proFileEntity.getName());
//            put("cdms", (sql, tableName) -> "cdms"+proFileEntity.getName());
//            put("altstreets", (sql, tableName) -> "altstreets"+proFileEntity.getName());
//        }};
        //System.err.println("xxx:" + Thread.currentThread().getName());
        //String area = myTableName.get();
        HashMap<String, TableNameHandler> map = new HashMap<String, TableNameHandler>() {{
            put("streets", (sql, tableName) -> "streets"+myTableName.get());
            put("mtdarea", (sql, tableName) -> "mtdarea"+myTableName.get());
            put("mtddst", (sql, tableName) -> "mtddst"+myTableName.get());
            put("zlevels", (sql, tableName) -> "zlevels"+myTableName.get());
            put("cdms", (sql, tableName) -> "cdms"+myTableName.get());
            put("altstreets", (sql, tableName) -> "altstreets"+myTableName.get());
            put("cdmsdtmod", (sql, tableName) -> "cdmsdtmod"+myTableName.get());
            put("cndmod", (sql, tableName) -> "cndmod"+myTableName.get());
            put("rdms", (sql, tableName) -> "rdms"+myTableName.get());
            put("streettrans", (sql, tableName) -> "streettrans"+myTableName.get());
            put("link_m", (sql, tableName) -> "link_m"+myTableName.get());
            put("link", (sql, tableName) -> "link"+myTableName.get());
            put("node_m", (sql, tableName) -> "node_m"+myTableName.get());
            put("node", (sql, tableName) -> "node"+myTableName.get());
            put("relation_m", (sql, tableName) -> "relation_m"+myTableName.get());
            put("rule_m", (sql, tableName) -> "rule_m"+myTableName.get());
            put("poi", (sql, tableName) -> "poi"+myTableName.get());
            put("poi_m", (sql, tableName) -> "poi_m"+myTableName.get());
            put("hll_order", (sql, tableName) -> "hll_order"+myTableName.get());
            put("place", (sql, tableName) -> "place"+myTableName.get());
            put("pointaddress", (sql, tableName) -> "pointaddress"+myTableName.get());
            put("pntaddrtrans", (sql, tableName) -> "pntaddrtrans"+myTableName.get());
            put("hn_point_address", (sql, tableName) -> "hn_point_address"+myTableName.get());
            put("order_poi", (sql, tableName) -> "order_poi"+myTableName.get());
            put("metadst", (sql, tableName) -> "metadst"+myTableName.get());
            put("poi_src", (sql, tableName) -> "poi_src"+myTableName.get());
            put("zlevel_m", (sql, tableName) -> "zlevel_m"+myTableName.get());
            put("zlevel", (sql, tableName) -> "zlevel"+myTableName.get());
//            put("streets", (sql, tableName) -> "streets"+area);
//            put("zlevels", (sql, tableName) -> "zlevels"+area);
//            put("cdms", (sql, tableName) -> "cdms"+area);
//            put("altstreets", (sql, tableName) -> "altstreets"+area);

//            put("streets", (sql, tableName) -> "streets"+myTableName);
//            put("zlevels", (sql, tableName) -> "zlevels"+myTableName);
//            put("cdms", (sql, tableName) -> "cdms"+myTableName);
//            put("altstreets", (sql, tableName) -> "altstreets"+myTableName);
//            put("cdmsdtmod", (sql, tableName) -> "cdmsdtmod"+myTableName);
//            put("cndmod", (sql, tableName) -> "cndmod"+myTableName);
//            put("rdms", (sql, tableName) -> "rdms"+myTableName);
//            put("streettrans", (sql, tableName) -> "streettrans"+myTableName);
//            put("link", (sql, tableName) -> "link"+myTableName);
//            put("node", (sql, tableName) -> "node"+myTableName);
//            put("relation", (sql, tableName) -> "relation"+myTableName);
//            put("rule", (sql, tableName) -> "rule"+myTableName);
        }};
        dynamicTableNameInnerInterceptor.setTableNameHandlerMap(map);
//        interceptor.addInnerInterceptor(new CustomTableNameInterceptor());
        interceptor.addInnerInterceptor(dynamicTableNameInnerInterceptor);
        interceptor.addInnerInterceptor(new CustomInnerInterceptor());
        return interceptor;
    }
}
