package com.hll.mapdataservice.business.api.road.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.third.InheritIDService;
import com.hll.mapdataservice.business.third.dto.InheritIDDTO;
import com.hll.mapdataservice.common.entity.NodeM;
import com.hll.mapdataservice.common.mapper.NodeMMapper;
import com.hll.mapdataservice.common.service.INodeMService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-28
 */
@Slf4j
@Service
public class NodeMServiceImpl extends ServiceImpl<NodeMMapper, NodeM> implements INodeMService {

    @Resource
    private InheritIDService inheritIDService;

    @Resource
    private NodeMMapper nodeMMapper;

    @Override
    @Async("asyncTaskExecutor")
    public void handleId(String area, String country, CountDownLatch countDownLatch, List<NodeM> nodeList) {
        try {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            int fieldNum = BeanUtil.beanToMap(new NodeM()).keySet().size();
            int batchSize = 32767 / fieldNum;
            log.info("batchInsert size is {}", batchSize);
            List<List<NodeM>> splitList = ListUtil.split(nodeList, batchSize);
            for (List<NodeM> nodes : splitList) {
                List<NodeM> resNodeList = new ArrayList<>();
                for (NodeM node : nodes) {
                    if (node.getMainnodeid().length()==18) {
                        continue;
                    }
                    // 处理 mainnodeid
                    if (StrUtil.isNotEmpty(node.getMainnodeid())&&node.getMainnodeid().length() < 18) {
                        node.setMainnodeid(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(node.getMainnodeid()))).get(0)));
                    } else {
                        node.setMainnodeid(node.getMainnodeid());
                    }
                    // 处理 subnodeid
                    if (StrUtil.isNotEmpty(node.getSubnodeid())) {
                        if (node.getSubnodeid().contains("|")) {
                            List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(node.getSubnodeid().split("\\|"))));
                            String resSubNodeid = CollUtil.join(inheritID, "|");
                            node.setSubnodeid(resSubNodeid);
                        } else if (node.getSubnodeid().length() < 18) {
                            node.setSubnodeid(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(node.getSubnodeid()))).get(0)));
                        }
                    } else {
                        node.setSubnodeid(node.getSubnodeid());
                    }

                    // 处理 subnodeid2
                    if (StrUtil.isNotEmpty(node.getSubnodeid2())) {
                        if (node.getSubnodeid2().contains("|")) {
                            List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(node.getSubnodeid2().split("\\|"))));
                            String resSubNodeid = CollUtil.join(inheritID, "|");
                            node.setSubnodeid2(resSubNodeid);
                        } else if (node.getSubnodeid2().length() < 18) {
                            node.setSubnodeid2(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(node.getSubnodeid2()))).get(0)));
                        }
                    } else {
                        node.setSubnodeid2(node.getSubnodeid2());
                    }
                    resNodeList.add(node);
                }
                // log.info("insert datasource is {}", DynamicDataSourceContextHolder.peek());
                // nodeMMapper.mysqlInsertOrUpdateBath(resNodeList);
                this.saveOrUpdateBatch(resNodeList);
            }
        } catch (Exception e) {
            log.error("handle node inherited id error,detail is {}", e.getMessage());
            throw e;
        } finally {
            countDownLatch.countDown();
        }
    }
}
