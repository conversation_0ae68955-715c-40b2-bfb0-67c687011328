package com.hll.mapdataservice.business.third;

import com.hll.mapdataservice.business.config.FeignConfig;
import com.hll.mapdataservice.business.third.dto.RoadMatchDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "roadmatchservice", configuration = FeignConfig.class, url = "${third.roadmatchservice.url}")
public interface RoadMatchService {
    /**
     * http://192.168.106.199:8080/track/flow
     */
    @PostMapping(value = "/track/flow",consumes = MediaType.APPLICATION_JSON_VALUE)
    String getMatchResult(@RequestBody String trackInfo);
}
