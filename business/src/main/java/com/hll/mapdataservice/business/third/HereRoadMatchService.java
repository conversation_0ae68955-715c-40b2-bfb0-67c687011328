package com.hll.mapdataservice.business.third;

import com.hll.mapdataservice.business.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "hereroadmatchservice", configuration = FeignConfig.class, url = "${third.hereroadmatchservice.url}")
public interface HereRoadMatchService {
    /**
     * http://192.168.106.199:9002/track/flow
     */
    @PostMapping(value = "/track/flow",consumes = MediaType.APPLICATION_JSON_VALUE)
    String getMatchResult(@RequestBody String trackInfo);
}
