// package com.hll.mapdataservice.business.config;
//
// import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
// import org.springframework.beans.factory.annotation.Value;
// import org.springframework.context.annotation.Bean;
// import org.springframework.context.annotation.Configuration;
//
// @Configuration
// public class XxlJobConfig {
//
//     private Logger logger = LoggerFactory.getLogger(XxlJobConfig.class);
//
//     @Value("${xxl.job.admin.addresses}")
//     private String adminAddresses;
//
//     @Value("${xxl.job.accessToken}")
//     private String accessToken;
//
//     @Value("${xxl.job.executor.appname}")
//     private String appname;
//
//     @Value("${xxl.job.executor.address}")
//     private String address;
//
//     @Value("${xxl.job.executor.ip}")
//     private String ip;
//
//     @Value("${xxl.job.executor.netty-port}")
//     private int nettyPort;
//
//     @Value("${xxl.job.executor.logpath}")
//     private String logPath;
//
//     @Value("${xxl.job.executor.logretentiondays}")
//     private int logRetentionDays;
//
//
//     @Bean
//     public XxlJobSpringExecutor xxlJobExecutor() {
//         logger.info("start xxlJobExecutor");
//         XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
//         xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
//         xxlJobSpringExecutor.setAppname(appname);
//         xxlJobSpringExecutor.setAddress(address);
//         xxlJobSpringExecutor.setIp(ip);
//         xxlJobSpringExecutor.setPort(nettyPort);
//         xxlJobSpringExecutor.setAccessToken(accessToken);
//         xxlJobSpringExecutor.setLogPath(logPath);
//         xxlJobSpringExecutor.setLogRetentionDays(logRetentionDays);
//
//         return xxlJobSpringExecutor;
//     }
// }
