package com.hll.mapdataservice.business.api.road.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.hll.mapdataservice.business.api.poi.service.MtdareaServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.third.InheritIDService;
import com.hll.mapdataservice.business.third.dto.InheritIDDTO;
import com.hll.mapdataservice.common.LangEnum;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.mapper.HnPointAddressMapper;
import com.hll.mapdataservice.common.service.IHnPointAddressService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * @Classname HnPointAddressServiceImpl
 * @Description here门址编译服务实现类
 * @Date 2021/12/9 4:18 下午
 * @Created by qunfu
 */
@Service
@Slf4j
public class HnPointAddressServiceImpl extends ServiceImpl<HnPointAddressMapper, HnPointAddress> implements IHnPointAddressService {

    @Resource
    HnPointAddressMapper hnPointAddressMapper;
    @Resource
    StreetsServiceImpl streetsService;
    @Resource
    HerePhaAltstreetsServiceImpl herePhaAltstreetsService;
    @Resource
    StreettransServiceImpl streettransService;
    @Resource
    PntAddrTransServiceImpl pntAddrTransService;
    @Resource
    MtdareaServiceImpl mtdareaService;
    @Resource
    InheritIDService inheritIDService;


    @Async("asyncTaskExecutor")
    public void pointAddressConvert(List<PointAddress> pointAddressList, Boolean isCompileTrans, String area, String country, String version, CountDownLatch countDownLatch) {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            log.info("processing country:" + CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        log.info("tmp thread is:" + Thread.currentThread().getName());

        List<HnPointAddress> hnPointAddressList = new ArrayList<>();

        try {
            if (pointAddressList.size() > 0) {
                for (PointAddress pointAddress : pointAddressList) {

                    HnPointAddress hnPointAddress = new HnPointAddress();

                    // hn_id
                    // hnPointAddress.setHnId(UUID.randomUUID().toString());
                    // source_id
                    hnPointAddress.setSourceId(pointAddress.getPtAddrId().toString());
                    // link_id
                    // 使用id继承，2022-02-10
                    // List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(pointAddress.getLinkId().toString())));
                   hnPointAddress.setLinkId(pointAddress.getLinkId().toString());
                    // hnPointAddress.setLinkId(String.valueOf(inheritID.get(0)));
                    // side
                    hnPointAddress.setSide(pointAddress.getSide());

                    List<Streets> streetsList = streetsService.lambdaQuery().eq(Streets::getLinkId, pointAddress.getLinkId()).list();
                    for (Streets streets : streetsList) {
                        // street_name
                        String streetName = "";
                        List<HerePhaAltstreets> herePhaAltstreetsList = herePhaAltstreetsService.lambdaQuery().eq(HerePhaAltstreets::getLinkId, streets.getLinkId()).list();
                        if (streets.getNumStnmes() == 1) {
                            hnPointAddress.setStreetName(streets.getStName());
                        } else if (streets.getNumStnmes() > 1) {
                            if ("N".equals(streets.getStalename())) {
                                streetName += streets.getStName();
                                if (herePhaAltstreetsList.size() > 0) {
                                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                    ) {
                                        if ("N".equals(herePhaAltstreets.getStalename())) {
                                            streetName += "|" + herePhaAltstreets.getStName();
                                        }
                                    }
                                }
                            } else {
                                if (herePhaAltstreetsList.size() > 0) {
                                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                    ) {
                                        if ("N".equals(herePhaAltstreets.getStalename())) {
                                            streetName += "|" + herePhaAltstreets.getStName();
                                        }
                                    }
                                }
                            }
                            if (streetName.length() > 0) {
                                if ("|".equals(streetName.substring(0, 1))) {
                                    streetName = streetName.replaceFirst("\\|", "");
                                }
                                hnPointAddress.setStreetName(streetName);
                            } else {
                                hnPointAddress.setStreetName(null);
                            }
                        }

                        // st_name_alias
                        String stNameAlias = "";
                        if (streets.getNumStnmes() == 1) {
                            if ("Y".equals(streets.getVanityname())) {
                                stNameAlias += streets.getStName();
                                hnPointAddress.setStNameAlias(stNameAlias);
                            }
                        } else if (streets.getNumStnmes() > 1) {
                            if ("Y".equals(streets.getVanityname())) {
                                stNameAlias += streets.getStName();
                                if (herePhaAltstreetsList.size() > 0) {
                                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList) {
                                        if ("Y".equals(herePhaAltstreets.getVanityname())) {
                                            stNameAlias += "|" + herePhaAltstreets.getStName();
                                        }
                                    }
                                }
                            } else {
                                if (herePhaAltstreetsList.size() > 0) {
                                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                    ) {
                                        if ("Y".equals(herePhaAltstreets.getStalename())) {
                                            stNameAlias += "|" + herePhaAltstreets.getStName();
                                        }
                                    }
                                }
                            }
                            if (stNameAlias.length() > 0) {
                                if ("|".equals(stNameAlias.substring(0, 1))) {
                                    stNameAlias = stNameAlias.replaceFirst("\\|", "");
                                }
                                hnPointAddress.setStNameAlias(stNameAlias);
                            } else {
                                hnPointAddress.setStNameAlias(null);
                            }
                        }

                        // st_name_stale
                        String stNameStale = "";
                        if (streets.getNumStnmes() == 1) {
                            if ("Y".equals(streets.getStalename())) {
                                stNameStale += streets.getStName();
                                hnPointAddress.setStNameStale(stNameStale);
                            }
                        } else if (streets.getNumStnmes() > 1) {
                            if ("Y".equals(streets.getStalename())) {
                                stNameStale += streets.getStName();
                                if (herePhaAltstreetsList.size() > 0) {
                                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                    ) {
                                        if ("Y".equals(herePhaAltstreets.getStalename())) {
                                            stNameStale += "|" + herePhaAltstreets.getStName();
                                        }
                                    }
                                }
                            } else {
                                if (herePhaAltstreetsList.size() > 0) {
                                    for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                    ) {
                                        if ("Y".equals(herePhaAltstreets.getStalename())) {
                                            stNameStale += "|" + herePhaAltstreets.getStName();
                                        }
                                    }
                                }
                            }
                            if (stNameStale.length() > 0) {
                                if ("|".equals(stNameStale.substring(0, 1))) {
                                    stNameStale = stNameStale.replaceFirst("\\|", "");
                                }
                                hnPointAddress.setStNameStale(stNameStale);
                            } else {
                                hnPointAddress.setStNameStale(null);
                            }
                        }

                        if (isCompileTrans) {
                            if (streets.getFeatId() > 0) {
                                List<Streettrans> streettransList = streettransService.lambdaQuery().eq(Streettrans::getFeatureId, streets.getFeatId()).list();
                                if (streettransList.size() > 0) {
                                    // st_name_trans
                                    String stNameTrans = "";
                                    if (streets.getNumStnmes() == 1) {
                                        if (streettransList.size() > 0) {
                                            hnPointAddress.setStNameTrans(streettransList.get(0).getStNameTr());
                                        }
                                    } else if (streets.getNumStnmes() > 1) {
                                        if ("N".equals(streets.getStalename())) {
                                            stNameTrans += streettransList.get(0).getStNameTr();
                                            if (herePhaAltstreetsList.size() > 0) {
                                                for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                                ) {
                                                    if ("N".equals(herePhaAltstreets.getStalename())) {
                                                        List<Streettrans> altStreettransList = streettransService.lambdaQuery()
                                                                .eq(Streettrans::getFeatureId, herePhaAltstreets.getFeatId()).list();
                                                        if (altStreettransList.size() > 0) {
                                                            stNameTrans += "|" + altStreettransList.get(0).getStNameTr();
                                                        }
                                                    }
                                                }
                                            }
                                        } else {
                                            if (herePhaAltstreetsList.size() > 0) {
                                                for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                                ) {
                                                    List<Streettrans> altStreettransList = streettransService.lambdaQuery()
                                                            .eq(Streettrans::getFeatureId, herePhaAltstreets.getFeatId()).list();
                                                    if (altStreettransList.size() > 0) {
                                                        stNameTrans += "|" + altStreettransList.get(0).getStNameTr();
                                                    }
                                                }
                                            }
                                        }
                                        if (stNameTrans.length() > 0) {
                                            if ("|".equals(stNameTrans.substring(0, 1))) {
                                                stNameTrans = stNameTrans.replaceFirst("\\|", "");
                                            }
                                            hnPointAddress.setStNameTrans(stNameTrans);
                                        } else {
                                            hnPointAddress.setStNameTrans(null);
                                        }
                                    }

                                    // st_name_alias_trans
                                    String stNameAliasTrans = "";
                                    if (streets.getNumStnmes() == 1) {
                                        if ("Y".equals(streets.getVanityname())) {
                                            if (streettransList.size() > 0) {
                                                stNameAliasTrans += streettransList.get(0).getStNameTr();
                                                hnPointAddress.setStNameAliasTrans(stNameAliasTrans);
                                            }
                                        }
                                    } else if (streets.getNumStnmes() > 1) {
                                        if ("Y".equals(streets.getVanityname())) {
                                            if (streettransList.size() > 0) {
                                                stNameAliasTrans += streettransList.get(0).getStNameTr();
                                            }
                                            if (herePhaAltstreetsList.size() > 0) {
                                                for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                                ) {
                                                    if ("Y".equals(herePhaAltstreets.getVanityname())) {
                                                        List<Streettrans> altStreettransList = streettransService.lambdaQuery()
                                                                .eq(Streettrans::getFeatureId, herePhaAltstreets.getFeatId()).list();
                                                        if (altStreettransList.size() > 0) {
                                                            stNameAliasTrans += "|" + altStreettransList.get(0).getStNameTr();
                                                        }
                                                    }
                                                }
                                            }
                                        } else {
                                            if (herePhaAltstreetsList.size() > 0) {
                                                for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                                ) {
                                                    if ("Y".equals(herePhaAltstreets.getVanityname())) {
                                                        List<Streettrans> altStreettransList = streettransService.lambdaQuery()
                                                                .eq(Streettrans::getFeatureId, herePhaAltstreets.getFeatId()).list();
                                                        if (altStreettransList.size() > 0) {
                                                            stNameAliasTrans += "|" + altStreettransList.get(0).getStNameTr();
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        if (stNameAliasTrans.length() > 0) {
                                            if ("|".equals(stNameAliasTrans.substring(0, 1))) {
                                                stNameAliasTrans = stNameAliasTrans.replaceFirst("\\|", "");
                                            }
                                            hnPointAddress.setStNameAliasTrans(stNameAliasTrans);
                                        } else {
                                            hnPointAddress.setStNameAliasTrans(null);
                                        }
                                    }

                                    // st_name_stale_trans
                                    String stNameStaleTrans = "";
                                    if (streets.getNumStnmes() == 1) {
                                        if ("Y".equals(streets.getStalename())) {
                                            if (streettransList.size() > 0) {
                                                stNameStaleTrans += streettransList.get(0).getStNameTr();
                                                hnPointAddress.setStNameStaleTrans(stNameStaleTrans);
                                            }
                                        }
                                    } else if (streets.getNumStnmes() > 1) {
                                        if ("Y".equals(streets.getStalename())) {
                                            if (streettransList.size() > 0) {
                                                stNameStaleTrans += streettransList.get(0).getStNameTr();
                                            }
                                            if (herePhaAltstreetsList.size() > 0) {
                                                for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                                ) {
                                                    if ("Y".equals(herePhaAltstreets.getStalename())) {
                                                        List<Streettrans> altStreettransList = streettransService.lambdaQuery()
                                                                .eq(Streettrans::getFeatureId, herePhaAltstreets.getFeatId()).list();
                                                        if (altStreettransList.size() > 0) {
                                                            stNameStaleTrans += "|" + altStreettransList.get(0).getStNameTr();
                                                        }
                                                    }
                                                }
                                            }
                                        } else {
                                            if (herePhaAltstreetsList.size() > 0) {
                                                for (HerePhaAltstreets herePhaAltstreets : herePhaAltstreetsList
                                                ) {
                                                    if ("Y".equals(herePhaAltstreets.getStalename())) {
                                                        List<Streettrans> altStreettransList = streettransService.lambdaQuery()
                                                                .eq(Streettrans::getFeatureId, herePhaAltstreets.getFeatId()).list();
                                                        if (altStreettransList.size() > 0) {
                                                            stNameStaleTrans += "|" + altStreettransList.get(0).getStNameTr();
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        if (stNameStaleTrans.length() > 0) {
                                            if ("|".equals(stNameStaleTrans.substring(0, 1))) {
                                                stNameStaleTrans = stNameStaleTrans.replaceFirst("\\|", "");
                                            }
                                            hnPointAddress.setStNameStaleTrans(stNameStaleTrans);
                                        } else {
                                            hnPointAddress.setStNameStaleTrans(null);
                                        }
                                    }

                                } else {
                                    log.info("cannot find the featureid,linkId is:" + streets.getLinkId() + ",featureid is:" + streets.getFeatId());
                                }
                            } else {
                                log.info("streets featureid =0,linkId is:" + streets.getLinkId() + ",featureid is:" + streets.getFeatId());
                            }

                            // bldg_nm_trans
                            List<PntAddrTrans> pntAddrTransList = pntAddrTransService.lambdaQuery().eq(PntAddrTrans::getPtAddrId, pointAddress.getPtAddrId()).list();
                            if (pntAddrTransList.size() > 0) {
                                hnPointAddress.setBldgNmTrans(pntAddrTransList.get(0).getBldgNmTr());
                                // trans_type
                                hnPointAddress.setTransType(pntAddrTransList.get(0).getTransType());
                            }

                        }
                    }
                    // address
                    hnPointAddress.setAddress(pointAddress.getAddress());
                    // bldg_nm
                    hnPointAddress.setBldgNm(pointAddress.getBldgNm());
                    // address_type
                    hnPointAddress.setAddressType(pointAddress.getAddrType());
                    // language_code
                    hnPointAddress.setLanguageCode(pointAddress.getPaLangcd());
                    // DISP_LON
                    String pointWkt = pointAddress.getGeom();
                    if (StrUtil.isNotBlank(pointAddress.getDispLon())) {
                        hnPointAddress.setDispLon(pointAddress.getDiapLon());
                    } else {
                        if (StrUtil.isNotBlank(pointWkt)) {
                            String[] pointSplitWkt = StrUtil.subBetween(pointWkt, "(", ")").split(" ");
                            hnPointAddress.setDispLon(pointSplitWkt[0]);
                        }
                    }
                    // DISP_LAT
                    if (StrUtil.isNotBlank(pointAddress.getDispLon())) {
                        hnPointAddress.setDispLat(pointAddress.getDispLat());
                    } else {
                        if (StrUtil.isNotBlank(pointWkt)) {
                            String[] pointSplitWkt = StrUtil.subBetween(pointWkt, "(", ")").split(" ");
                            hnPointAddress.setDispLat(pointSplitWkt[1]);
                        }
                    }
                    if (StrUtil.isNotBlank(pointWkt)) {
                        hnPointAddress.setGeom("SRID=4326;" + pointWkt);
                    }
                    // admin_level
                    if (streetsList.size() > 0) {
                        Long areaId = null;
                        List<Mtdarea> mtdareaList = new ArrayList<>();
                        if ("R".equals(pointAddress.getSide())) {
                            areaId = streetsList.get(0).getrAreaId();
                        } else if ("L".equals(pointAddress.getSide())) {
                            areaId = streetsList.get(0).getlAreaId();
                        } else if ("N".equals(pointAddress.getSide())) {
                            areaId = streetsList.get(0).getlAreaId();
                        }
                        if (areaId == null) {
                            log.info("cannot find linkId in streests: " + streetsList.get(0).getLinkId());
                        } else {
                            mtdareaList = mtdareaService.lambdaQuery().eq(Mtdarea::getAreaId, areaId).list().stream().filter(mtdarea -> LangEnum.getLangByCName(country).equals(mtdarea.getLangCode()) && "B".equals(mtdarea.getAreaType())).collect(Collectors.toList());
                            if (mtdareaList.size() > 0) {
                                Mtdarea mtdarea = mtdareaList.get(0);
                                Integer adminLvl = mtdarea.getAdminLvl();
                                Map<Integer, Integer> codeMap = new HashMap<>();
                                codeMap.put(1, mtdarea.getAreacode1());
                                codeMap.put(2, mtdarea.getAreacode2());
                                codeMap.put(3, mtdarea.getAreacode3());
                                codeMap.put(4, mtdarea.getAreacode4());
                                codeMap.put(5, mtdarea.getAreacode5());

                                for (int i = adminLvl; i > 0; i--) {
                                    assembleData(i, codeMap, hnPointAddress, country);
                                }
                            }
                        }
                    }
                    // AR_LINK_ID
                    hnPointAddress.setArLinkId(pointAddress.getArLinkId());
                    // if (StrUtil.isNotEmpty(pointAddress.getArLinkId())) {
                    //     hnPointAddress.setArLinkId(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(pointAddress.getArLinkId()))).get(0)));
                    // } else {
                    //     hnPointAddress.setArLinkId(pointAddress.getArLinkId());
                    // }

                    // AR_SIDE
                    hnPointAddress.setArSide(pointAddress.getArSide());
                    // vesion
                    hnPointAddress.setVersion(version);

                    hnPointAddressList.add(hnPointAddress);
                }
                log.info("pointAddressList size is:" + pointAddressList.size());
                List<String> srcLinkIds = hnPointAddressList.stream().map(HnPointAddress::getLinkId).collect(Collectors.toList());
                List<Long> inheritLinkIds = inheritIDService.inheritID(new InheritIDDTO(12L, srcLinkIds));
                for (int i = 0; i < hnPointAddressList.size(); i++) {
                    hnPointAddressList.get(i).setLinkId(String.valueOf(inheritLinkIds.get(i)));
                }
                List<String> srcArLinkIds = hnPointAddressList.stream().map(HnPointAddress::getArLinkId).collect(Collectors.toList());
                List<Long> inheritArLinkIds = inheritIDService.inheritID(new InheritIDDTO(12L, srcArLinkIds));
                for (int i = 0; i < hnPointAddressList.size(); i++) {
                    hnPointAddressList.get(i).setArLinkId(String.valueOf(inheritArLinkIds.get(i)));
                }


                List<Long> ids = inheritIDService.createID(new InheritIDDTO(12L, Long.valueOf(hnPointAddressList.size())));
                for (int i = 0; i < hnPointAddressList.size(); i++) {
                    hnPointAddressList.get(i).setHnId(ids.get(i).toString());
                }
                int batchSize = 65535 / BeanUtil.beanToMap(new HnPointAddress()).keySet().size();
                List<List<HnPointAddress>> listHnPointAddress = Lists.partition(hnPointAddressList, batchSize);
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
                for (List<HnPointAddress> addressList : listHnPointAddress) {
                    // log.info("hnPointAddressService threadlocal info is:" + Thread.currentThread().getName());
                    hnPointAddressMapper.mysqlInsertOrUpdateBath(addressList);
                }

            }
        } finally {
            countDownLatch.countDown();
        }
    }

    private void assembleData(int i, Map<Integer, Integer> codeMap, HnPointAddress hnPointAddress, String country) {
        if (codeMap.containsKey(i + 1)) {
            codeMap.put(i + 1, null);
        }
        List<Mtdarea> filterList = mtdareaService.lambdaQuery().eq(Mtdarea::getAreacode1, Optional.ofNullable(codeMap.get(1)).orElse(0))
                .eq(Mtdarea::getAreacode2, Optional.ofNullable(codeMap.get(2)).orElse(0))
                .eq(Mtdarea::getAreacode3, Optional.ofNullable(codeMap.get(3)).orElse(0))
                .eq(Mtdarea::getAreacode4, Optional.ofNullable(codeMap.get(4)).orElse(0))
                .eq(Mtdarea::getAreacode5, Optional.ofNullable(codeMap.get(5)).orElse(0))
                .eq(Mtdarea::getAdminLvl, i)
                .eq(Mtdarea::getLangCode, LangEnum.getLangByCName(country))
                .eq(Mtdarea::getAreaType, "B")
                .list();
        if (CollUtil.isNotEmpty(filterList)) {
            Mtdarea mtdarea1 = filterList.get(0);
            switch (i) {
                case 1:
                    hnPointAddress.setAdminLevel1Govncode(mtdarea1.getGovtCode().toString());
                    hnPointAddress.setAdminLevel1Name(mtdarea1.getAreaName());
                    break;
                case 2:
                    hnPointAddress.setAdminLevel2Govncode(mtdarea1.getGovtCode().toString());
                    hnPointAddress.setAdminLevel2Name(mtdarea1.getAreaName());
                    break;
                case 3:
                    hnPointAddress.setAdminLevel3Govncode(mtdarea1.getGovtCode().toString());
                    hnPointAddress.setAdminLevel3Name(mtdarea1.getAreaName());
                    break;
                case 4:
                    hnPointAddress.setAdminLevel4Govncode(mtdarea1.getGovtCode().toString());
                    hnPointAddress.setAdminLevel4Name(mtdarea1.getAreaName());
                    break;
                case 5:
                    hnPointAddress.setAdminLevel5Govncode(mtdarea1.getGovtCode().toString());
                    hnPointAddress.setAdminLevel5Name(mtdarea1.getAreaName());
                    break;
            }
        }
    }

    @Override
    @Async("asyncTaskExecutor")
    public void handleId(String area, String country, CountDownLatch countDownLatch, List<HnPointAddress> hnPointAddressList) {
        try {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            int fieldNum = BeanUtil.beanToMap(new HnPointAddress()).keySet().size();
            int batchSize = 32767 / fieldNum;
            log.info("batchInsert size is {}", batchSize);
            List<List<HnPointAddress>> splitList = ListUtil.split(hnPointAddressList, batchSize);
            for (List<HnPointAddress> hnPointAddresses : splitList) {
                List<HnPointAddress> resHnPointAddressList = new ArrayList<>();
                for (HnPointAddress hnPointAddress : hnPointAddresses) {
                    // 处理 ar_link_id
                    if (StrUtil.isNotEmpty(hnPointAddress.getArLinkId())) {
                        hnPointAddress.setArLinkId(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(hnPointAddress.getArLinkId()))).get(0)));
                    } else {
                        hnPointAddress.setArLinkId(hnPointAddress.getArLinkId());
                    }
                    resHnPointAddressList.add(hnPointAddress);
                    // log.info("insert datasource is {}", DynamicDataSourceContextHolder.peek());
                    hnPointAddressMapper.mysqlInsertOrUpdateBath(resHnPointAddressList);
                }
            }
        } catch (Exception e) {
            log.error("handle hn_point_address inherited id(ar_link_id) error,detail is {}", e.getMessage());
            throw e;
        } finally {
            countDownLatch.countDown();
        }
    }
}
