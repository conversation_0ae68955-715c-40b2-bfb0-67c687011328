package com.hll.mapdataservice.business.sqlInjector;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import org.apache.ibatis.executor.keygen.NoKeyGenerator;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;
import org.springframework.util.StringUtils;

public class MysqlInsertOrUpdateBath extends AbstractMethod {

    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        final String sql = "<script>insert into %s %s values %s ON DUPLICATE KEY UPDATE %s</script>";
        final String pgsql = "<script>insert into %s %s values %s ON CONFLICT(" + tableInfo.getKeyColumn() + ") DO UPDATE SET %s</script>";
        final String tableName = tableInfo.getTableName();
        final String filedSql = prepareFieldSql(tableInfo);
        final String modelValuesSql = prepareModelValuesSql(tableInfo);
        final String duplicateKeySql = prepareDuplicateKeySql(tableInfo);
        final String duplicateKeySqlpg = prepareDuplicateKeySqlpg(tableInfo);
        final String sqlResult = String.format(sql, tableName, filedSql, modelValuesSql, duplicateKeySql);
        final String pgsqlResult = String.format(pgsql, tableName, filedSql, modelValuesSql, duplicateKeySqlpg);
//        System.out.println("MysqlInsertOrUpdateBath="+Thread.currentThread().getName());
//        System.out.println("tableName is:"+tableName);
//        System.out.println("savaorupdatesqlsql="+pgsqlResult);
        SqlSource sqlSource = languageDriver.createSqlSource(configuration, pgsqlResult, modelClass);
        return this.addInsertMappedStatement(mapperClass, modelClass, "mysqlInsertOrUpdateBath", sqlSource, new NoKeyGenerator(), null, null);
    }

    /**
     * 准备ON DUPLICATE KEY UPDATE sql
     *
     * @param tableInfo
     * @return
     */
    private String prepareDuplicateKeySql(TableInfo tableInfo) {
        final StringBuilder duplicateKeySql = new StringBuilder();
        if (!StringUtils.isEmpty(tableInfo.getKeyColumn())) {
            duplicateKeySql.append(tableInfo.getKeyColumn()).append("=values(").append(tableInfo.getKeyColumn()).append("),");
        }

        tableInfo.getFieldList().forEach(x -> {
            duplicateKeySql.append(x.getColumn())
                    .append("=values(")
                    .append(x.getColumn())
                    .append("),");
        });
        duplicateKeySql.delete(duplicateKeySql.length() - 1, duplicateKeySql.length());
        return duplicateKeySql.toString();
    }

    /**
     * 准备ON DUPLICATE KEY UPDATE sql
     *
     * @param tableInfo
     * @return
     */
    private String prepareDuplicateKeySqlpg(TableInfo tableInfo) {
        final StringBuilder duplicateKeySql = new StringBuilder();
//        INSERT INTO your_table (id, hll_linkid, link_id)
//        VALUES (1, 'new_value_for_hll_linkid', 'new_value_for_link_id')
//        ON CONFLICT (id) DO UPDATE
//                SET
//        hll_linkid = COALESCE(EXCLUDED.hll_linkid, your_table.hll_linkid),
//                link_id = COALESCE(EXCLUDED.link_id, your_table.link_id);
        //new version(20240312) which can deal with column is null then not change
        if (!StringUtils.isEmpty(tableInfo.getKeyColumn())) {
            duplicateKeySql.append(tableInfo.getKeyColumn()).append("=COALESCE(EXCLUDED.").append(tableInfo.getKeyColumn()).append(",")
                    .append(tableInfo.getTableName()).append(".").append(tableInfo.getKeyColumn()).append("),");
        }

        tableInfo.getFieldList().forEach(x -> {
                    duplicateKeySql.append(x.getColumn())
                            .append("=COALESCE(EXCLUDED.")
                            .append(x.getColumn()).append(",")
                            .append(tableInfo.getTableName()).append(".").append(x.getColumn()).append("),");
                }
                //old version cannot deal with value = null situation(column would be change to null)
//        if(!StringUtils.isEmpty(tableInfo.getKeyColumn())) {
//            duplicateKeySql.append(tableInfo.getKeyColumn()).append("=EXCLUDED.").append(tableInfo.getKeyColumn()).append(",");
//        }
//
//        tableInfo.getFieldList().forEach(x -> {
//            duplicateKeySql.append(x.getColumn())
//                    .append("=EXCLUDED.")
//                    .append(x.getColumn()).append(",");
//        }

        );
//        for (TableFieldInfo tableFieldInfo:tableInfo.getFieldList()
//             ) {
//            if(tableFieldInfo.getColumn().contains("geo")){
//                duplicateKeySql.append(tableFieldInfo.getColumn())
//                        .append("=EXCLUDED.ST_GeomFromText(")
//                        .append(tableFieldInfo.getColumn()).append("),");
//            }else {
//                duplicateKeySql.append(tableFieldInfo.getColumn())
//                        .append("=EXCLUDED.")
//                        .append(tableFieldInfo.getColumn()).append(",");
//            }
//        }
        duplicateKeySql.delete(duplicateKeySql.length() - 1, duplicateKeySql.length());
        return duplicateKeySql.toString();
    }

    /**
     * 准备属性名
     *
     * @param tableInfo
     * @return
     */
    private String prepareFieldSql(TableInfo tableInfo) {
        StringBuilder fieldSql = new StringBuilder();
        fieldSql.append(tableInfo.getKeyColumn()).append(",");
        tableInfo.getFieldList().forEach(x -> {
            fieldSql.append(x.getColumn()).append(",");
        });
        fieldSql.delete(fieldSql.length() - 1, fieldSql.length());
        fieldSql.insert(0, "(");
        fieldSql.append(")");
        return fieldSql.toString();
    }

    private String prepareModelValuesSql(TableInfo tableInfo) {
        final StringBuilder valueSql = new StringBuilder();
        valueSql.append("<foreach collection=\"list\" item=\"item\" index=\"index\" open=\"(\" separator=\"),(\" close=\")\">");
        if (!StringUtils.isEmpty(tableInfo.getKeyProperty())) {
            valueSql.append("#{item.").append(tableInfo.getKeyProperty()).append("},");
        }
//        tableInfo.getFieldList().forEach(x -> valueSql.append("#{item.").append(x.getProperty()).append("},"));
        tableInfo.getFieldList().forEach(x -> {
            if ("name".equals(x.getProperty())) {
                valueSql.append("#{item.").append(x.getProperty()).append(",typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},");
            } else {
                valueSql.append("#{item.").append(x.getProperty()).append("},");
            }
        });
//        for (TableFieldInfo tableFieldInfo:tableInfo.getFieldList()
//             ) {
//            if(tableFieldInfo.getColumn().contains("geo")){
//                valueSql.append("#{item.").append(tableFieldInfo.getProperty()).append(",typeHandler = MyGeometryTypeHandler.class},");
//            }else {
//                valueSql.append("#{item.").append(tableFieldInfo.getProperty()).append("},");
//            }
//        }
        valueSql.delete(valueSql.length() - 1, valueSql.length());
        valueSql.append("</foreach>");
        return valueSql.toString();
    }
}
