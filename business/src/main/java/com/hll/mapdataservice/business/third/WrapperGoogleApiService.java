package com.hll.mapdataservice.business.third;

import com.hll.mapdataservice.business.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@FeignClient(name = "wrapperGoogleNearbyPoiSearch", configuration = FeignConfig.class, url = "${third.wrapperGoogleNearByPoiSearch.url}")
public interface WrapperGoogleApiService {
    @GetMapping(value = "/nearbysearch")
    String getMatchResult(@RequestParam Map<String, Object> map);
}
