package com.hll.mapdataservice.business.api.road.service.dto;

import com.hll.mapdataservice.business.vo.RoadPropertyHere;
import com.hll.mapdataservice.common.entity.MnrNetwGeoLink;
import com.hll.mapdataservice.common.vo.RoadProperty;

import java.util.List;

/*
 * <AUTHOR>
 * @date  2/26/21 4:21 PM
 * @Email:<EMAIL>
 */
public class LinkData {

    /**
     * tomtom数据列表
     */
    private List<RoadProperty> tomtomList;

    /**
     * here道路数据
     */
    private List<RoadProperty> hereList;

    /**
     * osm道路数据
     */
    private List<RoadProperty> osmList;

    public List<RoadProperty> getTomtomList() {
        return tomtomList;
    }

    public void setTomtomList(List<RoadProperty> featId) {
        this.tomtomList = featId;
    }

    public List<RoadProperty> getHereList() {
        return hereList;
    }

    public void setHereList(List<RoadProperty> hereList) {
        this.hereList = hereList;
    }

    public List<RoadProperty> getOsmList() {
        return osmList;
    }

    public void setOsmList(List<RoadProperty> osmList) {
        this.osmList = osmList;
    }
}
