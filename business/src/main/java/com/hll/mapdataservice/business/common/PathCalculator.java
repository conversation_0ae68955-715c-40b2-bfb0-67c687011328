package com.hll.mapdataservice.business.common;

import com.hll.mapdataservice.business.api.road.service.LinkEServiceImpl;
import com.hll.mapdataservice.business.api.road.service.NodeEServiceImpl;
import com.hll.mapdataservice.common.entity.LinkE;
import com.hll.mapdataservice.common.entity.NodeE;
import com.vividsolutions.jts.io.ParseException;
import com.vividsolutions.jts.io.WKTReader;
import lombok.Getter;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-10-01
 */
@Getter
public class PathCalculator {

    private final String inlinkId;

    private final String outlinkId;

    private final List<String> directionList_1 = new ArrayList<>(Arrays.asList("0", "1", "2"));

    private final List<String> directionList_2 = new ArrayList<>(Arrays.asList("0", "1", "3"));

    private final Map<String, LinkE> linkMap;

    // private final Map<String, String> joinNodeMap;

    public PathCalculator(String inlinkId, String outlinkId) {
        LinkEServiceImpl linkService = (LinkEServiceImpl) SpringContainerGetter.getBean(LinkEServiceImpl.class);
        NodeEServiceImpl nodeService = (NodeEServiceImpl) SpringContainerGetter.getBean(NodeEServiceImpl.class);

        LinkE inLink = linkService.getById(inlinkId);

        List<String> nodeIds = new ArrayList<>();
        List<LinkE> links = null;
        try {
            links = linkService.queryByExtent(new WKTReader().read(inLink.getGeometryWkt()).buffer(0.015D).toString(), "0,2,3");
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        if (!CollectionUtils.isEmpty(links)) {

            nodeIds.addAll(links.stream().map(LinkE::getHllSNid).collect(Collectors.toList()));
            nodeIds.addAll(links.stream().map(LinkE::getHllENid).collect(Collectors.toList()));
        }

        Map<String, LinkE> linkMap = new LinkedHashMap<>();
        for (LinkE autoRuleDTO : links) {
            linkMap.put(autoRuleDTO.getHllLinkid(), autoRuleDTO);
        }

        List<NodeE> nodes = nodeService.lambdaQuery().in(NodeE::getHllNodeid, nodeIds).list();
        Map<String, String> joinNodeMap = new HashMap<>();
        for (NodeE node : nodes) {
            joinNodeMap.put(node.getHllNodeid(), node.getAdjoinNid());
        }

        this.inlinkId = inlinkId;
        this.outlinkId = outlinkId;
        this.linkMap = linkMap;
        // this.joinNodeMap = joinNodeMap;
    }

    private List<String> findTopology(LinkE autoRule) {
        String hllLinkId = autoRule.getHllLinkid();
        String hll_s_nid = autoRule.getHllSNid();
        String hll_e_nid = autoRule.getHllENid();
        String dir = autoRule.getDir();
        if (dir.equals("4")) {
            return null;
        }
        List<LinkE> topologyRuleList = new LinkedList<>();

        LinkE temporaryAutoPath;
        String tapHllSNid;
        String tapHllENid;
        String joinSNid;
        String joinENid;
        for (Map.Entry<String, LinkE> map : linkMap.entrySet()) {
            temporaryAutoPath = map.getValue();
            tapHllSNid = temporaryAutoPath.getHllSNid();
            tapHllENid = temporaryAutoPath.getHllENid();
            if (dir.equals("2")) {
                if (tapHllSNid.equals(hll_e_nid) || tapHllENid.equals(hll_e_nid)) {
                    topologyRuleList.add(temporaryAutoPath);
                }
                // joinENid = joinNodeMap.get(hll_e_nid);
                // if (tapHllSNid.equals(joinENid) || tapHllENid.equals(joinENid)) {
                //     topologyRuleList.add(temporaryAutoPath);
                // }
            } else if (dir.equals("3")) {
                if (tapHllSNid.equals(hll_s_nid) || tapHllENid.equals(hll_s_nid)) {
                    topologyRuleList.add(temporaryAutoPath);
                }
                // joinSNid = joinNodeMap.get(hll_s_nid);
                // if (tapHllSNid.equals(joinSNid) || tapHllENid.equals(joinSNid)) {
                //     topologyRuleList.add(temporaryAutoPath);
                // }
            } else {
                if (tapHllSNid.equals(hll_s_nid) || tapHllSNid.equals(hll_e_nid) || tapHllENid.equals(hll_s_nid) || tapHllENid.equals(hll_e_nid)) {
                    topologyRuleList.add(temporaryAutoPath);
                }
                // joinSNid = joinNodeMap.get(hll_s_nid);
                // joinENid = joinNodeMap.get(hll_e_nid);
                // if (tapHllSNid.equals(joinSNid) || tapHllSNid.equals(joinENid)) {
                //     topologyRuleList.add(temporaryAutoPath);
                // }
                // if (tapHllENid.equals(joinSNid) || tapHllENid.equals(joinENid)) {
                //     topologyRuleList.add(temporaryAutoPath);
                // }


            }
        }

        List<String> topologyLinks = new LinkedList<>();
        String outlinkId;
        String outlink_s_nid;
        String outlink_e_nid;
        String outlink_dir;
        for (LinkE link : topologyRuleList) {
            outlinkId = link.getHllLinkid();
            outlink_s_nid = link.getHllSNid();
            outlink_e_nid = link.getHllENid();
            outlink_dir = link.getDir();

            // joinSNid = joinNodeMap.get(hll_s_nid);
            // joinENid = joinNodeMap.get(hll_e_nid);
            if (outlinkId.equals(hllLinkId)) {
                continue;
            }
            if ((outlink_s_nid.equals(hll_s_nid)
                 // || outlink_s_nid.equals(joinSNid)
                 || outlink_s_nid.equals(hll_e_nid)
                 // || outlink_s_nid.equals(joinENid)
                 ) && !directionList_1.contains(outlink_dir)) {
                continue;
            }
            if ((outlink_e_nid.equals(hll_s_nid)
                 // || outlink_e_nid.equals(joinSNid)
                 || outlink_e_nid.equals(hll_e_nid)
                 // || outlink_e_nid.equals(joinENid)
                 ) &&
                !directionList_2.contains(outlink_dir)) {
                continue;
            }
            topologyLinks.add(outlinkId);
        }
        return topologyLinks;
    }


    private List<String> minPath(List<List<String>> pathArray) {
        int minPathSize = Integer.MAX_VALUE;
        for (List<String> path : pathArray) {
            if (path.size() <= minPathSize) {
                minPathSize = path.size();
            }
        }
        for (List<String> path : pathArray) {
            if (path.size() == minPathSize) {
                pathArray.remove(path);
                return path;
            }
        }
        return new ArrayList<>();
    }

    public List<String> ruleComplete() {
        if (this.inlinkId.equals(outlinkId)) {
            return new ArrayList<>(Arrays.asList(this.inlinkId, this.outlinkId));
        }
        List<List<String>> pathArray = new LinkedList<>();
        pathArray.add(new ArrayList<>(Collections.singletonList(this.inlinkId)));
        List<String> path;
        List<String> temporaryPath;
        List<String> temp;
        int index = 0;
        LinkE link;
        while (true) {
            index++;
            if (index > 5000) {
                return null;
            }
            path = minPath(pathArray);
            assert path != null;
            if (path.isEmpty()) {
                return null;
            }
            if (path.size() > 15) {
                return null;
            }

            link = linkMap.get(path.get(path.size() - 1));
            if (Objects.isNull(link)) throw new RuntimeException("Path calculation failed.");

            temporaryPath = findTopology(link);
            if (Objects.isNull(temporaryPath)) {
                throw new RuntimeException("Path calculation failed.");
            }
            for (String linkid : temporaryPath) {
                temp = new LinkedList<>(path);
                // 不走回头路
                if (temp.contains(linkid)) {
                    continue;
                }
                temp.add(linkid);
                if (linkid.equals(this.outlinkId)) {
                    return temp;
                }
                pathArray.add(temp);
            }
        }
    }
}