package com.hll.mapdataservice.business.api.road.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hll.mapdataservice.common.entity.RelationM;
import com.hll.mapdataservice.common.mapper.RelationMMapper;
import com.hll.mapdataservice.common.service.IRelationMService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-28
 */
@Service
//@DS("db8")
public class RelationMServiceImpl extends ServiceImpl<RelationMMapper, RelationM> implements IRelationMService {

}
