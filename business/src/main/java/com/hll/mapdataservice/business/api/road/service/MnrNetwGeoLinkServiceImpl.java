package com.hll.mapdataservice.business.api.road.service;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.hll.mapdataservice.business.entity.PhlTrackCoorInfoBak;
import com.hll.mapdataservice.business.entity.TrackCoorInfo;
import com.hll.mapdataservice.business.mapper.TrackCoorInfoMapper;
import com.hll.mapdataservice.business.service.PhlTrackCoorInfoBakServiceImpl;
import com.hll.mapdataservice.business.service.TrackCoorInfoServiceImpl;
import com.hll.mapdataservice.business.third.HereRoadMatchService;
import com.hll.mapdataservice.business.third.RoadMatchService;
import com.hll.mapdataservice.business.vo.TrackMatchReq;
import com.hll.mapdataservice.common.entity.MnrNetwGeoLink;
import com.hll.mapdataservice.common.mapper.MnrNetwGeoLinkMapper;
import com.hll.mapdataservice.common.service.IMnrNetwGeoLinkService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-24
 */
@Service
@Component
@Slf4j
//@DS("db2")
public class MnrNetwGeoLinkServiceImpl extends ServiceImpl<MnrNetwGeoLinkMapper, MnrNetwGeoLink> implements IMnrNetwGeoLinkService {

    @Resource
    private PhlTrackCoorInfoBakServiceImpl phlTrackCoorInfoBakService;
    @Resource
    private TrackCoorInfoServiceImpl trackCoorInfoService;
    @Resource
    private RoadMatchService roadMatchService;
    @Resource
    private HereRoadMatchService hereRoadMatchService;

    @Async("asyncTaskExecutor")
    public void getTrackMatch(String orderId,String dataSource) throws ParseException {
        log.info("Start processing orderId:"+orderId);
        //拼接请求对象
        List<TrackCoorInfo> trackCoorInfoList = trackCoorInfoService.lambdaQuery().eq(TrackCoorInfo::getOrderId, orderId).orderByAsc(TrackCoorInfo::getLocTime).list();
        List<String> trackList = new ArrayList<>();
        for (TrackCoorInfo trackCoorInfo : trackCoorInfoList) {
            //{"coorType":"wgs84", "originalTrack":["100.5996747,13.8047994,1603644352,1.2980503,305.71634",
            //"100.5991847,13.8047918,1603644355,0.0,305.71634",
            //"100.5990894,13.8052371,1603644361,5.4530067,307.7255",
            //"100.5991361,13.8051712,1603644367,6.5436397,311.42847",
            //"100.5991901,13.8052015,1603644370,6.042252,309.5075"]
            //}
            trackList.add(trackCoorInfo.getLongitude() + "," + trackCoorInfo.getLatitude() + "," + (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH).parse(trackCoorInfo.getLocTime()).getTime()) + "," + "0" + "," + trackCoorInfo.getBearing());
        }
        System.out.println(trackList);
        log.info("processing list:{}",trackList.toString());
        TrackMatchReq trackMatchReq = new TrackMatchReq("wgs84", trackList);
        String matchResult = null;
        //请求匹配结果
        if(dataSource.compareTo("tomtom") == 0){
            matchResult = roadMatchService.getMatchResult(JSON.toJSONString(trackMatchReq));
        } else  if(dataSource.compareTo("here") == 0){
            matchResult = hereRoadMatchService.getMatchResult(JSON.toJSONString(trackMatchReq));
        } else {
            System.out.println("no such datasource"+dataSource+" please check it!");
            log.info("no such datasource:{}",dataSource," please check it!");
            return;
        }
        //解析返回data
        JSONObject object = JSONObject.parseObject(matchResult);
        if (object.get("ret").toString().compareTo("-1") == 0) {
            System.out.println("trace match no result ret = -1;orderId is:" + orderId);
            log.info("trace match no result ret = -1;orderId is:" + orderId);
            return;
        }
        JSONObject dataObject = object.getJSONObject("data");
        JSONArray array = dataObject.getJSONArray("data");
        if (array.size() == 0) {
            System.out.println("trace match no result;orderId is:" + orderId);
            log.info("trace match no result;orderId is:" + orderId);
            return;
        }
        //根据返回结果更新数据
        int i = 0;
        List<TrackCoorInfo> trackCoorInfoUpdateList = new ArrayList<>();
        for (TrackCoorInfo trackCoorInfo : trackCoorInfoList) {
            System.out.println(i);
            log.info("i:"+i);
            if (array.getJSONObject(i).get("link_id") == null) {
                if(dataSource.compareTo("tomtom") == 0){
                    trackCoorInfo.setTtLink("-1");
                } else  if(dataSource.compareTo("here") == 0){
                    trackCoorInfo.setHereLink("-1");
                } else {
                    System.out.println("no such datasource"+dataSource+" please check it!");
                    log.info("no such datasource"+dataSource+" please check it!");
                    return;
                }
            } else {
                if(dataSource.compareTo("tomtom") == 0){
                    trackCoorInfo.setTtLink(array.getJSONObject(i).get("link_id").toString());
                } else  if(dataSource.compareTo("here") == 0){
                    trackCoorInfo.setHereLink(array.getJSONObject(i).get("link_id").toString());
                } else {
                    System.out.println("no such datasource"+dataSource+" please check it!");
                    log.info("no such datasource"+dataSource+" please check it!");
                    return;
                }
            }
            System.out.println(trackCoorInfo.getId() + "," + trackCoorInfo.getOrderId() + "," + trackCoorInfo.getLongitude() + ","
                    + trackCoorInfo.getLatitude() + "," + trackCoorInfo.getLocTime() + ",0.0," + trackCoorInfo.getBearing());
            System.out.println(array.getJSONObject(i).get("link_id"));
            log.info(trackCoorInfo.getId() + "," + trackCoorInfo.getOrderId() + "," + trackCoorInfo.getLongitude() + ","
                    + trackCoorInfo.getLatitude() + "," + trackCoorInfo.getLocTime() + ",0.0," + trackCoorInfo.getBearing());
            log.info("link_id is:"+array.getJSONObject(i).get("link_id"));
            trackCoorInfoUpdateList.add(trackCoorInfo);
            i++;
        }
        trackCoorInfoService.updateBatchById(trackCoorInfoUpdateList);
        System.out.println("finish processing trackorder:" + orderId);
        log.info("finish processing trackorder:" + orderId);
    }

    public void insertTrackMatch(String path) throws ParseException, IOException {
        String readFile = "/Users/<USER>/Downloads/work-doc/2000W/test.csv";
        BufferedReader bufferedReader = null;
        bufferedReader = new BufferedReader(new InputStreamReader(new FileInputStream(new File(readFile))));
        String line;
        int counter = 0;
        while (!StringUtils.isEmpty(line = bufferedReader.readLine())) {
//            log.info("this is {} line",counter++);
            TrackCoorInfo tci = new TrackCoorInfo();
//            tci.set
        }
    }

    @Async("asyncTaskExecutor")
//    @DS("db5")
    public void getPhlTrackMatch(String orderId,String dataSource) throws ParseException {
        log.info("Start processing orderId:"+orderId);
        //拼接请求对象
        List<PhlTrackCoorInfoBak> trackCoorInfoList = phlTrackCoorInfoBakService.lambdaQuery().eq(PhlTrackCoorInfoBak::getOrderId, orderId).orderByAsc(PhlTrackCoorInfoBak::getLocTime).list();
        List<String> trackList = new ArrayList<>();
        for (PhlTrackCoorInfoBak trackCoorInfo : trackCoorInfoList) {
            //{"coorType":"wgs84", "originalTrack":["100.5996747,13.8047994,1603644352,1.2980503,305.71634",
            //"100.5991847,13.8047918,1603644355,0.0,305.71634",
            //"100.5990894,13.8052371,1603644361,5.4530067,307.7255",
            //"100.5991361,13.8051712,1603644367,6.5436397,311.42847",
            //"100.5991901,13.8052015,1603644370,6.042252,309.5075"]
            //}
            trackList.add(trackCoorInfo.getLongitude() + "," + trackCoorInfo.getLatitude() + "," + (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH).parse(trackCoorInfo.getLocTime()).getTime()) + "," + "0" + "," + trackCoorInfo.getBearing());
        }
        System.out.println(trackList);
        log.info("tracklist is:{}",trackList);
        TrackMatchReq trackMatchReq = new TrackMatchReq("wgs84", trackList);
        String matchResult = null;
        //请求匹配结果
        if(dataSource.compareTo("tomtom") == 0){
            matchResult = roadMatchService.getMatchResult(JSON.toJSONString(trackMatchReq));
        } else  if(dataSource.compareTo("here") == 0){
            matchResult = hereRoadMatchService.getMatchResult(JSON.toJSONString(trackMatchReq));
        } else {
            System.out.println("no such datasource"+dataSource+" please check it!");
            log.info("no such datasource"+dataSource+" please check it!");
            return;
        }
        //解析返回data
        JSONObject object = JSONObject.parseObject(matchResult);
        if (object.get("ret").toString().compareTo("-1") == 0) {
            System.out.println("trace match no result ret = -1;orderId is:" + orderId);
            log.info("trace match no result ret = -1;orderId is:" + orderId);
            return;
        }
        JSONObject dataObject = object.getJSONObject("data");
        JSONArray array = dataObject.getJSONArray("data");
        if (array.size() == 0) {
            System.out.println("trace match no result;orderId is:" + orderId);
            log.info("trace match no result;orderId is:" + orderId);
            return;
        }
        //根据返回结果更新数据
        int i = 0;
        List<PhlTrackCoorInfoBak> trackCoorInfoUpdateList = new ArrayList<>();
        for (PhlTrackCoorInfoBak trackCoorInfo : trackCoorInfoList) {
            System.out.println(i);
            log.info("i is:"+i);
            if (array.getJSONObject(i).get("link_id") == null) {
                if(dataSource.compareTo("tomtom") == 0){
                    trackCoorInfo.setTtLink("-1");
                } else  if(dataSource.compareTo("here") == 0){
                    trackCoorInfo.setHereLink("-1");
                } else {
                    System.out.println("no such datasource"+dataSource+" please check it!");
                    log.info("no such datasource"+dataSource+" please check it!");
                    return;
                }
            } else {
                if(dataSource.compareTo("tomtom") == 0){
                    trackCoorInfo.setTtLink(array.getJSONObject(i).get("link_id").toString());
                } else  if(dataSource.compareTo("here") == 0){
                    trackCoorInfo.setHereLink(array.getJSONObject(i).get("link_id").toString());
                } else {
                    System.out.println("no such datasource"+dataSource+" please check it!");
                    log.info("no such datasource"+dataSource+" please check it!");
                    return;
                }
            }
            System.out.println(trackCoorInfo.getId() + "," + trackCoorInfo.getOrderId() + "," + trackCoorInfo.getLongitude() + ","
                    + trackCoorInfo.getLatitude() + "," + trackCoorInfo.getLocTime() + ",0.0," + trackCoorInfo.getBearing());
            System.out.println(array.getJSONObject(i).get("link_id"));
            log.info(trackCoorInfo.getId() + "," + trackCoorInfo.getOrderId() + "," + trackCoorInfo.getLongitude() + ","
                    + trackCoorInfo.getLatitude() + "," + trackCoorInfo.getLocTime() + ",0.0," + trackCoorInfo.getBearing());
            log.info("link_id is:"+array.getJSONObject(i).get("link_id"));
            trackCoorInfoUpdateList.add(trackCoorInfo);
            i++;
        }
        phlTrackCoorInfoBakService.updateBatchById(trackCoorInfoUpdateList);
        System.out.println("finish processing trackorder:" + orderId);
        log.info("finish processing trackorder:" + orderId);
    }
}
