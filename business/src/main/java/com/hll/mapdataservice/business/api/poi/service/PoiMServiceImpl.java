package com.hll.mapdataservice.business.api.poi.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.map.BiMap;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.business.api.road.service.HerePhaStreetsServiceImpl;
import com.hll.mapdataservice.business.api.road.service.HnPointAddressServiceImpl;
import com.hll.mapdataservice.business.api.road.service.PointAddressServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.third.GoogleApiService;
import com.hll.mapdataservice.business.third.InheritIDService;
import com.hll.mapdataservice.business.third.MiddlePlatformService;
import com.hll.mapdataservice.business.third.WrapperGoogleApiService;
import com.hll.mapdataservice.business.third.dto.InheritIDDTO;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.mapper.HnPointAddressMapper;
import com.hll.mapdataservice.common.mapper.PoiMatchMapper;
import com.hll.mapdataservice.common.mapper.*;
import com.hll.mapdataservice.common.service.IPoiMService;
import com.hll.mapdataservice.common.service.IPoiMatchResService;
import com.hll.mapdataservice.common.service.IPoiMatchService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import com.hll.mapdataservice.common.vo.PoiBase;
import com.vividsolutions.jts.geom.Point;
import com.vividsolutions.jts.io.ParseException;
import com.vividsolutions.jts.io.WKTReader;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.json.XML;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.net.ConnectException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

import static com.graphhopper.util.Helper.readFile;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-24
 */
@Service
@Slf4j
//@DS("db8")
public class PoiMServiceImpl extends ServiceImpl<PoiMMapper, PoiM> implements IPoiMService {

    @Resource
    HerePhaPoitransServiceImpl herePhaPoitransService;
    @Resource
    HerePhaPoiattrServiceImpl herePhaPoiattrService;
    @Resource
    HerePhaRestrntsServiceImpl herePhaRestrntsService;
    @Resource
    MtdrefServiceImpl mtdrefService;
    @Resource
    HerePhaPoiassocServiceImpl herePhaPoiassocService;
    @Resource
    MtdareaServiceImpl mtdareaService;
    @Resource
    HerePhaStreetsServiceImpl herePhaStreetsService;
    @Resource
    IPoiMatchResService poiMatchResService;
    @Resource
    GoogleApiService googleApiService;
    @Resource
    WrapperGoogleApiService wrapperGoogleApiService;
    @Resource
    PoiMatchMapper poiMatchMapper;
    @Resource
    IPoiMatchService poiMatchService;

    @Resource
    PoiMMapper poiMMapper;
    @Resource
    InheritIDService inheritIDService;
    @Resource
    PoiCoverageResMapper poiCoverageResMapper;
    @Resource
    OrderPoiMapper orderPoiMapper;
    @Resource
    HnPointAddressMapper hnPointAddressMapper;
    @Resource
    PoiCoverageHereGoogleMapper poiCoverageHereGoogleMapper;
    @Resource
    PoiCoverageMpResMapper poiCoverageMpResMapper;
    @Resource
    MiddlePlatformService middlePlatformService;
    @Resource
    PoiSampleResMapper poiSampleResMapper;
    @Resource
    HnPointAddressServiceImpl hnPointAddressService;
    @Resource
    PoiSrcMapper poiSrcMapper;
    @Resource
    PoiDiffMapper poiDiffMapper;
    @Resource
    PoiFusionResultServiceImpl poiFusionResultService;
    @Resource
    RedisTemplate<String, Object> redisTemplate;

    public void poiConvert(List<PoiBase> poiBaseList) {

        List<PoiM> poiInfoList = new ArrayList<>();
        if (poiBaseList.size() > 0) {
            for (PoiBase herePhaAutosvc : poiBaseList
            ) {
                PoiM poiInfo = new PoiM();
                // POI_ID
                poiInfo.setPoiId(UUID.randomUUID().toString());
                // Source_ID
                poiInfo.setSourceId(herePhaAutosvc.getPoiId().toString());
                // Kind
                // poiInfo.setKind(herePhaAutosvc.get);
                // Kind_Code
                poiInfo.setKindCode(herePhaAutosvc.getFacType().toString());
                // Name_S
                if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                    poiInfo.setNameS(herePhaAutosvc.getPoiName());
                } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                // Alias
                {
                    poiInfo.setAlias(herePhaAutosvc.getPoiName());
                } else
                // Name
                {
                    poiInfo.setName(herePhaAutosvc.getPoiName());
                }
                // Name_ENG
                List<HerePhaPoitrans> herePhaPoitransList = herePhaPoitransService.lambdaQuery().eq(HerePhaPoitrans::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoitransList.size() > 0) {
                    // Name_S_ENG
                    if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                        poiInfo.setNameSEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                    // Alias_ENG
                    {
                        poiInfo.setAliasEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else
                    // Name_ENG
                    {
                        poiInfo.setNameEng(herePhaPoitransList.get(0).getPoiNmTr());
                    }
                }
                // Address
                poiInfo.setAddress(herePhaAutosvc.getStName() + herePhaAutosvc.getPoiStNum());
                // Address_ENG
                if (herePhaPoitransList.size() > 0) {
                    if (herePhaPoitransList.get(0).getPoiNmTr().length() > 0) {
                        poiInfo.setAddressEng(herePhaPoitransList.get(0).getStNmTr());
                    } else {
                        poiInfo.setAddressEng(herePhaPoitransList.get(0).getActaddrTr());
                    }
                }
                // lng_84
                // poiInfo.setLongitudeWgs84();
                // Lat_84
                // poiInfo.setLatitudeWgs84();
                // Lon_Guide_84
                poiInfo.setLonGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[0].replace("POINT(", "")));
                // Lat_Guide_84
                poiInfo.setLatGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[1].replace(")", "")));
                // Link_ID
                poiInfo.setLinkId(herePhaAutosvc.getLinkId().toString());
                // Side
                poiInfo.setSide(herePhaAutosvc.getPoiStSd().isEmpty() ? "N" : herePhaAutosvc.getPoiStSd());
                // Importance
                poiInfo.setImportance("Y".equals(herePhaAutosvc.getNatImport()) ? "1" : "");
                // VAdmin_Code
                poiInfo.setVadminCode(herePhaAutosvc.getVancityId().toString());
                // Zip_Code
                poiInfo.setZipCode(herePhaAutosvc.getActPostal());
                // Telephone
                poiInfo.setTelephone(herePhaAutosvc.getPhNumber());
                // Tel_Type
                poiInfo.setTelType("未调查");
                // POI_Class
                poiInfo.setPoiClass("0");
                // Star_Rating
                List<HerePhaPoiattr> herePhaPoiattrList = herePhaPoiattrService.lambdaQuery()
                        .eq(HerePhaPoiattr::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiattrList.size() > 0) {
                    HerePhaPoiattr herePhaPoiattr = herePhaPoiattrList.get(0);
                    if ("7011".equals(herePhaPoiattr.getFacType()) && "33".equals(herePhaPoiattr.getAttrType())) {
                        switch (herePhaPoiattr.getAttrValue()) {
                            case "17":
                                poiInfo.setStarRating("0");
                                break;
                            case "18":
                                poiInfo.setStarRating("1");
                                break;
                            case "1026":
                                poiInfo.setStarRating("1");
                                break;
                            case "1030":
                                poiInfo.setStarRating("8");
                                break;
                            case "1029":
                                poiInfo.setStarRating("3");
                                break;
                            case "1028":
                                poiInfo.setStarRating("4");
                                break;
                            case "1027":
                                poiInfo.setStarRating("5");
                                break;
                            default:
                                poiInfo.setStarRating("0");
                        }
                    }
                }
                // TG_Type
                poiInfo.setTgType("1");
                // Access_Flag
                // Truck_Flag
                // Food_Type
                List<HerePhaRestrnts> herePhaRestrntsList = herePhaRestrntsService.lambdaQuery()
                        .eq(HerePhaRestrnts::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaRestrntsList.size() > 0) {
                    poiInfo.setFoodType(herePhaRestrntsList.get(0).getFoodType());
                }
                // Airpt_Code
                // Navi_Loc
                // Admin releated
                List<HerePhaStreets> herePhaStreetsList = herePhaStreetsService.lambdaQuery()
                        .eq(HerePhaStreets::getLinkId, herePhaAutosvc.getLinkId()).list();
                if (herePhaStreetsList.size() > 0) {
                    List<Mtdarea> mtdareaList = new ArrayList<>();
                    if ("R".equals(herePhaAutosvc.getPoiStSd())) {
                        mtdareaList = mtdareaService.lambdaQuery().eq(Mtdarea::getAreaId, herePhaStreetsList.get(0).getrAreaId()).list();
                    } else if ("L".equals(herePhaAutosvc.getPoiStSd())) {
                        mtdareaList = mtdareaService.lambdaQuery().eq(Mtdarea::getAreaId, herePhaStreetsList.get(0).getlAreaId()).list();
                    }
                    if (mtdareaList.size() > 0) {
                        // Country_Code
                        poiInfo.setAdminlvl1Code(mtdareaList.stream()
                                .filter(mtdarea -> "1".equals(mtdarea.getAdminLvl())).collect(Collectors.toList()).get(0).getGovtCode().toString());
                        // Country_Name
                        poiInfo.setAdminlvl1Name(mtdareaList.stream()
                                .filter(mtdarea -> "1".equals(mtdarea.getAdminLvl()) && "ENG".equals(mtdarea.getLangCode()) && "B".equals(mtdarea.getAreaType()))
                                .collect(Collectors.toList()).get(0).getAreaName());
                        // Country_Name_Eng
                        poiInfo.setAdminlvl1NameEng(mtdareaList.stream()
                                .filter(mtdarea -> "1".equals(mtdarea.getAdminLvl()) && "ENG".equals(mtdarea.getLangCode()) && "B".equals(mtdarea.getAreaType()))
                                .collect(Collectors.toList()).get(0).getAreaName());

                        // Region_Code
                        poiInfo.setAdminlvl2Code(mtdareaList.stream()
                                .filter(mtdarea -> "2".equals(mtdarea.getAdminLvl())).collect(Collectors.toList()).get(0).getGovtCode().toString());
                        // Region_Name
                        poiInfo.setAdminlvl2Name(mtdareaList.stream()
                                .filter(mtdarea -> "2".equals(mtdarea.getAdminLvl()) && "ENG".equals(mtdarea.getLangCode()) && "B".equals(mtdarea.getAreaType()))
                                .collect(Collectors.toList()).get(0).getAreaName());
                        // Region_Name_Eng
                        poiInfo.setAdminlvl2NameEng(mtdareaList.stream()
                                .filter(mtdarea -> "3".equals(mtdarea.getAdminLvl()) && "ENG".equals(mtdarea.getLangCode()) && "B".equals(mtdarea.getAreaType()))
                                .collect(Collectors.toList()).get(0).getAreaName());
                        // Prov_Code
                        poiInfo.setAdminlvl3Code(mtdareaList.stream()
                                .filter(mtdarea -> "3".equals(mtdarea.getAdminLvl())).collect(Collectors.toList()).get(0).getGovtCode().toString());
                        // Prov_Name
                        poiInfo.setAdminlvl3Name(mtdareaList.stream()
                                .filter(mtdarea -> "3".equals(mtdarea.getAdminLvl()) && "ENG".equals(mtdarea.getLangCode()) && "B".equals(mtdarea.getAreaType()))
                                .collect(Collectors.toList()).get(0).getAreaName());
                        // Prov_Name_Eng
                        poiInfo.setAdminlvl3NameEng(mtdareaList.stream()
                                .filter(mtdarea -> "3".equals(mtdarea.getAdminLvl()) && "ENG".equals(mtdarea.getLangCode()) && "B".equals(mtdarea.getAreaType()))
                                .collect(Collectors.toList()).get(0).getAreaName());

                        // City_Code
                        poiInfo.setAdminlvl4Code(mtdareaList.stream()
                                .filter(mtdarea -> "4".equals(mtdarea.getAdminLvl())).collect(Collectors.toList()).get(0).getGovtCode().toString());
                        // City_Name
                        poiInfo.setAdminlvl4Name(mtdareaList.stream()
                                .filter(mtdarea -> "4".equals(mtdarea.getAdminLvl()) && "ENG".equals(mtdarea.getLangCode()) && "B".equals(mtdarea.getAreaType()))
                                .collect(Collectors.toList()).get(0).getAreaName());
                        // City_Name_Eng
                        poiInfo.setAdminlvl4NameEng(mtdareaList.stream()
                                .filter(mtdarea -> "4".equals(mtdarea.getAdminLvl()) && "ENG".equals(mtdarea.getLangCode()) && "B".equals(mtdarea.getAreaType()))
                                .collect(Collectors.toList()).get(0).getAreaName());
                        // Ad_Code
                        poiInfo.setAdminlvl5Code(mtdareaList.stream()
                                .filter(mtdarea -> "5".equals(mtdarea.getAdminLvl())).collect(Collectors.toList()).get(0).getGovtCode().toString());
                        // Ad_Name
                        poiInfo.setAdminlvl5Name(mtdareaList.stream()
                                .filter(mtdarea -> "5".equals(mtdarea.getAdminLvl()) && "ENG".equals(mtdarea.getLangCode()) && "B".equals(mtdarea.getAreaType()))
                                .collect(Collectors.toList()).get(0).getAreaName());
                        // Ad_Name_Eng
                        poiInfo.setAdminlvl5NameEng(mtdareaList.stream()
                                .filter(mtdarea -> "5".equals(mtdarea.getAdminLvl()) && "ENG".equals(mtdarea.getLangCode()) && "B".equals(mtdarea.getAreaType()))
                                .collect(Collectors.toList()).get(0).getAreaName());
                    }
                }

                // Brand
                List<Mtdref> mtdrefList = mtdrefService.lambdaQuery().eq(Mtdref::getCode, herePhaAutosvc.getChainId().toString()).list();
                if (mtdrefList.size() > 0) {
                    poiInfo.setBrand(mtdrefList.get(0).getRefClass());
                }
                // Parent
                List<HerePhaPoiassoc> herePhaPoiassocList = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getChildId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList.size() > 0) {
                    // poiInfo.setIsParent("1");
                    List<String> parentIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList
                    ) {
                        parentIds.add(herePhaPoiassoc.getParentId().toString());
                    }
                    poiInfo.setParent(String.join(",", parentIds));
                }
                // Children
                List<HerePhaPoiassoc> herePhaPoiassocList1 = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getParentId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList1.size() > 0) {
                    // poiInfo.setIsChild("1");
                    List<String> childIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList1
                    ) {
                        childIds.add(herePhaPoiassoc.getChildId().toString());
                    }
                    poiInfo.setChildren(String.join(",", childIds));
                }
                // POI_Geo
                poiInfo.setPoiGeo(herePhaAutosvc.getGeom());
                // Update_Time
                poiInfo.setUpdateTime(LocalDateTime.now());
                // Status
                poiInfo.setStatus(0);
                poiInfoList.add(poiInfo);
            }
            this.saveOrUpdateBatch(poiInfoList);
        }

    }

    @Async("asyncTaskExecutor")
    public void poiConvertTomTom(List<PlanetOsmPoint> planetOsmPointList, String type, String source, String country, String area) {

//        List<Poi> poiInfoList = new ArrayList<>();
        if (planetOsmPointList.size() > 0) {
            // 32767/73 = 448 (POI表有73个字段)
            log.info("processing planetOsmPointList size:{},type:{}", planetOsmPointList.size(), type);
            if (planetOsmPointList.size() > 400) {
                List<List<PlanetOsmPoint>> planetOsmPointListList = ListUtil.split(planetOsmPointList, 400);
                log.info("split planetOsmPointListList size:{},type{}", planetOsmPointListList.size(), type);
                for (List<PlanetOsmPoint> planetOsmPointList1 : planetOsmPointListList
                ) {
                    List<PoiM> poiInfoList = new ArrayList<>();
                    for (PlanetOsmPoint planetOsmPoint : planetOsmPointList1) {
                        PoiM poiInfo = new PoiM();
                        poiInfo = convertPlanetOsmPointToPoi(planetOsmPoint, type, country);
                        poiInfoList.add(poiInfo);
                    }
                    if (!country.isEmpty()) {
                        DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
//                        log.info("processing country:" + CommonUtils.getDsbyCountry(country, false));
                    }
                    if (!area.isEmpty()) {
                        MybatisPlusConfig.myTableName.set("_" + area);
                    } else {
                        MybatisPlusConfig.myTableName.set("");
                    }
                    if (!source.isEmpty()) {
                        MybatisPlusConfig.myTableName.set("_" + source);
                    } else {
                        MybatisPlusConfig.myTableName.set("");
                    }
//            this.saveOrUpdateBatch(poiInfoList);
                    poiMMapper.mysqlInsertOrUpdateBath(poiInfoList);
                }
            } else {
                List<PoiM> poiInfoList = new ArrayList<>();
                for (PlanetOsmPoint planetOsmPoint : planetOsmPointList) {
                    PoiM poiInfo = new PoiM();
                    poiInfo = convertPlanetOsmPointToPoi(planetOsmPoint, type, country);
                    poiInfoList.add(poiInfo);
                }
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                    log.info("processing country:" + CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
                if (!source.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + source);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
//            this.saveOrUpdateBatch(poiInfoList);
                poiMMapper.mysqlInsertOrUpdateBath(poiInfoList);
            }
        }
    }

    public PoiM convertPlanetOsmPointToPoi(PlanetOsmPoint planetOsmPoint, String type, String country) {
        PoiM poiInfo = new PoiM();
        // POI_ID
        poiInfo.setPoiId(UUID.randomUUID().toString());
        // Source_ID
        poiInfo.setSourceId(planetOsmPoint.getOsmId().toString());
        // Kind
        poiInfo.setKind(type);
        // Kind_Code
        // Name_S
        // Alias
        poiInfo.setAlias(planetOsmPoint.getTags() != null && planetOsmPoint.getTags().containsKey("alt_name")
                ? planetOsmPoint.getTags().get("alt_name") : "");
        // Name
        poiInfo.setName(planetOsmPoint.getName());
        // Name_ENG
        // Address
        poiInfo.setAddress(planetOsmPoint.getAddrHousename());
        // street_name
        poiInfo.setStreetName(planetOsmPoint.getTags() != null && planetOsmPoint.getTags().containsKey("addr:street")
                ? planetOsmPoint.getTags().get("addr:street") : "");
        // lonlat
        double lat = 0.0;
        double lon = 0.0;
        // adapt situation in "wgs84;110.11234 35.12432"
        if (planetOsmPoint.getWay().contains(";")) {
            lon = Double.parseDouble(planetOsmPoint.getWay().split(";")[1].split(" ")[0].replace("POINT(", ""));
            lat = Double.parseDouble(planetOsmPoint.getWay().split(";")[1].split(" ")[1].replace(")", ""));
        } else {
            lon = Double.parseDouble(planetOsmPoint.getWay().split(" ")[0].replace("POINT(", ""));
            lat = Double.parseDouble(planetOsmPoint.getWay().split(" ")[1].replace(")", ""));
        }
        // lng_84
        poiInfo.setLongitude(lon);
        // Lat_84
        poiInfo.setLatitude(lat);
        // Lon_Guide_84
        poiInfo.setLonGuide(lon);
        // Lat_Guide_84
        poiInfo.setLatGuide(lat);
        // Link_ID
        // Side
        // Importance
        // VAdmin_Code
        // Zip_Code
        poiInfo.setZipCode(planetOsmPoint.getTags() != null && planetOsmPoint.getTags().containsKey("addr:postcode")
                ? planetOsmPoint.getTags().get("addr:postcode") : "");
        // Telephone
        poiInfo.setTelephone(planetOsmPoint.getTags() != null && planetOsmPoint.getTags().containsKey("phone")
                ? planetOsmPoint.getTags().get("phone") : "");
        // Tel_Type
        poiInfo.setTelType("0");
        // POI_Class
        poiInfo.setPoiClass("0");
        // Star_Rating
        // TG_Type
        poiInfo.setTgType("1");
        // Access_Flag
        // Truck_Flag
        // Food_Type
        // Airpt_Code
        // Navi_Loc
        // province
        if ("l2".equals(CommonUtils.getCityLevelByCountry(country))) {
            poiInfo.setAdminlvl1Name(planetOsmPoint.getTags() != null && planetOsmPoint.getTags().containsKey("addr:state")
                    ? planetOsmPoint.getTags().get("addr:state") : "");
            // city
            poiInfo.setAdminlvl2Name(planetOsmPoint.getTags() != null && planetOsmPoint.getTags().containsKey("addr:city")
                    ? planetOsmPoint.getTags().get("addr:city") : "");
        }
        if ("l3".equals(CommonUtils.getCityLevelByCountry(country))) {
            poiInfo.setAdminlvl2Name(planetOsmPoint.getTags() != null && planetOsmPoint.getTags().containsKey("addr:state")
                    ? planetOsmPoint.getTags().get("addr:state") : "");
            poiInfo.setAdminlvl3Name(planetOsmPoint.getTags() != null && planetOsmPoint.getTags().containsKey("addr:city")
                    ? planetOsmPoint.getTags().get("addr:city") : "");
        }
        if ("l4".equals(CommonUtils.getCityLevelByCountry(country))) {
            poiInfo.setAdminlvl2Name(planetOsmPoint.getTags() != null && planetOsmPoint.getTags().containsKey("addr:state")
                    ? planetOsmPoint.getTags().get("addr:state") : "");
            poiInfo.setAdminlvl4Name(planetOsmPoint.getTags() != null && planetOsmPoint.getTags().containsKey("addr:city")
                    ? planetOsmPoint.getTags().get("addr:city") : "");
        }
        // Brand
        poiInfo.setBrand(planetOsmPoint.getBrand());
        // Parent
        // Children
        // house number
        poiInfo.setHouseNumber(planetOsmPoint.getAddrHousenumber());
        // POI_Geo
        poiInfo.setPoiGeo(planetOsmPoint.getWay());
        // Update_Time
        poiInfo.setUpdateTime(LocalDateTime.now());
        // Status
        poiInfo.setStatus(0);
        return poiInfo;
    }

    @Async("asyncTaskExecutor")
    public void poiConvertAutosvc(List<HerePhaAutosvc> herePhaAutosvcList) {
        List<PoiM> poiInfoList = new ArrayList<>();
        if (herePhaAutosvcList.size() > 0) {
            for (HerePhaAutosvc herePhaAutosvc : herePhaAutosvcList
            ) {
                PoiM poiInfo = new PoiM();
                // POI_ID
                poiInfo.setPoiId(UUID.randomUUID().toString());
                // Source_ID
                poiInfo.setSourceId(herePhaAutosvc.getPoiId().toString());
                // Kind
                // poiInfo.setKind(herePhaAutosvc.get);
                // Kind_Code
                poiInfo.setKindCode(herePhaAutosvc.getFacType().toString());
                // Name_S
                if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                    poiInfo.setNameS(herePhaAutosvc.getPoiName());
                } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                // Alias
                {
                    poiInfo.setAlias(herePhaAutosvc.getPoiName());
                } else
                // Name
                {
                    poiInfo.setName(herePhaAutosvc.getPoiName());
                }
                // Name_ENG
                List<HerePhaPoitrans> herePhaPoitransList = herePhaPoitransService.lambdaQuery().eq(HerePhaPoitrans::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoitransList.size() > 0) {
                    // Name_S_ENG
                    if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                        poiInfo.setNameSEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                    // Alias_ENG
                    {
                        poiInfo.setAliasEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else
                    // Name_ENG
                    {
                        poiInfo.setNameEng(herePhaPoitransList.get(0).getPoiNmTr());
                    }
                }
                // Address
                poiInfo.setAddress(herePhaAutosvc.getStName() + herePhaAutosvc.getPoiStNum());
                // Address_ENG
                if (herePhaPoitransList.size() > 0) {
                    if (herePhaPoitransList.get(0).getPoiNmTr().length() > 0) {
                        poiInfo.setAddressEng(herePhaPoitransList.get(0).getStNmTr());
                    } else {
                        poiInfo.setAddressEng(herePhaPoitransList.get(0).getActaddrTr());
                    }
                }
                // lng_84
                // poiInfo.setLongitudeWgs84();
                // Lat_84
                // poiInfo.setLatitudeWgs84();
                // Lon_Guide_84
                poiInfo.setLonGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[0].replace("POINT(", "")));
                // Lat_Guide_84
                poiInfo.setLatGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[1].replace(")", "")));
                // Link_ID
                poiInfo.setLinkId(herePhaAutosvc.getLinkId().toString());
                // Side
                poiInfo.setSide(herePhaAutosvc.getPoiStSd().isEmpty() ? "N" : herePhaAutosvc.getPoiStSd());
                // Importance
                poiInfo.setImportance("Y".equals(herePhaAutosvc.getNatImport()) ? "1" : "");
                // VAdmin_Code
                poiInfo.setVadminCode(herePhaAutosvc.getVancityId().toString());
                // Zip_Code
                poiInfo.setZipCode(herePhaAutosvc.getActPostal());
                // Telephone
                poiInfo.setTelephone(herePhaAutosvc.getPhNumber());
                // Tel_Type
                poiInfo.setTelType("未调查");
                // POI_Class
                poiInfo.setPoiClass("0");
                // Star_Rating
                List<HerePhaPoiattr> herePhaPoiattrList = herePhaPoiattrService.lambdaQuery()
                        .eq(HerePhaPoiattr::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiattrList.size() > 0) {
                    HerePhaPoiattr herePhaPoiattr = herePhaPoiattrList.get(0);
                    if ("7011".equals(herePhaPoiattr.getFacType()) && "33".equals(herePhaPoiattr.getAttrType())) {
                        switch (herePhaPoiattr.getAttrValue()) {
                            case "17":
                                poiInfo.setStarRating("0");
                                break;
                            case "18":
                                poiInfo.setStarRating("1");
                                break;
                            case "1026":
                                poiInfo.setStarRating("1");
                                break;
                            case "1030":
                                poiInfo.setStarRating("8");
                                break;
                            case "1029":
                                poiInfo.setStarRating("3");
                                break;
                            case "1028":
                                poiInfo.setStarRating("4");
                                break;
                            case "1027":
                                poiInfo.setStarRating("5");
                                break;
                            default:
                                poiInfo.setStarRating("0");
                        }
                    }
                }
                // TG_Type
                poiInfo.setTgType("1");
                // Access_Flag
                // Truck_Flag
                // Food_Type
                List<HerePhaRestrnts> herePhaRestrntsList = herePhaRestrntsService.lambdaQuery()
                        .eq(HerePhaRestrnts::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaRestrntsList.size() > 0) {
                    poiInfo.setFoodType(herePhaRestrntsList.get(0).getFoodType());
                }
                // Airpt_Code
                // Navi_Loc
                // Admin releated
                List<HerePhaStreets> herePhaStreetsList = herePhaStreetsService.lambdaQuery()
                        .eq(HerePhaStreets::getLinkId, herePhaAutosvc.getLinkId()).list();
                if (herePhaStreetsList.size() > 0) {
                    List<Mtdarea> mtdareaList = new ArrayList<>();
                    if ("R".equals(herePhaAutosvc.getPoiStSd())) {
                        mtdareaList = mtdareaService.lambdaQuery().eq(Mtdarea::getAreaId, herePhaStreetsList.get(0).getrAreaId()).list();
                    } else if ("L".equals(herePhaAutosvc.getPoiStSd())) {
                        mtdareaList = mtdareaService.lambdaQuery().eq(Mtdarea::getAreaId, herePhaStreetsList.get(0).getlAreaId()).list();
                    }
                    if (mtdareaList.size() > 0) {
                        // Country_Code
                        poiInfo.setAdminlvl1Code(mtdareaList.stream()
                                .filter(mtdarea -> "1".equals(mtdarea.getAdminLvl())).collect(Collectors.toList()).get(0).getGovtCode().toString());
                        // Country_Name
                        poiInfo.setAdminlvl1Name(mtdareaList.stream()
                                .filter(mtdarea -> "1".equals(mtdarea.getAdminLvl()) && "ENG".equals(mtdarea.getLangCode()) && "B".equals(mtdarea.getAreaType()))
                                .collect(Collectors.toList()).get(0).getAreaName());
                        // Country_Name_Eng
                        poiInfo.setAdminlvl1NameEng(mtdareaList.stream()
                                .filter(mtdarea -> "1".equals(mtdarea.getAdminLvl()) && "ENG".equals(mtdarea.getLangCode()) && "B".equals(mtdarea.getAreaType()))
                                .collect(Collectors.toList()).get(0).getAreaName());

                        // Region_Code
                        poiInfo.setAdminlvl2Code(mtdareaList.stream()
                                .filter(mtdarea -> "2".equals(mtdarea.getAdminLvl())).collect(Collectors.toList()).get(0).getGovtCode().toString());
                        // Region_Name
                        poiInfo.setAdminlvl2Name(mtdareaList.stream()
                                .filter(mtdarea -> "2".equals(mtdarea.getAdminLvl()) && "ENG".equals(mtdarea.getLangCode()) && "B".equals(mtdarea.getAreaType()))
                                .collect(Collectors.toList()).get(0).getAreaName());
                        // Region_Name_Eng
                        poiInfo.setAdminlvl2NameEng(mtdareaList.stream()
                                .filter(mtdarea -> "3".equals(mtdarea.getAdminLvl()) && "ENG".equals(mtdarea.getLangCode()) && "B".equals(mtdarea.getAreaType()))
                                .collect(Collectors.toList()).get(0).getAreaName());
                        // Prov_Code
                        poiInfo.setAdminlvl3Code(mtdareaList.stream()
                                .filter(mtdarea -> "3".equals(mtdarea.getAdminLvl())).collect(Collectors.toList()).get(0).getGovtCode().toString());
                        // Prov_Name
                        poiInfo.setAdminlvl3Name(mtdareaList.stream()
                                .filter(mtdarea -> "3".equals(mtdarea.getAdminLvl()) && "ENG".equals(mtdarea.getLangCode()) && "B".equals(mtdarea.getAreaType()))
                                .collect(Collectors.toList()).get(0).getAreaName());
                        // Prov_Name_Eng
                        poiInfo.setAdminlvl3NameEng(mtdareaList.stream()
                                .filter(mtdarea -> "3".equals(mtdarea.getAdminLvl()) && "ENG".equals(mtdarea.getLangCode()) && "B".equals(mtdarea.getAreaType()))
                                .collect(Collectors.toList()).get(0).getAreaName());

                        // City_Code
                        poiInfo.setAdminlvl4Code(mtdareaList.stream()
                                .filter(mtdarea -> "4".equals(mtdarea.getAdminLvl())).collect(Collectors.toList()).get(0).getGovtCode().toString());
                        // City_Name
                        poiInfo.setAdminlvl4Name(mtdareaList.stream()
                                .filter(mtdarea -> "4".equals(mtdarea.getAdminLvl()) && "ENG".equals(mtdarea.getLangCode()) && "B".equals(mtdarea.getAreaType()))
                                .collect(Collectors.toList()).get(0).getAreaName());
                        // City_Name_Eng
                        poiInfo.setAdminlvl4NameEng(mtdareaList.stream()
                                .filter(mtdarea -> "4".equals(mtdarea.getAdminLvl()) && "ENG".equals(mtdarea.getLangCode()) && "B".equals(mtdarea.getAreaType()))
                                .collect(Collectors.toList()).get(0).getAreaName());
                        // Ad_Code
                        poiInfo.setAdminlvl5Code(mtdareaList.stream()
                                .filter(mtdarea -> "5".equals(mtdarea.getAdminLvl())).collect(Collectors.toList()).get(0).getGovtCode().toString());
                        // Ad_Name
                        poiInfo.setAdminlvl5Name(mtdareaList.stream()
                                .filter(mtdarea -> "5".equals(mtdarea.getAdminLvl()) && "ENG".equals(mtdarea.getLangCode()) && "B".equals(mtdarea.getAreaType()))
                                .collect(Collectors.toList()).get(0).getAreaName());
                        // Ad_Name_Eng
                        poiInfo.setAdminlvl5NameEng(mtdareaList.stream()
                                .filter(mtdarea -> "5".equals(mtdarea.getAdminLvl()) && "ENG".equals(mtdarea.getLangCode()) && "B".equals(mtdarea.getAreaType()))
                                .collect(Collectors.toList()).get(0).getAreaName());
                    }
                }

                // Brand
                List<Mtdref> mtdrefList = mtdrefService.lambdaQuery().eq(Mtdref::getCode, herePhaAutosvc.getChainId().toString()).list();
                if (mtdrefList.size() > 0) {
                    poiInfo.setBrand(mtdrefList.get(0).getRefClass());
                }
                // Parent
                List<HerePhaPoiassoc> herePhaPoiassocList = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getChildId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList.size() > 0) {
                    // poiInfo.setIsParent("1");
                    List<String> parentIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList
                    ) {
                        parentIds.add(herePhaPoiassoc.getParentId().toString());
                    }
                    poiInfo.setParent(String.join(",", parentIds));
                }
                // Children
                List<HerePhaPoiassoc> herePhaPoiassocList1 = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getParentId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList1.size() > 0) {
                    // poiInfo.setIsChild("1");
                    List<String> childIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList1
                    ) {
                        childIds.add(herePhaPoiassoc.getChildId().toString());
                    }
                    poiInfo.setChildren(String.join(",", childIds));
                }
                // POI_Geo
                poiInfo.setPoiGeo(herePhaAutosvc.getGeom());
                // Update_Time
                poiInfo.setUpdateTime(LocalDateTime.now());
                // Status
                poiInfo.setStatus(0);
                poiInfoList.add(poiInfo);
            }
            this.saveOrUpdateBatch(poiInfoList);
        }
    }
//    "here_pha_commsvc", "here_pha_eduinsts", "here_pha_entertn",
//            "here_pha_fininsts", "here_pha_hamlet", "here_pha_hospital", "here_pha_misccategories", "here_pha_namedplc", "here_pha_parking",
//            "here_pha_parkrec", "here_pha_poiassoc", "here_pha_poiattr", "here_pha_poicontact", "here_pha_poifileassoc", "here_pha_poirelat",
//            "here_pha_poitrans", "here_pha_restrnts", "here_pha_shopping", "here_pha_transhubs", "here_pha_travdest"


    @Async("asyncTaskExecutor")
    public void poiConvertBusiness(List<HerePhaBusiness> herePhaAutosvcList) {
        List<PoiM> poiInfoList = new ArrayList<>();
        if (herePhaAutosvcList.size() > 0) {
            for (HerePhaBusiness herePhaAutosvc : herePhaAutosvcList
            ) {
                PoiM poiInfo = new PoiM();
                // POI_ID
                poiInfo.setPoiId(UUID.randomUUID().toString());
                // Source_ID
                poiInfo.setSourceId(herePhaAutosvc.getPoiId().toString());
                // Kind
                // poiInfo.setKind(herePhaAutosvc.get);
                // Kind_Code
                poiInfo.setKindCode(herePhaAutosvc.getFacType().toString());
                // Name_S
                if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                    poiInfo.setNameS(herePhaAutosvc.getPoiName());
                } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                // Alias
                {
                    poiInfo.setAlias(herePhaAutosvc.getPoiName());
                } else
                // Name
                {
                    poiInfo.setName(herePhaAutosvc.getPoiName());
                }
                // Name_ENG
                List<HerePhaPoitrans> herePhaPoitransList = herePhaPoitransService.lambdaQuery().eq(HerePhaPoitrans::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoitransList.size() > 0) {
                    // Name_S_ENG
                    if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                        poiInfo.setNameSEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                    // Alias_ENG
                    {
                        poiInfo.setAliasEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else
                    // Name_ENG
                    {
                        poiInfo.setNameEng(herePhaPoitransList.get(0).getPoiNmTr());
                    }
                }
                // Address
                poiInfo.setAddress(herePhaAutosvc.getStName() + herePhaAutosvc.getPoiStNum());
                // Address_ENG
                poiInfo.setAddressEng(herePhaPoitransList.size() > 0 ? herePhaPoitransList.get(0).getActaddrTr() : "");
                // lng_84
                // poiInfo.setLongitudeWgs84();
                // Lat_84
                // poiInfo.setLatitudeWgs84();
                // Lon_Guide_84
                poiInfo.setLonGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[0].replace("POINT(", "")));
                // Lat_Guide_84
                poiInfo.setLatGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[1].replace(")", "")));
                // Link_ID
                poiInfo.setLinkId(herePhaAutosvc.getLinkId().toString());
                // Side
                poiInfo.setSide(herePhaAutosvc.getPoiStSd().isEmpty() ? "N" : herePhaAutosvc.getPoiStSd());
                // Importance
                poiInfo.setImportance("Y".equals(herePhaAutosvc.getNatImport()) ? "1" : "");
                // VAdmin_Code
                poiInfo.setVadminCode(herePhaAutosvc.getVancityId().toString());
                // Zip_Code
                poiInfo.setZipCode(herePhaAutosvc.getActPostal());
                // Telephone
                poiInfo.setTelephone(herePhaAutosvc.getPhNumber());
                // Tel_Type
                // poiInfo.setTelType(herePhaAutosvc.get);
                // Star_Rating
                List<HerePhaPoiattr> herePhaPoiattrList = herePhaPoiattrService.lambdaQuery()
                        .eq(HerePhaPoiattr::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiattrList.size() > 0) {
                    HerePhaPoiattr herePhaPoiattr = herePhaPoiattrList.get(0);
                    if ("7011".equals(herePhaPoiattr.getFacType()) && "33".equals(herePhaPoiattr.getAttrType())) {
                        switch (herePhaPoiattr.getAttrValue()) {
                            case "17":
                                poiInfo.setStarRating("0");
                                break;
                            case "18":
                                poiInfo.setStarRating("1");
                                break;
                            case "1026":
                                poiInfo.setStarRating("1");
                                break;
                            case "1030":
                                poiInfo.setStarRating("8");
                                break;
                            case "1029":
                                poiInfo.setStarRating("3");
                                break;
                            case "1028":
                                poiInfo.setStarRating("4");
                                break;
                            case "1027":
                                poiInfo.setStarRating("5");
                                break;
                            default:
                                poiInfo.setStarRating("0");
                        }
                    }
                }
                // Truck_Flag
                // Food_Type
                List<HerePhaRestrnts> herePhaRestrntsList = herePhaRestrntsService.lambdaQuery()
                        .eq(HerePhaRestrnts::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaRestrntsList.size() > 0) {
                    poiInfo.setFoodType(herePhaRestrntsList.get(0).getFoodType());
                }
                // Airpt_Code
                // Brand
                List<Mtdref> mtdrefList = mtdrefService.lambdaQuery().eq(Mtdref::getCode, herePhaAutosvc.getChainId()).list();
                if (mtdrefList.size() > 0) {
                    poiInfo.setBrand(mtdrefList.get(0).getRefClass());
                }
                // Parent
                List<HerePhaPoiassoc> herePhaPoiassocList = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getChildId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList.size() > 0) {
                    // poiInfo.setIsParent("1");
                    List<String> parentIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList
                    ) {
                        parentIds.add(herePhaPoiassoc.getParentId().toString());
                    }
                    poiInfo.setParent(String.join(",", parentIds));
                }
                // Children
                List<HerePhaPoiassoc> herePhaPoiassocList1 = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getParentId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList1.size() > 0) {
                    // poiInfo.setIsChild("1");
                    List<String> childIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList1
                    ) {
                        childIds.add(herePhaPoiassoc.getChildId().toString());
                    }
                    poiInfo.setChildren(String.join(",", childIds));
                }
                // POI_Geo
                poiInfo.setPoiGeo(herePhaAutosvc.getGeom());
                // Update_Time
                poiInfo.setUpdateTime(LocalDateTime.now());
                poiInfoList.add(poiInfo);
            }
            this.saveOrUpdateBatch(poiInfoList);
        }
    }

    //    "here_pha_commsvc", "here_pha_eduinsts", "here_pha_entertn",
//            "here_pha_fininsts", "here_pha_hamlet", "here_pha_hospital", "here_pha_misccategories", "here_pha_namedplc", "here_pha_parking",
//            "here_pha_parkrec", "here_pha_poiassoc", "here_pha_poiattr", "here_pha_poicontact", "here_pha_poifileassoc", "here_pha_poirelat",
//            "here_pha_poitrans", "here_pha_restrnts", "here_pha_shopping", "here_pha_transhubs", "here_pha_travdest"


    @Async("asyncTaskExecutor")
    public void poiConvertCommsvc(List<HerePhaCommsvc> herePhaAutosvcList) {
        List<PoiM> poiInfoList = new ArrayList<>();
        if (herePhaAutosvcList.size() > 0) {
            for (HerePhaCommsvc herePhaAutosvc : herePhaAutosvcList
            ) {
                PoiM poiInfo = new PoiM();
                // POI_ID
                poiInfo.setPoiId(UUID.randomUUID().toString());
                // Source_ID
                poiInfo.setSourceId(herePhaAutosvc.getPoiId().toString());
                // Kind
                // poiInfo.setKind(herePhaAutosvc.get);
                // Kind_Code
                poiInfo.setKindCode(herePhaAutosvc.getFacType().toString());
                // Name_S
                if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                    poiInfo.setNameS(herePhaAutosvc.getPoiName());
                } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                // Alias
                {
                    poiInfo.setAlias(herePhaAutosvc.getPoiName());
                } else
                // Name
                {
                    poiInfo.setName(herePhaAutosvc.getPoiName());
                }
                // Name_ENG
                List<HerePhaPoitrans> herePhaPoitransList = herePhaPoitransService.lambdaQuery().eq(HerePhaPoitrans::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoitransList.size() > 0) {
                    // Name_S_ENG
                    if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                        poiInfo.setNameSEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                    // Alias_ENG
                    {
                        poiInfo.setAliasEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else
                    // Name_ENG
                    {
                        poiInfo.setNameEng(herePhaPoitransList.get(0).getPoiNmTr());
                    }
                }
                // Address
                poiInfo.setAddress(herePhaAutosvc.getStName() + herePhaAutosvc.getPoiStNum());
                // Address_ENG
                poiInfo.setAddressEng(herePhaPoitransList.size() > 0 ? herePhaPoitransList.get(0).getActaddrTr() : "");
                // lng_84
                // poiInfo.setLongitudeWgs84();
                // Lat_84
                // poiInfo.setLatitudeWgs84();
                // Lon_Guide_84
                poiInfo.setLonGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[0].replace("POINT(", "")));
                // Lat_Guide_84
                poiInfo.setLatGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[1].replace(")", "")));
                // Link_ID
                poiInfo.setLinkId(herePhaAutosvc.getLinkId().toString());
                // Side
                poiInfo.setSide(herePhaAutosvc.getPoiStSd().isEmpty() ? "N" : herePhaAutosvc.getPoiStSd());
                // Importance
                poiInfo.setImportance("Y".equals(herePhaAutosvc.getNatImport()) ? "1" : "");
                // VAdmin_Code
                poiInfo.setVadminCode(herePhaAutosvc.getVancityId().toString());
                // Zip_Code
                poiInfo.setZipCode(herePhaAutosvc.getActPostal());
                // Telephone
                poiInfo.setTelephone(herePhaAutosvc.getPhNumber());
                // Tel_Type
                // poiInfo.setTelType(herePhaAutosvc.get);
                // Star_Rating
                List<HerePhaPoiattr> herePhaPoiattrList = herePhaPoiattrService.lambdaQuery()
                        .eq(HerePhaPoiattr::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiattrList.size() > 0) {
                    HerePhaPoiattr herePhaPoiattr = herePhaPoiattrList.get(0);
                    if ("7011".equals(herePhaPoiattr.getFacType()) && "33".equals(herePhaPoiattr.getAttrType())) {
                        switch (herePhaPoiattr.getAttrValue()) {
                            case "17":
                                poiInfo.setStarRating("0");
                                break;
                            case "18":
                                poiInfo.setStarRating("1");
                                break;
                            case "1026":
                                poiInfo.setStarRating("1");
                                break;
                            case "1030":
                                poiInfo.setStarRating("8");
                                break;
                            case "1029":
                                poiInfo.setStarRating("3");
                                break;
                            case "1028":
                                poiInfo.setStarRating("4");
                                break;
                            case "1027":
                                poiInfo.setStarRating("5");
                                break;
                            default:
                                poiInfo.setStarRating("0");
                        }
                    }
                }
                // Truck_Flag
                // Food_Type
                List<HerePhaRestrnts> herePhaRestrntsList = herePhaRestrntsService.lambdaQuery()
                        .eq(HerePhaRestrnts::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaRestrntsList.size() > 0) {
                    poiInfo.setFoodType(herePhaRestrntsList.get(0).getFoodType());
                }
                // Airpt_Code
                // Brand
                List<Mtdref> mtdrefList = mtdrefService.lambdaQuery().eq(Mtdref::getCode, herePhaAutosvc.getChainId()).list();
                if (mtdrefList.size() > 0) {
                    poiInfo.setBrand(mtdrefList.get(0).getRefClass());
                }
                // Parent
                List<HerePhaPoiassoc> herePhaPoiassocList = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getChildId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList.size() > 0) {
                    // poiInfo.setIsParent("1");
                    List<String> parentIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList
                    ) {
                        parentIds.add(herePhaPoiassoc.getParentId().toString());
                    }
                    poiInfo.setParent(String.join(",", parentIds));
                }
                // Children
                List<HerePhaPoiassoc> herePhaPoiassocList1 = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getParentId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList1.size() > 0) {
                    // poiInfo.setIsChild("1");
                    List<String> childIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList1
                    ) {
                        childIds.add(herePhaPoiassoc.getChildId().toString());
                    }
                    poiInfo.setChildren(String.join(",", childIds));
                }
                // POI_Geo
                poiInfo.setPoiGeo(herePhaAutosvc.getGeom());
                // Update_Time
                poiInfo.setUpdateTime(LocalDateTime.now());
                poiInfoList.add(poiInfo);
            }
            this.saveOrUpdateBatch(poiInfoList);
        }
    }

    //    "here_pha_eduinsts", "here_pha_entertn",
//            "here_pha_fininsts", "here_pha_hamlet", "here_pha_hospital", "here_pha_misccategories", "here_pha_namedplc", "here_pha_parking",
//            "here_pha_parkrec", "here_pha_poiassoc", "here_pha_poiattr", "here_pha_poicontact", "here_pha_poifileassoc", "here_pha_poirelat",
//            "here_pha_poitrans", "here_pha_restrnts", "here_pha_shopping", "here_pha_transhubs", "here_pha_travdest"


    @Async()
    public void poiConvertEduinsts(List<HerePhaEduinsts> herePhaAutosvcList) {
        List<PoiM> poiInfoList = new ArrayList<>();
        if (herePhaAutosvcList.size() > 0) {
            for (HerePhaEduinsts herePhaAutosvc : herePhaAutosvcList
            ) {
                PoiM poiInfo = new PoiM();
                // POI_ID
                poiInfo.setPoiId(UUID.randomUUID().toString());
                // Source_ID
                poiInfo.setSourceId(herePhaAutosvc.getPoiId().toString());
                // Kind
                // poiInfo.setKind(herePhaAutosvc.get);
                // Kind_Code
                poiInfo.setKindCode(herePhaAutosvc.getFacType());
                // Name_S
                if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                    poiInfo.setNameS(herePhaAutosvc.getPoiName());
                } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                // Alias
                {
                    poiInfo.setAlias(herePhaAutosvc.getPoiName());
                } else
                // Name
                {
                    poiInfo.setName(herePhaAutosvc.getPoiName());
                }
                // Name_ENG
                List<HerePhaPoitrans> herePhaPoitransList = herePhaPoitransService.lambdaQuery().eq(HerePhaPoitrans::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoitransList.size() > 0) {
                    // Name_S_ENG
                    if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                        poiInfo.setNameSEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                    // Alias_ENG
                    {
                        poiInfo.setAliasEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else
                    // Name_ENG
                    {
                        poiInfo.setNameEng(herePhaPoitransList.get(0).getPoiNmTr());
                    }
                }
                // Address
                poiInfo.setAddress(herePhaAutosvc.getStName() + herePhaAutosvc.getPoiStNum());
                // Address_ENG
                poiInfo.setAddressEng(herePhaPoitransList.size() > 0 ? herePhaPoitransList.get(0).getActaddrTr() : "");
                // lng_84
                // poiInfo.setLongitudeWgs84();
                // Lat_84
                // poiInfo.setLatitudeWgs84();
                // Lon_Guide_84
                poiInfo.setLonGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[0].replace("POINT(", "")));
                // Lat_Guide_84
                poiInfo.setLatGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[1].replace(")", "")));
                // Link_ID
                poiInfo.setLinkId(herePhaAutosvc.getLinkId());
                // Side
                poiInfo.setSide(herePhaAutosvc.getPoiStSd().isEmpty() ? "N" : herePhaAutosvc.getPoiStSd());
                // Importance
                poiInfo.setImportance("Y".equals(herePhaAutosvc.getNatImport()) ? "1" : "");
                // VAdmin_Code
                poiInfo.setVadminCode(herePhaAutosvc.getVancityId());
                // Zip_Code
                poiInfo.setZipCode(herePhaAutosvc.getActPostal());
                // Telephone
                poiInfo.setTelephone(herePhaAutosvc.getPhNumber());
                // Tel_Type
                // poiInfo.setTelType(herePhaAutosvc.get);
                // Star_Rating
                List<HerePhaPoiattr> herePhaPoiattrList = herePhaPoiattrService.lambdaQuery()
                        .eq(HerePhaPoiattr::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiattrList.size() > 0) {
                    HerePhaPoiattr herePhaPoiattr = herePhaPoiattrList.get(0);
                    if ("7011".equals(herePhaPoiattr.getFacType()) && "33".equals(herePhaPoiattr.getAttrType())) {
                        switch (herePhaPoiattr.getAttrValue()) {
                            case "17":
                                poiInfo.setStarRating("0");
                                break;
                            case "18":
                                poiInfo.setStarRating("1");
                                break;
                            case "1026":
                                poiInfo.setStarRating("1");
                                break;
                            case "1030":
                                poiInfo.setStarRating("8");
                                break;
                            case "1029":
                                poiInfo.setStarRating("3");
                                break;
                            case "1028":
                                poiInfo.setStarRating("4");
                                break;
                            case "1027":
                                poiInfo.setStarRating("5");
                                break;
                            default:
                                poiInfo.setStarRating("0");
                        }
                    }
                }
                // Truck_Flag
                // Food_Type
                List<HerePhaRestrnts> herePhaRestrntsList = herePhaRestrntsService.lambdaQuery()
                        .eq(HerePhaRestrnts::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaRestrntsList.size() > 0) {
                    poiInfo.setFoodType(herePhaRestrntsList.get(0).getFoodType());
                }
                // Airpt_Code
                // Brand
                List<Mtdref> mtdrefList = mtdrefService.lambdaQuery().eq(Mtdref::getCode, herePhaAutosvc.getChainId()).list();
                if (mtdrefList.size() > 0) {
                    poiInfo.setBrand(mtdrefList.get(0).getRefClass());
                }
                // Parent
                List<HerePhaPoiassoc> herePhaPoiassocList = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getChildId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList.size() > 0) {
                    // poiInfo.setIsParent("1");
                    List<String> parentIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList
                    ) {
                        parentIds.add(herePhaPoiassoc.getParentId().toString());
                    }
                    poiInfo.setParent(String.join(",", parentIds));
                }
                // Children
                List<HerePhaPoiassoc> herePhaPoiassocList1 = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getParentId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList1.size() > 0) {
                    // poiInfo.setIsChild("1");
                    List<String> childIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList1
                    ) {
                        childIds.add(herePhaPoiassoc.getChildId().toString());
                    }
                    poiInfo.setChildren(String.join(",", childIds));
                }
                // POI_Geo
                poiInfo.setPoiGeo(herePhaAutosvc.getGeom());
                // Update_Time
                poiInfo.setUpdateTime(LocalDateTime.now());
                poiInfoList.add(poiInfo);
            }
            this.saveOrUpdateBatch(poiInfoList);
        }
    }

//    , "here_pha_entertn",
//            "here_pha_fininsts", "here_pha_hamlet", "here_pha_hospital", "here_pha_misccategories", "here_pha_namedplc", "here_pha_parking",
//            "here_pha_parkrec", "here_pha_poiassoc", "here_pha_poiattr", "here_pha_poicontact", "here_pha_poifileassoc", "here_pha_poirelat",
//            "here_pha_poitrans", "here_pha_restrnts", "here_pha_shopping", "here_pha_transhubs", "here_pha_travdest"


    @Async()
    public void poiConvertEntertn(List<HerePhaEntertn> herePhaAutosvcList) {
        List<PoiM> poiInfoList = new ArrayList<>();
        if (herePhaAutosvcList.size() > 0) {
            for (HerePhaEntertn herePhaAutosvc : herePhaAutosvcList
            ) {
                PoiM poiInfo = new PoiM();
                // POI_ID
                poiInfo.setPoiId(UUID.randomUUID().toString());
                // Source_ID
                poiInfo.setSourceId(herePhaAutosvc.getPoiId().toString());
                // Kind
                // poiInfo.setKind(herePhaAutosvc.get);
                // Kind_Code
                poiInfo.setKindCode(herePhaAutosvc.getFacType());
                // Name_S
                if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                    poiInfo.setNameS(herePhaAutosvc.getPoiName());
                } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                // Alias
                {
                    poiInfo.setAlias(herePhaAutosvc.getPoiName());
                } else
                // Name
                {
                    poiInfo.setName(herePhaAutosvc.getPoiName());
                }
                // Name_ENG
                List<HerePhaPoitrans> herePhaPoitransList = herePhaPoitransService.lambdaQuery().eq(HerePhaPoitrans::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoitransList.size() > 0) {
                    // Name_S_ENG
                    if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                        poiInfo.setNameSEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                    // Alias_ENG
                    {
                        poiInfo.setAliasEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else
                    // Name_ENG
                    {
                        poiInfo.setNameEng(herePhaPoitransList.get(0).getPoiNmTr());
                    }
                }
                // Address
                poiInfo.setAddress(herePhaAutosvc.getStName() + herePhaAutosvc.getPoiStNum());
                // Address_ENG
                poiInfo.setAddressEng(herePhaPoitransList.size() > 0 ? herePhaPoitransList.get(0).getActaddrTr() : "");
                // lng_84
                // poiInfo.setLongitudeWgs84();
                // Lat_84
                // poiInfo.setLatitudeWgs84();
                // Lon_Guide_84
                poiInfo.setLonGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[0].replace("POINT(", "")));
                // Lat_Guide_84
                poiInfo.setLatGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[1].replace(")", "")));
                // Link_ID
                poiInfo.setLinkId(herePhaAutosvc.getLinkId());
                // Side
                poiInfo.setSide(herePhaAutosvc.getPoiStSd().isEmpty() ? "N" : herePhaAutosvc.getPoiStSd());
                // Importance
                poiInfo.setImportance("Y".equals(herePhaAutosvc.getNatImport()) ? "1" : "");
                // VAdmin_Code
                poiInfo.setVadminCode(herePhaAutosvc.getVancityId());
                // Zip_Code
                poiInfo.setZipCode(herePhaAutosvc.getActPostal());
                // Telephone
                poiInfo.setTelephone(herePhaAutosvc.getPhNumber());
                // Tel_Type
                // poiInfo.setTelType(herePhaAutosvc.get);
                // Star_Rating
                List<HerePhaPoiattr> herePhaPoiattrList = herePhaPoiattrService.lambdaQuery()
                        .eq(HerePhaPoiattr::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiattrList.size() > 0) {
                    HerePhaPoiattr herePhaPoiattr = herePhaPoiattrList.get(0);
                    if ("7011".equals(herePhaPoiattr.getFacType()) && "33".equals(herePhaPoiattr.getAttrType())) {
                        switch (herePhaPoiattr.getAttrValue()) {
                            case "17":
                                poiInfo.setStarRating("0");
                                break;
                            case "18":
                                poiInfo.setStarRating("1");
                                break;
                            case "1026":
                                poiInfo.setStarRating("1");
                                break;
                            case "1030":
                                poiInfo.setStarRating("8");
                                break;
                            case "1029":
                                poiInfo.setStarRating("3");
                                break;
                            case "1028":
                                poiInfo.setStarRating("4");
                                break;
                            case "1027":
                                poiInfo.setStarRating("5");
                                break;
                            default:
                                poiInfo.setStarRating("0");
                        }
                    }
                }
                // Truck_Flag
                // Food_Type
                List<HerePhaRestrnts> herePhaRestrntsList = herePhaRestrntsService.lambdaQuery()
                        .eq(HerePhaRestrnts::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaRestrntsList.size() > 0) {
                    poiInfo.setFoodType(herePhaRestrntsList.get(0).getFoodType());
                }
                // Airpt_Code
                // Brand
                List<Mtdref> mtdrefList = mtdrefService.lambdaQuery().eq(Mtdref::getCode, herePhaAutosvc.getChainId()).list();
                if (mtdrefList.size() > 0) {
                    poiInfo.setBrand(mtdrefList.get(0).getRefClass());
                }
                // Parent
                List<HerePhaPoiassoc> herePhaPoiassocList = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getChildId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList.size() > 0) {
                    // poiInfo.setIsParent("1");
                    List<String> parentIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList
                    ) {
                        parentIds.add(herePhaPoiassoc.getParentId().toString());
                    }
                    poiInfo.setParent(String.join(",", parentIds));
                }
                // Children
                List<HerePhaPoiassoc> herePhaPoiassocList1 = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getParentId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList1.size() > 0) {
                    // poiInfo.setIsChild("1");
                    List<String> childIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList1
                    ) {
                        childIds.add(herePhaPoiassoc.getChildId().toString());
                    }
                    poiInfo.setChildren(String.join(",", childIds));
                }
                // POI_Geo
                poiInfo.setPoiGeo(herePhaAutosvc.getGeom());
                // Update_Time
                poiInfo.setUpdateTime(LocalDateTime.now());
                poiInfoList.add(poiInfo);
            }
            this.saveOrUpdateBatch(poiInfoList);
        }
    }

    //            "here_pha_fininsts", "here_pha_hamlet", "here_pha_hospital", "here_pha_misccategories", "here_pha_namedplc", "here_pha_parking",
//            "here_pha_parkrec", "here_pha_poiassoc", "here_pha_poiattr", "here_pha_poicontact", "here_pha_poifileassoc", "here_pha_poirelat",
//            "here_pha_poitrans", "here_pha_restrnts", "here_pha_shopping", "here_pha_transhubs", "here_pha_travdest"
    @Async()
    public void poiConvertFininsts(List<HerePhaFininsts> herePhaAutosvcList) {
        List<PoiM> poiInfoList = new ArrayList<>();
        if (herePhaAutosvcList.size() > 0) {
            for (HerePhaFininsts herePhaAutosvc : herePhaAutosvcList
            ) {
                PoiM poiInfo = new PoiM();
                // POI_ID
                poiInfo.setPoiId(UUID.randomUUID().toString());
                // Source_ID
                poiInfo.setSourceId(herePhaAutosvc.getPoiId().toString());
                // Kind
                // poiInfo.setKind(herePhaAutosvc.get);
                // Kind_Code
                poiInfo.setKindCode(herePhaAutosvc.getFacType());
                // Name_S
                if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                    poiInfo.setNameS(herePhaAutosvc.getPoiName());
                } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                // Alias
                {
                    poiInfo.setAlias(herePhaAutosvc.getPoiName());
                } else
                // Name
                {
                    poiInfo.setName(herePhaAutosvc.getPoiName());
                }
                // Name_ENG
                List<HerePhaPoitrans> herePhaPoitransList = herePhaPoitransService.lambdaQuery().eq(HerePhaPoitrans::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoitransList.size() > 0) {
                    // Name_S_ENG
                    if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                        poiInfo.setNameSEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                    // Alias_ENG
                    {
                        poiInfo.setAliasEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else
                    // Name_ENG
                    {
                        poiInfo.setNameEng(herePhaPoitransList.get(0).getPoiNmTr());
                    }
                }
                // Address
                poiInfo.setAddress(herePhaAutosvc.getStName() + herePhaAutosvc.getPoiStNum());
                // Address_ENG
                poiInfo.setAddressEng(herePhaPoitransList.size() > 0 ? herePhaPoitransList.get(0).getActaddrTr() : "");
                // lng_84
                // poiInfo.setLongitudeWgs84();
                // Lat_84
                // poiInfo.setLatitudeWgs84();
                // Lon_Guide_84
                poiInfo.setLonGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[0].replace("POINT(", "")));
                // Lat_Guide_84
                poiInfo.setLatGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[1].replace(")", "")));
                // Link_ID
                poiInfo.setLinkId(herePhaAutosvc.getLinkId());
                // Side
                poiInfo.setSide(herePhaAutosvc.getPoiStSd().isEmpty() ? "N" : herePhaAutosvc.getPoiStSd());
                // Importance
                poiInfo.setImportance("Y".equals(herePhaAutosvc.getNatImport()) ? "1" : "");
                // VAdmin_Code
                poiInfo.setVadminCode(herePhaAutosvc.getVancityId());
                // Zip_Code
                poiInfo.setZipCode(herePhaAutosvc.getActPostal());
                // Telephone
                poiInfo.setTelephone(herePhaAutosvc.getPhNumber());
                // Tel_Type
                // poiInfo.setTelType(herePhaAutosvc.get);
                // Star_Rating
                List<HerePhaPoiattr> herePhaPoiattrList = herePhaPoiattrService.lambdaQuery()
                        .eq(HerePhaPoiattr::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiattrList.size() > 0) {
                    HerePhaPoiattr herePhaPoiattr = herePhaPoiattrList.get(0);
                    if ("7011".equals(herePhaPoiattr.getFacType()) && "33".equals(herePhaPoiattr.getAttrType())) {
                        switch (herePhaPoiattr.getAttrValue()) {
                            case "17":
                                poiInfo.setStarRating("0");
                                break;
                            case "18":
                                poiInfo.setStarRating("1");
                                break;
                            case "1026":
                                poiInfo.setStarRating("1");
                                break;
                            case "1030":
                                poiInfo.setStarRating("8");
                                break;
                            case "1029":
                                poiInfo.setStarRating("3");
                                break;
                            case "1028":
                                poiInfo.setStarRating("4");
                                break;
                            case "1027":
                                poiInfo.setStarRating("5");
                                break;
                            default:
                                poiInfo.setStarRating("0");
                        }
                    }
                }
                // Truck_Flag
                // Food_Type
                List<HerePhaRestrnts> herePhaRestrntsList = herePhaRestrntsService.lambdaQuery()
                        .eq(HerePhaRestrnts::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaRestrntsList.size() > 0) {
                    poiInfo.setFoodType(herePhaRestrntsList.get(0).getFoodType());
                }
                // Airpt_Code
                // Brand
                List<Mtdref> mtdrefList = mtdrefService.lambdaQuery().eq(Mtdref::getCode, herePhaAutosvc.getChainId()).list();
                if (mtdrefList.size() > 0) {
                    poiInfo.setBrand(mtdrefList.get(0).getRefClass());
                }
                // Parent
                List<HerePhaPoiassoc> herePhaPoiassocList = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getChildId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList.size() > 0) {
                    // poiInfo.setIsParent("1");
                    List<String> parentIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList
                    ) {
                        parentIds.add(herePhaPoiassoc.getParentId().toString());
                    }
                    poiInfo.setParent(String.join(",", parentIds));
                }
                // Children
                List<HerePhaPoiassoc> herePhaPoiassocList1 = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getParentId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList1.size() > 0) {
                    // poiInfo.setIsChild("1");
                    List<String> childIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList1
                    ) {
                        childIds.add(herePhaPoiassoc.getChildId().toString());
                    }
                    poiInfo.setChildren(String.join(",", childIds));
                }
                // POI_Geo
                poiInfo.setPoiGeo(herePhaAutosvc.getGeom());
                // Update_Time
                poiInfo.setUpdateTime(LocalDateTime.now());
                poiInfoList.add(poiInfo);
            }
            this.saveOrUpdateBatch(poiInfoList);
        }
    }

    //   "here_pha_hamlet", "here_pha_hospital", "here_pha_misccategories", "here_pha_namedplc", "here_pha_parking",
    //            "here_pha_parkrec", "here_pha_poiassoc", "here_pha_poiattr", "here_pha_poicontact", "here_pha_poifileassoc", "here_pha_poirelat",
//            "here_pha_poitrans", "here_pha_restrnts", "here_pha_shopping", "here_pha_transhubs", "here_pha_travdest"
    @Async()
    public void poiConvertHamlet(List<HerePhaHamlet> herePhaAutosvcList) {
        List<PoiM> poiInfoList = new ArrayList<>();
        if (herePhaAutosvcList.size() > 0) {
            for (HerePhaHamlet herePhaAutosvc : herePhaAutosvcList
            ) {
                PoiM poiInfo = new PoiM();
                // POI_ID
                poiInfo.setPoiId(UUID.randomUUID().toString());
                // Source_ID
                poiInfo.setSourceId(herePhaAutosvc.getPoiId().toString());
                // Kind
                // poiInfo.setKind(herePhaAutosvc.get);
                // Kind_Code
                poiInfo.setKindCode(herePhaAutosvc.getFacType());
                // Name_S
                if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                    poiInfo.setNameS(herePhaAutosvc.getPoiName());
                } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                // Alias
                {
                    poiInfo.setAlias(herePhaAutosvc.getPoiName());
                } else
                // Name
                {
                    poiInfo.setName(herePhaAutosvc.getPoiName());
                }
                // Name_ENG
                List<HerePhaPoitrans> herePhaPoitransList = herePhaPoitransService.lambdaQuery().eq(HerePhaPoitrans::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoitransList.size() > 0) {
                    // Name_S_ENG
                    if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                        poiInfo.setNameSEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                    // Alias_ENG
                    {
                        poiInfo.setAliasEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else
                    // Name_ENG
                    {
                        poiInfo.setNameEng(herePhaPoitransList.get(0).getPoiNmTr());
                    }
                }
                // Address
                poiInfo.setAddress(herePhaAutosvc.getStName() + herePhaAutosvc.getPoiStNum());
                // Address_ENG
                poiInfo.setAddressEng(herePhaPoitransList.size() > 0 ? herePhaPoitransList.get(0).getActaddrTr() : "");
                // lng_84
                // poiInfo.setLongitudeWgs84();
                // Lat_84
                // poiInfo.setLatitudeWgs84();
                // Lon_Guide_84
                poiInfo.setLonGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[0].replace("POINT(", "")));
                // Lat_Guide_84
                poiInfo.setLatGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[1].replace(")", "")));
                // Link_ID
                poiInfo.setLinkId(herePhaAutosvc.getLinkId());
                // Side
                poiInfo.setSide(herePhaAutosvc.getPoiStSd().isEmpty() ? "N" : herePhaAutosvc.getPoiStSd());
                // Importance
                poiInfo.setImportance("Y".equals(herePhaAutosvc.getNatImport()) ? "1" : "");
                // VAdmin_Code
                poiInfo.setVadminCode(herePhaAutosvc.getVancityId());
                // Zip_Code
                poiInfo.setZipCode(herePhaAutosvc.getActPostal());
                // Telephone
                poiInfo.setTelephone(herePhaAutosvc.getPhNumber());
                // Tel_Type
                // poiInfo.setTelType(herePhaAutosvc.get);
                // Star_Rating
                List<HerePhaPoiattr> herePhaPoiattrList = herePhaPoiattrService.lambdaQuery()
                        .eq(HerePhaPoiattr::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiattrList.size() > 0) {
                    HerePhaPoiattr herePhaPoiattr = herePhaPoiattrList.get(0);
                    if ("7011".equals(herePhaPoiattr.getFacType()) && "33".equals(herePhaPoiattr.getAttrType())) {
                        switch (herePhaPoiattr.getAttrValue()) {
                            case "17":
                                poiInfo.setStarRating("0");
                                break;
                            case "18":
                                poiInfo.setStarRating("1");
                                break;
                            case "1026":
                                poiInfo.setStarRating("1");
                                break;
                            case "1030":
                                poiInfo.setStarRating("8");
                                break;
                            case "1029":
                                poiInfo.setStarRating("3");
                                break;
                            case "1028":
                                poiInfo.setStarRating("4");
                                break;
                            case "1027":
                                poiInfo.setStarRating("5");
                                break;
                            default:
                                poiInfo.setStarRating("0");
                        }
                    }
                }
                // Truck_Flag
                // Food_Type
                List<HerePhaRestrnts> herePhaRestrntsList = herePhaRestrntsService.lambdaQuery()
                        .eq(HerePhaRestrnts::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaRestrntsList.size() > 0) {
                    poiInfo.setFoodType(herePhaRestrntsList.get(0).getFoodType());
                }
                // Airpt_Code
                // Brand
                List<Mtdref> mtdrefList = mtdrefService.lambdaQuery().eq(Mtdref::getCode, herePhaAutosvc.getChainId()).list();
                if (mtdrefList.size() > 0) {
                    poiInfo.setBrand(mtdrefList.get(0).getRefClass());
                }
                // Parent
                List<HerePhaPoiassoc> herePhaPoiassocList = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getChildId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList.size() > 0) {
                    // poiInfo.setIsParent("1");
                    List<String> parentIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList
                    ) {
                        parentIds.add(herePhaPoiassoc.getParentId().toString());
                    }
                    poiInfo.setParent(String.join(",", parentIds));
                }
                // Children
                List<HerePhaPoiassoc> herePhaPoiassocList1 = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getParentId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList1.size() > 0) {
                    // poiInfo.setIsChild("1");
                    List<String> childIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList1
                    ) {
                        childIds.add(herePhaPoiassoc.getChildId().toString());
                    }
                    poiInfo.setChildren(String.join(",", childIds));
                }
                // POI_Geo
                poiInfo.setPoiGeo(herePhaAutosvc.getGeom());
                // Update_Time
                poiInfo.setUpdateTime(LocalDateTime.now());
                poiInfoList.add(poiInfo);
            }
            this.saveOrUpdateBatch(poiInfoList);
        }
    }

    //   "here_pha_hospital", "here_pha_misccategories", "here_pha_namedplc", "here_pha_parking",
    //            "here_pha_parkrec", "here_pha_poiassoc", "here_pha_poiattr", "here_pha_poicontact", "here_pha_poifileassoc", "here_pha_poirelat",
//            "here_pha_poitrans", "here_pha_restrnts", "here_pha_shopping", "here_pha_transhubs", "here_pha_travdest"
    @Async()
    public void poiConvertHospital(List<HerePhaHospital> herePhaAutosvcList) {
        List<PoiM> poiInfoList = new ArrayList<>();
        if (herePhaAutosvcList.size() > 0) {
            for (HerePhaHospital herePhaAutosvc : herePhaAutosvcList
            ) {
                PoiM poiInfo = new PoiM();
                // POI_ID
                poiInfo.setPoiId(UUID.randomUUID().toString());
                // Source_ID
                poiInfo.setSourceId(herePhaAutosvc.getPoiId().toString());
                // Kind
                // poiInfo.setKind(herePhaAutosvc.get);
                // Kind_Code
                poiInfo.setKindCode(herePhaAutosvc.getFacType());
                // Name_S
                if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                    poiInfo.setNameS(herePhaAutosvc.getPoiName());
                } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                // Alias
                {
                    poiInfo.setAlias(herePhaAutosvc.getPoiName());
                } else
                // Name
                {
                    poiInfo.setName(herePhaAutosvc.getPoiName());
                }
                // Name_ENG
                List<HerePhaPoitrans> herePhaPoitransList = herePhaPoitransService.lambdaQuery().eq(HerePhaPoitrans::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoitransList.size() > 0) {
                    // Name_S_ENG
                    if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                        poiInfo.setNameSEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                    // Alias_ENG
                    {
                        poiInfo.setAliasEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else
                    // Name_ENG
                    {
                        poiInfo.setNameEng(herePhaPoitransList.get(0).getPoiNmTr());
                    }
                }
                // Address
                poiInfo.setAddress(herePhaAutosvc.getStName() + herePhaAutosvc.getPoiStNum());
                // Address_ENG
                poiInfo.setAddressEng(herePhaPoitransList.size() > 0 ? herePhaPoitransList.get(0).getActaddrTr() : "");
                // lng_84
                // poiInfo.setLongitudeWgs84();
                // Lat_84
                // poiInfo.setLatitudeWgs84();
                // Lon_Guide_84
                poiInfo.setLonGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[0].replace("POINT(", "")));
                // Lat_Guide_84
                poiInfo.setLatGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[1].replace(")", "")));
                // Link_ID
                poiInfo.setLinkId(herePhaAutosvc.getLinkId());
                // Side
                poiInfo.setSide(herePhaAutosvc.getPoiStSd().isEmpty() ? "N" : herePhaAutosvc.getPoiStSd());
                // Importance
                poiInfo.setImportance("Y".equals(herePhaAutosvc.getNatImport()) ? "1" : "");
                // VAdmin_Code
                poiInfo.setVadminCode(herePhaAutosvc.getVancityId());
                // Zip_Code
                poiInfo.setZipCode(herePhaAutosvc.getActPostal());
                // Telephone
                poiInfo.setTelephone(herePhaAutosvc.getPhNumber());
                // Tel_Type
                // poiInfo.setTelType(herePhaAutosvc.get);
                // Star_Rating
                List<HerePhaPoiattr> herePhaPoiattrList = herePhaPoiattrService.lambdaQuery()
                        .eq(HerePhaPoiattr::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiattrList.size() > 0) {
                    HerePhaPoiattr herePhaPoiattr = herePhaPoiattrList.get(0);
                    if ("7011".equals(herePhaPoiattr.getFacType()) && "33".equals(herePhaPoiattr.getAttrType())) {
                        switch (herePhaPoiattr.getAttrValue()) {
                            case "17":
                                poiInfo.setStarRating("0");
                                break;
                            case "18":
                                poiInfo.setStarRating("1");
                                break;
                            case "1026":
                                poiInfo.setStarRating("1");
                                break;
                            case "1030":
                                poiInfo.setStarRating("8");
                                break;
                            case "1029":
                                poiInfo.setStarRating("3");
                                break;
                            case "1028":
                                poiInfo.setStarRating("4");
                                break;
                            case "1027":
                                poiInfo.setStarRating("5");
                                break;
                            default:
                                poiInfo.setStarRating("0");
                        }
                    }
                }
                // Truck_Flag
                // Food_Type
                List<HerePhaRestrnts> herePhaRestrntsList = herePhaRestrntsService.lambdaQuery()
                        .eq(HerePhaRestrnts::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaRestrntsList.size() > 0) {
                    poiInfo.setFoodType(herePhaRestrntsList.get(0).getFoodType());
                }
                // Airpt_Code
                // Brand
                List<Mtdref> mtdrefList = mtdrefService.lambdaQuery().eq(Mtdref::getCode, herePhaAutosvc.getChainId()).list();
                if (mtdrefList.size() > 0) {
                    poiInfo.setBrand(mtdrefList.get(0).getRefClass());
                }
                // Parent
                List<HerePhaPoiassoc> herePhaPoiassocList = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getChildId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList.size() > 0) {
                    // poiInfo.setIsParent("1");
                    List<String> parentIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList
                    ) {
                        parentIds.add(herePhaPoiassoc.getParentId().toString());
                    }
                    poiInfo.setParent(String.join(",", parentIds));
                }
                // Children
                List<HerePhaPoiassoc> herePhaPoiassocList1 = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getParentId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList1.size() > 0) {
                    // poiInfo.setIsChild("1");
                    List<String> childIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList1
                    ) {
                        childIds.add(herePhaPoiassoc.getChildId().toString());
                    }
                    poiInfo.setChildren(String.join(",", childIds));
                }
                // POI_Geo
                poiInfo.setPoiGeo(herePhaAutosvc.getGeom());
                // Update_Time
                poiInfo.setUpdateTime(LocalDateTime.now());
                poiInfoList.add(poiInfo);
            }
            this.saveOrUpdateBatch(poiInfoList);
        }
    }

    //    "here_pha_misccategories", "here_pha_namedplc", "here_pha_parking",
//            "here_pha_parkrec", "here_pha_poiassoc", "here_pha_poiattr", "here_pha_poicontact", "here_pha_poifileassoc", "here_pha_poirelat",
//            "here_pha_poitrans", "here_pha_restrnts", "here_pha_shopping", "here_pha_transhubs", "here_pha_travdest"
    @Async()
    public void poiConvertMisccategories(List<HerePhaMisccategories> herePhaAutosvcList) {
        List<PoiM> poiInfoList = new ArrayList<>();
        if (herePhaAutosvcList.size() > 0) {
            for (HerePhaMisccategories herePhaAutosvc : herePhaAutosvcList
            ) {
                PoiM poiInfo = new PoiM();
                // POI_ID
                poiInfo.setPoiId(UUID.randomUUID().toString());
                // Source_ID
                poiInfo.setSourceId(herePhaAutosvc.getPoiId().toString());
                // Kind
                // poiInfo.setKind(herePhaAutosvc.get);
                // Kind_Code
                poiInfo.setKindCode(herePhaAutosvc.getFacType());
                // Name_S
                if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                    poiInfo.setNameS(herePhaAutosvc.getPoiName());
                } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                // Alias
                {
                    poiInfo.setAlias(herePhaAutosvc.getPoiName());
                } else
                // Name
                {
                    poiInfo.setName(herePhaAutosvc.getPoiName());
                }
                // Name_ENG
                List<HerePhaPoitrans> herePhaPoitransList = herePhaPoitransService.lambdaQuery().eq(HerePhaPoitrans::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoitransList.size() > 0) {
                    // Name_S_ENG
                    if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                        poiInfo.setNameSEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                    // Alias_ENG
                    {
                        poiInfo.setAliasEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else
                    // Name_ENG
                    {
                        poiInfo.setNameEng(herePhaPoitransList.get(0).getPoiNmTr());
                    }
                }
                // Address
                poiInfo.setAddress(herePhaAutosvc.getStName() + herePhaAutosvc.getPoiStNum());
                // Address_ENG
                poiInfo.setAddressEng(herePhaPoitransList.size() > 0 ? herePhaPoitransList.get(0).getActaddrTr() : "");
                // lng_84
                // poiInfo.setLongitudeWgs84();
                // Lat_84
                // poiInfo.setLatitudeWgs84();
                // Lon_Guide_84
                poiInfo.setLonGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[0].replace("POINT(", "")));
                // Lat_Guide_84
                poiInfo.setLatGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[1].replace(")", "")));
                // Link_ID
                poiInfo.setLinkId(herePhaAutosvc.getLinkId());
                // Side
                poiInfo.setSide(herePhaAutosvc.getPoiStSd().isEmpty() ? "N" : herePhaAutosvc.getPoiStSd());
                // Importance
                poiInfo.setImportance("Y".equals(herePhaAutosvc.getNatImport()) ? "1" : "");
                // VAdmin_Code
                poiInfo.setVadminCode(herePhaAutosvc.getVancityId());
                // Zip_Code
                poiInfo.setZipCode(herePhaAutosvc.getActPostal());
                // Telephone
                poiInfo.setTelephone(herePhaAutosvc.getPhNumber());
                // Tel_Type
                // poiInfo.setTelType(herePhaAutosvc.get);
                // Star_Rating
                List<HerePhaPoiattr> herePhaPoiattrList = herePhaPoiattrService.lambdaQuery()
                        .eq(HerePhaPoiattr::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiattrList.size() > 0) {
                    HerePhaPoiattr herePhaPoiattr = herePhaPoiattrList.get(0);
                    if ("7011".equals(herePhaPoiattr.getFacType()) && "33".equals(herePhaPoiattr.getAttrType())) {
                        switch (herePhaPoiattr.getAttrValue()) {
                            case "17":
                                poiInfo.setStarRating("0");
                                break;
                            case "18":
                                poiInfo.setStarRating("1");
                                break;
                            case "1026":
                                poiInfo.setStarRating("1");
                                break;
                            case "1030":
                                poiInfo.setStarRating("8");
                                break;
                            case "1029":
                                poiInfo.setStarRating("3");
                                break;
                            case "1028":
                                poiInfo.setStarRating("4");
                                break;
                            case "1027":
                                poiInfo.setStarRating("5");
                                break;
                            default:
                                poiInfo.setStarRating("0");
                        }
                    }
                }
                // Truck_Flag
                // Food_Type
                List<HerePhaRestrnts> herePhaRestrntsList = herePhaRestrntsService.lambdaQuery()
                        .eq(HerePhaRestrnts::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaRestrntsList.size() > 0) {
                    poiInfo.setFoodType(herePhaRestrntsList.get(0).getFoodType());
                }
                // Airpt_Code
                // Brand
                List<Mtdref> mtdrefList = mtdrefService.lambdaQuery().eq(Mtdref::getCode, herePhaAutosvc.getChainId()).list();
                if (mtdrefList.size() > 0) {
                    poiInfo.setBrand(mtdrefList.get(0).getRefClass());
                }
                // Parent
                List<HerePhaPoiassoc> herePhaPoiassocList = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getChildId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList.size() > 0) {
                    // poiInfo.setIsParent("1");
                    List<String> parentIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList
                    ) {
                        parentIds.add(herePhaPoiassoc.getParentId().toString());
                    }
                    poiInfo.setParent(String.join(",", parentIds));
                }
                // Children
                List<HerePhaPoiassoc> herePhaPoiassocList1 = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getParentId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList1.size() > 0) {
                    // poiInfo.setIsChild("1");
                    List<String> childIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList1
                    ) {
                        childIds.add(herePhaPoiassoc.getChildId().toString());
                    }
                    poiInfo.setChildren(String.join(",", childIds));
                }
                // POI_Geo
                poiInfo.setPoiGeo(herePhaAutosvc.getGeom());
                // Update_Time
                poiInfo.setUpdateTime(LocalDateTime.now());
                poiInfoList.add(poiInfo);
            }
            this.saveOrUpdateBatch(poiInfoList);
        }
    }

    //    , "here_pha_namedplc", "here_pha_parking",
//            "here_pha_parkrec", "here_pha_poiassoc", "here_pha_poiattr", "here_pha_poicontact", "here_pha_poifileassoc", "here_pha_poirelat",
//            "here_pha_poitrans", "here_pha_restrnts", "here_pha_shopping", "here_pha_transhubs", "here_pha_travdest"
    @Async()
    public void poiConvertNamedplc(List<HerePhaNamedplc> herePhaAutosvcList) {
        List<PoiM> poiInfoList = new ArrayList<>();
        if (herePhaAutosvcList.size() > 0) {
            for (HerePhaNamedplc herePhaAutosvc : herePhaAutosvcList
            ) {
                PoiM poiInfo = new PoiM();
                // POI_ID
                poiInfo.setPoiId(UUID.randomUUID().toString());
                // Source_ID
                poiInfo.setSourceId(herePhaAutosvc.getPoiId().toString());
                // Kind
                // poiInfo.setKind(herePhaAutosvc.get);
                // Kind_Code
                poiInfo.setKindCode(herePhaAutosvc.getFacType());
                // Name_S
                if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                    poiInfo.setNameS(herePhaAutosvc.getPoiName());
                } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                // Alias
                {
                    poiInfo.setAlias(herePhaAutosvc.getPoiName());
                } else
                // Name
                {
                    poiInfo.setName(herePhaAutosvc.getPoiName());
                }
                // Name_ENG
                List<HerePhaPoitrans> herePhaPoitransList = herePhaPoitransService.lambdaQuery().eq(HerePhaPoitrans::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoitransList.size() > 0) {
                    // Name_S_ENG
                    if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                        poiInfo.setNameSEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                    // Alias_ENG
                    {
                        poiInfo.setAliasEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else
                    // Name_ENG
                    {
                        poiInfo.setNameEng(herePhaPoitransList.get(0).getPoiNmTr());
                    }
                }
                // Address
                poiInfo.setAddress(herePhaAutosvc.getStName() + herePhaAutosvc.getPoiStNum());
                // Address_ENG
                poiInfo.setAddressEng(herePhaPoitransList.size() > 0 ? herePhaPoitransList.get(0).getActaddrTr() : "");
                // lng_84
                // poiInfo.setLongitudeWgs84();
                // Lat_84
                // poiInfo.setLatitudeWgs84();
                // Lon_Guide_84
                poiInfo.setLonGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[0].replace("POINT(", "")));
                // Lat_Guide_84
                poiInfo.setLatGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[1].replace(")", "")));
                // Link_ID
                poiInfo.setLinkId(herePhaAutosvc.getLinkId());
                // Side
                poiInfo.setSide(herePhaAutosvc.getPoiStSd().isEmpty() ? "N" : herePhaAutosvc.getPoiStSd());
                // Importance
                poiInfo.setImportance("Y".equals(herePhaAutosvc.getNatImport()) ? "1" : "");
                // VAdmin_Code
                poiInfo.setVadminCode(herePhaAutosvc.getVancityId());
                // Zip_Code
                poiInfo.setZipCode(herePhaAutosvc.getActPostal());
                // Telephone
                poiInfo.setTelephone(herePhaAutosvc.getPhNumber());
                // Tel_Type
                // poiInfo.setTelType(herePhaAutosvc.get);
                // Star_Rating
                List<HerePhaPoiattr> herePhaPoiattrList = herePhaPoiattrService.lambdaQuery()
                        .eq(HerePhaPoiattr::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiattrList.size() > 0) {
                    HerePhaPoiattr herePhaPoiattr = herePhaPoiattrList.get(0);
                    if ("7011".equals(herePhaPoiattr.getFacType()) && "33".equals(herePhaPoiattr.getAttrType())) {
                        switch (herePhaPoiattr.getAttrValue()) {
                            case "17":
                                poiInfo.setStarRating("0");
                                break;
                            case "18":
                                poiInfo.setStarRating("1");
                                break;
                            case "1026":
                                poiInfo.setStarRating("1");
                                break;
                            case "1030":
                                poiInfo.setStarRating("8");
                                break;
                            case "1029":
                                poiInfo.setStarRating("3");
                                break;
                            case "1028":
                                poiInfo.setStarRating("4");
                                break;
                            case "1027":
                                poiInfo.setStarRating("5");
                                break;
                            default:
                                poiInfo.setStarRating("0");
                        }
                    }
                }
                // Truck_Flag
                // Food_Type
                List<HerePhaRestrnts> herePhaRestrntsList = herePhaRestrntsService.lambdaQuery()
                        .eq(HerePhaRestrnts::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaRestrntsList.size() > 0) {
                    poiInfo.setFoodType(herePhaRestrntsList.get(0).getFoodType());
                }
                // Airpt_Code
                // Brand
                List<Mtdref> mtdrefList = mtdrefService.lambdaQuery().eq(Mtdref::getCode, herePhaAutosvc.getChainId()).list();
                if (mtdrefList.size() > 0) {
                    poiInfo.setBrand(mtdrefList.get(0).getRefClass());
                }
                // Parent
                List<HerePhaPoiassoc> herePhaPoiassocList = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getChildId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList.size() > 0) {
                    // poiInfo.setIsParent("1");
                    List<String> parentIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList
                    ) {
                        parentIds.add(herePhaPoiassoc.getParentId().toString());
                    }
                    poiInfo.setParent(String.join(",", parentIds));
                }
                // Children
                List<HerePhaPoiassoc> herePhaPoiassocList1 = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getParentId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList1.size() > 0) {
                    // poiInfo.setIsChild("1");
                    List<String> childIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList1
                    ) {
                        childIds.add(herePhaPoiassoc.getChildId().toString());
                    }
                    poiInfo.setChildren(String.join(",", childIds));
                }
                // POI_Geo
                poiInfo.setPoiGeo(herePhaAutosvc.getGeom());
                // Update_Time
                poiInfo.setUpdateTime(LocalDateTime.now());
                poiInfoList.add(poiInfo);
            }
            this.saveOrUpdateBatch(poiInfoList);
        }
    }

    //   "here_pha_parking",
    //   "here_pha_parkrec", "here_pha_poiassoc", "here_pha_poiattr", "here_pha_poicontact", "here_pha_poifileassoc", "here_pha_poirelat",
//            "here_pha_poitrans", "here_pha_restrnts", "here_pha_shopping", "here_pha_transhubs", "here_pha_travdest"
    @Async()
    public void poiConvertParking(List<HerePhaParking> herePhaAutosvcList) {
        List<PoiM> poiInfoList = new ArrayList<>();
        if (herePhaAutosvcList.size() > 0) {
            for (HerePhaParking herePhaAutosvc : herePhaAutosvcList
            ) {
                PoiM poiInfo = new PoiM();
                // POI_ID
                poiInfo.setPoiId(UUID.randomUUID().toString());
                // Source_ID
                poiInfo.setSourceId(herePhaAutosvc.getPoiId().toString());
                // Kind
                // poiInfo.setKind(herePhaAutosvc.get);
                // Kind_Code
                poiInfo.setKindCode(herePhaAutosvc.getFacType());
                // Name_S
                if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                    poiInfo.setNameS(herePhaAutosvc.getPoiName());
                } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                // Alias
                {
                    poiInfo.setAlias(herePhaAutosvc.getPoiName());
                } else
                // Name
                {
                    poiInfo.setName(herePhaAutosvc.getPoiName());
                }
                // Name_ENG
                List<HerePhaPoitrans> herePhaPoitransList = herePhaPoitransService.lambdaQuery().eq(HerePhaPoitrans::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoitransList.size() > 0) {
                    // Name_S_ENG
                    if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                        poiInfo.setNameSEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                    // Alias_ENG
                    {
                        poiInfo.setAliasEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else
                    // Name_ENG
                    {
                        poiInfo.setNameEng(herePhaPoitransList.get(0).getPoiNmTr());
                    }
                }
                // Address
                poiInfo.setAddress(herePhaAutosvc.getStName() + herePhaAutosvc.getPoiStNum());
                // Address_ENG
                poiInfo.setAddressEng(herePhaPoitransList.size() > 0 ? herePhaPoitransList.get(0).getActaddrTr() : "");
                // lng_84
                // poiInfo.setLongitudeWgs84();
                // Lat_84
                // poiInfo.setLatitudeWgs84();
                // Lon_Guide_84
                poiInfo.setLonGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[0].replace("POINT(", "")));
                // Lat_Guide_84
                poiInfo.setLatGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[1].replace(")", "")));
                // Link_ID
                poiInfo.setLinkId(herePhaAutosvc.getLinkId());
                // Side
                poiInfo.setSide(herePhaAutosvc.getPoiStSd().isEmpty() ? "N" : herePhaAutosvc.getPoiStSd());
                // Importance
                poiInfo.setImportance("Y".equals(herePhaAutosvc.getNatImport()) ? "1" : "");
                // VAdmin_Code
                poiInfo.setVadminCode(herePhaAutosvc.getVancityId());
                // Zip_Code
                poiInfo.setZipCode(herePhaAutosvc.getActPostal());
                // Telephone
                poiInfo.setTelephone(herePhaAutosvc.getPhNumber());
                // Tel_Type
                // poiInfo.setTelType(herePhaAutosvc.get);
                // Star_Rating
                List<HerePhaPoiattr> herePhaPoiattrList = herePhaPoiattrService.lambdaQuery()
                        .eq(HerePhaPoiattr::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiattrList.size() > 0) {
                    HerePhaPoiattr herePhaPoiattr = herePhaPoiattrList.get(0);
                    if ("7011".equals(herePhaPoiattr.getFacType()) && "33".equals(herePhaPoiattr.getAttrType())) {
                        switch (herePhaPoiattr.getAttrValue()) {
                            case "17":
                                poiInfo.setStarRating("0");
                                break;
                            case "18":
                                poiInfo.setStarRating("1");
                                break;
                            case "1026":
                                poiInfo.setStarRating("1");
                                break;
                            case "1030":
                                poiInfo.setStarRating("8");
                                break;
                            case "1029":
                                poiInfo.setStarRating("3");
                                break;
                            case "1028":
                                poiInfo.setStarRating("4");
                                break;
                            case "1027":
                                poiInfo.setStarRating("5");
                                break;
                            default:
                                poiInfo.setStarRating("0");
                        }
                    }
                }
                // Truck_Flag
                // Food_Type
                List<HerePhaRestrnts> herePhaRestrntsList = herePhaRestrntsService.lambdaQuery()
                        .eq(HerePhaRestrnts::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaRestrntsList.size() > 0) {
                    poiInfo.setFoodType(herePhaRestrntsList.get(0).getFoodType());
                }
                // Airpt_Code
                // Brand
                List<Mtdref> mtdrefList = mtdrefService.lambdaQuery().eq(Mtdref::getCode, herePhaAutosvc.getChainId()).list();
                if (mtdrefList.size() > 0) {
                    poiInfo.setBrand(mtdrefList.get(0).getRefClass());
                }
                // Parent
                List<HerePhaPoiassoc> herePhaPoiassocList = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getChildId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList.size() > 0) {
                    // poiInfo.setIsParent("1");
                    List<String> parentIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList
                    ) {
                        parentIds.add(herePhaPoiassoc.getParentId().toString());
                    }
                    poiInfo.setParent(String.join(",", parentIds));
                }
                // Children
                List<HerePhaPoiassoc> herePhaPoiassocList1 = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getParentId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList1.size() > 0) {
                    // poiInfo.setIsChild("1");
                    List<String> childIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList1
                    ) {
                        childIds.add(herePhaPoiassoc.getChildId().toString());
                    }
                    poiInfo.setChildren(String.join(",", childIds));
                }
                // POI_Geo
                poiInfo.setPoiGeo(herePhaAutosvc.getGeom());
                // Update_Time
                poiInfo.setUpdateTime(LocalDateTime.now());
                poiInfoList.add(poiInfo);
            }
            this.saveOrUpdateBatch(poiInfoList);
        }
    }

    //   "here_pha_parkrec", "here_pha_poiassoc", "here_pha_poiattr", "here_pha_poicontact", "here_pha_poifileassoc", "here_pha_poirelat",
//            "here_pha_poitrans", "here_pha_restrnts", "here_pha_shopping", "here_pha_transhubs", "here_pha_travdest"
    @Async()
    public void poiConvertParkrec(List<HerePhaParkrec> herePhaAutosvcList) {
        List<PoiM> poiInfoList = new ArrayList<>();
        if (herePhaAutosvcList.size() > 0) {
            for (HerePhaParkrec herePhaAutosvc : herePhaAutosvcList
            ) {
                PoiM poiInfo = new PoiM();
                // POI_ID
                poiInfo.setPoiId(UUID.randomUUID().toString());
                // Source_ID
                poiInfo.setSourceId(herePhaAutosvc.getPoiId().toString());
                // Kind
                // poiInfo.setKind(herePhaAutosvc.get);
                // Kind_Code
                poiInfo.setKindCode(herePhaAutosvc.getFacType());
                // Name_S
                if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                    poiInfo.setNameS(herePhaAutosvc.getPoiName());
                } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                // Alias
                {
                    poiInfo.setAlias(herePhaAutosvc.getPoiName());
                } else
                // Name
                {
                    poiInfo.setName(herePhaAutosvc.getPoiName());
                }
                // Name_ENG
                List<HerePhaPoitrans> herePhaPoitransList = herePhaPoitransService.lambdaQuery().eq(HerePhaPoitrans::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoitransList.size() > 0) {
                    // Name_S_ENG
                    if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                        poiInfo.setNameSEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                    // Alias_ENG
                    {
                        poiInfo.setAliasEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else
                    // Name_ENG
                    {
                        poiInfo.setNameEng(herePhaPoitransList.get(0).getPoiNmTr());
                    }
                }
                // Address
                poiInfo.setAddress(herePhaAutosvc.getStName() + herePhaAutosvc.getPoiStNum());
                // Address_ENG
                poiInfo.setAddressEng(herePhaPoitransList.size() > 0 ? herePhaPoitransList.get(0).getActaddrTr() : "");
                // lng_84
                // poiInfo.setLongitudeWgs84();
                // Lat_84
                // poiInfo.setLatitudeWgs84();
                // Lon_Guide_84
                poiInfo.setLonGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[0].replace("POINT(", "")));
                // Lat_Guide_84
                poiInfo.setLatGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[1].replace(")", "")));
                // Link_ID
                poiInfo.setLinkId(herePhaAutosvc.getLinkId());
                // Side
                poiInfo.setSide(herePhaAutosvc.getPoiStSd().isEmpty() ? "N" : herePhaAutosvc.getPoiStSd());
                // Importance
                poiInfo.setImportance("Y".equals(herePhaAutosvc.getNatImport()) ? "1" : "");
                // VAdmin_Code
                poiInfo.setVadminCode(herePhaAutosvc.getVancityId());
                // Zip_Code
                poiInfo.setZipCode(herePhaAutosvc.getActPostal());
                // Telephone
                poiInfo.setTelephone(herePhaAutosvc.getPhNumber());
                // Tel_Type
                // poiInfo.setTelType(herePhaAutosvc.get);
                // Star_Rating
                List<HerePhaPoiattr> herePhaPoiattrList = herePhaPoiattrService.lambdaQuery()
                        .eq(HerePhaPoiattr::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiattrList.size() > 0) {
                    HerePhaPoiattr herePhaPoiattr = herePhaPoiattrList.get(0);
                    if ("7011".equals(herePhaPoiattr.getFacType()) && "33".equals(herePhaPoiattr.getAttrType())) {
                        switch (herePhaPoiattr.getAttrValue()) {
                            case "17":
                                poiInfo.setStarRating("0");
                                break;
                            case "18":
                                poiInfo.setStarRating("1");
                                break;
                            case "1026":
                                poiInfo.setStarRating("1");
                                break;
                            case "1030":
                                poiInfo.setStarRating("8");
                                break;
                            case "1029":
                                poiInfo.setStarRating("3");
                                break;
                            case "1028":
                                poiInfo.setStarRating("4");
                                break;
                            case "1027":
                                poiInfo.setStarRating("5");
                                break;
                            default:
                                poiInfo.setStarRating("0");
                        }
                    }
                }
                // Truck_Flag
                // Food_Type
                List<HerePhaRestrnts> herePhaRestrntsList = herePhaRestrntsService.lambdaQuery()
                        .eq(HerePhaRestrnts::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaRestrntsList.size() > 0) {
                    poiInfo.setFoodType(herePhaRestrntsList.get(0).getFoodType());
                }
                // Airpt_Code
                // Brand
                List<Mtdref> mtdrefList = mtdrefService.lambdaQuery().eq(Mtdref::getCode, herePhaAutosvc.getChainId()).list();
                if (mtdrefList.size() > 0) {
                    poiInfo.setBrand(mtdrefList.get(0).getRefClass());
                }
                // Parent
                List<HerePhaPoiassoc> herePhaPoiassocList = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getChildId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList.size() > 0) {
                    // poiInfo.setIsParent("1");
                    List<String> parentIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList
                    ) {
                        parentIds.add(herePhaPoiassoc.getParentId().toString());
                    }
                    poiInfo.setParent(String.join(",", parentIds));
                }
                // Children
                List<HerePhaPoiassoc> herePhaPoiassocList1 = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getParentId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList1.size() > 0) {
                    // poiInfo.setIsChild("1");
                    List<String> childIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList1
                    ) {
                        childIds.add(herePhaPoiassoc.getChildId().toString());
                    }
                    poiInfo.setChildren(String.join(",", childIds));
                }
                // POI_Geo
                poiInfo.setPoiGeo(herePhaAutosvc.getGeom());
                // Update_Time
                poiInfo.setUpdateTime(LocalDateTime.now());
                poiInfoList.add(poiInfo);
            }
            this.saveOrUpdateBatch(poiInfoList);
        }
    }

    // "here_pha_restrnts", "here_pha_shopping", "here_pha_transhubs", "here_pha_travdest"
    @Async()
    public void poiConvertRestrnts(List<HerePhaRestrnts> herePhaAutosvcList) {
        List<PoiM> poiInfoList = new ArrayList<>();
        if (herePhaAutosvcList.size() > 0) {
            for (HerePhaRestrnts herePhaAutosvc : herePhaAutosvcList
            ) {
                PoiM poiInfo = new PoiM();
                // POI_ID
                poiInfo.setPoiId(UUID.randomUUID().toString());
                // Source_ID
                poiInfo.setSourceId(herePhaAutosvc.getPoiId().toString());
                // Kind
                // poiInfo.setKind(herePhaAutosvc.get);
                // Kind_Code
                poiInfo.setKindCode(herePhaAutosvc.getFacType());
                // Name_S
                if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                    poiInfo.setNameS(herePhaAutosvc.getPoiName());
                } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                // Alias
                {
                    poiInfo.setAlias(herePhaAutosvc.getPoiName());
                } else
                // Name
                {
                    poiInfo.setName(herePhaAutosvc.getPoiName());
                }
                // Name_ENG
                List<HerePhaPoitrans> herePhaPoitransList = herePhaPoitransService.lambdaQuery().eq(HerePhaPoitrans::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoitransList.size() > 0) {
                    // Name_S_ENG
                    if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                        poiInfo.setNameSEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                    // Alias_ENG
                    {
                        poiInfo.setAliasEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else
                    // Name_ENG
                    {
                        poiInfo.setNameEng(herePhaPoitransList.get(0).getPoiNmTr());
                    }
                }
                // Address
                poiInfo.setAddress(herePhaAutosvc.getStName() + herePhaAutosvc.getPoiStNum());
                // Address_ENG
                poiInfo.setAddressEng(herePhaPoitransList.size() > 0 ? herePhaPoitransList.get(0).getActaddrTr() : "");
                // lng_84
                // poiInfo.setLongitudeWgs84();
                // Lat_84
                // poiInfo.setLatitudeWgs84();
                // Lon_Guide_84
                poiInfo.setLonGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[0].replace("POINT(", "")));
                // Lat_Guide_84
                poiInfo.setLatGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[1].replace(")", "")));
                // Link_ID
                poiInfo.setLinkId(herePhaAutosvc.getLinkId());
                // Side
                poiInfo.setSide(herePhaAutosvc.getPoiStSd().isEmpty() ? "N" : herePhaAutosvc.getPoiStSd());
                // Importance
                poiInfo.setImportance("Y".equals(herePhaAutosvc.getNatImport()) ? "1" : "");
                // VAdmin_Code
                poiInfo.setVadminCode(herePhaAutosvc.getVancityId());
                // Zip_Code
                poiInfo.setZipCode(herePhaAutosvc.getActPostal());
                // Telephone
                poiInfo.setTelephone(herePhaAutosvc.getPhNumber());
                // Tel_Type
                // poiInfo.setTelType(herePhaAutosvc.get);
                // Star_Rating
                List<HerePhaPoiattr> herePhaPoiattrList = herePhaPoiattrService.lambdaQuery()
                        .eq(HerePhaPoiattr::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiattrList.size() > 0) {
                    HerePhaPoiattr herePhaPoiattr = herePhaPoiattrList.get(0);
                    if ("7011".equals(herePhaPoiattr.getFacType()) && "33".equals(herePhaPoiattr.getAttrType())) {
                        switch (herePhaPoiattr.getAttrValue()) {
                            case "17":
                                poiInfo.setStarRating("0");
                                break;
                            case "18":
                                poiInfo.setStarRating("1");
                                break;
                            case "1026":
                                poiInfo.setStarRating("1");
                                break;
                            case "1030":
                                poiInfo.setStarRating("8");
                                break;
                            case "1029":
                                poiInfo.setStarRating("3");
                                break;
                            case "1028":
                                poiInfo.setStarRating("4");
                                break;
                            case "1027":
                                poiInfo.setStarRating("5");
                                break;
                            default:
                                poiInfo.setStarRating("0");
                        }
                    }
                }
                // Truck_Flag
                // Food_Type
                List<HerePhaRestrnts> herePhaRestrntsList = herePhaRestrntsService.lambdaQuery()
                        .eq(HerePhaRestrnts::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaRestrntsList.size() > 0) {
                    poiInfo.setFoodType(herePhaRestrntsList.get(0).getFoodType());
                }
                // Airpt_Code
                // Brand
                List<Mtdref> mtdrefList = mtdrefService.lambdaQuery().eq(Mtdref::getCode, herePhaAutosvc.getChainId()).list();
                if (mtdrefList.size() > 0) {
                    poiInfo.setBrand(mtdrefList.get(0).getRefClass());
                }
                // Parent
                List<HerePhaPoiassoc> herePhaPoiassocList = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getChildId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList.size() > 0) {
                    // poiInfo.setIsParent("1");
                    List<String> parentIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList
                    ) {
                        parentIds.add(herePhaPoiassoc.getParentId().toString());
                    }
                    poiInfo.setParent(String.join(",", parentIds));
                }
                // Children
                List<HerePhaPoiassoc> herePhaPoiassocList1 = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getParentId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList1.size() > 0) {
                    // poiInfo.setIsChild("1");
                    List<String> childIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList1
                    ) {
                        childIds.add(herePhaPoiassoc.getChildId().toString());
                    }
                    poiInfo.setChildren(String.join(",", childIds));
                }
                // POI_Geo
                poiInfo.setPoiGeo(herePhaAutosvc.getGeom());
                // Update_Time
                poiInfo.setUpdateTime(LocalDateTime.now());
                poiInfoList.add(poiInfo);
            }
            this.saveOrUpdateBatch(poiInfoList);
        }
    }

    //"here_pha_shopping", "here_pha_transhubs", "here_pha_travdest"
    @Async()
    public void poiConvertShopping(List<HerePhaShopping> herePhaAutosvcList) {
        List<PoiM> poiInfoList = new ArrayList<>();
        if (herePhaAutosvcList.size() > 0) {
            for (HerePhaShopping herePhaAutosvc : herePhaAutosvcList
            ) {
                PoiM poiInfo = new PoiM();
                // POI_ID
                poiInfo.setPoiId(UUID.randomUUID().toString());
                // Source_ID
                poiInfo.setSourceId(herePhaAutosvc.getPoiId().toString());
                // Kind
                // poiInfo.setKind(herePhaAutosvc.get);
                // Kind_Code
                poiInfo.setKindCode(herePhaAutosvc.getFacType());
                // Name_S
                if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                    poiInfo.setNameS(herePhaAutosvc.getPoiName());
                } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                // Alias
                {
                    poiInfo.setAlias(herePhaAutosvc.getPoiName());
                } else
                // Name
                {
                    poiInfo.setName(herePhaAutosvc.getPoiName());
                }
                // Name_ENG
                List<HerePhaPoitrans> herePhaPoitransList = herePhaPoitransService.lambdaQuery().eq(HerePhaPoitrans::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoitransList.size() > 0) {
                    // Name_S_ENG
                    if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                        poiInfo.setNameSEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                    // Alias_ENG
                    {
                        poiInfo.setAliasEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else
                    // Name_ENG
                    {
                        poiInfo.setNameEng(herePhaPoitransList.get(0).getPoiNmTr());
                    }
                }
                // Address
                poiInfo.setAddress(herePhaAutosvc.getStName() + herePhaAutosvc.getPoiStNum());
                // Address_ENG
                poiInfo.setAddressEng(herePhaPoitransList.size() > 0 ? herePhaPoitransList.get(0).getActaddrTr() : "");
                // lng_84
                // poiInfo.setLongitudeWgs84();
                // Lat_84
                // poiInfo.setLatitudeWgs84();
                // Lon_Guide_84
                poiInfo.setLonGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[0].replace("POINT(", "")));
                // Lat_Guide_84
                poiInfo.setLatGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[1].replace(")", "")));
                // Link_ID
                poiInfo.setLinkId(herePhaAutosvc.getLinkId());
                // Side
                poiInfo.setSide(herePhaAutosvc.getPoiStSd().isEmpty() ? "N" : herePhaAutosvc.getPoiStSd());
                // Importance
                poiInfo.setImportance("Y".equals(herePhaAutosvc.getNatImport()) ? "1" : "");
                // VAdmin_Code
                poiInfo.setVadminCode(herePhaAutosvc.getVancityId());
                // Zip_Code
                poiInfo.setZipCode(herePhaAutosvc.getActPostal());
                // Telephone
                poiInfo.setTelephone(herePhaAutosvc.getPhNumber());
                // Tel_Type
                // poiInfo.setTelType(herePhaAutosvc.get);
                // Star_Rating
                List<HerePhaPoiattr> herePhaPoiattrList = herePhaPoiattrService.lambdaQuery()
                        .eq(HerePhaPoiattr::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiattrList.size() > 0) {
                    HerePhaPoiattr herePhaPoiattr = herePhaPoiattrList.get(0);
                    if ("7011".equals(herePhaPoiattr.getFacType()) && "33".equals(herePhaPoiattr.getAttrType())) {
                        switch (herePhaPoiattr.getAttrValue()) {
                            case "17":
                                poiInfo.setStarRating("0");
                                break;
                            case "18":
                                poiInfo.setStarRating("1");
                                break;
                            case "1026":
                                poiInfo.setStarRating("1");
                                break;
                            case "1030":
                                poiInfo.setStarRating("8");
                                break;
                            case "1029":
                                poiInfo.setStarRating("3");
                                break;
                            case "1028":
                                poiInfo.setStarRating("4");
                                break;
                            case "1027":
                                poiInfo.setStarRating("5");
                                break;
                            default:
                                poiInfo.setStarRating("0");
                        }
                    }
                }
                // Truck_Flag
                // Food_Type
                List<HerePhaRestrnts> herePhaRestrntsList = herePhaRestrntsService.lambdaQuery()
                        .eq(HerePhaRestrnts::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaRestrntsList.size() > 0) {
                    poiInfo.setFoodType(herePhaRestrntsList.get(0).getFoodType());
                }
                // Airpt_Code
                // Brand
                List<Mtdref> mtdrefList = mtdrefService.lambdaQuery().eq(Mtdref::getCode, herePhaAutosvc.getChainId()).list();
                if (mtdrefList.size() > 0) {
                    poiInfo.setBrand(mtdrefList.get(0).getRefClass());
                }
                // Parent
                List<HerePhaPoiassoc> herePhaPoiassocList = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getChildId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList.size() > 0) {
                    // poiInfo.setIsParent("1");
                    List<String> parentIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList
                    ) {
                        parentIds.add(herePhaPoiassoc.getParentId().toString());
                    }
                    poiInfo.setParent(String.join(",", parentIds));
                }
                // Children
                List<HerePhaPoiassoc> herePhaPoiassocList1 = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getParentId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList1.size() > 0) {
                    // poiInfo.setIsChild("1");
                    List<String> childIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList1
                    ) {
                        childIds.add(herePhaPoiassoc.getChildId().toString());
                    }
                    poiInfo.setChildren(String.join(",", childIds));
                }
                // POI_Geo
                poiInfo.setPoiGeo(herePhaAutosvc.getGeom());
                // Update_Time
                poiInfo.setUpdateTime(LocalDateTime.now());
                poiInfoList.add(poiInfo);
            }
            this.saveOrUpdateBatch(poiInfoList);
        }
    }

    //"here_pha_transhubs", "here_pha_travdest"
    @Async()
    public void poiConvertTranshubs(List<HerePhaTranshubs> herePhaAutosvcList) {
        List<PoiM> poiInfoList = new ArrayList<>();
        if (herePhaAutosvcList.size() > 0) {
            for (HerePhaTranshubs herePhaAutosvc : herePhaAutosvcList
            ) {
                PoiM poiInfo = new PoiM();
                // POI_ID
                poiInfo.setPoiId(UUID.randomUUID().toString());
                // Source_ID
                poiInfo.setSourceId(herePhaAutosvc.getPoiId().toString());
                // Kind
                // poiInfo.setKind(herePhaAutosvc.get);
                // Kind_Code
                poiInfo.setKindCode(herePhaAutosvc.getFacType());
                // Name_S
                if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                    poiInfo.setNameS(herePhaAutosvc.getPoiName());
                } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                // Alias
                {
                    poiInfo.setAlias(herePhaAutosvc.getPoiName());
                } else
                // Name
                {
                    poiInfo.setName(herePhaAutosvc.getPoiName());
                }
                // Name_ENG
                List<HerePhaPoitrans> herePhaPoitransList = herePhaPoitransService.lambdaQuery().eq(HerePhaPoitrans::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoitransList.size() > 0) {
                    // Name_S_ENG
                    if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                        poiInfo.setNameSEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                    // Alias_ENG
                    {
                        poiInfo.setAliasEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else
                    // Name_ENG
                    {
                        poiInfo.setNameEng(herePhaPoitransList.get(0).getPoiNmTr());
                    }
                }
                // Address
                poiInfo.setAddress(herePhaAutosvc.getStName() + herePhaAutosvc.getPoiStNum());
                // Address_ENG
                poiInfo.setAddressEng(herePhaPoitransList.size() > 0 ? herePhaPoitransList.get(0).getActaddrTr() : "");
                // lng_84
                // poiInfo.setLongitudeWgs84();
                // Lat_84
                // poiInfo.setLatitudeWgs84();
                // Lon_Guide_84
                poiInfo.setLonGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[0].replace("POINT(", "")));
                // Lat_Guide_84
                poiInfo.setLatGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[1].replace(")", "")));
                // Link_ID
                poiInfo.setLinkId(herePhaAutosvc.getLinkId());
                // Side
                poiInfo.setSide(herePhaAutosvc.getPoiStSd().isEmpty() ? "N" : herePhaAutosvc.getPoiStSd());
                // Importance
                poiInfo.setImportance("Y".equals(herePhaAutosvc.getNatImport()) ? "1" : "");
                // VAdmin_Code
                poiInfo.setVadminCode(herePhaAutosvc.getVancityId());
                // Zip_Code
                poiInfo.setZipCode(herePhaAutosvc.getActPostal());
                // Telephone
                poiInfo.setTelephone(herePhaAutosvc.getPhNumber());
                // Tel_Type
                // poiInfo.setTelType(herePhaAutosvc.get);
                // Star_Rating
                List<HerePhaPoiattr> herePhaPoiattrList = herePhaPoiattrService.lambdaQuery()
                        .eq(HerePhaPoiattr::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiattrList.size() > 0) {
                    HerePhaPoiattr herePhaPoiattr = herePhaPoiattrList.get(0);
                    if ("7011".equals(herePhaPoiattr.getFacType()) && "33".equals(herePhaPoiattr.getAttrType())) {
                        switch (herePhaPoiattr.getAttrValue()) {
                            case "17":
                                poiInfo.setStarRating("0");
                                break;
                            case "18":
                                poiInfo.setStarRating("1");
                                break;
                            case "1026":
                                poiInfo.setStarRating("1");
                                break;
                            case "1030":
                                poiInfo.setStarRating("8");
                                break;
                            case "1029":
                                poiInfo.setStarRating("3");
                                break;
                            case "1028":
                                poiInfo.setStarRating("4");
                                break;
                            case "1027":
                                poiInfo.setStarRating("5");
                                break;
                            default:
                                poiInfo.setStarRating("0");
                        }
                    }
                }
                // Truck_Flag
                // Food_Type
                List<HerePhaRestrnts> herePhaRestrntsList = herePhaRestrntsService.lambdaQuery()
                        .eq(HerePhaRestrnts::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaRestrntsList.size() > 0) {
                    poiInfo.setFoodType(herePhaRestrntsList.get(0).getFoodType());
                }
                // Airpt_Code
                // Brand
                List<Mtdref> mtdrefList = mtdrefService.lambdaQuery().eq(Mtdref::getCode, herePhaAutosvc.getChainId()).list();
                if (mtdrefList.size() > 0) {
                    poiInfo.setBrand(mtdrefList.get(0).getRefClass());
                }
                // Parent
                List<HerePhaPoiassoc> herePhaPoiassocList = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getChildId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList.size() > 0) {
                    // poiInfo.setIsParent("1");
                    List<String> parentIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList
                    ) {
                        parentIds.add(herePhaPoiassoc.getParentId().toString());
                    }
                    poiInfo.setParent(String.join(",", parentIds));
                }
                // Children
                List<HerePhaPoiassoc> herePhaPoiassocList1 = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getParentId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList1.size() > 0) {
                    // poiInfo.setIsChild("1");
                    List<String> childIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList1
                    ) {
                        childIds.add(herePhaPoiassoc.getChildId().toString());
                    }
                    poiInfo.setChildren(String.join(",", childIds));
                }
                // POI_Geo
                poiInfo.setPoiGeo(herePhaAutosvc.getGeom());
                // Update_Time
                poiInfo.setUpdateTime(LocalDateTime.now());
                poiInfoList.add(poiInfo);
            }
            this.saveOrUpdateBatch(poiInfoList);
        }
    }

    //"here_pha_travdest"
    @Async()
    public void poiConvertTravdest(List<HerePhaTravdest> herePhaAutosvcList) {
        List<PoiM> poiInfoList = new ArrayList<>();
        if (herePhaAutosvcList.size() > 0) {
            for (HerePhaTravdest herePhaAutosvc : herePhaAutosvcList
            ) {
                PoiM poiInfo = new PoiM();
                // POI_ID
                poiInfo.setPoiId(UUID.randomUUID().toString());
                // Source_ID
                poiInfo.setSourceId(herePhaAutosvc.getPoiId().toString());
                // Kind
                // poiInfo.setKind(herePhaAutosvc.get);
                // Kind_Code
                poiInfo.setKindCode(herePhaAutosvc.getFacType());
                // Name_S
                if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                    poiInfo.setNameS(herePhaAutosvc.getPoiName());
                } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                // Alias
                {
                    poiInfo.setAlias(herePhaAutosvc.getPoiName());
                } else
                // Name
                {
                    poiInfo.setName(herePhaAutosvc.getPoiName());
                }
                // Name_ENG
                List<HerePhaPoitrans> herePhaPoitransList = herePhaPoitransService.lambdaQuery().eq(HerePhaPoitrans::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoitransList.size() > 0) {
                    // Name_S_ENG
                    if ("J".equals(herePhaAutosvc.getPoiNmtype())) {
                        poiInfo.setNameSEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else if ("S".equals(herePhaAutosvc.getPoiNmtype()))
                    // Alias_ENG
                    {
                        poiInfo.setAliasEng(herePhaPoitransList.get(0).getPoiNmTr());
                    } else
                    // Name_ENG
                    {
                        poiInfo.setNameEng(herePhaPoitransList.get(0).getPoiNmTr());
                    }
                }
                // Address
                poiInfo.setAddress(herePhaAutosvc.getStName() + herePhaAutosvc.getPoiStNum());
                // Address_ENG
                poiInfo.setAddressEng(herePhaPoitransList.size() > 0 ? herePhaPoitransList.get(0).getActaddrTr() : "");
                // lng_84
                // poiInfo.setLongitudeWgs84();
                // Lat_84
                // poiInfo.setLatitudeWgs84();
                // Lon_Guide_84
                poiInfo.setLonGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[0].replace("POINT(", "")));
                // Lat_Guide_84
                poiInfo.setLatGuide(Double.parseDouble(herePhaAutosvc.getGeom().split(" ")[1].replace(")", "")));
                // Link_ID
                poiInfo.setLinkId(herePhaAutosvc.getLinkId());
                // Side
                poiInfo.setSide(herePhaAutosvc.getPoiStSd().isEmpty() ? "N" : herePhaAutosvc.getPoiStSd());
                // Importance
                poiInfo.setImportance("Y".equals(herePhaAutosvc.getNatImport()) ? "1" : "");
                // VAdmin_Code
                poiInfo.setVadminCode(herePhaAutosvc.getVancityId());
                // Zip_Code
                poiInfo.setZipCode(herePhaAutosvc.getActPostal());
                // Telephone
                poiInfo.setTelephone(herePhaAutosvc.getPhNumber());
                // Tel_Type
                // poiInfo.setTelType(herePhaAutosvc.get);
                // Star_Rating
                List<HerePhaPoiattr> herePhaPoiattrList = herePhaPoiattrService.lambdaQuery()
                        .eq(HerePhaPoiattr::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiattrList.size() > 0) {
                    HerePhaPoiattr herePhaPoiattr = herePhaPoiattrList.get(0);
                    if ("7011".equals(herePhaPoiattr.getFacType()) && "33".equals(herePhaPoiattr.getAttrType())) {
                        switch (herePhaPoiattr.getAttrValue()) {
                            case "17":
                                poiInfo.setStarRating("0");
                                break;
                            case "18":
                                poiInfo.setStarRating("1");
                                break;
                            case "1026":
                                poiInfo.setStarRating("1");
                                break;
                            case "1030":
                                poiInfo.setStarRating("8");
                                break;
                            case "1029":
                                poiInfo.setStarRating("3");
                                break;
                            case "1028":
                                poiInfo.setStarRating("4");
                                break;
                            case "1027":
                                poiInfo.setStarRating("5");
                                break;
                            default:
                                poiInfo.setStarRating("0");
                        }
                    }
                }
                // Truck_Flag
                // Food_Type
                List<HerePhaRestrnts> herePhaRestrntsList = herePhaRestrntsService.lambdaQuery()
                        .eq(HerePhaRestrnts::getPoiId, herePhaAutosvc.getPoiId()).list();
                if (herePhaRestrntsList.size() > 0) {
                    poiInfo.setFoodType(herePhaRestrntsList.get(0).getFoodType());
                }
                // Airpt_Code
                // Brand
                List<Mtdref> mtdrefList = mtdrefService.lambdaQuery().eq(Mtdref::getCode, herePhaAutosvc.getChainId()).list();
                if (mtdrefList.size() > 0) {
                    poiInfo.setBrand(mtdrefList.get(0).getRefClass());
                }
                // Parent
                List<HerePhaPoiassoc> herePhaPoiassocList = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getChildId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList.size() > 0) {
                    // poiInfo.setIsParent("1");
                    List<String> parentIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList
                    ) {
                        parentIds.add(herePhaPoiassoc.getParentId().toString());
                    }
                    poiInfo.setParent(String.join(",", parentIds));
                }
                // Children
                List<HerePhaPoiassoc> herePhaPoiassocList1 = herePhaPoiassocService.lambdaQuery()
                        .eq(HerePhaPoiassoc::getParentId, herePhaAutosvc.getPoiId()).list();
                if (herePhaPoiassocList1.size() > 0) {
                    // poiInfo.setIsChild("1");
                    List<String> childIds = new ArrayList<>();
                    for (HerePhaPoiassoc herePhaPoiassoc : herePhaPoiassocList1
                    ) {
                        childIds.add(herePhaPoiassoc.getChildId().toString());
                    }
                    poiInfo.setChildren(String.join(",", childIds));
                }
                // POI_Geo
                poiInfo.setPoiGeo(herePhaAutosvc.getGeom());
                // Update_Time
                poiInfo.setUpdateTime(LocalDateTime.now());
                poiInfoList.add(poiInfo);
            }
            this.saveOrUpdateBatch(poiInfoList);
        }
    }

    @Async("asyncTaskExecutor")
    public void poiMatch(List<HllOrder> hllOrderList, Integer version, String country, CountDownLatch countDownLatch) throws IOException {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        MybatisPlusConfig.myTableName.set("");
        try {
            List<PoiMatchRes> poiMatchResList = new ArrayList<>();
            for (HllOrder hllOrder : hllOrderList) {
                PoiMatchRes poiMatchRes = new PoiMatchRes();
                poiMatchRes.setId(UUID.randomUUID().toString());
                poiMatchRes.setHllOrderPoiId(hllOrder.getPlaceId());
                poiMatchRes.setVersion(version);
                poiMatchRes.setLongitude(new BigDecimal(hllOrder.getLongitude()));
                poiMatchRes.setLatitude(new BigDecimal(hllOrder.getLatitude()));
                poiMatchRes.setCreateTime(LocalDateTime.now());
                poiMatchRes.setUpdateTime(LocalDateTime.now());

                if (hllOrder.getLatitude() != null && hllOrder.getLongitude() != null) {
                    String geoWkt = "POINT(" + hllOrder.getLongitude() + " " + hllOrder.getLatitude() + ")";
                    try {
                        poiMatchRes.setPoiGeo(new WKTReader().read(geoWkt).toString());
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                }
                // 1.根据坐标查询google api对应的数据
                List<String> googlePoiNameList = new ArrayList<>();
                List<String> googleIdList = new ArrayList<>();
                // 双向map，key = poi id，value =  poi名称
                BiMap<String, String> googleMap = new BiMap<String, String>(new HashMap<>());

                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("location", hllOrder.getLatitude() + "%2c" + hllOrder.getLongitude());
                paramMap.put("radius", 50);
                paramMap.put("key", "AIzaSyDsNcYWxVX_1NX5bb2VJz8rOhYDtoDQmlQ");
                // String responseJSON = callGoogleApi(paramMap);
                String responseJSON = callGoogleApiFeign(paramMap);

                JSONObject jsonObject = JSONObject.parseObject(responseJSON);
                jsonObject = Optional.ofNullable(jsonObject).orElse(new JSONObject());
                if ("OK".equals(jsonObject.get("status"))) {
                    JSONArray jsonArray = jsonObject.getJSONArray("results");
                    if (jsonArray.size() > 0) {
                        for (int i = 0; i < jsonArray.size(); i++) {
                            JSONObject internalObj = (JSONObject) jsonArray.get(i);
                            googlePoiNameList.add((String) internalObj.get("name"));
                            googleIdList.add((String) internalObj.get("place_id"));
                            googleMap.put((String) internalObj.get("place_id"), (String) internalObj.get("name"));
                        }
                    }
                }

                // 2.根据坐标查询here poi 范围数据
                List<PoiM> herePoiList = this.lambdaQuery().select(PoiM::getName, PoiM::getSourceId)
                        .last("where name is not null and ST_DWithin(poi_geo, ST_GeomFromText('POINT(" + hllOrder.getLongitude() + " " + hllOrder.getLatitude() + ")',4326), 0.0005)").list();
                List<String> herePoiNameList = herePoiList.stream().map(PoiM::getName).collect(Collectors.toList());
                List<String> hereIdList = herePoiList.stream().map(PoiM::getSourceId).collect(Collectors.toList());
                Map<String, String> hereTmpMap = herePoiList.stream().collect(Collectors.toMap(PoiM::getSourceId, PoiM::getName));
                BiMap<String, String> hereMap = new BiMap<>(hereTmpMap);
                // 3.调用算法获取匹配度
                if (CollUtil.isNotEmpty(googlePoiNameList) && CollUtil.isNotEmpty(herePoiNameList)) {
                    for (String gName : googlePoiNameList) {
                        for (String hName : herePoiNameList) {
                            float levenshtein = CommonUtils.levenshtein(StrUtil.trim(gName).toLowerCase(), StrUtil.trim(hName).toLowerCase());
                            if (levenshtein > 0.8) {
                                poiMatchRes.setIsMatch(1);
                                poiMatchRes.setSimilarity(new BigDecimal(levenshtein));
                                poiMatchRes.setHereMatchPoiId(hereMap.getKey(hName));
                                poiMatchRes.setGoogleMatchPoiId(googleMap.getKey(gName));
                                break;
                            } else {
                                poiMatchRes.setIsMatch(0);
                                poiMatchRes.setSimilarity(new BigDecimal(levenshtein));
                            }
                        }
                        if (poiMatchRes.getSimilarity().compareTo(new BigDecimal(0.8)) > -1) {
                            break;
                        }
                    }
                } else {
                    poiMatchRes.setIsMatch(0);
                    poiMatchRes.setSimilarity(new BigDecimal(0));
                }

                // 4.封装数据
                poiMatchRes.setGooglePoiName(CollUtil.join(googlePoiNameList, ","));
                poiMatchRes.setHerePoiName(CollUtil.join(herePoiNameList, ","));
                poiMatchRes.setGooglePoiId(CollUtil.join(googleIdList, ","));
                poiMatchRes.setHerePoiId(CollUtil.join(hereIdList, ","));
                poiMatchResList.add(poiMatchRes);
            }

            poiMatchResService.saveBatch(poiMatchResList);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("here google poi match error,detail is {}", e.getMessage());
            // Thread.currentThread().interrupt();
            throw e;
        } finally {
            countDownLatch.countDown();
        }
    }

    private String callGoogleApi(Map<String, Object> paramMap) throws IOException {
        String googleApiUrl = "https://maps.googleapis.com/maps/api/place/nearbysearch/json";
        StringBuilder sb = new StringBuilder(googleApiUrl);
        String paramUrl = "?";
        for (Map.Entry<String, Object> param : paramMap.entrySet()) {
            paramUrl = paramUrl + param.getKey() + "=" + param.getValue() + "&";
        }
        paramUrl = StrUtil.removeSuffix(paramUrl, "&");

        OkHttpClient client = new OkHttpClient().newBuilder().build();
        Request request = new Request.Builder().url(sb.append(paramUrl).toString()).method("GET", null).build();
        System.out.println("send url:" + sb.append(paramUrl));
        Response response;
        String jsonString = "";
        try {
            response = client.newCall(request).execute();
            jsonString = response.body().string();
        } catch (IOException e) {
            e.printStackTrace();
            if (e instanceof ConnectException) {
                while (true) {
                    try {
                        response = client.newCall(request).execute();
                        jsonString = response.body().string();
                    } catch (IOException ex) {
                        ex.printStackTrace();
                    }
                    if (StrUtil.isNotEmpty(jsonString)) {
                        break;
                    }
                }
            }
        }
        return jsonString;
    }


    private String callGoogleApiFeign(Map<String, Object> paramMap) {
        String result = googleApiService.getMatchResult(paramMap);
        return result;
    }


    private String callWrapperGoogleApiFeign(Map<String, Object> paramMap) {
        String result = wrapperGoogleApiService.getMatchResult(paramMap);
        return result;
    }

    @Override
    public int googlePoiMatch(List<Place> placeList, String countryPrefix) {
        // 1.获取国家对应的版本号，如果没有，则为1
        int version;
        String lastSql = "limit 1";
        List<PoiMatch> matchList = poiMatchService.lambdaQuery().select(PoiMatch::getVersion).eq(PoiMatch::getCountryPrefix, countryPrefix).eq(PoiMatch::getMatchSrc, "GOOGLE").orderByDesc(PoiMatch::getVersion).last(lastSql).list();
        if (CollUtil.isNotEmpty(matchList)) {
            version = matchList.get(0).getVersion() + 1;
        } else {
            version = 1;
        }

        List<PoiMatch> poiMatchList = new ArrayList<>();
        for (Place place : placeList) {
            PoiMatch poiMatchRes = new PoiMatch();
            poiMatchRes.setId(UUID.randomUUID().toString());
            poiMatchRes.setPoiName(place.getPlaceName());
            poiMatchRes.setVersion(version);
            poiMatchRes.setCountryPrefix(countryPrefix);
            poiMatchRes.setLongitude(new BigDecimal(place.getLongitude()));
            poiMatchRes.setLatitude(new BigDecimal(place.getLatitude()));
            poiMatchRes.setCreateTime(LocalDateTime.now());
            poiMatchRes.setUpdateTime(LocalDateTime.now());
            poiMatchRes.setMatchSrc("GOOGLE");

            if (place.getLatitude() != null && place.getLongitude() != null) {
                String geoWkt = "POINT(" + place.getLongitude() + " " + place.getLatitude() + ")";
                try {
                    Point geometry = (Point) new WKTReader().read(geoWkt);
                    geometry.setSRID(4326);
                    poiMatchRes.setPoiGeo(geometry.toString());
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            }
            // 1.根据坐标查询google api对应的数据
            List<String> googlePoiNameList = new ArrayList<>();
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("location", place.getLatitude() + "%2c" + place.getLongitude());
            paramMap.put("radius", 50);
            paramMap.put("key", "AIzaSyDsNcYWxVX_1NX5bb2VJz8rOhYDtoDQmlQ");
            String responseJSON = null;
            try {
                responseJSON = callGoogleApi(paramMap);
            } catch (IOException e) {
                e.printStackTrace();
            }
            // String responseJSON = callGoogleApiFeign(paramMap);

            JSONObject jsonObject = JSONObject.parseObject(responseJSON);
            jsonObject = Optional.ofNullable(jsonObject).orElse(new JSONObject());
            if ("OK".equals(jsonObject.get("status"))) {
                JSONArray jsonArray = jsonObject.getJSONArray("results");
                if (jsonArray.size() > 0) {
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject internalObj = (JSONObject) jsonArray.get(i);
                        googlePoiNameList.add((String) internalObj.get("name"));
                    }
                }
            }

            // 2.调用算法获取匹配度
            if (CollUtil.isNotEmpty(googlePoiNameList)) {
                for (String gName : googlePoiNameList) {
                    float levenshtein = CommonUtils.levenshtein(StrUtil.trim(gName).toLowerCase(), StrUtil.trim(place.getPlaceName()).toLowerCase());
                    if (levenshtein > 0.8) {
                        poiMatchRes.setIsMatch(1);
                        poiMatchRes.setSimilarity(new BigDecimal(levenshtein));
                        poiMatchRes.setGooglePoiName(gName);
                        break;
                    }
                }
                if (StrUtil.isEmpty(poiMatchRes.getGooglePoiName())) {
                    poiMatchRes.setIsMatch(0);
                    poiMatchRes.setSimilarity(new BigDecimal(0));
                    poiMatchRes.setGooglePoiName(null);
                }
            } else {
                poiMatchRes.setIsMatch(0);
                poiMatchRes.setSimilarity(new BigDecimal(0));
                poiMatchRes.setGooglePoiName(null);
            }
            poiMatchList.add(poiMatchRes);
        }
        int matchFieldNum = BeanUtil.beanToMap(new PoiMatch()).keySet().size();
        int matchBatchSize = 32767 / matchFieldNum;
        log.info("the number of insert poiMatch is {}", poiMatchList.size());
        List<List<PoiMatch>> splitPoiMatchList = CollUtil.splitList(poiMatchList, matchBatchSize);
        for (List<PoiMatch> insertPoiMatchs : splitPoiMatchList) {
            poiMatchMapper.insertBatch(insertPoiMatchs);
        }
        return version;
    }

    @Override
    public int herePoiMatch(List<Place> placeList, String country) {
        // 1.获取国家对应的版本号，如果没有，则为1
        int version;
        String lastSql = "limit 1";
        List<PoiMatch> matchList = poiMatchService.lambdaQuery().select(PoiMatch::getVersion).eq(PoiMatch::getCountryPrefix, country).eq(PoiMatch::getMatchSrc, "HERE").orderByDesc(PoiMatch::getVersion).last(lastSql).list();
        if (CollUtil.isNotEmpty(matchList)) {
            version = matchList.get(0).getVersion() + 1;
        } else {
            version = 1;
        }
        if (StrUtil.isNotEmpty(country)) {
            MybatisPlusConfig.myTableName.set("_" + country);
        }
        List<PoiMatch> poiMatchList = new ArrayList<>();
        for (Place place : placeList) {
            PoiMatch poiMatchRes = new PoiMatch();
            poiMatchRes.setId(UUID.randomUUID().toString());
            poiMatchRes.setPoiName(place.getPlaceName());
            poiMatchRes.setVersion(version);
            poiMatchRes.setCountryPrefix(country);
            poiMatchRes.setLongitude(new BigDecimal(place.getLongitude()));
            poiMatchRes.setLatitude(new BigDecimal(place.getLatitude()));
            poiMatchRes.setCreateTime(LocalDateTime.now());
            poiMatchRes.setUpdateTime(LocalDateTime.now());
            poiMatchRes.setMatchSrc("HERE");
            poiMatchRes.setPoiId(place.getPlaceId());

            if (place.getLatitude() != null && place.getLongitude() != null) {
                String geoWkt = "POINT(" + place.getLongitude() + " " + place.getLatitude() + ")";
                poiMatchRes.setPoiGeo(geoWkt);
            }
            // 1.根据坐标查询here对应的数据
            List<PoiM> herePoiList = this.lambdaQuery().select(PoiM::getName, PoiM::getAlias, PoiM::getLatitude, PoiM::getLongitude, PoiM::getSourceId)
                    .last("where name is not null and ST_DWithin(poi_geo, ST_GeomFromText('POINT(" + place.getLongitude() + " " + place.getLatitude() + ")',4326), 0.003)").list();
            // List<String> herePoiNameList = herePoiList.stream().map(Poi::getName).collect(Collectors.toList());
            // 2.调用算法获取匹配度
            if (CollUtil.isNotEmpty(herePoiList)) {
                for (PoiM poiM : herePoiList) {
                    float levenshtein = CommonUtils.levenshtein(StrUtil.trim(poiM.getName()).toLowerCase(), StrUtil.trim(place.getPlaceName()).toLowerCase());
                    if (levenshtein < 0.6 && StrUtil.isNotEmpty(poiM.getAlias())) {
                        levenshtein = CommonUtils.levenshtein(StrUtil.trim(poiM.getAlias()).toLowerCase(), StrUtil.trim(place.getPlaceName()).toLowerCase());
                        if (levenshtein > 0.6) {
                            poiMatchRes.setIsMatch(1);
                            poiMatchRes.setSimilarity(new BigDecimal(levenshtein));
                            poiMatchRes.setHerePoiName(poiM.getAlias());
                            poiMatchRes.setHerePoiId(poiM.getSourceId());
                            poiMatchRes.setHereLongitude(new BigDecimal(poiM.getLongitude()));
                            poiMatchRes.setHereLatitude(new BigDecimal(poiM.getLatitude()));
                            break;
                        } else {
                            poiMatchRes.setIsMatch(0);
                            poiMatchRes.setSimilarity(new BigDecimal(levenshtein));
                            poiMatchRes.setHerePoiName(poiM.getAlias());
                            poiMatchRes.setHerePoiId(poiM.getSourceId());
                            poiMatchRes.setHereLongitude(new BigDecimal(poiM.getLongitude()));
                            poiMatchRes.setHereLatitude(new BigDecimal(poiM.getLatitude()));
                        }
                    }
                    if (levenshtein > 0.6) {
                        poiMatchRes.setIsMatch(1);
                        poiMatchRes.setSimilarity(new BigDecimal(levenshtein));
                        poiMatchRes.setHerePoiName(poiM.getName());
                        poiMatchRes.setHerePoiId(poiM.getSourceId());
                        poiMatchRes.setHereLongitude(new BigDecimal(poiM.getLongitude()));
                        poiMatchRes.setHereLatitude(new BigDecimal(poiM.getLatitude()));
                        break;
                    } else {
                        poiMatchRes.setIsMatch(0);
                        poiMatchRes.setSimilarity(new BigDecimal(levenshtein));
                        poiMatchRes.setHerePoiName(poiM.getName());
                        poiMatchRes.setHerePoiId(poiM.getSourceId());
                        poiMatchRes.setHereLongitude(new BigDecimal(poiM.getLongitude()));
                        poiMatchRes.setHereLatitude(new BigDecimal(poiM.getLatitude()));
                    }
                }
                // if (StrUtil.isEmpty(poiMatchRes.getHerePoiName())) {
                //    poiMatchRes.setIsMatch(0);
                //    poiMatchRes.setSimilarity(new BigDecimal(0));
                //    poiMatchRes.setHerePoiName(null);
                //}
            } else {
                poiMatchRes.setIsMatch(0);
                poiMatchRes.setSimilarity(new BigDecimal(0));
                poiMatchRes.setHerePoiName(null);
            }
            poiMatchList.add(poiMatchRes);
        }
        int matchFieldNum = BeanUtil.beanToMap(new PoiMatch()).keySet().size();
        int matchBatchSize = 32767 / matchFieldNum;
        log.info("the number of insert poiMatch is {}", poiMatchList.size());
        List<List<PoiMatch>> splitPoiMatchList = CollUtil.splitList(poiMatchList, matchBatchSize);
        for (List<PoiMatch> insertPoiMatchs : splitPoiMatchList) {
            poiMatchMapper.insertBatch(insertPoiMatchs);
        }
        return version;
    }

    @Override
    public int hereGooglePoiMatch(List<PoiM> poiList, String countryPrefix) {
        // 1.获取国家对应的版本号，如果没有，则为1
        int version;
        String lastSql = "limit 1";
        List<PoiMatch> matchList = poiMatchService.lambdaQuery().select(PoiMatch::getVersion).eq(PoiMatch::getCountryPrefix, countryPrefix).eq(PoiMatch::getMatchSrc, "GOOGLE").orderByDesc(PoiMatch::getVersion).last(lastSql).list();
        if (CollUtil.isNotEmpty(matchList)) {
            version = matchList.get(0).getVersion() + 1;
        } else {
            version = 1;
        }

        List<PoiMatch> poiMatchList = new ArrayList<>();
        for (PoiM poiM : poiList) {
            PoiMatch poiMatchRes = new PoiMatch();
            poiMatchRes.setId(UUID.randomUUID().toString());
            poiMatchRes.setPoiName(poiM.getName());
            poiMatchRes.setVersion(version);
            poiMatchRes.setCountryPrefix(countryPrefix);
            poiMatchRes.setLongitude(new BigDecimal(poiM.getLongitude()));
            poiMatchRes.setLatitude(new BigDecimal(poiM.getLatitude()));
            poiMatchRes.setCreateTime(LocalDateTime.now());
            poiMatchRes.setUpdateTime(LocalDateTime.now());
            poiMatchRes.setMatchSrc("GOOGLE");

            if (poiM.getLatitude() != null && poiM.getLongitude() != null) {
                String geoWkt = "POINT(" + poiM.getLongitude() + " " + poiM.getLatitude() + ")";
                poiMatchRes.setPoiGeo("SRID=4326;" + geoWkt);
            }
            // 1.根据坐标查询google api对应的数据
            List<String> googlePoiNameList = new ArrayList<>();
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("location", poiM.getLatitude() + "%2c" + poiM.getLongitude());
            paramMap.put("radius", 50);
            paramMap.put("key", "AIzaSyDsNcYWxVX_1NX5bb2VJz8rOhYDtoDQmlQ");
            String responseJSON = null;
            try {
                responseJSON = callGoogleApi(paramMap);
            } catch (IOException e) {
                e.printStackTrace();
            }
            // String responseJSON = callGoogleApiFeign(paramMap);

            JSONObject jsonObject = JSONObject.parseObject(responseJSON);
            jsonObject = Optional.ofNullable(jsonObject).orElse(new JSONObject());
            if ("OK".equals(jsonObject.get("status"))) {
                JSONArray jsonArray = jsonObject.getJSONArray("results");
                if (jsonArray.size() > 0) {
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject internalObj = (JSONObject) jsonArray.get(i);
                        googlePoiNameList.add((String) internalObj.get("name"));
                    }
                }
            }

            // 2.调用算法获取匹配度
            if (CollUtil.isNotEmpty(googlePoiNameList)) {
                for (String gName : googlePoiNameList) {
                    float levenshtein = CommonUtils.levenshtein(StrUtil.trim(gName).toLowerCase(), StrUtil.trim(poiM.getName()).toLowerCase());
                    if (levenshtein < 0.5 && StrUtil.isNotEmpty(poiM.getAlias())) {
                        levenshtein = CommonUtils.levenshtein(StrUtil.trim(gName).toLowerCase(), StrUtil.trim(poiM.getAlias()).toLowerCase());
                        if (levenshtein > 0.5) {
                            poiMatchRes.setIsMatch(1);
                            poiMatchRes.setSimilarity(new BigDecimal(levenshtein));
                            poiMatchRes.setHerePoiName(poiM.getAlias());
                            break;
                        }
                    }
                    if (levenshtein > 0.5) {
                        poiMatchRes.setIsMatch(1);
                        poiMatchRes.setSimilarity(new BigDecimal(levenshtein));
                        poiMatchRes.setGooglePoiName(gName);
                        break;
                    }
                }
                if (StrUtil.isEmpty(poiMatchRes.getGooglePoiName())) {
                    poiMatchRes.setIsMatch(0);
                    poiMatchRes.setSimilarity(new BigDecimal(0));
                    poiMatchRes.setGooglePoiName(null);
                }
            } else {
                poiMatchRes.setIsMatch(0);
                poiMatchRes.setSimilarity(new BigDecimal(0));
                poiMatchRes.setGooglePoiName(null);
            }
            poiMatchList.add(poiMatchRes);
        }
        poiMatchMapper.insertBatch(poiMatchList);
        return version;
    }

    @Async("asyncTaskExecutor")
    public void updatePoiIds(String area, String country, CountDownLatch countDownLatch, List<PoiM> poiList) {
        try {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            int fieldNum = BeanUtil.beanToMap(new PoiM()).keySet().size();
            int batchSize = 32767 / fieldNum;
            log.info("batchInsert size is {}", batchSize);
            List<List<PoiM>> splitList = ListUtil.split(poiList, batchSize);
            for (List<PoiM> pois : splitList) {
                List<PoiM> resPoiList = new ArrayList<>();
                List<String> srcPoiIds = pois.stream().map(PoiM::getPoiId).collect(Collectors.toList());
                List<Long> poiIds = inheritIDService.inheritID(new InheritIDDTO(12L, srcPoiIds));
                if (srcPoiIds.size() == poiIds.size()) {
                    for (int j = 0; j < pois.size(); j++) {
                        pois.get(j).setPoiId(srcPoiIds.get(j));
                        resPoiList.add(pois.get(j));
                    }
                }

                // log.info("insert datasource is {}", DynamicDataSourceContextHolder.peek());
                poiMMapper.mysqlInsertOrUpdateBath(resPoiList);
            }
        } catch (Exception e) {
            log.error("handle poi inherited id(link_id) error,detail is {}", e.getMessage());
            throw e;
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    @Async("asyncTaskExecutor")
    public void handleId(String area, String country, CountDownLatch countDownLatch, List<PoiM> poiList) {
        try {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            int fieldNum = BeanUtil.beanToMap(new PoiM()).keySet().size();
            int batchSize = 32767 / fieldNum;
            log.info("batchInsert size is {}", batchSize);
            List<List<PoiM>> splitList = ListUtil.split(poiList, batchSize);
            for (List<PoiM> pois : splitList) {
                List<PoiM> resPoiList = new ArrayList<>();
                for (PoiM poiM : pois) {
                    // 处理 link_id
                    if (StrUtil.isNotEmpty(poiM.getLinkId())) {
                        poiM.setLinkId(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(poiM.getLinkId()))).get(0)));
                    } else {
                        poiM.setLinkId(poiM.getLinkId());
                    }
                    resPoiList.add(poiM);
                    // log.info("insert datasource is {}", DynamicDataSourceContextHolder.peek());
                    poiMMapper.mysqlInsertOrUpdateBath(resPoiList);
                }
            }
        } catch (Exception e) {
            log.error("handle poi inherited id(link_id) error,detail is {}", e.getMessage());
            throw e;
        } finally {
            countDownLatch.countDown();
        }
    }

    public Integer autoCalcPoiCoverageRatio(String ds, String country, String radius, Double similarity) {
        try {
            //1.获取某个国家poi信息
            if (!ds.isEmpty()) {
                // local.yml:mnr
                DynamicDataSourceContextHolder.push("db2");
            }
            if (!country.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + country);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<OrderPoi> orderPois = orderPoiMapper.selectList(null);

            //2.依次计算相似度
            PoiCoverageRes poiCoverageRes;
            String sourceDb = CommonUtils.getDsbyCountry(country, false);
            List<PoiCoverageRes> resList = new ArrayList<>();
            for (OrderPoi pois : orderPois) {
                poiCoverageRes = new PoiCoverageRes();
                BeanUtil.copyProperties(pois, poiCoverageRes);
                CompletableFuture<PoiCoverageRes> task1 = calcNameVsHerePoiName(sourceDb, pois, poiCoverageRes, radius, similarity);
                CompletableFuture<PoiCoverageRes> task2 = calcNameVsHerePoiAliasEng(sourceDb, pois, poiCoverageRes, radius, similarity);
                CompletableFuture<PoiCoverageRes> task3 = calcNameVsHerePaName(sourceDb, pois, poiCoverageRes, radius, similarity);
                CompletableFuture<PoiCoverageRes> task4 = calcAddressVsHerePoiName(sourceDb, pois, poiCoverageRes, radius, similarity);
                CompletableFuture<PoiCoverageRes> task5 = calcAddressVsHerePoiAliasEng(sourceDb, pois, poiCoverageRes, radius, similarity);
                CompletableFuture<PoiCoverageRes> task6 = calcAddressVsHerePaName(sourceDb, pois, poiCoverageRes, radius, similarity);
                CompletableFuture<PoiCoverageRes> task7 = calcNameVsGoogle(sourceDb, pois, poiCoverageRes, radius, similarity);
                CompletableFuture.allOf(task1, task2, task3, task4, task5, task6, task7);

                poiCoverageRes.setNameHerePoiName(task1.get().getNameHerePoiName());
                poiCoverageRes.setOrderNamePoiNameSimilarity(task1.get().getOrderNamePoiNameSimilarity());
                poiCoverageRes.setOrderNamePoiNameMatch(task1.get().getOrderNamePoiNameMatch());

                poiCoverageRes.setNameHerePoiAliasEng(task2.get().getNameHerePoiAliasEng());
                poiCoverageRes.setOrderNamePoiAliasSimilarity(task2.get().getOrderNamePoiAliasSimilarity());
                poiCoverageRes.setOrderNamePoiAliasMatch(task2.get().getOrderNamePoiAliasMatch());

                poiCoverageRes.setNameHerePointAddressName(task3.get().getNameHerePointAddressName());
                poiCoverageRes.setOrderNamePaNameSimilarity(task3.get().getOrderNamePaNameSimilarity());
                poiCoverageRes.setOrderNamePaNameMatch(task3.get().getOrderNamePaNameMatch());


                poiCoverageRes.setAddressHerePoiName(task4.get().getAddressHerePoiName());
                poiCoverageRes.setOrderAddressPoiNameSimilarity(task4.get().getOrderAddressPoiNameSimilarity());
                poiCoverageRes.setOrderAddressPoiNameMatch(task4.get().getOrderAddressPoiNameMatch());

                poiCoverageRes.setAddressHerePoiAliasEng(task5.get().getAddressHerePoiAliasEng());
                poiCoverageRes.setOrderAddressPoiAliasSimilarity(task5.get().getOrderAddressPoiAliasSimilarity());
                poiCoverageRes.setOrderAddressPoiAliasMatch(task5.get().getOrderAddressPoiAliasMatch());

                poiCoverageRes.setAddressHerePointAddressName(task6.get().getAddressHerePointAddressName());
                poiCoverageRes.setOrderAddressPaNameSimilarity(task6.get().getOrderAddressPaNameSimilarity());
                poiCoverageRes.setOrderAddressPaNameMatch(task6.get().getOrderAddressPaNameMatch());

                poiCoverageRes.setNameGooglePoiName(task7.get().getNameGooglePoiName());
                poiCoverageRes.setOrderNameGooglePoiNameSimilarity(task7.get().getOrderNameGooglePoiNameSimilarity());
                poiCoverageRes.setOrderNameGooglePoiNameMatch(task7.get().getOrderNameGooglePoiNameMatch());

                poiCoverageRes.setUpDate(LocalDateTime.now());
                poiCoverageRes.setRadius(radius);
                poiCoverageRes.setSimilarity(similarity);
                resList.add(poiCoverageRes);
            }
            //3.保存匹配结果
            if (!ds.isEmpty()) {
                // local.yml:mnr
                DynamicDataSourceContextHolder.push("db2");
            }
            int batchNum = 32767 / BeanUtil.beanToMap(new PoiCoverageRes()).keySet().size();
            List<List<PoiCoverageRes>> lists = CollUtil.splitList(resList, batchNum);
            int resNum = 0;
            for (List<PoiCoverageRes> coverageResList : lists) {
                resNum += poiCoverageResMapper.insertBatch(coverageResList);
            }
            return resNum;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Async("asyncTaskExecutor")
    protected CompletableFuture<PoiCoverageRes> calcNameVsGoogle(String sourceDb, OrderPoi pois, PoiCoverageRes poiCoverageRes, String radius, Double similarity) {

        // 1.根据坐标查询google api对应的数据
        List<String> googlePoiNameList = new ArrayList<>();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("location", pois.getLongitude() + "," + pois.getLatitude());
        paramMap.put("radius", (int) (Double.valueOf(radius) * 100000));
        paramMap.put("key", "8294-319c5fda0a1f");
        String responseJSON = callWrapperGoogleApiFeign(paramMap);

        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(responseJSON);
        Integer res = (Integer) jsonObject.get("ret");
        if (res != 0) {
            return CompletableFuture.completedFuture(poiCoverageRes);
        }
        cn.hutool.json.JSONObject jsonObject1 = (cn.hutool.json.JSONObject) jsonObject.get("data");
        if (jsonObject1 == null) {
            return CompletableFuture.completedFuture(poiCoverageRes);
        }
        cn.hutool.json.JSONArray jsonArray = jsonObject1.getJSONArray("pois");
        if (jsonArray != null && jsonArray.size() > 0) {
            for (Object o : jsonArray) {
                String name = (String) ((cn.hutool.json.JSONObject) o).get("name");
                googlePoiNameList.add(name);
            }
        }

        // 2.调用算法获取匹配度
        if (CollUtil.isNotEmpty(googlePoiNameList)) {
            for (String gName : googlePoiNameList) {
                float levenshtein = CommonUtils.levenshtein(StrUtil.trim(gName).toLowerCase(), StrUtil.trim(pois.getName()).toLowerCase());
                if (levenshtein > similarity) {
                    poiCoverageRes.setOrderNameGooglePoiNameMatch(1);
                    poiCoverageRes.setOrderNameGooglePoiNameSimilarity(Double.valueOf(levenshtein));
                    poiCoverageRes.setNameGooglePoiName(gName);
                    break;
                }
            }
        }
        return CompletableFuture.completedFuture(poiCoverageRes);
    }

    @Async("asyncTaskExecutor")
    protected CompletableFuture<PoiCoverageRes> calcAddressVsHerePoiAliasEng(String sourceDb, OrderPoi orderPoi, PoiCoverageRes poiCoverageRes, String radius, Double similarity) {
        //1.获取here poi信息
        if (!sourceDb.isEmpty()) {
            // local.yml:mnr
            DynamicDataSourceContextHolder.push(sourceDb);
        }
        MybatisPlusConfig.myTableName.set("");
        QueryWrapper<PoiM> qw = new QueryWrapper<>();
        qw.last("where name is not null and ST_DWithin(poi_geo, ST_GeomFromText('POINT(" + orderPoi.getLongitude() + " " + orderPoi.getLatitude() + ")'), " + radius + ")");
        List<PoiM> poiList = poiMMapper.selectList(qw);
        //2.计算相似度
        for (PoiM poiM : poiList) {
            if (StrUtil.isEmpty(poiM.getAliasEng())) {
                continue;
            }
            float levenshtein = CommonUtils.levenshtein(StrUtil.trim(poiM.getAliasEng()).toLowerCase(), StrUtil.trim(orderPoi.getAddress()).toLowerCase());
            if (levenshtein > similarity) {
                poiCoverageRes.setAddressHerePoiAliasEng(poiM.getAliasEng());
                poiCoverageRes.setOrderAddressPoiAliasSimilarity(Double.valueOf(levenshtein));
                poiCoverageRes.setOrderAddressPoiAliasMatch(1);
                break;
            } else {
                // 把相似度最高的名称保留记录
                if (levenshtein > poiCoverageRes.getOrderAddressPoiAliasSimilarity()) {
                    poiCoverageRes.setAddressHerePoiAliasEng(poiM.getAliasEng());
                    poiCoverageRes.setOrderAddressPoiAliasSimilarity(Double.valueOf(levenshtein));
                }
            }
        }
        if (poiCoverageRes.getOrderAddressPoiAliasMatch() == null || poiCoverageRes.getOrderAddressPoiAliasSimilarity() < similarity) {
            poiCoverageRes.setOrderAddressPoiAliasMatch(0);
        }
        // poiList = poiList.stream().filter(s->StrUtil.isNotEmpty(s.getAliasEng())).collect(Collectors.toList());
        // List<Poi> pois = poiList.stream().filter(s -> CommonUtils.levenshtein(StrUtil.trim(s.getAliasEng()).toLowerCase(), StrUtil.trim(orderPoi.getAddress()).toLowerCase()) > similarity).collect(Collectors.toList());
        // if (CollUtil.isNotEmpty(pois)) {
        //     poiCoverageRes.setAddressHerePoiAliasEng(CollUtil.join(pois.stream().map(Poi::getName).collect(Collectors.toList()), ","));
        //     poiCoverageRes.setOrderAddressPoiAliasMatch(1);
        // } else {
        //     poiCoverageRes.setOrderAddressPoiAliasMatch(0);
        // }
        return CompletableFuture.completedFuture(poiCoverageRes);
    }

    @Async("asyncTaskExecutor")
    protected CompletableFuture<PoiCoverageRes> calcNameVsHerePoiAliasEng(String sourceDb, OrderPoi orderPoi, PoiCoverageRes poiCoverageRes, String radius, Double similarity) {
        //1.获取here poi信息
        if (!sourceDb.isEmpty()) {
            // local.yml:mnr
            DynamicDataSourceContextHolder.push(sourceDb);
        }
        MybatisPlusConfig.myTableName.set("");
        QueryWrapper<PoiM> qw = new QueryWrapper<>();
        qw.last("where name is not null and ST_DWithin(poi_geo, ST_GeomFromText('POINT(" + orderPoi.getLongitude() + " " + orderPoi.getLatitude() + ")'), " + radius + ")");
        List<PoiM> poiList = poiMMapper.selectList(qw);
        //2.计算相似度
        for (PoiM poiM : poiList) {
            if (StrUtil.isEmpty(poiM.getAliasEng())) {
                continue;
            }
            float levenshtein = CommonUtils.levenshtein(StrUtil.trim(poiM.getAliasEng()).toLowerCase(), StrUtil.trim(orderPoi.getName()).toLowerCase());
            if (levenshtein > similarity) {
                poiCoverageRes.setNameHerePoiAliasEng(poiM.getAliasEng());
                poiCoverageRes.setOrderNamePoiAliasSimilarity(Double.valueOf(levenshtein));
                poiCoverageRes.setOrderNamePoiAliasMatch(1);
                break;
            } else {
                if (levenshtein > poiCoverageRes.getOrderNamePoiAliasSimilarity()) {
                    poiCoverageRes.setNameHerePoiAliasEng(poiM.getAliasEng());
                    poiCoverageRes.setOrderNamePoiAliasSimilarity(Double.valueOf(levenshtein));
                }
            }
        }
        if (poiCoverageRes.getOrderNamePoiAliasMatch() == null || poiCoverageRes.getOrderNamePoiAliasSimilarity() < similarity) {
            poiCoverageRes.setOrderNamePoiAliasMatch(0);
        }
        // poiList = poiList.stream().filter(s->StrUtil.isNotEmpty(s.getAliasEng())).collect(Collectors.toList());
        // List<Poi> pois = poiList.stream().filter(s -> CommonUtils.levenshtein(StrUtil.trim(s.getAliasEng()).toLowerCase(), StrUtil.trim(orderPoi.getName()).toLowerCase()) > similarity).collect(Collectors.toList());
        // if (CollUtil.isNotEmpty(pois)) {
        //     poiCoverageRes.setNameHerePoiAliasEng(CollUtil.join(pois.stream().map(Poi::getName).collect(Collectors.toList()), ","));
        //     poiCoverageRes.setOrderNamePoiAliasMatch(1);
        // } else {
        //     poiCoverageRes.setOrderNamePoiAliasMatch(0);
        // }
        return CompletableFuture.completedFuture(poiCoverageRes);
    }

    @Async("asyncTaskExecutor")
    protected CompletableFuture<PoiCoverageRes> calcAddressVsHerePaName(String sourceDb, OrderPoi orderPoi, PoiCoverageRes poiCoverageRes, String radius, Double similarity) {
        //1.获取here point address信息
        if (!sourceDb.isEmpty()) {
            // local.yml:mnr
            DynamicDataSourceContextHolder.push(sourceDb);
        }
        MybatisPlusConfig.myTableName.set("");
        QueryWrapper<HnPointAddress> qw = new QueryWrapper<>();
        qw.last("where street_name is not null and ST_DWithin(geom, ST_GeomFromText('POINT(" + orderPoi.getLongitude() + " " + orderPoi.getLatitude() + ")',4326), " + radius + ")");
        List<HnPointAddress> hnPointAddresses = hnPointAddressMapper.selectList(qw);
        //2.计算相似度
        for (HnPointAddress hnPointAddress : hnPointAddresses) {
            float levenshtein = CommonUtils.levenshtein(StrUtil.trim(hnPointAddress.getStreetName()).toLowerCase(), StrUtil.trim(orderPoi.getAddress()).toLowerCase());
            if (levenshtein > similarity) {
                poiCoverageRes.setAddressHerePointAddressName(hnPointAddress.getStreetName());
                poiCoverageRes.setOrderAddressPaNameSimilarity(Double.valueOf(levenshtein));
                poiCoverageRes.setOrderAddressPaNameMatch(1);
                break;
            } else {
                // 把相似度最高的名称保留记录
                if (levenshtein > poiCoverageRes.getOrderAddressPoiAliasSimilarity()) {
                    poiCoverageRes.setAddressHerePointAddressName(hnPointAddress.getStreetName());
                    poiCoverageRes.setOrderAddressPaNameSimilarity(Double.valueOf(levenshtein));
                }
            }
        }
        if (poiCoverageRes.getOrderAddressPaNameMatch() == null || poiCoverageRes.getOrderAddressPaNameSimilarity() < similarity) {
            poiCoverageRes.setOrderAddressPaNameMatch(0);
        }
        // List<HnPointAddress> pois = hnPointAddresses.stream().filter(s -> CommonUtils.levenshtein(StrUtil.trim(s.getStreetName()).toLowerCase(), StrUtil.trim(orderPoi.getAddress()).toLowerCase()) > similarity).collect(Collectors.toList());
        // if (CollUtil.isNotEmpty(pois)) {
        //     poiCoverageRes.setAddressHerePointAddressName(CollUtil.join(pois.stream().map(HnPointAddress::getStreetName).collect(Collectors.toList()), ","));
        //     poiCoverageRes.setOrderAddressPaNameMatch(1);
        // } else {
        //     poiCoverageRes.setOrderAddressPaNameMatch(0);
        // }

        return CompletableFuture.completedFuture(poiCoverageRes);
    }

    @Async("asyncTaskExecutor")
    protected CompletableFuture<PoiCoverageRes> calcAddressVsHerePoiName(String sourceDb, OrderPoi orderPoi, PoiCoverageRes poiCoverageRes, String radius, Double similarity) {
        //1.获取here poi信息
        if (!sourceDb.isEmpty()) {
            // local.yml:mnr
            DynamicDataSourceContextHolder.push(sourceDb);
        }
        MybatisPlusConfig.myTableName.set("");
        QueryWrapper<PoiM> qw = new QueryWrapper<>();
        qw.last("where name is not null and ST_DWithin(poi_geo, ST_GeomFromText('POINT(" + orderPoi.getLongitude() + " " + orderPoi.getLatitude() + ")'), " + radius + ")");
        List<PoiM> poiList = poiMMapper.selectList(qw);
        //2.计算相似度
        for (PoiM poiM : poiList) {
            float levenshtein = CommonUtils.levenshtein(StrUtil.trim(poiM.getName()).toLowerCase(), StrUtil.trim(orderPoi.getAddress()).toLowerCase());
            if (levenshtein > similarity) {
                poiCoverageRes.setAddressHerePoiName(poiM.getName());
                poiCoverageRes.setOrderAddressPoiNameSimilarity(Double.valueOf(levenshtein));
                poiCoverageRes.setOrderAddressPoiNameMatch(1);
                break;
            } else {
                if (levenshtein > poiCoverageRes.getOrderAddressPoiNameSimilarity()) {
                    poiCoverageRes.setAddressHerePoiName(poiM.getName());
                    poiCoverageRes.setOrderAddressPoiNameSimilarity(Double.valueOf(levenshtein));
                }
            }
        }
        if (poiCoverageRes.getOrderAddressPoiNameMatch() == null || poiCoverageRes.getOrderAddressPoiNameSimilarity() < similarity) {
            poiCoverageRes.setOrderAddressPoiNameMatch(0);
        }
        // List<Poi> pois = poiList.stream().filter(s -> CommonUtils.levenshtein(StrUtil.trim(s.getName()).toLowerCase(), StrUtil.trim(orderPoi.getAddress()).toLowerCase()) > similarity).collect(Collectors.toList());
        // if (CollUtil.isNotEmpty(pois)) {
        //     poiCoverageRes.setAddressHerePoiName(CollUtil.join(pois.stream().map(Poi::getName).collect(Collectors.toList()), ","));
        //     poiCoverageRes.setOrderAddressPoiNameMatch(1);
        // } else {
        //     poiCoverageRes.setOrderAddressPoiNameMatch(0);
        // }

        return CompletableFuture.completedFuture(poiCoverageRes);
    }

    @Async("asyncTaskExecutor")
    protected CompletableFuture<PoiCoverageRes> calcNameVsHerePaName(String sourceDb, OrderPoi orderPoi, PoiCoverageRes poiCoverageRes, String radius, Double similarity) {
        //1.获取here point address信息
        if (!sourceDb.isEmpty()) {
            // local.yml:mnr
            DynamicDataSourceContextHolder.push(sourceDb);
        }
        MybatisPlusConfig.myTableName.set("");
        QueryWrapper<HnPointAddress> qw = new QueryWrapper<>();
        qw.last("where street_name is not null and ST_DWithin(geom, ST_GeomFromText('POINT(" + orderPoi.getLongitude() + " " + orderPoi.getLatitude() + ")',4326), " + radius + ")");
        List<HnPointAddress> hnPointAddresses = hnPointAddressMapper.selectList(qw);
        //2.计算相似度
        for (HnPointAddress hnPointAddress : hnPointAddresses) {
            float levenshtein = CommonUtils.levenshtein(StrUtil.trim(hnPointAddress.getStreetName()).toLowerCase(), StrUtil.trim(orderPoi.getName()).toLowerCase());
            if (levenshtein > similarity) {
                poiCoverageRes.setNameHerePointAddressName(hnPointAddress.getStreetName());
                poiCoverageRes.setOrderNamePaNameSimilarity(Double.valueOf(levenshtein));
                poiCoverageRes.setOrderNamePaNameMatch(1);
                break;
            } else {
                if (levenshtein > poiCoverageRes.getOrderNamePaNameSimilarity()) {
                    poiCoverageRes.setNameHerePointAddressName(hnPointAddress.getStreetName());
                    poiCoverageRes.setOrderNamePaNameSimilarity(Double.valueOf(levenshtein));
                }
            }
        }
        if (poiCoverageRes.getOrderNamePaNameMatch() == null || poiCoverageRes.getOrderNamePaNameSimilarity() < similarity) {
            poiCoverageRes.setOrderNamePaNameMatch(0);
        }

        // List<HnPointAddress> pois = hnPointAddresses.stream().filter(s -> CommonUtils.levenshtein(StrUtil.trim(s.getStreetName()).toLowerCase(), StrUtil.trim(orderPoi.getName()).toLowerCase()) > similarity).collect(Collectors.toList());
        // if (CollUtil.isNotEmpty(pois)) {
        //     poiCoverageRes.setNameHerePointAddressName(CollUtil.join(pois.stream().map(HnPointAddress::getStreetName).collect(Collectors.toList()), ","));
        //     poiCoverageRes.setOrderNamePaNameMatch(1);
        // } else {
        //     poiCoverageRes.setOrderNamePaNameMatch(0);
        // }
        return CompletableFuture.completedFuture(poiCoverageRes);
    }

    @Async("asyncTaskExecutor")
    protected CompletableFuture<PoiCoverageRes> calcNameVsHerePoiName(String sourceDb, OrderPoi orderPoi, PoiCoverageRes poiCoverageRes, String radius, Double similarity) {
        //1.获取here poi信息
        if (!sourceDb.isEmpty()) {
            // local.yml
            DynamicDataSourceContextHolder.push(sourceDb);
        }
        MybatisPlusConfig.myTableName.set("");
        QueryWrapper<PoiM> qw = new QueryWrapper<>();
        qw.last("where name is not null and ST_DWithin(poi_geo, ST_GeomFromText('POINT(" + orderPoi.getLongitude() + " " + orderPoi.getLatitude() + ")'), " + radius + ")");
        List<PoiM> poiList = poiMMapper.selectList(qw);
        //2.计算相似度
        for (PoiM poiM : poiList) {
            float levenshtein = CommonUtils.levenshtein(StrUtil.trim(poiM.getName()).toLowerCase(), StrUtil.trim(orderPoi.getName()).toLowerCase());
            if (levenshtein > similarity) {
                poiCoverageRes.setNameHerePoiName(poiM.getName());
                poiCoverageRes.setOrderNamePoiNameSimilarity(Double.valueOf(levenshtein));
                poiCoverageRes.setOrderNamePoiNameMatch(1);
                break;
            } else {
                if (levenshtein > poiCoverageRes.getOrderNamePoiNameSimilarity()) {
                    poiCoverageRes.setNameHerePoiName(poiM.getName());
                    poiCoverageRes.setOrderNamePoiNameSimilarity(Double.valueOf(levenshtein));
                }
            }
        }
        if (poiCoverageRes.getOrderNamePoiNameMatch() == null || poiCoverageRes.getOrderNamePoiNameSimilarity() < similarity) {
            poiCoverageRes.setOrderNamePoiNameMatch(0);
        }
        // List<Poi> pois = poiList.stream().filter(s -> CommonUtils.levenshtein(StrUtil.trim(s.getName()).toLowerCase(), StrUtil.trim(orderPoi.getName()).toLowerCase()) > similarity).collect(Collectors.toList());
        // if (CollUtil.isNotEmpty(pois)) {
        //     poiCoverageRes.setNameHerePoiName(CollUtil.join(pois.stream().map(Poi::getName).collect(Collectors.toList()), ","));
        //     poiCoverageRes.setOrderNamePoiNameMatch(1);
        // } else {
        //     poiCoverageRes.setOrderNamePoiNameMatch(0);
        // }
        return CompletableFuture.completedFuture(poiCoverageRes);
    }

    public Integer autoCalcPoiCoverageRatioHereGoogle(String ds, String country, String radius, Double similarity) {
        if (!ds.isEmpty()) {
            DynamicDataSourceContextHolder.push(ds);
        }
        if (!country.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + country);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        List<OrderPoi> orderPois = orderPoiMapper.selectList(null);
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        MybatisPlusConfig.myTableName.set("");

        List<PoiCoverageHereGoogle> resList = new ArrayList<>();
        for (OrderPoi orderPoi : orderPois) {
            PoiCoverageHereGoogle poiCoverageHereGoogle = new PoiCoverageHereGoogle();
            BeanUtil.copyProperties(orderPoi, poiCoverageHereGoogle);
            poiCoverageHereGoogle.setOrderPoiName(orderPoi.getName());
            poiCoverageHereGoogle.setOrderLongitude(orderPoi.getLongitude());
            poiCoverageHereGoogle.setOrderLatitude(orderPoi.getLatitude());
            // 1. here around poi info
            List<PoiM> herePois = this.lambdaQuery().select(PoiM::getName, PoiM::getLatitude, PoiM::getLongitude, PoiM::getSourceId)
                    .last("where name is not null and ST_DWithin(poi_geo, ST_GeomFromText('POINT(" + orderPoi.getLongitude() + " " + orderPoi.getLatitude() + ")'), " + radius + ")").list();
            List<String> herePoiNameList = herePois.stream().map(PoiM::getName).collect(Collectors.toList());

            // 2. google around poi info
            List<String> googlePoiNameList = new ArrayList<>();
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("location", orderPoi.getLongitude() + "," + orderPoi.getLatitude());
            paramMap.put("radius", (int) (Double.valueOf(radius) * 100000));
            paramMap.put("key", "8294-319c5fda0a1f");
            String responseJSON = callWrapperGoogleApiFeign(paramMap);

            cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(responseJSON);
            Integer res = (Integer) jsonObject.get("ret");
            if (res == 0) {
                cn.hutool.json.JSONObject jsonObject1 = (cn.hutool.json.JSONObject) jsonObject.get("data");
                if (jsonObject1 != null) {
                    cn.hutool.json.JSONArray jsonArray = jsonObject1.getJSONArray("pois");
                    if (jsonArray != null && jsonArray.size() > 0) {
                        for (Object o : jsonArray) {
                            String name = (String) ((cn.hutool.json.JSONObject) o).get("name");
                            googlePoiNameList.add(name);
                        }
                    }
                }
            }

            // 3. calc similarity
            if (CollUtil.isNotEmpty(googlePoiNameList) && CollUtil.isNotEmpty(herePoiNameList)) {
                for (String gName : googlePoiNameList) {
                    for (String hName : herePoiNameList) {
                        float levenshtein = CommonUtils.levenshtein(StrUtil.trim(gName).toLowerCase(), StrUtil.trim(hName).toLowerCase());
                        if (levenshtein > similarity) {
                            poiCoverageHereGoogle.setHerePoiName(hName);
                            poiCoverageHereGoogle.setGooglePoiName(gName);
                            poiCoverageHereGoogle.setHereGooglePoiNameMatch(1);
                            poiCoverageHereGoogle.setHereGooglePoiNameSimilarity(Double.valueOf(levenshtein));
                            break;
                        } else {
                            poiCoverageHereGoogle.setHereGooglePoiNameSimilarity(Double.valueOf(levenshtein));
                        }
                    }
                    if (poiCoverageHereGoogle.getHereGooglePoiNameSimilarity().compareTo(similarity) > -1) {
                        break;
                    }
                }
                poiCoverageHereGoogle.setHerePoiNameList(CollUtil.join(herePoiNameList, ","));
                poiCoverageHereGoogle.setGooglePoiNameList(CollUtil.join(googlePoiNameList, ","));
            }
            poiCoverageHereGoogle.setUpTime(LocalDateTime.now());
            poiCoverageHereGoogle.setRadius(radius);
            poiCoverageHereGoogle.setSimilarity(similarity);
            resList.add(poiCoverageHereGoogle);
        }

        //3.保存匹配结果
        if (!ds.isEmpty()) {
            // local.yml:mnr
            DynamicDataSourceContextHolder.push(ds);
        }
        int batchNum = 32767 / BeanUtil.beanToMap(new PoiCoverageHereGoogle()).keySet().size();
        List<List<PoiCoverageHereGoogle>> lists = CollUtil.splitList(resList, batchNum);
        int resNum = 0;
        for (List<PoiCoverageHereGoogle> coverageResList : lists) {
            resNum += poiCoverageHereGoogleMapper.insertBatch(coverageResList);
        }
        return resNum;
    }

    public Integer autoCalcPoiCoverageRatioHereGoogleByMp(String ds, String country, String radius, Double similarity) {
        if (!ds.isEmpty()) {
            DynamicDataSourceContextHolder.push(ds);
        }
        if (!country.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + country);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        List<OrderPoi> orderPois = orderPoiMapper.selectList(null);
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        MybatisPlusConfig.myTableName.set("");

        String targetCountry = "";
        if ("sgp".equals(country)) {
            targetCountry = "sg";
        }
        if ("phl".equals(country)) {
            targetCountry = "ph";
        }
        List<PoiCoverageMpRes> resList = new ArrayList<>();
        for (OrderPoi orderPoi : orderPois) {
            PoiCoverageMpRes poiCoverageMpRes = new PoiCoverageMpRes();
            poiCoverageMpRes.setSimilarity(similarity);
            poiCoverageMpRes.setRadius(radius);
            BeanUtil.copyProperties(orderPoi, poiCoverageMpRes);
            String hereSearchCondition = "q=" + orderPoi.getName() + "&lang=en&in=circle:" + orderPoi.getLatitude() + "," + orderPoi.getLongitude() + ";r%3D" + (int) (Double.valueOf(radius) * 100000);
            log.info("hereSearchCondition is {}", hereSearchCondition);
            String hereAsRes = middlePlatformService.getHereAs(hereSearchCondition);
            handleHereAsRes(hereAsRes, poiCoverageMpRes);

            String googleSearchCondition = "input=" + orderPoi.getName() + "&location=" + orderPoi.getLatitude() + "," + orderPoi.getLongitude() + "&components=country:" + targetCountry + "&language=en&radius=" + (int) (Double.valueOf(radius) * 100000);
            log.info("googleSearchCondition is {}", googleSearchCondition);
            String googleAcRes = middlePlatformService.getGoogleAc(googleSearchCondition);
            handleGoogleAcRes(googleAcRes, poiCoverageMpRes);


            resList.add(poiCoverageMpRes);
        }
        //3.保存匹配结果
        if (!ds.isEmpty()) {
            // local.yml:mnr
            DynamicDataSourceContextHolder.push(ds);
        }
        int batchNum = 32767 / BeanUtil.beanToMap(new PoiCoverageMpRes()).keySet().size();
        List<List<PoiCoverageMpRes>> lists = CollUtil.splitList(resList, batchNum);
        int resNum = 0;
        for (List<PoiCoverageMpRes> coverageResList : lists) {
            resNum += poiCoverageMpResMapper.insertBatch(coverageResList);
        }
        return resNum;
    }

    private void handleGoogleAcRes(String googleAcRes, PoiCoverageMpRes poiCoverageMpRes) {

        cn.hutool.json.JSONObject entries = JSONUtil.parseObj(googleAcRes);
        Integer ret = (Integer) entries.get("ret");
        if (ret == 0) {
            List<String> nameList = new ArrayList<>();
            cn.hutool.json.JSONArray jsonArray = entries.getJSONObject("data").getJSONArray("predictions");
            if (jsonArray != null & jsonArray.size() > 0) {
                jsonArray.forEach(item -> {
                    cn.hutool.json.JSONObject item1 = (cn.hutool.json.JSONObject) item;
                    String title = StrUtil.split((String) item1.get("description"), ',').get(0);
                    nameList.add(title);
                });
            }
            for (String hereName : nameList) {
                float levenshtein = CommonUtils.levenshtein(StrUtil.trim(hereName).toLowerCase(), StrUtil.trim(poiCoverageMpRes.getName()).toLowerCase());
                if (levenshtein > poiCoverageMpRes.getSimilarity()) {
                    poiCoverageMpRes.setGooglePoiName(hereName);
                    poiCoverageMpRes.setGooglePoiNameSimilarity(Double.valueOf(levenshtein));
                    poiCoverageMpRes.setGooglePoiNameMatch(1);
                    break;
                }
                if (levenshtein > poiCoverageMpRes.getGooglePoiNameSimilarity()) {
                    poiCoverageMpRes.setGooglePoiName(hereName);
                    poiCoverageMpRes.setGooglePoiNameSimilarity(Double.valueOf(levenshtein));
                }
            }
            poiCoverageMpRes.setGooglePoiNameList(CollUtil.join(nameList, ","));

        }
    }

    private void handleHereAsRes(String hereAsRes, PoiCoverageMpRes poiCoverageMpRes) {

        cn.hutool.json.JSONObject entries = JSONUtil.parseObj(hereAsRes);
        Integer ret = (Integer) entries.get("ret");
        if (ret == 0) {
            List<String> nameList = new ArrayList<>();
            cn.hutool.json.JSONArray jsonArray = entries.getJSONObject("data").getJSONArray("items");
            if (jsonArray != null & jsonArray.size() > 0) {
                jsonArray.forEach(item -> {
                    cn.hutool.json.JSONObject item1 = (cn.hutool.json.JSONObject) item;
                    String title = (String) item1.get("title");
                    nameList.add(title);
                });
            }
            for (String hereName : nameList) {
                float levenshtein = CommonUtils.levenshtein(StrUtil.trim(hereName).toLowerCase(), StrUtil.trim(poiCoverageMpRes.getName()).toLowerCase());
                if (levenshtein > poiCoverageMpRes.getSimilarity()) {
                    poiCoverageMpRes.setHerePoiName(hereName);
                    poiCoverageMpRes.setHerePoiNameSimilarity(Double.valueOf(levenshtein));
                    poiCoverageMpRes.setHerePoiNameMatch(1);
                    break;
                }
                if (levenshtein > poiCoverageMpRes.getHerePoiNameSimilarity()) {
                    poiCoverageMpRes.setHerePoiName(hereName);
                    poiCoverageMpRes.setHerePoiNameSimilarity(Double.valueOf(levenshtein));
                }
            }
            poiCoverageMpRes.setHerePoiNameList(CollUtil.join(nameList, ","));

        }
    }

    public Integer evaluateSample(List<PoiSample> poiSampleSrc, List<PoiSampleRes> resList, String country) {
        for (PoiSample poiSample : poiSampleSrc) {
            PoiSampleRes poiSampleRes = new PoiSampleRes();
            BeanUtil.copyProperties(poiSample, poiSampleRes);
            // 2.调用中台api，获取poi type
            callMpGoogleDetail(poiSampleRes);
            // 3.周边查询自建poi，hn
            callSelfBuildSearch(poiSampleRes);
            callHereAs(poiSampleRes);

            // log.info("poiSampleRes is {}", poiSampleRes);
            resList.add(poiSampleRes);
        }

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        MybatisPlusConfig.myTableName.set("");
        int batchNum = 32767 / BeanUtil.beanToMap(new PoiSampleRes()).keySet().size();
        List<List<PoiSampleRes>> lists = CollUtil.splitList(resList, batchNum);
        int num = 0;
        for (List<PoiSampleRes> list : lists) {
            num += poiSampleResMapper.mysqlInsertOrUpdateBath(list);
        }
        return num;
    }

    private void callHereAs(PoiSampleRes poiSampleRes) {
        // 200m查询here推荐
        String hereSearchCondition = "q=" + poiSampleRes.getPickupNameHlang() + "&lang=en&in=circle:" + poiSampleRes.getStartLatitude() + "," + poiSampleRes.getStartLongitude() + ";r%3D200";
        String hereAsRes = middlePlatformService.getHereAs(hereSearchCondition);
        cn.hutool.json.JSONObject entries = JSONUtil.parseObj(hereAsRes);
        Integer ret = (Integer) entries.get("ret");
        if (ret == 0) {
            List<String> nameList = new ArrayList<>();
            cn.hutool.json.JSONArray jsonArray = entries.getJSONObject("data").getJSONArray("items");
            if (jsonArray != null & jsonArray.size() > 0) {
                jsonArray.forEach(item -> {
                    cn.hutool.json.JSONObject item1 = (cn.hutool.json.JSONObject) item;
                    String title = (String) item1.get("title");
                    nameList.add(title);
                });
            }
            poiSampleRes.setHereAsPoi200m(CollUtil.join(nameList, "|"));
        }

    }

    private void callMpGoogleDetail(PoiSampleRes poiSampleRes) {

        String googlePoiDetailCondition = "placeid=" + poiSampleRes.getPickupId() + "&language=en&fields=formatted_address,address_component,geometry,name,place_id,type";
        String googleDetail = middlePlatformService.getGoogleDetail(googlePoiDetailCondition);
        cn.hutool.json.JSONObject responseObj = JSONUtil.parseObj(googleDetail).getJSONObject("data").getJSONObject("result");
        if (responseObj != null) {
            poiSampleRes.setGoogleName(responseObj.getStr("name"));
            poiSampleRes.setGoogleAddress(responseObj.getStr("formatted_address"));
            cn.hutool.json.JSONArray poiTypes = responseObj.getJSONArray("types");
            poiSampleRes.setGooglePoiType(poiTypes.join(","));
            poiSampleRes.setGoogleLatitude(responseObj.getJSONObject("geometry").getJSONObject("location").getDouble("lat"));
            poiSampleRes.setGoogleLongitude(responseObj.getJSONObject("geometry").getJSONObject("location").getDouble("lng"));
        } else {
            log.warn("google poi detail is null,placeid is {}", poiSampleRes.getPickupId());
        }
    }

    private void callSelfBuildSearch(PoiSampleRes poiSampleRes) {
        // poi周边查询
        //  半径50m范围查询
        List<PoiM> poiList50 = this.lambdaQuery().select(PoiM::getName)
                .last("where name is not null and ST_DWithin(poi_geo, ST_GeomFromText('POINT(" + poiSampleRes.getStartLongitude() + " " + poiSampleRes.getStartLatitude() + ")'), 0.0005)").list();
        if (CollUtil.isNotEmpty(poiList50)) {
            Map<Float, String> tmpMap = new TreeMap<>(Comparator.reverseOrder());
            for (PoiM poiM : poiList50) {
                float levenshtein = CommonUtils.levenshtein(StrUtil.trim(poiSampleRes.getPickupNameHlang().replace(" ", "")).toLowerCase(), StrUtil.trim(poiM.getName().replace(" ", "")).toLowerCase());
                if (levenshtein > 0.5) {
                    tmpMap.put(levenshtein, poiM.getName());
                }
            }
            if (tmpMap.keySet().size() > 10) {
                // 只取前10个values
                poiSampleRes.setHerePoi50m(CollUtil.join(CollUtil.split(tmpMap.values(), 10).get(0), ","));
            } else if (tmpMap.keySet().size() > 0) {
                poiSampleRes.setHerePoi50m(CollUtil.join(tmpMap.values(), ","));
            }

        }
        //  半径100m范围查询
        List<PoiM> poiList100 = this.lambdaQuery().select(PoiM::getName)
                .last("where name is not null and ST_DWithin(poi_geo, ST_GeomFromText('POINT(" + poiSampleRes.getStartLongitude() + " " + poiSampleRes.getStartLatitude() + ")'), 0.001)").list();
        if (CollUtil.isNotEmpty(poiList100)) {
            Map<Float, String> tmpMap = new TreeMap<>(Comparator.reverseOrder());
            for (PoiM poiM : poiList100) {
                float levenshtein = CommonUtils.levenshtein(StrUtil.trim(poiSampleRes.getPickupNameHlang().replace(" ", "")).toLowerCase(), StrUtil.trim(poiM.getName().replace(" ", "")).toLowerCase());
                if (levenshtein > 0.5) {
                    tmpMap.put(levenshtein, poiM.getName());
                }
            }
            if (tmpMap.keySet().size() > 10) {
                // 只取前10个values
                poiSampleRes.setHerePoi100m(CollUtil.join(CollUtil.split(tmpMap.values(), 10).get(0), ","));
            } else if (tmpMap.keySet().size() > 0) {
                poiSampleRes.setHerePoi100m(CollUtil.join(tmpMap.values(), ","));
            }

        }
        //  半径200m范围查询
        List<PoiM> poiList200 = this.lambdaQuery().select(PoiM::getName)
                .last("where name is not null and ST_DWithin(poi_geo, ST_GeomFromText('POINT(" + poiSampleRes.getStartLongitude() + " " + poiSampleRes.getStartLatitude() + ")'), 0.002)").list();
        if (CollUtil.isNotEmpty(poiList200)) {
            Map<Float, String> tmpMap = new TreeMap<>(Comparator.reverseOrder());
            for (PoiM poiM : poiList200) {
                float levenshtein = CommonUtils.levenshtein(StrUtil.trim(poiSampleRes.getPickupNameHlang().replace(" ", "")).toLowerCase(), StrUtil.trim(poiM.getName().replace(" ", "")).toLowerCase());
                if (levenshtein > 0.5) {
                    tmpMap.put(levenshtein, poiM.getName());
                }
            }
            if (tmpMap.keySet().size() > 10) {
                // 只取前10个values
                poiSampleRes.setHerePoi200m(CollUtil.join(CollUtil.split(tmpMap.values(), 10).get(0), ","));
            } else if (tmpMap.keySet().size() > 0) {
                poiSampleRes.setHerePoi200m(CollUtil.join(tmpMap.values(), ","));
            }

        }
        //  半径200m范围查询，相似度大于0.8
        List<PoiM> poiList200_8 = this.lambdaQuery().select(PoiM::getName)
                .last("where name is not null and ST_DWithin(poi_geo, ST_GeomFromText('POINT(" + poiSampleRes.getStartLongitude() + " " + poiSampleRes.getStartLatitude() + ")'), 0.002)").list();
        if (CollUtil.isNotEmpty(poiList200_8)) {
            Map<Float, String> tmpMap = new TreeMap<>(Comparator.reverseOrder());
            for (PoiM poiM : poiList200_8) {
                float levenshtein = CommonUtils.levenshtein(StrUtil.trim(poiSampleRes.getPickupNameHlang().replace(" ", "")).toLowerCase(), StrUtil.trim(poiM.getName().replace(" ", "")).toLowerCase());
                if (levenshtein > 0.8) {
                    tmpMap.put(levenshtein, poiM.getName());
                }
            }
            if (tmpMap.keySet().size() > 10) {
                // 只取前10个values
                poiSampleRes.setHerePoi200m8(CollUtil.join(CollUtil.split(tmpMap.values(), 10).get(0), ","));
            } else if (tmpMap.keySet().size() > 0) {
                poiSampleRes.setHerePoi200m8(CollUtil.join(tmpMap.values(), ","));
            }

        }
        // hn周边查询
        //  半径50m范围查询
        List<HnPointAddress> hnList50 = hnPointAddressService.lambdaQuery().select(HnPointAddress::getAddress, HnPointAddress::getStreetName)
                .last("where street_name is not null and ST_DWithin(geom, ST_GeomFromText('POINT(" + poiSampleRes.getStartLongitude() + " " + poiSampleRes.getStartLatitude() + ")',4326), 0.0005)").list();
        if (CollUtil.isNotEmpty(hnList50)) {
            Map<Float, String> tmpMap = new TreeMap<>(Comparator.reverseOrder());
            for (HnPointAddress hnPointAddress : hnList50) {
                Float levenshtein = (float) 0;
                if (poiSampleRes.getPickupNameHlang().contains(",")) {
                    String[] srcNameSplits = poiSampleRes.getPickupNameHlang().split(",");
                    if (srcNameSplits.length > 1) {
                        String compareName = srcNameSplits[0] + srcNameSplits[1];
                        levenshtein = CommonUtils.levenshtein(StrUtil.trim(compareName.replace(" ", "")).toLowerCase(), StrUtil.trim((hnPointAddress.getAddress() + hnPointAddress.getStreetName()).replace(" ", "")).toLowerCase());
                    } else {
                        String compareName = srcNameSplits[0];
                        levenshtein = CommonUtils.levenshtein(StrUtil.trim(compareName.replace(" ", "")).toLowerCase(), StrUtil.trim(hnPointAddress.getStreetName().replace(" ", "")).toLowerCase());
                    }
                } else {
                    levenshtein = CommonUtils.levenshtein(StrUtil.trim(poiSampleRes.getPickupNameHlang().replace(" ", "")).toLowerCase(), StrUtil.trim((hnPointAddress.getAddress() + hnPointAddress.getStreetName()).replace(" ", "")).toLowerCase());
                }
                if (levenshtein > 0.5) {
                    tmpMap.put(levenshtein, hnPointAddress.getAddress() + "-" + hnPointAddress.getStreetName());
                }
            }
            if (tmpMap.keySet().size() > 10) {
                // 只取前10个values
                poiSampleRes.setHereHn50m(CollUtil.join(CollUtil.split(tmpMap.values(), 10).get(0), "|"));
            } else if (tmpMap.keySet().size() > 0) {
                poiSampleRes.setHereHn50m(CollUtil.join(tmpMap.values(), "|"));
            }
        }
        //  半径100m范围查询
        List<HnPointAddress> hnList100 = hnPointAddressService.lambdaQuery().select(HnPointAddress::getAddress, HnPointAddress::getStreetName)
                .last("where street_name is not null and ST_DWithin(geom, ST_GeomFromText('POINT(" + poiSampleRes.getStartLongitude() + " " + poiSampleRes.getStartLatitude() + ")',4326), 0.001)").list();
        if (CollUtil.isNotEmpty(hnList100)) {
            Map<Float, String> tmpMap = new TreeMap<>(Comparator.reverseOrder());
            for (HnPointAddress hnPointAddress : hnList100) {
                Float levenshtein = (float) 0;
                if (poiSampleRes.getPickupNameHlang().contains(",")) {
                    String[] srcNameSplits = poiSampleRes.getPickupNameHlang().split(",");
                    if (srcNameSplits.length > 1) {
                        String compareName = srcNameSplits[0] + srcNameSplits[1];
                        levenshtein = CommonUtils.levenshtein(StrUtil.trim(compareName.replace(" ", "")).toLowerCase(), StrUtil.trim((hnPointAddress.getAddress() + hnPointAddress.getStreetName()).replace(" ", "")).toLowerCase());
                    } else {
                        String compareName = srcNameSplits[0];
                        levenshtein = CommonUtils.levenshtein(StrUtil.trim(compareName.replace(" ", "")).toLowerCase(), StrUtil.trim(hnPointAddress.getStreetName().replace(" ", "")).toLowerCase());
                    }
                } else {
                    levenshtein = CommonUtils.levenshtein(StrUtil.trim(poiSampleRes.getPickupNameHlang().replace(" ", "")).toLowerCase(), StrUtil.trim((hnPointAddress.getAddress() + hnPointAddress.getStreetName()).replace(" ", "")).toLowerCase());
                }
                if (levenshtein > 0.5) {
                    tmpMap.put(levenshtein, hnPointAddress.getAddress() + "-" + hnPointAddress.getStreetName());
                }
            }
            if (tmpMap.keySet().size() > 10) {
                // 只取前10个values
                poiSampleRes.setHereHn100m(CollUtil.join(CollUtil.split(tmpMap.values(), 10).get(0), "|"));
            } else if (tmpMap.keySet().size() > 0) {
                poiSampleRes.setHereHn100m(CollUtil.join(tmpMap.values(), "|"));
            }
        }
        //  半径200m范围查询
        List<HnPointAddress> hnList200 = hnPointAddressService.lambdaQuery().select(HnPointAddress::getAddress, HnPointAddress::getStreetName)
                .last("where street_name is not null and ST_DWithin(geom, ST_GeomFromText('POINT(" + poiSampleRes.getStartLongitude() + " " + poiSampleRes.getStartLatitude() + ")',4326), 0.002)").list();
        if (CollUtil.isNotEmpty(hnList200)) {
            Map<Float, String> tmpMap = new TreeMap<>(Comparator.reverseOrder());
            for (HnPointAddress hnPointAddress : hnList200) {
                Float levenshtein = (float) 0;
                if (poiSampleRes.getPickupNameHlang().contains(",")) {
                    String[] srcNameSplits = poiSampleRes.getPickupNameHlang().split(",");
                    if (srcNameSplits.length > 1) {
                        String compareName = srcNameSplits[0] + srcNameSplits[1];
                        levenshtein = CommonUtils.levenshtein(StrUtil.trim(compareName.replace(" ", "")).toLowerCase(), StrUtil.trim((hnPointAddress.getAddress() + hnPointAddress.getStreetName()).replace(" ", "")).toLowerCase());
                    } else {
                        String compareName = srcNameSplits[0];
                        levenshtein = CommonUtils.levenshtein(StrUtil.trim(compareName.replace(" ", "")).toLowerCase(), StrUtil.trim(hnPointAddress.getStreetName().replace(" ", "")).toLowerCase());
                    }
                } else {
                    levenshtein = CommonUtils.levenshtein(StrUtil.trim(poiSampleRes.getPickupNameHlang().replace(" ", "")).toLowerCase(), StrUtil.trim((hnPointAddress.getAddress() + hnPointAddress.getStreetName()).replace(" ", "")).toLowerCase());
                }
                if (levenshtein > 0.5) {
                    tmpMap.put(levenshtein, hnPointAddress.getAddress() + "-" + hnPointAddress.getStreetName());
                }
            }
            if (tmpMap.keySet().size() > 10) {
                // 只取前10个values
                poiSampleRes.setHereHn200m(CollUtil.join(CollUtil.split(tmpMap.values(), 10).get(0), "|"));
            } else if (tmpMap.keySet().size() > 0) {
                poiSampleRes.setHereHn200m(CollUtil.join(tmpMap.values(), "|"));
            }
        }
        //  半径200m范围查询，相似度大于0.8
        List<HnPointAddress> hnList200_8 = hnPointAddressService.lambdaQuery().select(HnPointAddress::getAddress, HnPointAddress::getStreetName)
                .last("where street_name is not null and ST_DWithin(geom, ST_GeomFromText('POINT(" + poiSampleRes.getStartLongitude() + " " + poiSampleRes.getStartLatitude() + ")',4326), 0.002)").list();
        if (CollUtil.isNotEmpty(hnList200_8)) {
            Map<Float, String> tmpMap = new TreeMap<>(Comparator.reverseOrder());
            for (HnPointAddress hnPointAddress : hnList200_8) {
                Float levenshtein = (float) 0;
                if (poiSampleRes.getPickupNameHlang().contains(",")) {
                    String[] srcNameSplits = poiSampleRes.getPickupNameHlang().split(",");
                    if (srcNameSplits.length > 1) {
                        String compareName = srcNameSplits[0] + srcNameSplits[1];
                        levenshtein = CommonUtils.levenshtein(StrUtil.trim(compareName.replace(" ", "")).toLowerCase(), StrUtil.trim((hnPointAddress.getAddress() + hnPointAddress.getStreetName()).replace(" ", "")).toLowerCase());
                    } else {
                        String compareName = srcNameSplits[0];
                        levenshtein = CommonUtils.levenshtein(StrUtil.trim(compareName.replace(" ", "")).toLowerCase(), StrUtil.trim(hnPointAddress.getStreetName().replace(" ", "")).toLowerCase());
                    }
                } else {
                    levenshtein = CommonUtils.levenshtein(StrUtil.trim(poiSampleRes.getPickupNameHlang().replace(" ", "")).toLowerCase(), StrUtil.trim((hnPointAddress.getAddress() + hnPointAddress.getStreetName()).replace(" ", "")).toLowerCase());
                }
                if (levenshtein > 0.8) {
                    tmpMap.put(levenshtein, hnPointAddress.getAddress() + "-" + hnPointAddress.getStreetName());
                }
            }
            if (tmpMap.keySet().size() > 10) {
                // 只取前10个values
                poiSampleRes.setHereHn200m8(CollUtil.join(CollUtil.split(tmpMap.values(), 10).get(0), "|"));
            } else if (tmpMap.keySet().size() > 0) {
                poiSampleRes.setHereHn200m8(CollUtil.join(tmpMap.values(), "|"));
            }
        }
    }

    public Integer importPoiSrc(String xmlFilePath, String country) {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        MybatisPlusConfig.myTableName.set("");

        String[] newFilePaths;
        if (StrUtil.isNotEmpty(xmlFilePath) && xmlFilePath.contains(",")) {
            newFilePaths = xmlFilePath.split(",");
        } else {
            newFilePaths = new String[]{xmlFilePath};
        }


        int batchNum = 32767 / BeanUtil.beanToMap(new PoiSrc()).keySet().size();
        int totalNum = 0;
        for (String newFilePath : newFilePaths) {
            File newFile = new File(newFilePath);
            File[] files = newFile.listFiles(f -> f.getName().toLowerCase().endsWith(".xml"));
            ListUtil.sort(Arrays.asList(files), Comparator.comparing(File::getName));
            log.info("start processing filepath:" + newFilePath);
            for (File file : files) {
                List<PoiSrc> poiSrcs = readFile(file);
                log.info("file name is {}, poiSrcs size is {}", file.getName(), poiSrcs.size());
                List<List<PoiSrc>> lists = CollUtil.splitList(poiSrcs, batchNum);
                int num = 0;
                for (List<PoiSrc> list : lists) {
                    num += poiSrcMapper.insertBatch(list);
                }
                if (poiSrcs.size() != num) {
                    log.error("file name is {}, poiSrcs size is {},insert num is {}", file.getName(), poiSrcs.size(), num);
                    throw new RuntimeException();
                }
                log.info("file name is {}, poiSrcs size is {},insert num is {}", file.getName(), poiSrcs.size(), num);
                totalNum += num;

            }
        }
        log.info("{} srcPoi import finished.totalNum is {}", country, totalNum);
        return totalNum;
    }

    private List<PoiSrc> readFile(File file) {

        List<PoiSrc> resList = new ArrayList<>();
        try {
            FileInputStream inputStream = new FileInputStream(file);
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));

            String str;
            while ((str = bufferedReader.readLine()) != null) {
                if (str.contains("<PlaceList") || str.contains("/PlaceList>")) {
                    continue;
                }
                org.json.JSONObject xmlJSONObj = XML.toJSONObject(str);
                PoiSrc poiSrc = new PoiSrc();
                poiSrc.setSrcContent(xmlJSONObj.toString());
                resList.add(poiSrc);


            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return resList;
    }

    @Transactional(rollbackFor = Exception.class)
    public PoiMDiffRes quarterDiff(String preQuarter, String currentQuarter, String country, Boolean diffDeleteDataFlag) {

        PoiMDiffRes poiMDiffRes = new PoiMDiffRes();
        poiMDiffRes.setDistrict(country);
        poiMDiffRes.setCurrentQuarter(currentQuarter);
        poiMDiffRes.setPreQuarter(preQuarter);
        // 1.获取前一版poi source_id数据
        MybatisPlusConfig.myTableName.set("_" + preQuarter);
        List<PoiM> preIdList = this.lambdaQuery().select(PoiM::getSourceId).list();

        // 2.获取当前版poi source_id数据
        MybatisPlusConfig.myTableName.set("_" + currentQuarter);
        List<PoiM> currentIdList = this.lambdaQuery().select(PoiM::getSourceId).list();

        // 3.季度差分新增修改数据
        Set<String> preIdSet = preIdList.stream().map(PoiM::getSourceId).collect(Collectors.toSet());
        Set<String> currentIdSet = currentIdList.stream().map(PoiM::getSourceId).collect(Collectors.toSet());
        List<String> addIdList = new ArrayList<>();
        List<String> updateIdList = new ArrayList<>();
        for (String currentId : currentIdSet) {
            if (!preIdSet.contains(currentId)) {
                addIdList.add(currentId);
            } else {
                updateIdList.add(currentId);
            }
        }
        String[] array = updateIdList.toArray(new String[0]);
        Long addUpdateRes = redisTemplate.opsForSet().add(country + "_" + currentQuarter+"_vs_"+preQuarter + "_update_ids", (Object[]) array);
        Long addUpdateIdNum = redisTemplate.opsForSet().size(country + "_" + currentQuarter+"_vs_"+preQuarter + "_update_ids");
        // if (addUpdateIdNum != updateIdList.size()) {
        if (addUpdateRes != 1 && addUpdateIdNum != updateIdList.size()) {
            log.error("the number of adding redis updateIdList not equals to updateIdList's size! updateIdList size is [{}],actual add redis size is [{}]", updateIdList.size(), addUpdateIdNum);
        } else {
            log.info("the number of adding redis updateIdList is [{}],updateIdList size is [{}]", addUpdateIdNum, updateIdList.size());
            log.info("quarter diff(update) finished,num is {}", addUpdateIdNum);
            poiMDiffRes.setUpdateNum(addUpdateIdNum);
        }

        LocalDateTime now = LocalDateTime.now();
        String[] addArray = addIdList.toArray(new String[0]);
        Long addInsertRes = redisTemplate.opsForSet().add(country + "_" + currentQuarter+"_vs_"+preQuarter + "_insert_ids", (Object[]) addArray);
        Long addInsertIdNum = redisTemplate.opsForSet().size(country + "_" + currentQuarter+"_vs_"+preQuarter + "_insert_ids");
        if (addInsertRes != 1 && addInsertIdNum != addIdList.size()) {
            log.error("the number of adding redis addIdList not equals to addIdList's size! addIdList size is [{}],actual add redis size is [{}]", addIdList.size(), addInsertIdNum);
        } else {
            log.info("the number of adding redis addIdList is [{}],addIdList size is [{}]", addInsertIdNum, addIdList.size());
            log.info("quarter diff(update) finished,num is {}", addInsertIdNum);
            poiMDiffRes.setAddNum(addInsertIdNum);
        }
        // List<List<String>> splitAddIdLists = CollUtil.split(addIdList, 2000);
        // int addNum = 0;
        // for (List<String> splitAddIdList : splitAddIdLists) {
        //
        //     // // 4.获取新增poi数据
        // MybatisPlusConfig.myTableName.set("_" + currentQuarter);
        //     List<PoiM> addPoiList = this.lambdaQuery().in(PoiM::getSourceId, splitAddIdList).list();
        //     //
        //     // 5.封装poi_diff数据
        //     List<PoiDiff> resList = new ArrayList<>();
        //     PoiDiff poiDiffAdd;
        //     for (PoiM poiM : addPoiList) {
        //         poiDiffAdd = new PoiDiff();
        //         poiDiffAdd.setDataid(poiM.getSourceId());
        //         poiDiffAdd.setSource("H");
        //         poiDiffAdd.setName(poiM.getName());
        //         poiDiffAdd.setAddress(poiM.getAddress());
        //         poiDiffAdd.setKind(poiM.getKind());
        //         poiDiffAdd.setDiffType("A");
        //         poiDiffAdd.setLon(poiM.getLongitude());
        //         poiDiffAdd.setLat(poiM.getLatitude());
        //         poiDiffAdd.setPreQuarter(preQuarter);
        //         poiDiffAdd.setCurrentQuarter(currentQuarter);
        //         poiDiffAdd.setCreateTime(now);
        //         poiDiffAdd.setUpdateTime(now);
        //
        //         resList.add(poiDiffAdd);
        //     }
        //
        //     // 6.入库
        //     int batchNum = 32767 / BeanUtil.beanToMap(new PoiDiff()).keySet().size();
        //     List<List<PoiDiff>> lists = CollUtil.splitList(resList, batchNum);
        //     int poiDiffInsertNum = 0;
        //     for (List<PoiDiff> list : lists) {
        //         poiDiffInsertNum += poiDiffMapper.batchInsert(list);
        //     }
        //     if (poiDiffInsertNum != resList.size()) {
        //         log.error("quarter diff(insert) batch insert num is not equal to resList size,insert num is {}", poiDiffInsertNum);
        //         throw new RuntimeException();
        //     }
        //     addNum+=poiDiffInsertNum;
        //     log.info("quarter diff(insert) batch handle finished,num is {}", resList.size());
        // }
        // log.info("quarter diff(insert) finished,num is {}", addNum);
        // poiMDiffRes.setAddNum(num);
        // poiMDiffRes.setAddNum((long) addIdList.size());

        if (diffDeleteDataFlag) {
            // here 季度差分删除数据
            List<String> deleteIdList = new ArrayList<>();
            for (String preId : preIdSet) {
                if (!currentIdSet.contains(preId)) {
                    deleteIdList.add(preId);
                }
            }
            log.info("delete num {}", deleteIdList.size());
            List<List<String>> splitDeleteIdLists = CollUtil.split(deleteIdList, 2000);
            int num = 0;
            for (List<String> splitDeleteIdList : splitDeleteIdLists) {

                // 4.获取删除poi数据
                MybatisPlusConfig.myTableName.set("_" + preQuarter);
                List<PoiM> deletePoiList = this.lambdaQuery().select(PoiM::getSourceId, PoiM::getName, PoiM::getAddress, PoiM::getKind, PoiM::getLongitude, PoiM::getLatitude).in(PoiM::getSourceId, splitDeleteIdList).list();

                // 5.封装poi_diff数据
                List<PoiDiff> resPoiDiffDList = new ArrayList<>();
                PoiDiff poiDiff;
                for (PoiM poiM : deletePoiList) {
                    poiDiff = new PoiDiff();
                    poiDiff.setDataid(poiM.getSourceId());
                    poiDiff.setSource("H");
                    poiDiff.setName(poiM.getName());
                    poiDiff.setAddress(poiM.getAddress());
                    poiDiff.setKind(poiM.getKind());
                    poiDiff.setDiffType("D");
                    poiDiff.setLon(poiM.getLongitude());
                    poiDiff.setLat(poiM.getLatitude());
                    poiDiff.setPreQuarter(preQuarter);
                    poiDiff.setCurrentQuarter(currentQuarter);
                    poiDiff.setCreateTime(now);
                    poiDiff.setUpdateTime(now);

                    resPoiDiffDList.add(poiDiff);
                }

                // 6.入库
                int batchNum = 32767 / BeanUtil.beanToMap(new PoiDiff()).keySet().size();
                List<List<PoiDiff>> lists = CollUtil.splitList(resPoiDiffDList, batchNum);
                // int num = 0;
                int affectNum = 0;
                for (List<PoiDiff> list : lists) {
                    affectNum += poiDiffMapper.batchInsert(list);
                }
                if (affectNum != resPoiDiffDList.size()) {
                    log.error("quarter diff(delete) batch insert num is not equal to resList size,insert num is {},resList size is {}", affectNum, resPoiDiffDList.size());
                    throw new RuntimeException();
                }
                num += affectNum;
                log.info("quarter diff(delete) batch handle finished,num is {}", resPoiDiffDList.size());
            }
            log.info("quarter diff(delete) handle finished,num is {}", num);
            poiMDiffRes.setDeleteNum((long) num);
        }

        return poiMDiffRes;
    }

    @Transactional(rollbackFor = Exception.class)
    public String poiMerge(String country, String date) {

        String result = "";
        // 1.读取从hive拉取的poi_fusion_result数据
        List<PoiFusionResult> fusionList = poiFusionResultService.lambdaQuery().eq(PoiFusionResult::getDt, date).list();

        // 2.判断fusion_type，是走新增，还是修改
        List<PoiM> addPoiList = new ArrayList<>();
        List<PoiM> updatePoiList = new ArrayList<>();
        for (PoiFusionResult poiFusionResult : fusionList) {
            if ("A".equals(poiFusionResult.getFusionType())) {
                PoiM poiM = new PoiM();
                JSONObject jsonObject = JSON.parseObject(poiFusionResult.getData());
                PoiDTO poiDTO = jsonObject.toJavaObject(PoiDTO.class);
                poiM.setName(poiDTO.getName());
                poiM.setAddress(poiDTO.getAddress());
                poiM.setKind(poiDTO.getKind());
                poiM.setLongitude(Double.valueOf(poiDTO.getLongitude()));
                poiM.setLatitude(Double.valueOf(poiDTO.getLatitude()));
                poiM.setSourceId(poiFusionResult.getSourceId());
                if (poiM.getLatitude() != null && poiM.getLongitude() != null) {
                    String geoWkt = "POINT(" + poiM.getLongitude() + " " + poiM.getLatitude() + ")";
                    poiM.setPoiGeo(geoWkt);
                }
                poiM.setSource("U");
                poiM.setStatus(0);
                addPoiList.add(poiM);

            }
            if ("M".equals(poiFusionResult.getFusionType())) {
                PoiM poiM = new PoiM();
                JSONObject jsonObject = JSON.parseObject(poiFusionResult.getData());
                PoiDTO poiDTO = jsonObject.toJavaObject(PoiDTO.class);
                poiM.setName(poiDTO.getName());
                poiM.setAddress(poiDTO.getAddress());
                poiM.setKind(poiDTO.getKind());
                poiM.setLongitude(Double.valueOf(poiDTO.getLongitude()));
                poiM.setLatitude(Double.valueOf(poiDTO.getLatitude()));
                poiM.setSourceId(poiFusionResult.getSourceId());
                poiM.setPoiId(poiFusionResult.getPoiId());
                poiM.setSourceId(poiFusionResult.getSourceId());
                if (poiM.getLatitude() != null && poiM.getLongitude() != null) {
                    String geoWkt = "POINT(" + poiM.getLongitude() + " " + poiM.getLatitude() + ")";
                    poiM.setPoiGeo(geoWkt);
                }
                PoiM prePoi = this.lambdaQuery().eq(PoiM::getPoiId, poiFusionResult.getPoiId()).one();
                poiM.setSource(prePoi.getSource() + ",U");
                updatePoiList.add(poiM);

            }
        }
        LocalDateTime now = LocalDateTime.now();
        addPoiList.forEach(s -> {
            s.setCreateTime(now);
            s.setUpdateTime(now);
        });
        updatePoiList.forEach(s -> {
            s.setUpdateTime(now);
        });
        // 3.新增入库
        if (CollUtil.isNotEmpty(addPoiList)) {
            List<Long> idsList = inheritIDService.createID(new InheritIDDTO(12L, Long.valueOf(addPoiList.size())));
            if (idsList.size() != addPoiList.size()) {
                log.error("poi merge inheritID num is not equal to addPoiList size,inheritID num is {}", idsList.size());
                throw new RuntimeException();
            }
            for (int i = 0; i < addPoiList.size(); i++) {
                addPoiList.get(i).setPoiId(String.valueOf(idsList.get(i)));
            }
            int batchNum = 32767 / BeanUtil.beanToMap(new PoiM()).keySet().size();
            List<List<PoiM>> lists = CollUtil.splitList(addPoiList, batchNum);
            int num = 0;
            for (List<PoiM> list : lists) {
                num += poiMMapper.mysqlInsertOrUpdateBath(list);
            }
            if (num != addPoiList.size()) {
                log.error("poi merge insert num is not equal to addPoiList size,insert num is {}", num);
                throw new RuntimeException();
            }
            log.info("poi merge add finished,add num is {}", num);
            result = "poi merge add finished,add num is " + num;
        }

        // 4.修改入库
        if (CollUtil.isNotEmpty(updatePoiList)) {
            int updateNum = 0;
            for (PoiM updatePoi : updatePoiList) {
                boolean update = this.lambdaUpdate().set(PoiM::getName, updatePoi.getName())
                        .set(PoiM::getAddress, updatePoi.getAddress())
                        .set(PoiM::getKind, updatePoi.getKind())
                        .set(PoiM::getLongitude, updatePoi.getLongitude())
                        .set(PoiM::getLatitude, updatePoi.getLatitude())
                        .set(PoiM::getPoiGeo, updatePoi.getPoiGeo())
                        .set(PoiM::getSource, updatePoi.getSource())
                        .set(PoiM::getSourceId, updatePoi.getSourceId())
                        .set(PoiM::getUpdateTime, updatePoi.getUpdateTime())
                        .eq(PoiM::getPoiId, updatePoi.getPoiId()).update();
                if (update) {
                    updateNum++;
                }
            }

            // int batchNum = 32767 / BeanUtil.beanToMap(new Poi()).keySet().size();
            // List<List<Poi>> lists = CollUtil.splitList(updatePoiList, batchNum);
            // int num = 0;
            // for (List<Poi> list : lists) {
            //     num += poiMapper.mysqlInsertOrUpdateBath(list);
            // }
            if (updateNum != updatePoiList.size()) {
                log.error("poi merge update num is not equal to updatePoiList size,update num is {}", updateNum);
                throw new RuntimeException();
            }
            log.info("poi merge update finished,update num is {}", updateNum);
            result += " poi merge update finished,update num is " + updateNum;
        }
        if (StrUtil.isBlank(result)) {
            result = "poi merge finished,without add or update data";
        }
        log.info("result:" + result);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public String quarterDiffUpdate(String preQuarter, String currentQuarter, String country) {
        // 1.缓存获取updateIds
        String cacheKey = country + "_" + currentQuarter + "_update_ids";
        if (!redisTemplate.hasKey(cacheKey)) {
            log.info("There is no quarterDiff updateIds needed to be updated!");
            return "There is no quarterDiff updateIds needed to be updated!";
        }
        Set<Object> updateIdSet = redisTemplate.opsForSet().members(cacheKey);
        if (updateIdSet.isEmpty()) {
            return "There is no data in cache key : " + cacheKey;
        }
        List<Long> updateIdList = updateIdSet.stream().map(s -> Long.valueOf(s.toString())).collect(Collectors.toList());

        int batchNum = 32767 / BeanUtil.beanToMap(new PoiM()).keySet().size();
        List<List<Long>> splitIds = CollUtil.split(updateIdList, batchNum);
        int updateNum = 0;
        for (List<Long> splitId : splitIds) {
            MybatisPlusConfig.myTableName.set("_" + currentQuarter);
            // 2.读取新poi数据，更新poi表
            List<PoiM> updatePoiList = this.lambdaQuery().in(PoiM::getSourceId, splitId).list();
            MybatisPlusConfig.myTableName.set("");
            updateNum += poiMMapper.mysqlInsertOrUpdateBath(updatePoiList);

        }
        if (updateNum != updateIdList.size()) {
            throw new RuntimeException("quarter diff update num not equals to src updateId num,update num is [" + updateNum + "],actual needed to update num is [" + updateIdList.size() + "]");
        }
        log.info("quarter diff update finished.Update Num is [{}],Quarter is [{}],District is [{}]", updateNum, currentQuarter, country);

        return "quarter diff update finished.Update Num is [" + updateNum + "],Quarter is [" + currentQuarter + "],District is [" + country + "]";
    }


    /**
     * @Classname PointAddressController
     * @Description here门址
     * @Date 2021/12/13 4:12 下午
     * @Created by qunfu
     */
    @RestController
    @Slf4j
    @RequestMapping("/api/road/pointaddress")
    public static class PointAddressController {

        @Resource
        PointAddressServiceImpl pointAddressService;
        @Resource
        HnPointAddressServiceImpl hnPointAddressService;
        @Resource
        HnPointAddressMapper hnPointAddressMapper;

        @ApiOperation(value = "here pointAddress convert")
        @PostMapping("/convert")
        public ResponseResult<Boolean> pointAddressConvert(@RequestParam(value = "step",
                                                                   required = false, defaultValue = "1") int step,
                                                           @RequestParam(value = "version", required = false) String version,
                                                           @RequestParam(value = "isCompileTrans", defaultValue = "false") boolean isCompileTrans,
                                                           @RequestParam(value = "area", required = false, defaultValue = "") String area,
                                                           @RequestParam(value = "country", required = true, defaultValue = "") String country

        ) throws InterruptedException {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            TimeInterval timer = DateUtil.timer();

            Integer listSize = pointAddressService.count();
            CountDownLatch countDownLatch = new CountDownLatch(listSize / step + 1);
            log.info("The records to be transfered:" + listSize);
            for (int i = 0; i <= listSize / step; i++) {
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
                List<PointAddress> pointAddressListi = pointAddressService.lambdaQuery()
                        .orderByDesc(PointAddress::getGid).last(" limit " + step + " offset " + i * step).list();
                log.info("process start limit " + step + " offset " + i * step);
                if (pointAddressListi.size() > 0) {
                    hnPointAddressService.pointAddressConvert(pointAddressListi, isCompileTrans, area, country, version, countDownLatch);
                }
            }
            countDownLatch.await();
            log.info("here pointAddress convert to HnPointAddress cost time is {}s,country is {},area is {}", timer.intervalSecond(), country, area);
            return ResponseResult.OK(true, true);
        }

        @ApiOperation("hande poi link_id")
        @GetMapping("handlePointAddressId")
        public ResponseResult<Boolean> handlePointAddressId(@RequestParam(value = "step", required = false, defaultValue = "1050") int step,
                                                            @RequestParam(value = "area", required = false, defaultValue = "") String area,
                                                            @RequestParam(value = "country", required = false, defaultValue = "") String country) throws InterruptedException {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            TimeInterval timer = DateUtil.timer();
            int addressCount = hnPointAddressService.lambdaQuery().last("where length(ar_link_id) != 18").count();
            int loop = addressCount % step != 0 ? (addressCount / step) + 1 : addressCount / step;
            CountDownLatch countDownLatch = new CountDownLatch(loop);
            for (int i = 0; i < loop; i++) {
                // List<HnPointAddress> hnPointAddressList = hnPointAddressMapper.selectUnhandleIds(step, i * step);
                List<HnPointAddress> hnPointAddressList = hnPointAddressService.lambdaQuery().last("where length(ar_link_id) != 18 order by hn_id" + " limit " + step + " offset " + i * step).list();
                hnPointAddressService.handleId(area, country, countDownLatch, hnPointAddressList);
            }
            countDownLatch.await();
            log.info("handle hn_point_address (ar_link_id) to be inherited, cost time is {}s,country is {},area is {}", timer.intervalSecond(), country, area);
            return ResponseResult.OK(true, true);
        }
    }
}
