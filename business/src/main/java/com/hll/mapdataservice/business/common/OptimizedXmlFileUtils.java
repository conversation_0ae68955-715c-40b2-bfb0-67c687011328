package com.hll.mapdataservice.business.common;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Stream;

/**
 * Optimized version of XmlFileUtils with better memory management
 */
@Component
@Slf4j
public class OptimizedXmlFileUtils {

    /**
     * Read RDF configuration file with try-with-resources for proper resource management
     * @param fileName the file path
     * @return Map of configuration values
     */
    public Map<String, String> rdfCfFileRead(String fileName) {
        Map<String, String> rdfCfMap = new HashMap<>();
        
        try (BufferedReader bufferedReader = Files.newBufferedReader(Paths.get(fileName), StandardCharsets.UTF_8)) {
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                String[] splitResult = line.split("\t");
                if (splitResult.length >= 2 && "I".equals(splitResult[1])) {
                    rdfCfMap.put(splitResult[0], splitResult[1]);
                }
            }
        } catch (IOException e) {
            log.error("Error reading RDF CF file: {}", fileName, e);
        }
        
        return rdfCfMap;
    }

    /**
     * Read RDF link configuration file with try-with-resources for proper resource management
     * @param fileName the file path
     * @return MultiValueMap of link configurations
     */
    public MultiValueMap<String, String> rdfLinkCfFileRead(String fileName) {
        MultiValueMap<String, String> rdfCfLinkMap = new LinkedMultiValueMap<>();
        
        try (BufferedReader bufferedReader = Files.newBufferedReader(Paths.get(fileName), StandardCharsets.UTF_8)) {
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                String[] splitResult = line.split("\t");
                if (splitResult.length >= 2) {
                    rdfCfLinkMap.add(splitResult[0], splitResult[1]);
                }
            }
        } catch (IOException e) {
            log.error("Error reading RDF Link CF file: {}", fileName, e);
        }
        
        return rdfCfLinkMap;
    }

    /**
     * Read RDF navigation link file with try-with-resources for proper resource management
     * @param fileName the file path (comma-separated list of files)
     * @param country the country code to filter by
     * @return Map of navigation link data
     */
    public Map<String, List<String>> rdfNavLinkFileRead(String fileName, String country) {
        Map<String, List<String>> rdfNavLinkMap = new HashMap<>();
        String[] singleFileArray = fileName.split(",");
        
        for (String singleFile : singleFileArray) {
            try (BufferedReader bufferedReader = Files.newBufferedReader(Paths.get(singleFile), StandardCharsets.UTF_8)) {
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    String[] splitResult = line.split("\t", -1);
                    if (splitResult.length >= 36 && country.toUpperCase().equals(splitResult[1])) {
                        rdfNavLinkMap.put(splitResult[0], Arrays.asList(splitResult[33], splitResult[35]));
                    }
                }
            } catch (IOException e) {
                log.error("Error reading RDF Nav Link file: {}", singleFile, e);
            }
        }
        
        return rdfNavLinkMap;
    }
    
    /**
     * Process a file in chunks to reduce memory usage
     * @param fileName the file path
     * @param processor the function to process each line
     */
    public void processFileInChunks(String fileName, LineProcessor processor) {
        try (BufferedReader bufferedReader = Files.newBufferedReader(Paths.get(fileName), StandardCharsets.UTF_8)) {
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                processor.process(line);
            }
        } catch (IOException e) {
            log.error("Error processing file in chunks: {}", fileName, e);
        }
    }
    
    /**
     * Functional interface for processing a line of text
     */
    @FunctionalInterface
    public interface LineProcessor {
        void process(String line);
    }
}