package com.hll.mapdataservice.business.api.road.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.entity.LinkBreak;
import com.hll.mapdataservice.common.entity.LinkM;
import com.hll.mapdataservice.common.mapper.LinkBreakMapper;
import com.hll.mapdataservice.common.service.ILinkBreakService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import com.vividsolutions.jts.geom.Geometry;
import com.vividsolutions.jts.io.WKTReader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;

/**
 * @Author: ares.chen
 * @Since: 2022/2/8
 */
@Service
@Slf4j
public class LinkBreakServiceImpl extends ServiceImpl<LinkBreakMapper, LinkBreak> implements ILinkBreakService {

    @Resource
    LinkBreakMapper linkBreakMapper;

    @Override
    @Async("asyncTaskExecutor")
    public void linkBreak(List<LinkM> batchList, CountDownLatch downLatch, Long version, String country, String area) {
        log.info("各线程截断道路数量：{}",batchList.size());
        log.info("各线程截断版本号：{}",version);
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        List<LinkBreak> linkBreakList = new ArrayList<>();
        try {
            for (LinkM link : batchList) {
                String geomWkt = link.getGeomwkt();
                Geometry linkLine = new WKTReader().read(geomWkt);
                double length = linkLine.getLength() * 111000;
                if (length > 20020) {
                    breakHandle(version, linkBreakList, link, geomWkt);
                } else {
                    LinkBreak linkBreak = new LinkBreak();
                    BeanUtil.copyProperties(link, linkBreak);
                    linkBreak.setSubLinkId(UUID.randomUUID().toString());
                    linkBreak.setSubGeom(link.getGeometry());
                    linkBreak.setCreateDate(LocalDateTime.now());
                    linkBreak.setUpDate(LocalDateTime.now());
                    linkBreak.setVersion(version);
                    linkBreakList.add(linkBreak);
                }
            }
            List<List<LinkBreak>> splitList = CollUtil.splitList(linkBreakList, batchList.size());
            for (List<LinkBreak> linkBreaks : splitList) {
                linkBreakMapper.mysqlInsertOrUpdateBath(linkBreaks);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("道路截断出现异常：{}",e.getMessage());
        }finally {
            downLatch.countDown();
        }
    }

    private void breakHandle(Long version, List<LinkBreak> linkBreakList, LinkM link, String geomWkt) {
        String subStr = StrUtil.subBetween(geomWkt, "(", ")");
        if (subStr.contains("(") || subStr.contains(")")) {
            subStr = StrUtil.removeAny(subStr, "(", ")");
        }
        String[] coordList = subStr.split(",");
        for (int i = 0; i < coordList.length; i++) {
            if (i == coordList.length - 1) {
                break;
            }
            LinkBreak linkBreak = new LinkBreak();
            BeanUtil.copyProperties(link, linkBreak);
            String start = coordList[i];
            String end = coordList[i + 1];

            String geoWkt = "MULTILINESTRING((" + start.split(" ")[0] + " " + start.split(" ")[1] +
                    "," + end.split(" ")[0] + " " + end.split(" ")[1] + "))";
            //linkBreak.setSubGeomWkt(geoWkt);
            linkBreak.setSubGeom("SRID=4326;"+geoWkt);
            linkBreak.setSubLinkId(UUID.randomUUID().toString());
            linkBreak.setGeom(link.getGeometry());
            linkBreak.setCreateDate(LocalDateTime.now());
            linkBreak.setUpDate(LocalDateTime.now());
            linkBreak.setVersion(version);
            linkBreakList.add(linkBreak);
        }
    }
}
