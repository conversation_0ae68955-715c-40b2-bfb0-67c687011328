package com.hll.mapdataservice.business.sqlInjector;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import org.apache.ibatis.executor.keygen.NoKeyGenerator;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;
import org.springframework.util.StringUtils;


/**
 * 批量更新方法实现，条件为主键，选择性更新
 */
public class MysqlUpdateBath extends AbstractMethod {
    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        String sql = "<script>\n<foreach collection=\"list\" item=\"item\" separator=\";\">\nupdate %s %s where %s=#{%s} %s\n</foreach>\n</script>";
        String additional = tableInfo.isWithVersion() ? tableInfo.getVersionFieldInfo().getVersionOli("item", "item.") : "" + tableInfo.getLogicDeleteSql(true, true);
        String setSql = sqlSet(tableInfo.isWithLogicDelete(), false, tableInfo, false, "item", "item.");
        String sqlResult = String.format(sql, tableInfo.getTableName(), setSql, tableInfo.getKeyColumn(), "item." + tableInfo.getKeyProperty(), additional);
        //log.debug("sqlResult----->{}", sqlResult);
        SqlSource sqlSource = languageDriver.createSqlSource(configuration, sqlResult, modelClass);
        // 第三个参数必须和RootMapper的自定义方法名一致
        return this.addUpdateMappedStatement(mapperClass, modelClass, "mysqlUpdateBath", sqlSource);
    }

//    @Override
//    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
//        final String sql = "<script>insert into %s %s values %s ON DUPLICATE KEY UPDATE %s</script>";
//        final String pgsql = "<script>update %s set values %s ON CONFLICT("+tableInfo.getKeyColumn()+") DO UPDATE SET %s</script>";
//        final String tableName = tableInfo.getTableName();
//        final String filedSql = prepareFieldSql(tableInfo);
//        final String modelValuesSql = prepareModelValuesSql(tableInfo);
//        final String duplicateKeySql =prepareDuplicateKeySql(tableInfo);
//        final String duplicateKeySqlpg =prepareDuplicateKeySqlpg(tableInfo);
//        final String sqlResult = String.format(sql, tableName, filedSql, modelValuesSql,duplicateKeySql);
//        final String pgsqlResult = String.format(pgsql, tableName, filedSql, modelValuesSql,duplicateKeySqlpg);
////        System.out.println("MysqlInsertOrUpdateBath="+Thread.currentThread().getName());
////        System.out.println("tableName is:"+tableName);
//        //System.out.println("savaorupdatesqlsql="+pgsqlResult);
//        SqlSource sqlSource = languageDriver.createSqlSource(configuration, pgsqlResult, modelClass);
//        return this.addInsertMappedStatement(mapperClass, modelClass, "mysqlInsertOrUpdateBath", sqlSource, new NoKeyGenerator(), null, null);
//    }

    /**
     * 准备ON DUPLICATE KEY UPDATE sql
     * @param tableInfo
     * @return
     */
    private String prepareDuplicateKeySql(TableInfo tableInfo) {
        final StringBuilder duplicateKeySql = new StringBuilder();
        if(!StringUtils.isEmpty(tableInfo.getKeyColumn())) {
            duplicateKeySql.append(tableInfo.getKeyColumn()).append("=values(").append(tableInfo.getKeyColumn()).append("),");
        }

        tableInfo.getFieldList().forEach(x -> {
            duplicateKeySql.append(x.getColumn())
                    .append("=values(")
                    .append(x.getColumn())
                    .append("),");
        });
        duplicateKeySql.delete(duplicateKeySql.length() - 1, duplicateKeySql.length());
        return duplicateKeySql.toString();
    }

    /**
     * 准备ON DUPLICATE KEY UPDATE sql
     * @param tableInfo
     * @return
     */
    private String prepareDuplicateKeySqlpg(TableInfo tableInfo) {
        final StringBuilder duplicateKeySql = new StringBuilder();
        if(!StringUtils.isEmpty(tableInfo.getKeyColumn())) {
            duplicateKeySql.append(tableInfo.getKeyColumn()).append("=EXCLUDED.").append(tableInfo.getKeyColumn()).append(",");
        }

        tableInfo.getFieldList().forEach(x -> {
            duplicateKeySql.append(x.getColumn())
                    .append("=EXCLUDED.")
                    .append(x.getColumn()).append(",");
        }

        );
//        for (TableFieldInfo tableFieldInfo:tableInfo.getFieldList()
//             ) {
//            if(tableFieldInfo.getColumn().contains("geo")){
//                duplicateKeySql.append(tableFieldInfo.getColumn())
//                        .append("=EXCLUDED.ST_GeomFromText(")
//                        .append(tableFieldInfo.getColumn()).append("),");
//            }else {
//                duplicateKeySql.append(tableFieldInfo.getColumn())
//                        .append("=EXCLUDED.")
//                        .append(tableFieldInfo.getColumn()).append(",");
//            }
//        }
        duplicateKeySql.delete(duplicateKeySql.length() - 1, duplicateKeySql.length());
        return duplicateKeySql.toString();
    }

    /**
     * 准备属性名
     * @param tableInfo
     * @return
     */
    private String prepareFieldSql(TableInfo tableInfo) {
        StringBuilder fieldSql = new StringBuilder();
        fieldSql.append(tableInfo.getKeyColumn()).append(",");
        tableInfo.getFieldList().forEach(x -> {
            fieldSql.append(x.getColumn()).append(",");
        });
        fieldSql.delete(fieldSql.length() - 1, fieldSql.length());
        fieldSql.insert(0, "(");
        fieldSql.append(")");
        return fieldSql.toString();
    }

    private String prepareModelValuesSql(TableInfo tableInfo){
        final StringBuilder valueSql = new StringBuilder();
        valueSql.append("<foreach collection=\"list\" item=\"item\" index=\"index\" open=\"(\" separator=\"),(\" close=\")\">");
        if(!StringUtils.isEmpty(tableInfo.getKeyProperty())) {
            valueSql.append("#{item.").append(tableInfo.getKeyProperty()).append("},");
        }
        tableInfo.getFieldList().forEach(x -> valueSql.append("#{item.").append(x.getProperty()).append("},"));
//        for (TableFieldInfo tableFieldInfo:tableInfo.getFieldList()
//             ) {
//            if(tableFieldInfo.getColumn().contains("geo")){
//                valueSql.append("#{item.").append(tableFieldInfo.getProperty()).append(",typeHandler = MyGeometryTypeHandler.class},");
//            }else {
//                valueSql.append("#{item.").append(tableFieldInfo.getProperty()).append("},");
//            }
//        }
        valueSql.delete(valueSql.length() - 1, valueSql.length());
        valueSql.append("</foreach>");
        return valueSql.toString();
    }
}
