package com.hll.mapdataservice.business.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hll.mapdataservice.business.entity.PhlTrackCoorInfoBak;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-12
 */
@DS("db5")
public interface PhlTrackCoorInfoBakMapper extends BaseMapper<PhlTrackCoorInfoBak> {

    @Select("select distinct(order_id) from phl_track_coor_info_bak order by order_id")
    List<String> getOrderId();

    @Select("select distinct(order_id) from phl_track_coor_info_bak where here_link is null")
    List<String> getUnprocessedHereId();

    @Select("select distinct(order_id) from phl_track_coor_info_bak where tt_link is null")
    List<String> getUnprocessedTomtomId();
}
