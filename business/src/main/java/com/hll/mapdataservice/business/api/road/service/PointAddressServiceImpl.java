package com.hll.mapdataservice.business.api.road.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.common.entity.PointAddress;
import com.hll.mapdataservice.common.mapper.PointAddressMapper;
import com.hll.mapdataservice.common.service.IPointAddressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.annotation.Resources;

/**
 * @Classname PointAddressServiceImpl
 * @Description TODO
 * @Date 2021/12/9 2:41 下午
 * @Created by qunfu
 */
@Service
public class PointAddressServiceImpl extends ServiceImpl<PointAddressMapper, PointAddress> implements IPointAddressService {
}
