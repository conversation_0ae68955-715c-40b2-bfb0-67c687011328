package com.hll.mapdataservice.business.entity;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix="datasourceinfo")
public class DataSourceInfo {

    public String getDatasource() {
        return datasource;
    }

    public void setDatasource(String datasource) {
        this.datasource = datasource;
    }

    private String datasource;
    private String cdmstable;

    public String getCdmstable() {
        return cdmstable;
    }

    public void setCdmstable(String cdmstable) {
        this.cdmstable = cdmstable;
    }

    public String getAltstreetstable() {
        return altstreetstable;
    }

    public void setAltstreetstable(String altstreetstable) {
        this.altstreetstable = altstreetstable;
    }

    public String getCdmsdtmodtable() {
        return cdmsdtmodtable;
    }

    public void setCdmsdtmodtable(String cdmsdtmodtable) {
        this.cdmsdtmodtable = cdmsdtmodtable;
    }

    public String getCndmodtable() {
        return cndmodtable;
    }

    public void setCndmodtable(String cndmodtable) {
        this.cndmodtable = cndmodtable;
    }

    public String getRdmstable() {
        return rdmstable;
    }

    public void setRdmstable(String rdmstable) {
        this.rdmstable = rdmstable;
    }

    public String getStreetstable() {
        return streetstable;
    }

    public void setStreetstable(String streetstable) {
        this.streetstable = streetstable;
    }

    public String getStreettranstable() {
        return streettranstable;
    }

    public void setStreettranstable(String streettranstable) {
        this.streettranstable = streettranstable;
    }

    public String getZleveltable() {
        return zleveltable;
    }

    public void setZleveltable(String zleveltable) {
        this.zleveltable = zleveltable;
    }

    private String altstreetstable;
    private String cdmsdtmodtable;
    private String cndmodtable;
    private String rdmstable;
    private String streetstable;
    private String streettranstable;

    @Override
    public String toString() {
        return "DataSourceInfo{" +
                "datasource='" + datasource + '\'' +
                ", cdmstable='" + cdmstable + '\'' +
                ", altstreetstable='" + altstreetstable + '\'' +
                ", cdmsdtmodtable='" + cdmsdtmodtable + '\'' +
                ", cndmodtable='" + cndmodtable + '\'' +
                ", rdmstable='" + rdmstable + '\'' +
                ", streetstable='" + streetstable + '\'' +
                ", streettranstable='" + streettranstable + '\'' +
                ", zleveltable='" + zleveltable + '\'' +
                '}';
    }

    private String zleveltable;


}
