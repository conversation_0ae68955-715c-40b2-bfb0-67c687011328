package com.hll.mapdataservice.business.api.poi.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.hll.mapdataservice.business.api.poi.service.*;
import com.hll.mapdataservice.business.api.road.service.HnPointAddressServiceImpl;
import com.hll.mapdataservice.business.api.road.service.StreetsServiceImpl;
import com.hll.mapdataservice.business.config.MapTTConfig;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.third.InheritIDService;
import com.hll.mapdataservice.business.third.MiddlePlatformService;
import com.hll.mapdataservice.business.third.dto.InheritIDDTO;
import com.hll.mapdataservice.common.GlobalCodeEnum;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.mapper.OrderPoiMapper;
import com.hll.mapdataservice.common.mapper.PoiMMapper;
import com.hll.mapdataservice.common.mapper.PoiSampleMapper;
import com.hll.mapdataservice.common.mapper.PoiSampleResMapper;
import com.hll.mapdataservice.common.service.*;
import com.hll.mapdataservice.common.utils.CommonUtils;
import com.hll.mapdataservice.business.common.XmlFileUtils;
import com.hll.mapdataservice.common.utils.EnDecryptUtils;
import com.hll.mapdataservice.common.vo.PoiBase;
import com.hll.mapdataservice.common.entity.PlanetOsmPoint;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-24
 */
@RestController
@ResponseBody
@Api(tags = "poi")
@Component
@Slf4j
@RequestMapping("/api/poi/herepoi")
//@DS("db8")
public class PoiMController {
    @Resource
    HerePhaPoiServiceImpl herePhaPoiService;
    @Resource
    PoiMServiceImpl poiMService;
    @Resource
    HerePhaAutosvcServiceImpl herePhaAutosvcService;
    @Resource
    HerePhaBusinessServiceImpl herePhaBusinessService;
    @Resource
    HerePhaCommsvcServiceImpl herePhaCommsvcService;
    @Resource
    HerePhaEduinstsServiceImpl herePhaEduinstsService;
    @Resource
    HerePhaEntertnServiceImpl herePhaEntertnService;
    @Resource
    HerePhaFininstsServiceImpl herePhaFininstsService;
    @Resource
    HerePhaHamletServiceImpl herePhaHamletService;
    @Resource
    HerePhaHospitalServiceImpl herePhaHospitalService;
    @Resource
    HerePhaMisccategoriesServiceImpl herePhaMisccategoriesService;
    @Resource
    HerePhaNamedplcServiceImpl herePhaNamedplcService;
    @Resource
    HerePhaParkingServiceImpl herePhaParkingService;
    @Resource
    HerePhaParkrecServiceImpl herePhaParkrecService;
    @Resource
    HerePhaRestrntsServiceImpl herePhaRestrntsService;
    @Resource
    HerePhaShoppingServiceImpl herePhaShoppingService;
    @Resource
    HerePhaTranshubsServiceImpl herePhaTranshubsService;
    @Resource
    HerePhaTravdestServiceImpl herePhaTravdestService;
    @Resource
    PoiMMapper poiMMapper;
    @Resource
    MtdareaServiceImpl mtdareaService;
    @Resource
    StreetsServiceImpl streetsService;
    @Resource
    PoiMatchResServiceImpl poiMatchResService;
    @Resource
    IHllOrderService hllOrderService;
    @Resource
    IPlaceService placeService;
    @Resource
    IPoiMatchService poiMatchService;
    @Resource
    InheritIDService inheritIDService;
    @Resource
    IPoiCoverageResService poiCoverageResService;
    @Resource
    OrderPoiMapper orderPoiMapper;
    @Resource
    PoiCoverageHereGoogleServiceImpl poiCoverageHereGoogleService;
    @Resource
    PoiCoverageMpResServiceImpl poiCoverageMpResService;
    @Resource
    RedisTemplate<String, Object> redisTemplate;
    @Resource
    HnPointAddressServiceImpl hnPointAddressService;
    @Resource
    PoiSampleMapper poiSampleMapper;
    @Resource
    PoiSampleResMapper poiSampleResMapper;
    @Resource
    MiddlePlatformService middlePlatformService;
    @Resource
    PlanetOsmPointServiceImpl planetOsmPointService;
    @Resource
    MapTTConfig mapTTConfig;

    @GetMapping("/redis")
    public ResponseResult<Boolean> testRedis() {
        redisTemplate.opsForValue().set("test", "test");
        redisTemplate.opsForHash().putIfAbsent("map", "map", "map");
        System.out.println(redisTemplate.opsForValue().get("test"));
        System.out.println(redisTemplate.opsForHash().get("map", "map"));
        return ResponseResult.OK(true, true);
    }

    @GetMapping("/deleteCacheKey")
    public ResponseResult<Boolean> deleteKey(String keyName) {
        if (!redisTemplate.hasKey(keyName)) {
            return ResponseResult.exceptionInfo(GlobalCodeEnum.GL_REPEAT_REQUEST_INFO_000008, "cacheKey not exist", false);
        } else {
            redisTemplate.delete(keyName);
            return ResponseResult.OK(true, true);
        }

    }


    @GetMapping("/keySize")
    public ResponseResult<String> keySize(String keyName) {
        // 调用MEMORY USAGE命令
        Long memoryUsage = redisTemplate.execute((RedisCallback<Long>) connection -> {
            Object nativeResult = connection.execute("MEMORY", "USAGE".getBytes(), keyName.getBytes());
            connection.stringCommands().bitCount(keyName.getBytes());
            return nativeResult != null ? Long.parseLong(nativeResult.toString()) : null;
        });
        if (memoryUsage != null) {
            return ResponseResult.OK(keyName + " size is [" + memoryUsage + "] Bytes,[" + memoryUsage / 1024 / 1024 + "] Mb", true);
        } else {
            return ResponseResult.OK(keyName + " size is null", true);
        }
    }

    @GetMapping("/id/prehandle")
    public ResponseResult<String> poiIdPrehandle(String year, String quarter, String district, String filePath) {

        String[] newFilePaths;
        String key = district + "_" + year + "_" + quarter;
        if (StrUtil.isNotEmpty(filePath) && filePath.contains(",")) {
            newFilePaths = filePath.split(",");
        } else {
            newFilePaths = new String[]{filePath};
        }
        Map<String, String> map1 = new HashMap<>();
        for (String newFilePath : newFilePaths) {
            File newFile = new File(newFilePath);
            if (!newFile.exists()) {
               log.warn(newFilePath + " not exist");
               continue;
            }
            File[] files = newFile.listFiles(f -> f.getName().toLowerCase().endsWith(".xml"));
            ListUtil.sort(Arrays.asList(files), Comparator.comparing(File::getName));
            for (File file : files) {
                List<String> sourceIds = XmlFileUtils.poiSourceIds(file.toString());
                for (String sourceId : sourceIds) {
                    map1.put(sourceId, EnDecryptUtils.convertHerePoiUUID2IDLongStringOnly(sourceId));
                }
            }
        }
        log.info("district [{}],sourceIds size is {}", district, map1.size());
        Map<String, Long> middleMap = new HashMap<>();

        List<String> convertSourceIds = new ArrayList<>(map1.values());
        List<List<String>> splitConvertSourceIds = CollUtil.split(convertSourceIds, 5000);
        for (List<String> splitConvertSourceId : splitConvertSourceIds) {
            List<Long> longs = inheritIDService.inheritID(new InheritIDDTO(12L, splitConvertSourceId));
            for (int i = 0; i < splitConvertSourceId.size(); i++) {
                middleMap.put(splitConvertSourceId.get(i), longs.get(i));
            }
        }

        log.info("district [{}],middleMap size is {}", district, middleMap.size());
        for (Map.Entry<String, String> map : map1.entrySet()) {
            if (middleMap.containsKey(map.getValue())) {
                redisTemplate.opsForHash().putIfAbsent(key, map.getKey(), middleMap.get(map.getValue()).toString());
            }
        }
        log.info("district [{}],poi id prehandle success,cache key [{}],cache size [{}]", district, key, redisTemplate.opsForHash().keys(key).size());

        return ResponseResult.OK("id prehandle success,sourceIds size:" + redisTemplate.opsForHash().keys(key).size(), true);
    }

    @ApiOperation(value = "here poi convert")
    @PostMapping("/convert")
    public ResponseResult<Boolean> herePoiConvert(@RequestParam(value = "step",
            required = false,
            defaultValue = "1") int step) {
        // here_pha_autosvc
        Integer listSize = herePhaAutosvcService.count();
        log.info("The autosvc records to be transfered:" + listSize);
        for (int i = 0; i <= listSize / step; i++) {
            List<HerePhaAutosvc> herePhaAutosvcListi = herePhaAutosvcService.lambdaQuery()
                    .orderByDesc(HerePhaAutosvc::getPoiId).last("limit " + step + " offset " + i * step).list();
            if (herePhaAutosvcListi.size() > 0) {
                List<PoiBase> poiBaseList = herePhaAutosvcListi.stream().map(herePhaAutosvc -> new PoiBase(herePhaAutosvc))
                        .collect(Collectors.toList());
                poiMService.poiConvert(poiBaseList);
                // poiService.poiConvertAutosvc(herePhaAutosvcListi);
            }
        }
        // here_pha_business
        Integer listSizeBusiness = herePhaBusinessService.count();
        log.info("The business records to be transfered:" + listSizeBusiness);
        for (int i = 0; i <= listSizeBusiness / step; i++) {
            List<HerePhaBusiness> herePhaBusinessListi = herePhaBusinessService.lambdaQuery()
                    .orderByDesc(HerePhaBusiness::getPoiId).last("limit " + step + " offset " + i * step).list();
            if (herePhaBusinessListi.size() > 0) {
                List<PoiBase> poiBaseList = herePhaBusinessListi.stream().map(herePhaAutosvc -> new PoiBase(herePhaAutosvc))
                        .collect(Collectors.toList());
                poiMService.poiConvert(poiBaseList);
                // poiService.poiConvertBusiness(herePhaBusinessListi);
            }
        }
        // here_pha_commsvc
        Integer listSizeCommsvc = herePhaCommsvcService.count();
        log.info("The commsvc records to be transfered:" + listSizeCommsvc);
        for (int i = 0; i <= listSizeCommsvc / step; i++) {
            List<HerePhaCommsvc> herePhaCommsvcListi = herePhaCommsvcService.lambdaQuery()
                    .orderByDesc(HerePhaCommsvc::getPoiId).last("limit " + step + " offset " + i * step).list();
            if (herePhaCommsvcListi.size() > 0) {
                poiMService.poiConvertCommsvc(herePhaCommsvcListi);
            }
        }
        // here_pha_eduinsts
        Integer listSizeEduinsts = herePhaEduinstsService.count();
        log.info("The Eduinsts records to be transfered:" + listSizeEduinsts);
        for (int i = 0; i <= listSizeEduinsts / step; i++) {
            List<HerePhaEduinsts> herePhaEduinstsListi = herePhaEduinstsService.lambdaQuery()
                    .orderByDesc(HerePhaEduinsts::getPoiId).last("limit " + step + " offset " + i * step).list();
            if (herePhaEduinstsListi.size() > 0) {
                poiMService.poiConvertEduinsts(herePhaEduinstsListi);
            }
        }
        // here_pha_entertn
        Integer listSizeEntertn = herePhaEntertnService.count();
        log.info("The Entertn records to be transfered:" + listSizeEntertn);
        for (int i = 0; i <= listSizeEntertn / step; i++) {
            List<HerePhaEntertn> herePhaEntertnListi = herePhaEntertnService.lambdaQuery()
                    .orderByDesc(HerePhaEntertn::getPoiId).last("limit " + step + " offset " + i * step).list();
            if (herePhaEntertnListi.size() > 0) {
                poiMService.poiConvertEntertn(herePhaEntertnListi);
            }
        }
        // here_pha_fininsts
        Integer listSizeFininsts = herePhaFininstsService.count();
        log.info("The Fininsts records to be transfered:" + listSizeFininsts);
        for (int i = 0; i <= listSizeFininsts / step; i++) {
            List<HerePhaFininsts> herePhaFininstsListi = herePhaFininstsService.lambdaQuery()
                    .orderByDesc(HerePhaFininsts::getPoiId).last("limit " + step + " offset " + i * step).list();
            if (herePhaFininstsListi.size() > 0) {
                poiMService.poiConvertFininsts(herePhaFininstsListi);
            }
        }
        // here_pha_hamlet
        Integer listSizeHamlet = herePhaHamletService.count();
        log.info("The Hamlet records to be transfered:" + listSizeHamlet);
        for (int i = 0; i <= listSizeHamlet / step; i++) {
            List<HerePhaHamlet> herePhaHamletListi = herePhaHamletService.lambdaQuery()
                    .orderByDesc(HerePhaHamlet::getPoiId).last("limit " + step + " offset " + i * step).list();
            if (herePhaHamletListi.size() > 0) {
                poiMService.poiConvertHamlet(herePhaHamletListi);
            }
        }
        // here_pha_hospital
        Integer listSizeHospital = herePhaHospitalService.count();
        log.info("The Hospital records to be transfered:" + listSizeHospital);
        for (int i = 0; i <= listSizeHospital / step; i++) {
            List<HerePhaHospital> herePhaHospitalListi = herePhaHospitalService.lambdaQuery()
                    .orderByDesc(HerePhaHospital::getPoiId).last("limit " + step + " offset " + i * step).list();
            if (herePhaHospitalListi.size() > 0) {
                poiMService.poiConvertHospital(herePhaHospitalListi);
            }
        }
        // here_pha_misccategories
        Integer listSizeMisccategories = herePhaMisccategoriesService.count();
        log.info("The misccategories records to be transfered:" + listSizeMisccategories);
        for (int i = 0; i <= listSizeMisccategories / step; i++) {
            List<HerePhaMisccategories> herePhaMisccategoriesListi = herePhaMisccategoriesService.lambdaQuery()
                    .orderByDesc(HerePhaMisccategories::getPoiId).last("limit " + step + " offset " + i * step).list();
            if (herePhaMisccategoriesListi.size() > 0) {
                poiMService.poiConvertMisccategories(herePhaMisccategoriesListi);
            }
        }
        // here_pha_namedplc
        Integer listSizeNamedplc = herePhaNamedplcService.count();
        log.info("The namedplc records to be transfered:" + listSizeNamedplc);
        for (int i = 0; i <= listSizeNamedplc / step; i++) {
            List<HerePhaNamedplc> herePhaMisccategoriesListi = herePhaNamedplcService.lambdaQuery()
                    .orderByDesc(HerePhaNamedplc::getPoiId).last("limit " + step + " offset " + i * step).list();
            if (herePhaMisccategoriesListi.size() > 0) {
                poiMService.poiConvertNamedplc(herePhaMisccategoriesListi);
            }
        }
        // here_pha_parking
        Integer listSizeParking = herePhaParkingService.count();
        log.info("The parking records to be transfered:" + listSizeParking);
        for (int i = 0; i <= listSizeParking / step; i++) {
            List<HerePhaParking> herePhaParkingListi = herePhaParkingService.lambdaQuery()
                    .orderByDesc(HerePhaParking::getPoiId).last("limit " + step + " offset " + i * step).list();
            if (herePhaParkingListi.size() > 0) {
                poiMService.poiConvertParking(herePhaParkingListi);
            }
        }
        // here_pha_parkrec
        Integer listSizeParkrec = herePhaParkrecService.count();
        log.info("The parkrec records to be transfered:" + listSizeParkrec);
        for (int i = 0; i <= listSizeParkrec / step; i++) {
            List<HerePhaParkrec> herePhaParkrecListi = herePhaParkrecService.lambdaQuery()
                    .orderByDesc(HerePhaParkrec::getPoiId).last("limit " + step + " offset " + i * step).list();
            if (herePhaParkrecListi.size() > 0) {
                poiMService.poiConvertParkrec(herePhaParkrecListi);
            }
        }
        // here_pha_restrnts
        Integer listSizeRestrnts = herePhaRestrntsService.count();
        log.info("The restrnts records to be transfered:" + listSizeRestrnts);
        for (int i = 0; i <= listSizeRestrnts / step; i++) {
            List<HerePhaRestrnts> herePhaRestrntsListi = herePhaRestrntsService.lambdaQuery()
                    .orderByDesc(HerePhaRestrnts::getPoiId).last("limit " + step + " offset " + i * step).list();
            if (herePhaRestrntsListi.size() > 0) {
                poiMService.poiConvertRestrnts(herePhaRestrntsListi);
            }
        }
        // here_pha_shopping
        Integer listSizeShopping = herePhaShoppingService.count();
        log.info("The shopping records to be transfered:" + listSizeShopping);
        for (int i = 0; i <= listSizeShopping / step; i++) {
            List<HerePhaShopping> herePhaShoppingListi = herePhaShoppingService.lambdaQuery()
                    .orderByDesc(HerePhaShopping::getPoiId).last("limit " + step + " offset " + i * step).list();
            if (herePhaShoppingListi.size() > 0) {
                poiMService.poiConvertShopping(herePhaShoppingListi);
            }
        }
        // here_pha_transhubs
        Integer listSizeTranshubs = herePhaTranshubsService.count();
        log.info("The transhubs records to be transfered:" + listSizeTranshubs);
        for (int i = 0; i <= listSizeTranshubs / step; i++) {
            List<HerePhaTranshubs> herePhaTranshubsListi = herePhaTranshubsService.lambdaQuery()
                    .orderByDesc(HerePhaTranshubs::getPoiId).last("limit " + step + " offset " + i * step).list();
            if (herePhaTranshubsListi.size() > 0) {
                poiMService.poiConvertTranshubs(herePhaTranshubsListi);
            }
        }
        // here_pha_travdest
        Integer listSizeTravdest = herePhaTravdestService.count();
        log.info("The travdest records to be transfered:" + listSizeTravdest);
        for (int i = 0; i <= listSizeTravdest / step; i++) {
            List<HerePhaTravdest> herePhaTravdestListi = herePhaTravdestService.lambdaQuery()
                    .orderByDesc(HerePhaTravdest::getPoiId).last("limit " + step + " offset " + i * step).list();
            if (herePhaTravdestListi.size() > 0) {
                poiMService.poiConvertTravdest(herePhaTravdestListi);
            }
        }

        return ResponseResult.OK(true, true);
    }

    @PostMapping("/import")
    @ApiOperation(value = "importHerePoi")
    //@DS("db3")
    public ResponseResult<Boolean> importPlaces(@RequestParam String filePath,
                                                @RequestParam(value = "step",
                                                        required = false,
                                                        defaultValue = "300") int step,
                                                @RequestParam(value = "create",
                                                        required = false,
                                                        defaultValue = "true") boolean create,
                                                @RequestParam(value = "area",
                                                        required = false,
                                                        defaultValue = "") String area,
                                                @RequestParam(value = "country",
                                                        required = false,
                                                        defaultValue = "") String country,
                                                @RequestParam String cacheKey,@RequestParam String datasource) throws Exception {
        if (!country.isEmpty()) {
            log.info("db is:" + CommonUtils.getDsbyCountry(country, true));
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        if (!redisTemplate.hasKey(cacheKey)) {
            return ResponseResult.exceptionInfo(GlobalCodeEnum.GL_REPEAT_REQUEST_INFO_000008, "cacheKey not exist", false);
        }
        TimeInterval timer = DateUtil.timer();
        log.info("folder path is:" + filePath);
        System.out.println("folder path is:" + filePath);
        String[] newFilePaths;
        // 2023q1之后，poi源数据文件路径有变化，遂调整此处
        if (StrUtil.isNotEmpty(filePath) && filePath.contains(",")) {
            newFilePaths = filePath.split(",");
        } else {
            newFilePaths = new String[]{filePath};
        }

        // File filePaths = new File(filePath);
        // File[] files = filePaths.listFiles(file -> file.getName().toLowerCase().endsWith(".xml"));
        // // 按文件名排序
        // List fileList = Arrays.asList(files);
        // Collections.sort(fileList, new Comparator<File>() {
        //     @Override
        //     public int compare(File o1, File o2) {
        //         if (o1.isDirectory() && o2.isFile()) {
        //             return -1;
        //         }
        //         if (o1.isFile() && o2.isDirectory()) {
        //             return 1;
        //         }
        //         return o1.getName().compareTo(o2.getName());
        //     }
        // });
        // mtdAreaList
        log.info("mtdareaService db is:" + DynamicDataSourceContextHolder.peek());
        List<Mtdarea> mtdareaList = mtdareaService.lambdaQuery().list();
        List<Streets> streetsList = streetsService.lambdaQuery().select(Streets::getLinkId, Streets::getlAreaId, Streets::getrAreaId).list();
        // linkAreaMap
        Map<Long, Long> linkIdLAreaIdMap = streetsList.stream().collect(Collectors.toMap(Streets::getLinkId, Streets::getlAreaId));
        Map<Long, Long> linkIdRAreaIdMap = streetsList.stream().collect(Collectors.toMap(Streets::getLinkId, Streets::getrAreaId));
        log.info("linkIdLAreaIdMap size is:" + linkIdLAreaIdMap.size());
        log.info("linkIdRAreaIdMap size is:" + linkIdRAreaIdMap.size());
//        Map<String, String> sourceIdPoiIdMap = new HashMap<>();
//        if (!create) {
//            if (!country.isEmpty()) {
//                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
//            }
//            // if (!area.isEmpty()) {
//            //    MybatisPlusConfig.myTableName.set("_" + area);
//            //} else {
//            //    MybatisPlusConfig.myTableName.set("");
//            //}
//            // map poiId and sourceid
//            MybatisPlusConfig.myTableName.set("");
//            List<Poi> poiList = poiService.list();
//            log.info("poiHphaList size is:" + poiList.size());
//            sourceIdPoiIdMap = poiList.stream().collect(Collectors.toMap(Poi::getSourceId, Poi::getPoiId));
//            log.info("sourceIdPoiIdMap size is:" + sourceIdPoiIdMap.size());
//        }

        for (String newFilePath : newFilePaths) {
            File newFile = new File(newFilePath);
            if (!newFile.exists()) {
                log.warn(newFilePath + " not exist");
                continue;
            }
            File[] files = newFile.listFiles(f -> f.getName().toLowerCase().endsWith(".xml"));
            ListUtil.sort(Arrays.asList(files), Comparator.comparing(File::getName));
            log.info("start processing filepath:" + newFilePath);
            for (File file : files) {
                // hereThaPoiService.saveHerePoi(file);
                // hereThaPoiListAll.addAll(new XmlFileUtils().herePoiXmlRead(file));
                System.out.println("processing file:" + file.toString());
                log.info("processing file:" + file.toString());
                long startTime = System.currentTimeMillis();
                List<PoiM> poiList = new ArrayList<>();
//                if (create) {
//                    poiList = new XmlFileUtils().poiHphaXmlRead(file.toString(), mtdareaList, linkIdLAreaIdMap, linkIdRAreaIdMap, true, null, country);
////                for (Poi poi : poiList) {
////                    List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(poi.getSourceId())));
////                    poi.setPoiId(inheritID.get(0).toString());
////                }
//                } else {
                poiList = new XmlFileUtils().poiHphaXmlRead(file.toString(), mtdareaList, linkIdLAreaIdMap, linkIdRAreaIdMap, false, null, country);
//                }
                // for (Poi poi : poiList) {
                //     if (StrUtil.isNotEmpty(poi.getLinkId())) {
                //         poi.setLinkId(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(poi.getLinkId()))).get(0)));
                //     }
                // }
                log.info("read file {} finished;Start saving poiInfo,poiInfo size is:{}", file.toString(), poiList.size());
//        for (HereThaPlaces hereThaPlaces: hereThaPlacesList
//             ) {
//            System.out.println(hereThaPlaces.toString());
//        }
                // poiService.saveOrUpdateBatch(poiList);
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }

                MybatisPlusConfig.myTableName.set("");

                // get poiIdMapper
                List<List<PoiM>> partition = Lists.partition(poiList, step);
                // Map<String, String> sourceIdPoiIdMap = new HashMap<>();
                for (int i = 0; i < partition.size(); i++) {
                    try {
//                    System.out.println(partition.get(i));
                        // poiMapper.mysqlInsertOrUpdateBath(partition.get(i));
                        List<PoiM> pois = partition.get(i);
                        List<String> srcLinkIds = pois.stream().map(PoiM::getLinkId).collect(Collectors.toList());
                        List<Long> linkIds = inheritIDService.inheritID(new InheritIDDTO(12L, srcLinkIds));
                        if (srcLinkIds.size() == linkIds.size()) {
                            for (int j = 0; j < pois.size(); j++) {
                                pois.get(j).setLinkId(linkIds.get(j).toString());
                            }
                        }
                        List<String> poiMiddleIds = pois.stream().map(PoiM::getPoiMiddleId).collect(Collectors.toList());
                        for (int k = 0; k < poiMiddleIds.size(); k++) {
                            poiMiddleIds.set(k, poiMiddleIds.get(k).split(",")[0]);
                        }

                        List<Long> poiIds = inheritIDService.inheritID(new InheritIDDTO(12L, poiMiddleIds));
                        if (poiMiddleIds.size() == poiIds.size()) {
                            for (int j = 0; j < pois.size(); j++) {
                                pois.get(j).setPoiId(poiIds.get(j).toString());
                                // sourceIdPoiIdMap.put(pois.get(j).getSourceId(), pois.get(j).getPoiId());
//                                if(pois.get(j).getParent().split(",").length>1){
//                                    List<Long> poiIdsParent = inheritIDService.inheritID(new InheritIDDTO(12L, Arrays.asList(pois.get(j).getParent().split(","))));
//                                    pois.get(j).setParent(poiIdsParent.stream().map(String::valueOf).collect(Collectors.joining(",")));
//                                }else if (pois.get(j).getParent().split(",").length==1){
//                                    pois.get(j).setParent(inheritIDService.inheritID(new InheritIDDTO(12L, Arrays.asList(pois.get(j).getParent()))).get(0).toString());
//                                }
//                                if(pois.get(j).getChildren().split(",").length>1){
//                                    List<Long> poiIdsChildren = inheritIDService.inheritID(new InheritIDDTO(12L, Arrays.asList(pois.get(j).getChildren().split(","))));
//                                    pois.get(j).setChildren(poiIdsChildren.stream().map(String::valueOf).collect(Collectors.joining(",")));
//                                }else if (pois.get(j).getChildren().split(",").length==1){
//                                    pois.get(j).setChildren(inheritIDService.inheritID(new InheritIDDTO(12L, Arrays.asList(pois.get(j).getChildren()))).get(0).toString());
//                                }
                            }
                        }
//                        poiService.saveOrUpdateBatch(pois);
//                        poiMapper.mysqlInsertOrUpdateBath(pois);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                // update poiId and save
                for (PoiM poi : poiList) {
                    // if (sourceIdPoiIdMap.containsKey(poi.getPoiId())) {
                    //     poi.setPoiId(sourceIdPoiIdMap.get(poi.getPoiId()));
                    // }
                    if (poi.getParent().length() > 0) {
                        String[] poiIds = poi.getParent().split(",");
                        List<String> poiIdsParents = new ArrayList<>();
                        if (poiIds.length > 1) {
                            for (int i = 0; i < poiIds.length; i++) {
                                if (redisTemplate.opsForHash().get(cacheKey, poiIds[i]) == null) {
                                    log.warn("poiId {} not exist in cacheKey {}", poiIds[i], cacheKey);
                                    continue;
                                }
                                poiIdsParents.add((String) redisTemplate.opsForHash().get(cacheKey, poiIds[i]));
                            }
                        } else if (poiIds.length == 1) {
                            if (redisTemplate.opsForHash().get(cacheKey, poiIds[0]) == null) {
                                log.warn("poiId {} not exist in cacheKey {}", poiIds[0], cacheKey);
                            } else {
                                poiIdsParents.add((String) redisTemplate.opsForHash().get(cacheKey, poiIds[0]));
                            }
                        }
                        poi.setParent(poiIdsParents.stream().collect(Collectors.joining(",")));
                    }
                    if (poi.getChildren().length() > 0) {
                        String[] poiIds = poi.getChildren().split(",");
                        List<String> poiIdsChildren = new ArrayList<>();
                        if (poiIds.length > 1) {
                            for (int i = 0; i < poiIds.length; i++) {
                                if (redisTemplate.opsForHash().get(cacheKey, poiIds[i]) == null) {
                                    log.warn("poiId {} not exist in cacheKey {}", poiIds[i], cacheKey);
                                    continue;
                                }
                                poiIdsChildren.add((String) redisTemplate.opsForHash().get(cacheKey, poiIds[i]));
                            }
                        } else if (poiIds.length == 1) {
                            if (redisTemplate.opsForHash().get(cacheKey, poiIds[0]) == null) {
                                log.warn("poiId {} not exist in cacheKey {}", poiIds[0], cacheKey);
                            } else {
                                poiIdsChildren.add((String) redisTemplate.opsForHash().get(cacheKey, poiIds[0]));
                            }
                        }
                        poi.setChildren(poiIdsChildren.stream().collect(Collectors.joining(",")));
                    }
                }
                step = 65535 / BeanUtil.beanToMap(new PoiM()).keySet().size();
                List<List<PoiM>> partition2 = Lists.partition(poiList, step);
                MybatisPlusConfig.myTableName.set("_" + datasource);
                partition2.get(0).forEach(poiM -> {
                    System.out.println(poiM.toString());
                });
                for (List<PoiM> pois : partition2) {
                    poiMMapper.mysqlInsertOrUpdateBath(pois);
                }
                // poiService.saveOrUpdateBatch(poiList);
                long endTime = System.currentTimeMillis();
                System.out.println("finished processing file:" + file.toString() + " cost " + (endTime - startTime) / 1000 + "s");
                log.info("finished processing file:" + file.toString() + " cost " + (endTime - startTime) / 1000 + "s");
            }
        }
        log.info("importHerePoi finished,country is{},area is{},cost time is {}s", country, area, timer.intervalSecond());
        return ResponseResult.OK(true, true);
    }

    public void poiConvertByType(String poiType) {

    }

    @ApiOperation(value = "here google poi match")
    @PostMapping("/poiMatch")
    public ResponseResult<String> poiMatch(@RequestParam(value = "country", required = false) String country,
                                           @RequestParam(value = "step", required = false, defaultValue = "1000") int step,
                                           @RequestParam(value = "edition", required = false) String edition) {

        // 1.获取坐标数据
        // List<Location> locationList = new ArrayList<>();
        // Location location = new Location();
        // location.setLatitude(-33.8670522);
        // location.setLongitude(151.1957362);
        // locationList.add(location);
        TimeInterval timer = DateUtil.timer();
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (StrUtil.isNotEmpty(edition)) {
            MybatisPlusConfig.myTableName.set("_" + edition);
        }
        // 从hllorder获取坐标数据
        String lastSql = "limit " + step + " offset 0";
        List<HllOrder> hllOrderList = hllOrderService.lambdaQuery().select(HllOrder::getPlaceId, HllOrder::getLongitude, HllOrder::getLatitude).last(lastSql).list();

        // 2.多线程处理参数准备，后期如果检测数据量大了，可以开启多线程，目前先暂停
        int batchSize = 500;
        int loop = hllOrderList.size() % batchSize > 0 ? hllOrderList.size() / batchSize + 1 : hllOrderList.size() / batchSize;
        CountDownLatch countDownLatch = new CountDownLatch(loop);
        List<List<HllOrder>> partition = Lists.partition(hllOrderList, batchSize);

        try {
            // 3.获取版本号，每次调用后，依次递增
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            List<PoiMatchRes> list = poiMatchResService.lambdaQuery().select(PoiMatchRes::getVersion).orderByDesc(PoiMatchRes::getVersion).list();
            Integer version;
            if (CollUtil.isNotEmpty(list)) {
                version = list.get(0).getVersion() + 1;
            } else {
                version = 1;
            }

            // 4.匹配检测
            for (List<HllOrder> orderList : partition) {
                poiMService.poiMatch(orderList, version, country, countDownLatch);
            }

            countDownLatch.await();

            // 5.计算匹配度
            int count = poiMatchResService.count(Wrappers.<PoiMatchRes>lambdaQuery().eq(PoiMatchRes::getVersion, version)
                    .eq(PoiMatchRes::getIsMatch, 1));
            BigDecimal matchNum = new BigDecimal(count);
            BigDecimal totalNum = new BigDecimal(hllOrderList.size());
            BigDecimal tmpRes = matchNum.divide(totalNum, 2, BigDecimal.ROUND_HALF_UP);
            NumberFormat percentInstance = NumberFormat.getPercentInstance();
            percentInstance.setMaximumFractionDigits(2);

            ResponseResult<String> result = new ResponseResult<>();
            result.setCode("200");
            result.setSuccess(true);
            result.setData(percentInstance.format(tmpRes.doubleValue()));
            log.info("poi match cost time is {}s", timer.intervalSecond());
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseResult.systemError(GlobalCodeEnum.GL_FAIL_999999, false);
        }
    }

    @ApiOperation(value = "google poi match")
    @PostMapping("/googlePoiMatch")
    public ResponseResult<String> googlePoiMatch(@RequestParam(value = "country") String country,
                                                 @RequestParam(value = "ds") String ds) {

        try {
            // 1.获取国家poi信息
            TimeInterval timer = DateUtil.timer();
            if (!ds.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(ds, false));
            }
            if (StrUtil.isNotEmpty(country)) {
                MybatisPlusConfig.myTableName.set("_" + country);
            }
            List<Place> placeList = placeService.list();

            // 2.调用匹配接口
            int version = poiMService.googlePoiMatch(placeList, country);
            log.info("匹配版本号：{}", version);

            // 3.计算匹配度
            int count = poiMatchService.count(Wrappers.<PoiMatch>lambdaQuery().eq(PoiMatch::getCountryPrefix, country)
                    .eq(PoiMatch::getVersion, version).eq(PoiMatch::getIsMatch, 1).eq(PoiMatch::getMatchSrc, "GOOGLE"));

            BigDecimal matchNum = new BigDecimal(count);
            BigDecimal totalNum = new BigDecimal(placeList.size());
            BigDecimal tmpRes = matchNum.divide(totalNum, 2, BigDecimal.ROUND_HALF_UP);
            NumberFormat percentInstance = NumberFormat.getPercentInstance();
            percentInstance.setMaximumFractionDigits(2);

            ResponseResult<String> result = new ResponseResult<>();
            result.setCode("200");
            result.setSuccess(true);
            result.setData(percentInstance.format(tmpRes.doubleValue()));
            log.info("poi google match cost time is {}s, result is {}", timer.intervalSecond(), result.getData());
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseResult.systemError(GlobalCodeEnum.GL_FAIL_999999, false);
        }
    }

    @ApiOperation(value = "here poi match")
    @PostMapping("/herePoiMatch")
    public ResponseResult<String> herePoiMatch(@RequestParam(value = "country") String country,
                                               @RequestParam(value = "ds") String ds) {

        try {
            // 1.获取国家poi信息
            TimeInterval timer = DateUtil.timer();
            if (!ds.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(ds, false));
            }
            if (StrUtil.isNotEmpty(country)) {
                MybatisPlusConfig.myTableName.set("_" + country);
            }
            List<Place> placeList = placeService.list();

            // 2.调用匹配接口
            int version = poiMService.herePoiMatch(placeList, country);

            // 3.计算匹配度
            int count = poiMatchService.count(Wrappers.<PoiMatch>lambdaQuery().eq(PoiMatch::getCountryPrefix, country)
                    .eq(PoiMatch::getVersion, version).eq(PoiMatch::getIsMatch, 1).eq(PoiMatch::getMatchSrc, "HERE"));

            BigDecimal matchNum = new BigDecimal(count);
            BigDecimal totalNum = new BigDecimal(placeList.size());
            BigDecimal tmpRes = matchNum.divide(totalNum, 2, BigDecimal.ROUND_HALF_UP);
            NumberFormat percentInstance = NumberFormat.getPercentInstance();
            percentInstance.setMaximumFractionDigits(2);

            ResponseResult<String> result = new ResponseResult<>();
            result.setCode("200");
            result.setSuccess(true);
            result.setData(percentInstance.format(tmpRes.doubleValue()));
            log.info("poi here match cost time is {}s, result is {}", timer.intervalSecond(), result.getData());
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseResult.systemError(GlobalCodeEnum.GL_FAIL_999999, false);
        }
    }

    @ApiOperation(value = "here google poi match")
    @PostMapping("/hereGooglePoiMatch")
    public ResponseResult<String> hereGooglePoiMatch(@RequestParam(value = "country") String country,
                                                     @RequestParam(value = "ds") String ds) {

        try {
            // 1.获取国家poi信息
            TimeInterval timer = DateUtil.timer();
            if (!ds.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(ds, false));
            }
            if (StrUtil.isNotEmpty(country)) {
                MybatisPlusConfig.myTableName.set("_" + country);
            }
            List<PoiM> poiList = poiMService.list();

            // 2.调用匹配接口
            int version = poiMService.hereGooglePoiMatch(poiList, country);
            log.info("匹配版本号：{}", version);

            // 3.计算匹配度
            int count = poiMatchService.count(Wrappers.<PoiMatch>lambdaQuery().eq(PoiMatch::getCountryPrefix, country)
                    .eq(PoiMatch::getVersion, version).eq(PoiMatch::getIsMatch, 1).eq(PoiMatch::getMatchSrc, "GOOGLE"));

            BigDecimal matchNum = new BigDecimal(count);
            BigDecimal totalNum = new BigDecimal(poiList.size());
            BigDecimal tmpRes = matchNum.divide(totalNum, 2, BigDecimal.ROUND_HALF_UP);
            NumberFormat percentInstance = NumberFormat.getPercentInstance();
            percentInstance.setMaximumFractionDigits(2);

            ResponseResult<String> result = new ResponseResult<>();
            result.setCode("200");
            result.setSuccess(true);
            result.setData(percentInstance.format(tmpRes.doubleValue()));
            log.info("poi here google match cost time is {}s", timer.intervalSecond());
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseResult.systemError(GlobalCodeEnum.GL_FAIL_999999, false);
        }
    }

    @ApiOperation("hande poi link_id")
    @GetMapping("handlePoiLinkId")
    public ResponseResult<Boolean> handlePoiLinkId(@RequestParam(value = "step", required = false, defaultValue = "330") int step,
                                                   @RequestParam(value = "area", required = false, defaultValue = "") String area,
                                                   @RequestParam(value = "country", required = false, defaultValue = "") String country) throws InterruptedException {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        TimeInterval timer = DateUtil.timer();
        int nodeCount = poiMService.lambdaQuery().last("where length(link_id) != 18").count();
        int loop = nodeCount % step != 0 ? (nodeCount / step) + 1 : nodeCount / step;
        CountDownLatch countDownLatch = new CountDownLatch(loop);
        for (int i = 0; i < loop; i++) {
            List<PoiM> poiList = poiMMapper.selectUnhandleIds(step, i * step);
            poiMService.handleId(area, country, countDownLatch, poiList);
        }
        countDownLatch.await();
        log.info("handle poi (link_id) to be inherited, cost time is {}s,country is {},area is {}", timer.intervalSecond(), country, area);
        return ResponseResult.OK(true, true);
    }

    @ApiOperation("update poiId")
    @GetMapping("updatePoiId")
    public ResponseResult<Boolean> updatePoiId(@RequestParam(value = "step", required = false, defaultValue = "330") int step,
                                               @RequestParam(value = "area", required = false, defaultValue = "") String area,
                                               @RequestParam(value = "country", required = false, defaultValue = "") String country) throws InterruptedException {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        TimeInterval timer = DateUtil.timer();
        int poiCount = poiMService.count();
        int loop = poiCount % step != 0 ? (poiCount / step) + 1 : poiCount / step;
        CountDownLatch countDownLatch = new CountDownLatch(loop);
        for (int i = 0; i < loop; i++) {
            List<PoiM> poiList = poiMService.lambdaQuery()
                    .orderByDesc(PoiM::getPoiId).last("limit " + step + " offset " + i * step).list();
            ;
            poiMService.updatePoiIds(area, country, countDownLatch, poiList);
        }
        countDownLatch.await();
        log.info("handle poi (link_id) to be inherited, cost time is {}s,country is {},area is {}", timer.intervalSecond(), country, area);
        return ResponseResult.OK(true, true);
    }

    @GetMapping("autoCalcPoiCoverageRatio")
    public ResponseResult<String> autoCalcPoiCoverageRatio(String country, String radius, Double similarity) {
        String ds = "mnr";
        Integer resCounts = poiMService.autoCalcPoiCoverageRatio(ds, country, radius, similarity);
        // calc ratio by market
        String market = "";
        if ("sgp".equals(country)) {
            market = "sg";
        }
        if ("phl".equals(country)) {
            market = "ph";
        }
        if (!ds.isEmpty()) {
            // local.yml:mnr
            DynamicDataSourceContextHolder.push("db2");
        }
        if (!country.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + country);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        Integer srcCounts = orderPoiMapper.selectCount(Wrappers.<OrderPoi>lambdaQuery().eq(OrderPoi::getMarket, market));
        log.info("srcCounts:" + srcCounts + " resCounts:" + resCounts);
        if (!srcCounts.equals(resCounts)) {
            return ResponseResult.OK("自动计算入库数量与源数量不匹配!", false);
        }

        Integer totalCounts = poiCoverageResService.lambdaQuery().eq(PoiCoverageRes::getMarket, market).eq(PoiCoverageRes::getRadius, radius).eq(PoiCoverageRes::getSimilarity, similarity).count();
        Integer orderNameVsPoiNameCounts = poiCoverageResService.lambdaQuery().eq(PoiCoverageRes::getMarket, market).eq(PoiCoverageRes::getOrderNamePoiNameMatch, 1).eq(PoiCoverageRes::getRadius, radius).eq(PoiCoverageRes::getSimilarity, similarity).count();
        Integer orderNameVsPoiAliasCounts = poiCoverageResService.lambdaQuery().eq(PoiCoverageRes::getMarket, market).eq(PoiCoverageRes::getOrderNamePoiAliasMatch, 1).eq(PoiCoverageRes::getRadius, radius).eq(PoiCoverageRes::getSimilarity, similarity).count();
        Integer orderNameVsPaNameCounts = poiCoverageResService.lambdaQuery().eq(PoiCoverageRes::getMarket, market).eq(PoiCoverageRes::getOrderNamePaNameMatch, 1).eq(PoiCoverageRes::getRadius, radius).eq(PoiCoverageRes::getSimilarity, similarity).count();
        Integer addressNameVsPoiNameCounts = poiCoverageResService.lambdaQuery().eq(PoiCoverageRes::getMarket, market).eq(PoiCoverageRes::getOrderAddressPoiNameMatch, 1).eq(PoiCoverageRes::getRadius, radius).eq(PoiCoverageRes::getSimilarity, similarity).count();
        Integer addressNameVsPoiAliasCounts = poiCoverageResService.lambdaQuery().eq(PoiCoverageRes::getMarket, market).eq(PoiCoverageRes::getOrderAddressPoiAliasMatch, 1).eq(PoiCoverageRes::getRadius, radius).eq(PoiCoverageRes::getSimilarity, similarity).count();
        Integer addressNameVsPaNameCounts = poiCoverageResService.lambdaQuery().eq(PoiCoverageRes::getMarket, market).eq(PoiCoverageRes::getOrderAddressPaNameMatch, 1).eq(PoiCoverageRes::getRadius, radius).eq(PoiCoverageRes::getSimilarity, similarity).count();
        Integer orderNameVsGooglePoiNameCounts = poiCoverageResService.lambdaQuery().eq(PoiCoverageRes::getMarket, market).eq(PoiCoverageRes::getOrderNameGooglePoiNameMatch, 1).eq(PoiCoverageRes::getRadius, radius).eq(PoiCoverageRes::getSimilarity, similarity).count();
        // Double orderNamePoiNameRatio = (double) orderNameVsPoiNameCounts / totalCounts;
        // Double orderNamePoiAliasRatio = (double) orderNameVsPoiAliasCounts / totalCounts;
        // Double orderNamePaNameRatio = (double) orderNameVsPaNameCounts / totalCounts;
        // Double orderAddressPoiNameRatio = (double) addressNameVsPoiNameCounts / totalCounts;
        // Double orderAddressPoiAliasRatio = (double) addressNameVsPoiAliasCounts / totalCounts;
        // Double orderAddressPaNameRatio = (double) addressNameVsPaNameCounts / totalCounts;


        BigDecimal orderNameVsPoiNameCountsBD = new BigDecimal(orderNameVsPoiNameCounts);
        BigDecimal orderNameVsPoiAliasCountsBD = new BigDecimal(orderNameVsPoiAliasCounts);
        BigDecimal orderNameVsPaNameCountsBD = new BigDecimal(orderNameVsPaNameCounts);
        BigDecimal addressNameVsPoiNameCountsBD = new BigDecimal(addressNameVsPoiNameCounts);
        BigDecimal addressNameVsPoiAliasCountsBD = new BigDecimal(addressNameVsPoiAliasCounts);
        BigDecimal addressNameVsPaNameCountsBD = new BigDecimal(addressNameVsPaNameCounts);
        BigDecimal orderNameVsGooglePoiNameCountsBD = new BigDecimal(orderNameVsGooglePoiNameCounts);
        BigDecimal totalNum = new BigDecimal(totalCounts);
        BigDecimal orderNamePoiNameRatioBD = orderNameVsPoiNameCountsBD.divide(totalNum, 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal orderNamePoiAliasRatioBD = orderNameVsPoiAliasCountsBD.divide(totalNum, 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal orderNamePaNameRatioBD = orderNameVsPaNameCountsBD.divide(totalNum, 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal orderAddressPoiNameRatioBD = addressNameVsPoiNameCountsBD.divide(totalNum, 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal orderAddressPoiAliasRatioBD = addressNameVsPoiAliasCountsBD.divide(totalNum, 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal orderAddressPaNameRatioBD = addressNameVsPaNameCountsBD.divide(totalNum, 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal orderNameGooglePoiNameRatioBD = orderNameVsGooglePoiNameCountsBD.divide(totalNum, 2, BigDecimal.ROUND_HALF_UP);
        NumberFormat percentInstance = NumberFormat.getPercentInstance();
        percentInstance.setMaximumFractionDigits(2);
        String orderNamePoiNameRatio = percentInstance.format(orderNamePoiNameRatioBD.doubleValue());
        String orderNamePoiAliasRatio = percentInstance.format(orderNamePoiAliasRatioBD.doubleValue());
        String orderNamePaNameRatio = percentInstance.format(orderNamePaNameRatioBD.doubleValue());
        String orderAddressPoiNameRatio = percentInstance.format(orderAddressPoiNameRatioBD.doubleValue());
        String orderAddressPoiAliasRatio = percentInstance.format(orderAddressPoiAliasRatioBD.doubleValue());
        String orderAddressPaNameRatio = percentInstance.format(orderAddressPaNameRatioBD.doubleValue());
        String orderNameGooglePoiNameRatio = percentInstance.format(orderNameGooglePoiNameRatioBD.doubleValue());

        log.info("name vs poi name:" + orderNamePoiNameRatio + ",name vs poi alias:" + orderNamePoiAliasRatio + ", name vs pa name:" + orderNamePaNameRatio + ", address vs poi name:" + orderAddressPoiNameRatio + " ,address vs poi alias:" + orderAddressPoiAliasRatio + " ,address vs pa name:" + orderAddressPaNameRatio + ",name vs google poi name:" + orderNameGooglePoiNameRatio);
        return ResponseResult.OK("name vs poi name:" + orderNamePoiNameRatio + ",name vs poi alias:" + orderNamePoiAliasRatio + ", name vs pa name:" + orderNamePaNameRatio + ", address vs poi name:" + orderAddressPoiNameRatio + " ,address vs poi alias:" + orderAddressPoiAliasRatio + " ,address vs pa name:" + orderAddressPaNameRatio + " ,name vs google poi name:" + orderNameGooglePoiNameRatio, true);
    }


    @GetMapping("autoCalcPoiCoverageRatioHereGoogle")
    public ResponseResult<String> autoCalcPoiCoverageRatioHereGoogle(String country, String radius, Double similarity) {
        String ds = "db2";
        Integer resCounts = poiMService.autoCalcPoiCoverageRatioHereGoogle(ds, country, radius, similarity);
        // calc ratio by market
        String market = "";
        if ("sgp".equals(country)) {
            market = "sg";
        }
        if ("phl".equals(country)) {
            market = "ph";
        }
        if (!ds.isEmpty()) {
            // local.yml:mnr
            DynamicDataSourceContextHolder.push(ds);
        }
        if (!country.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + country);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        Integer srcCounts = orderPoiMapper.selectCount(Wrappers.<OrderPoi>lambdaQuery().eq(OrderPoi::getMarket, market));
        log.info("srcCounts:" + srcCounts + " resCounts:" + resCounts);
        if (!srcCounts.equals(resCounts)) {
            return ResponseResult.OK("自动计算入库数量与源数量不匹配!", false);
        }

        Integer totalCounts = poiCoverageHereGoogleService.lambdaQuery().eq(PoiCoverageHereGoogle::getMarket, market).eq(PoiCoverageHereGoogle::getRadius, radius).eq(PoiCoverageHereGoogle::getSimilarity, similarity).count();
        Integer hereGoogleMatchCounts = poiCoverageHereGoogleService.lambdaQuery().eq(PoiCoverageHereGoogle::getMarket, market).eq(PoiCoverageHereGoogle::getHereGooglePoiNameMatch, 1).count();
        BigDecimal hereGoogleMatchCountsBD = new BigDecimal(hereGoogleMatchCounts).divide(new BigDecimal(totalCounts), 2, BigDecimal.ROUND_HALF_UP);
        NumberFormat percentInstance = NumberFormat.getPercentInstance();
        percentInstance.setMaximumFractionDigits(2);

        String orderNameGooglePoiNameRatio = percentInstance.format(hereGoogleMatchCountsBD.doubleValue());
        log.info("order_name: here_poi name in google_poi_name:" + orderNameGooglePoiNameRatio);
        return ResponseResult.OK("order_name: here_poi name in google_poi_name:" + orderNameGooglePoiNameRatio, true);

    }


    /**
     * 调用中台封装服务测试订单覆盖率
     *
     * @param country
     * @param radius
     * @param similarity
     * @return
     */
    @GetMapping("autoCalcPoiCoverageRatioHereGoogleByMp")
    public ResponseResult<String> autoCalcPoiCoverageRatioHereGoogleByMp(String country, String radius, Double similarity) {
        String ds = "db2";
        Integer resCounts = poiMService.autoCalcPoiCoverageRatioHereGoogleByMp(ds, country, radius, similarity);
        // calc ratio by market
        String market = "";
        if ("sgp".equals(country)) {
            market = "sg";
        }
        if ("phl".equals(country)) {
            market = "ph";
        }
        if (!ds.isEmpty()) {
            // local.yml:mnr
            DynamicDataSourceContextHolder.push(ds);
        }
        if (!country.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + country);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        Integer srcCounts = orderPoiMapper.selectCount(Wrappers.<OrderPoi>lambdaQuery().eq(OrderPoi::getMarket, market));
        log.info("srcCounts:" + srcCounts + " resCounts:" + resCounts);
        if (!srcCounts.equals(resCounts)) {
            return ResponseResult.OK("自动计算入库数量与源数量不匹配!", false);
        }

        Integer totalCounts = poiCoverageMpResService.lambdaQuery().eq(PoiCoverageMpRes::getMarket, market).eq(PoiCoverageMpRes::getRadius, radius).eq(PoiCoverageMpRes::getSimilarity, similarity).count();
        Integer hereMatchCounts = poiCoverageMpResService.lambdaQuery().eq(PoiCoverageMpRes::getMarket, market).eq(PoiCoverageMpRes::getHerePoiNameMatch, 1).count();
        Integer GoogleMatchCounts = poiCoverageMpResService.lambdaQuery().eq(PoiCoverageMpRes::getMarket, market).eq(PoiCoverageMpRes::getGooglePoiNameMatch, 1).count();
        BigDecimal hereMatchCountsBD = new BigDecimal(hereMatchCounts).divide(new BigDecimal(totalCounts), 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal googleMatchCountsBD = new BigDecimal(GoogleMatchCounts).divide(new BigDecimal(totalCounts), 2, BigDecimal.ROUND_HALF_UP);
        NumberFormat percentInstance = NumberFormat.getPercentInstance();
        percentInstance.setMaximumFractionDigits(2);

        String herePoiNameRatio = percentInstance.format(hereMatchCountsBD.doubleValue());
        String googlePoiNameRatio = percentInstance.format(googleMatchCountsBD.doubleValue());
        log.info("order_name vs here_poi " + herePoiNameRatio);
        log.info("order_name vs google_poi " + googlePoiNameRatio);
        return ResponseResult.OK("order_name vs here_poi is " + herePoiNameRatio + " order_name vs google_poi is " + googlePoiNameRatio, true);

    }

    @GetMapping("poi/evaluation")
    public ResponseResult<String> poiEvaluation(String country) {
        // 1.读取样本数据
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        MybatisPlusConfig.myTableName.set("");
        List<PoiSample> poiSampleSrc = poiSampleMapper.selectList(null);

        List<PoiSampleRes> resList = new ArrayList<>();
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        MybatisPlusConfig.myTableName.set("");

        poiMService.evaluateSample(poiSampleSrc, resList, country);

        log.info("poi evaluation finished,src size is [{}],res size is [{}]", poiSampleSrc.size(), resList.size());

        return ResponseResult.OK("poi evaluation finished,src size is [" + poiSampleSrc.size() + "],res size is [" + resList.size() + "]", true);
    }

    @GetMapping("poi/import")
    public ResponseResult<String> importPoiSrc(String xmlFilePath, String country) {
        Integer insertNum = 0;
        try {
            insertNum = poiMService.importPoiSrc(xmlFilePath, country);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return ResponseResult.OK("import poi src finished,country:" + country + ",num:" + insertNum, true);
    }

    @ApiOperation(value = "tomtom poi convert")
    @PostMapping("/tomtompoi/convert")
    public ResponseResult<Boolean> tomtomPoiConvert(@RequestParam(value = "step",
                                                            required = false,
                                                            defaultValue = "1") int step,
                                                    @RequestParam(value = "area",
                                                            required = false,
                                                            defaultValue = "") String area,
                                                    @RequestParam(value = "country",
                                                            required = false,
                                                            defaultValue = "") String country,
                                                    @RequestParam(value = "source",
                                                            required = false,
                                                            defaultValue = "") String source,
                                                    @RequestParam(value = "startnum",
                                                            required = false,
                                                            defaultValue = "0") int startnum,
                                                    @RequestParam(value = "ds",
                                                            required = false,
                                                            defaultValue = "") String ds) {
//        List<String> aerowayValues = Arrays.asList(
//                "aerodrome", "apron", "helipad", "heliport", "runway", "taxiway", "terminal"
//        );
//        List<String> amenityValues = Arrays.asList(
//                "adult_entertainment", "animal_boarding", "animal_breeding", "animal_shelter", "arts_and_culture",
//                "arts_centre", "atm", "baby_hatch", "baking_oven", "bank", "bar", "bbq", "bench", "bicycle_parking",
//                "bicycle_rental", "bicycle_repair_station", "biergarten", "blood_bank", "boat_rental", "boat_sharing",
//                "brothel", "bureau_de_change", "bus_station", "business", "cafe", "cafeteria", "car_rental", "car_sharing",
//                "car_wash", "casino", "charging_location", "childcare", "cinema", "clinic", "clock", "coffee_shop", "college",
//                "comedy_club", "commercial", "community_centre", "conference_centre", "courthouse", "crematorium", "cultural_centre",
//                "dentist", "doctors", "dog_toilet", "drinking_water", "driving", "driving_school", "eat_and_drink", "education",
//                "emergency_services", "events_venue", "facilities", "fast_food", "ferry_terminal", "finance", "fire_station",
//                "food_court", "food_sharing", "fountain", "fuel", "funeral_hall", "gambling", "give_box", "gold_exchange",
//                "grit_bin", "healthcare", "hospital", "hunting_stand", "internet_cafe", "juice_bar", "karaoke_box", "kindergarten",
//                "kitchen", "kneipp_water_cure", "language_school", "leisure", "library", "lodging", "lounger", "love_hotel",
//                "marketplace", "military", "monastery", "motorcycle_parking", "motorcycle_rental", "music_school", "nightclub",
//                "parcel_locker", "parking", "payment_centre", "payment_terminal", "pedestrian_subway", "pharmacy", "photo_booth",
//                "place_of_mourning", "place_of_worship", "planetarium", "police", "post_box", "post_depot", "post_office",
//                "prep_school", "prison", "pub", "public", "public_bath", "public_bookcase", "public_services", "public_transport_ticket_counter",
//                "ranger_station", "recycling", "refugee_site", "rent_a_car_parking", "rescue_station", "research_institute",
//                "restaurant", "road_rescue", "sanitary_dump_station", "school", "shelter", "shower", "social_centre", "social_facility",
//                "stock_exchange", "stripclub", "studio", "swingerclub", "taxi", "tea_house", "telephone", "theatre", "toilets",
//                "townhall", "toy_library", "training", "trolley_bay", "truck_stop", "truck_wash", "university", "vehicle_inspection",
//                "vending_machine", "veterinary", "waste_basket", "waste_disposal", "water_point", "weighbridge", "yes"
//        );
//        List<String> barrierValues = Arrays.asList(
//                "border_control", "security_gate", "toll_booth"
//        );
//        List<String> boundaryValues = Arrays.asList(
//                "aboriginal_lands", "national_park", "protected_area"
//        );
//        List<String> clubValues = Arrays.asList(
//                "beach", "scout", "sport", "yes"
//        );
//        List<String> craftValues = Arrays.asList(
//                "agricultural_engines", "atelier", "bakery", "basket_maker", "beekeeper", "blacksmith", "boatbuilder", "bookbinder",
//                "brewery", "builder", "cabinet_maker", "car_painter", "carpenter", "carpet_layer", "caterer", "chimney_sweeper",
//                "cleaning", "clockmaker", "confectionery", "cooper", "dental_technician", "distillery", "door_construction",
//                "dressmaker", "electrician", "electronics_repair", "embroiderer", "engraver", "floorer", "gardener", "glaziery",
//                "goldsmith", "grinding_mill", "handicraft", "hvac", "insulation", "interior_work", "jeweller", "joiner", "key_cutter",
//                "locksmith", "metal_construction", "mint", "musical_instrument", "oil_mill", "optician", "organ_builder", "painter",
//                "parquet_layer", "paver", "photographer", "photographic_laboratory", "piano_tuner", "plasterer", "plumber", "pottery",
//                "printer", "printmaker", "rigger", "roofer", "saddler", "sailmaker", "sawmill", "scaffolder", "sculptor", "shoemaker",
//                "signmaker", "stand_builder", "stonemason", "stove_fitter", "sun_protection", "tiler", "tinsmith", "toolmaker", "turner",
//                "upholsterer", "watchmaker", "water_well_drilling", "window_construction", "winery"
//        );
//        List<String> emeAmenityValues = Arrays.asList(
//                "social_facility"
//        );
//        List<String> emergencyValues = Arrays.asList(
//                "ambulance_station", "assembly_point", "defibrillator", "emergency_ward_entrance", "fire_hydrant", "phone", "water_tank"
//        );
//        List<String> geologicalValues = Arrays.asList(
//                "moraine", "palaeontological_site", "volcanic_caldera_rim", "volcanic_lava_field"
//        );
//        List<String> healthcareValues = Arrays.asList(
//                "alternative", "audiologist", "blood_donation", "counselling", "hospice", "laboratory", "physiotherapist", "podiatrist", "psychotherapist", "sample_collection"
//        );
//        List<String> highwayValues = Arrays.asList(
//                "rest_area", "services", "toll_gantry", "trailhead"
//        );
//        List<String> historicValues = Arrays.asList(
//                "aircraft", "archaeological_site", "battlefield", "bomb_crater", "boundary_stone", "building", "cannon", "castle",
//                "church", "city_gate", "creamery", "farm", "fort", "gallows", "locomotive", "manor", "memorial", "milestone", "monastery",
//                "monument", "pa", "pillory", "railway_car", "ruins", "rune_stone", "ship", "tank", "tomb", "vehicle", "wayside_cross",
//                "wayside_shrine", "wreck"
//        );
//        List<String> landuseValues = Arrays.asList(
//                "allotments", "artificial_ground", "brownfield", "builtup_area", "cemetery", "commercial", "construction", "farmland",
//                "farmyard", "flowerbed", "garages", "grass", "greenfield", "greenhouse_horticulture", "industrial", "landfill", "managed_green",
//                "meadow", "military", "orchard", "plant_nursery", "quarry", "railway", "recreation_ground", "religious", "residential", "retail",
//                "village_green", "vineyard", "winter_sports"
//        );
//        List<String> leisureValues = Arrays.asList(
//                "adult_gaming_centre", "amusement_arcade", "bandstand", "beach_resort", "bird_hide", "bowling_alley", "common", "dance",
//                "disc_golf_course", "dog_park", "entertainment", "escape_game", "firepit", "fishing", "fitness_centre", "fitness_station",
//                "flying_club", "garden", "golf_course", "hackerspace", "horse_riding", "ice_rink", "leisure_centre", "marina",
//                "miniature_golf", "nature_reserve", "outdoor", "park", "picnic_table", "pitch", "playground", "resort", "sauna", "slipway",
//                "sport", "sports_centre", "sports_hall", "stadium", "summer_camp", "swimming_pool", "track", "trampoline_park", "water_park"
//        );
//        List<String> manMadeValues = Arrays.asList(
//                "breakwater", "obelisk", "observatory", "pier", "stupa", "windmill", "works"
//        );
//        List<String> militaryValues = Arrays.asList(
//                "airfield", "base", "checkpoint"
//        );
//        List<String> mountainPassValues = Arrays.asList(
//                "yes"
//        );
//        List<String> naturalValues = Arrays.asList(
//                "arch", "arete", "bare_rock", "beach", "blowhole", "cape", "cave", "cave_entrance", "cliff", "cove", "dune", "earth_bank",
//                "fell", "fumarole", "geyser", "glacier", "grassland", "harbour", "heath", "hill", "hot_spring", "isthmus", "locale", "oasis",
//                "pan", "peak", "plain", "plateau", "rapids", "reef", "reservoir", "ridge", "river_crossing", "rock", "saddle", "sand", "scree",
//                "scrub", "shingle", "shoal", "shrubbery", "sinkhole", "spring", "tundra", "valley", "volcano", "wetland", "wood", "yes"
//        );
//        List<String> officeValues = Arrays.asList(
//                "accountant", "advertising_agency", "architect", "association", "bail_bond_agent", "charity", "company", "construction_company",
//                "consulting", "courier", "coworking", "diplomatic", "educational_institution", "employment_agency", "energy_supplier", "engineer",
//                "estate_agent", "financial", "financial_advisor", "forestry", "geodesist", "government", "graphic_design", "guide", "harbour_master",
//                "insurance", "it", "lawyer", "logistics", "motoring_organization", "moving_company", "newspaper", "ngo", "notary", "political_party",
//                "quango", "religion", "research", "security", "surveyor", "tax_advisor", "telecommunication", "therapist", "traffic", "travel_agent",
//                "union", "visa", "water_utility", "wedding_planner", "yes"
//        );
//        List<String> placeValues = Arrays.asList(
//                "archipelago", "farm", "island", "islet"
//        );
//        List<String> publicTransportValues = Arrays.asList(
//                "platform", "station", "stop_position"
//        );
//        List<String> railwayValues = Arrays.asList(
//                "halt", "station", "subway_entrance", "train_station_entrance", "tram_stop"
//        );
//        List<String> shopValues = Arrays.asList(
//                "agrarian", "alcohol", "anime", "antiques", "appliance", "art", "atv", "baby_goods", "bag", "bakery", "bathroom_furnishing",
//                "beauty", "bed", "beverages", "bicycle", "boat", "boat_parts", "bookmaker", "books", "boutique", "brewing_supplies", "bus",
//                "butcher", "camera", "candles", "cannabis", "car", "car_parts", "car_repair", "caravan", "carpet", "catalogue", "ceramics",
//                "charity", "cheese", "chemist", "chocolate", "christmas", "clothes", "coffee", "collector", "computer", "confectionery",
//                "convenience", "copyshop", "cosmetics", "country_store", "craft", "curtain", "dairy", "deli", "department_store", "doityourself",
//                "doors", "dry_cleaning", "e-cigarette", "electrical", "electronics", "energy", "erotic", "fabric", "factory_outlet", "farm",
//                "fashion_accessories", "fireplace", "fishing", "flooring", "florist", "food", "food_markets", "frame", "frozen_food", "fuel",
//                "funeral_directors", "furniture", "games", "garden_centre", "garden_furniture", "gas", "general", "gift", "glaziery", "golf",
//                "greengrocer", "grocery", "groundskeeping", "hairdresser", "hairdresser_supply", "hardware", "health_food", "hearing_aids",
//                "herbalist", "hifi", "household_linen", "houseware", "hunting", "ice_cream", "interior_decoration", "jetski", "jewelry", "kiosk",
//                "kitchen", "laundry", "leather", "lighting", "local_specialities", "locksmith", "lottery", "mall", "marine_electronics", "massage",
//                "medical_supply", "military_surplus", "mobile_phone", "model", "money_lender", "motorcycle", "motorcycle_repair", "music",
//                "musical_instrument", "newsagent", "nutrition_supplements", "nuts", "optician", "organic", "outdoor", "outpost", "paint", "party",
//                "pasta", "pastry", "pawnbroker", "perfumery", "pest_control", "pet", "pet_grooming", "pet_supplies", "photo", "plant_hire", "pottery",
//                "printer_ink", "pyrotechnics", "radiotechnics", "religion", "rental", "repair", "retail_outlet", "scuba_diving", "seafood",
//                "second_hand", "security", "sewing", "shoes", "shopping_service", "ski", "snowmobile", "specialty_food", "spices", "sports",
//                "stationery", "storage_rental", "supermarket", "swimming_pool", "tailor", "tattoo", "tea", "telecommunication", "ticket", "tiles",
//                "tobacco", "tool_hire", "toys", "trade", "trailer", "travel_agency", "trophy", "truck", "truck_repair", "tyres", "vacuum_cleaner",
//                "van", "variety_store", "video", "video_games", "watches", "water", "weapons", "wholesale", "window_blind", "windows", "wine", "wool",
//                "yes"
//        );
//        List<String> tourismValues = Arrays.asList(
//                "alpine_hut", "apartment", "aquarium", "artwork", "attraction", "bungalow", "cabin", "camp_site", "caravan_site", "chalet",
//                "cottage", "gallery", "guest_house", "holiday_rental", "hostel", "hotel", "information", "motel", "museum", "picnic_site",
//                "rest_camps", "theme_park", "viewpoint", "villa", "wilderness_hut", "zoo"
//        );
//        List<String> transportValues = Arrays.asList(
//                "yes"
//        );
//        List<String> utilityValues = Arrays.asList(
//                "yes"
//        );

//        if (!country.isEmpty()) {
//            log.info("db is:" + CommonUtils.getDsbyCountry(country, true));
//            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
//        }
        if (!ds.isEmpty()) {
            DynamicDataSourceContextHolder.push(ds);
        }
        Integer listSize = planetOsmPointService.count();
        log.info("The records to be transfered:" + listSize);
        for (int i = startnum; i <= listSize / step; i++) {
            if (!ds.isEmpty()) {
                DynamicDataSourceContextHolder.push(ds);
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            if (StrUtil.isNotEmpty(source)) {
                MybatisPlusConfig.myTableName.set("_" + source);
            }
            log.info("processing: limit " + step + " offset " + i * step);
            List<PlanetOsmPoint> planetOsmPointList = planetOsmPointService.lambdaQuery()
                    .orderByDesc(PlanetOsmPoint::getOsmId).last("limit " + step + " offset " + i * step).list();
            // 过滤出 aerialway = "station" 的数据
            List<PlanetOsmPoint> aerialwayStationList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getAerialwayValues().contains(point.getAerialway())) // 确保 null 安全，直接用 equals 对比
                    .collect(Collectors.toList());

            // 过滤出 aeroway 在指定范围内的数据
            List<PlanetOsmPoint> aerowayFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getAerowayValues().contains(point.getAeroway())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            // 过滤出 amenity 在指定范围内的数据
            List<PlanetOsmPoint> amenityFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getAmenityValues().contains(point.getAmenity())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            // 过滤出 barrier 在指定范围内的数据
            List<PlanetOsmPoint> barrierFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getBarrierValues().contains(point.getBarrier())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            // 过滤出 boundary 在指定范围内的数据
            List<PlanetOsmPoint> boundaryFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getBoundaryValues().contains(point.getBoundary())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            // 过滤出 club 在指定范围内的数据
            List<PlanetOsmPoint> clubFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getClubValues().contains(point.getClub())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());
            // 过滤出 craft 在指定范围内的数据
            List<PlanetOsmPoint> craftFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getCraftValues().contains(point.getCraft())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            // 过滤出 emeAmenity 在指定范围内的数据
            List<PlanetOsmPoint> emeAmenityFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getEmeAmenityValues().contains(point.getEmergencyAmenity())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());
            // 过滤出 emergency 在指定范围内的数据
            List<PlanetOsmPoint> emergencyFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getEmergencyValues().contains(point.getEmergency())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            // 过滤出 geological 在指定范围内的数据
            List<PlanetOsmPoint> geologicalFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getGeologicalValues().contains(point.getGeological())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            // 过滤出 healthcare 在指定范围内的数据
            List<PlanetOsmPoint> healthcareFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getHealthcareValues().contains(point.getHealthcare())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            // 过滤出 highway 在指定范围内的数据
            List<PlanetOsmPoint> highwayFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getHighwayValues().contains(point.getHighway())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            // 过滤出 historic 在指定范围内的数据
            List<PlanetOsmPoint> historicFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getHistoricValues().contains(point.getHistoric())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            // 过滤出 landuse 在指定范围内的数据
            List<PlanetOsmPoint> landuseFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getLanduseValues().contains(point.getLanduse())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            // 过滤出 leisure 在指定范围内的数据
            List<PlanetOsmPoint> leisureFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getLeisureValues().contains(point.getLeisure())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            // 过滤出 manMade 在指定范围内的数据
            List<PlanetOsmPoint> manMadeFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getManMadeValues().contains(point.getManMade())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            // 过滤出 military 在指定范围内的数据
            List<PlanetOsmPoint> militaryFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getMilitaryValues().contains(point.getMilitary())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            // 过滤出 mountainPass 在指定范围内的数据
            List<PlanetOsmPoint> mountainPassFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getMountainPassValues().contains(point.getMountainPass())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            // 过滤出 natural 在指定范围内的数据
            List<PlanetOsmPoint> naturalFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getNaturalValues().contains(point.getNatural())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            // 过滤出 office 在指定范围内的数据
            List<PlanetOsmPoint> officeFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getOfficeValues().contains(point.getOffice())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            // 过滤出 place 在指定范围内的数据
            List<PlanetOsmPoint> placeFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getPlaceValues().contains(point.getPlace())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            // 过滤出 publicTransport 在指定范围内的数据
            List<PlanetOsmPoint> publicTransportFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getPublicTransportValues().contains(point.getPublicTransport())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            // 过滤出 railway 在指定范围内的数据
            List<PlanetOsmPoint> railwayFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getRailwayValues().contains(point.getRailway())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            // 过滤出 shop 在指定范围内的数据
            List<PlanetOsmPoint> shopFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getShopValues().contains(point.getShop())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            // 过滤出 tourism 在指定范围内的数据
            List<PlanetOsmPoint> tourismFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getTourismValues().contains(point.getTourism())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            // 过滤出 transport 在指定范围内的数据
            List<PlanetOsmPoint> transportFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getTransportValues().contains(point.getTransport())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            // 过滤出 utility 在指定范围内的数据
            List<PlanetOsmPoint> utilityFilteredList = planetOsmPointList.stream()
                    .filter(point -> mapTTConfig.getUtilityValues().contains(point.getUtility())) // 确保值存在于指定集合中
                    .collect(Collectors.toList());

            if (aerialwayStationList.size() > 0) {
                log.info("The aerialway records to be transfered:" + aerialwayStationList.size());
                poiMService.poiConvertTomTom(aerialwayStationList, "aerialway", source, country, area);
                // poiService.poiConvertAutosvc(herePhaAutosvcListi);
            }
            if (aerowayFilteredList.size() > 0) {
                log.info("The aeroway records to be transfered:" + aerowayFilteredList.size());
                poiMService.poiConvertTomTom(aerowayFilteredList, "aeroway", source, country, area);
            }
            if (amenityFilteredList.size() > 0) {
                log.info("The amenity records to be transfered:" + amenityFilteredList.size());
                poiMService.poiConvertTomTom(amenityFilteredList, "amenity", source, country, area);
            }
            if (barrierFilteredList.size() > 0) {
                log.info("The barrier records to be transfered:" + barrierFilteredList.size());
                poiMService.poiConvertTomTom(barrierFilteredList, "barrier", source, country, area);
            }
            if (boundaryFilteredList.size() > 0) {
                log.info("The boundary records to be transfered:" + boundaryFilteredList.size());
                poiMService.poiConvertTomTom(boundaryFilteredList, "boundary", source, country, area);
            }
            if (clubFilteredList.size() > 0) {
                log.info("The club records to be transfered:" + clubFilteredList.size());
                poiMService.poiConvertTomTom(clubFilteredList, "club", source, country, area);
            }
            if (craftFilteredList.size() > 0) {
                log.info("The craft records to be transfered:" + craftFilteredList.size());
                poiMService.poiConvertTomTom(craftFilteredList, "craft", source, country, area);
            }
            if (emeAmenityFilteredList.size() > 0) {
                log.info("The emeAmenity records to be transfered:" + emeAmenityFilteredList.size());
                poiMService.poiConvertTomTom(emeAmenityFilteredList, "emeAmenity", source, country, area);
            }
            if (emergencyFilteredList.size() > 0) {
                log.info("The emergency records to be transfered:" + emergencyFilteredList.size());
                poiMService.poiConvertTomTom(emergencyFilteredList, "emergency", source, country, area);
            }
            if (geologicalFilteredList.size() > 0) {
                log.info("The geological records to be transfered:" + geologicalFilteredList.size());
                poiMService.poiConvertTomTom(geologicalFilteredList, "geological", source, country, area);
            }
            if (healthcareFilteredList.size() > 0) {
                log.info("The healthcare records to be transfered:" + healthcareFilteredList.size());
                poiMService.poiConvertTomTom(healthcareFilteredList, "healthcare", source, country, area);
            }
            if (highwayFilteredList.size() > 0) {
                log.info("The highway records to be transfered:" + highwayFilteredList.size());
                poiMService.poiConvertTomTom(highwayFilteredList, "highway", source, country, area);
            }
            if (historicFilteredList.size() > 0) {
                log.info("The historic records to be transfered:" + historicFilteredList.size());
                poiMService.poiConvertTomTom(historicFilteredList, "historic", source, country, area);
            }
            if (landuseFilteredList.size() > 0) {
                log.info("The landuse records to be transfered:" + landuseFilteredList.size());
                poiMService.poiConvertTomTom(landuseFilteredList, "landuse", source, country, area);
            }
            if (leisureFilteredList.size() > 0) {
                log.info("The leisure records to be transfered:" + leisureFilteredList.size());
                poiMService.poiConvertTomTom(leisureFilteredList, "leisure", source, country, area);
            }
            if (manMadeFilteredList.size() > 0) {
                log.info("The manMade records to be transfered:" + manMadeFilteredList.size());
                poiMService.poiConvertTomTom(manMadeFilteredList, "manMade", source, country, area);
            }
            if (militaryFilteredList.size() > 0) {
                log.info("The military records to be transfered:" + militaryFilteredList.size());
                poiMService.poiConvertTomTom(militaryFilteredList, "military", source, country, area);
            }
            if (mountainPassFilteredList.size() > 0) {
                log.info("The mountainPass records to be transfered:" + mountainPassFilteredList.size());
                poiMService.poiConvertTomTom(mountainPassFilteredList, "mountainPass", source, country, area);
            }
            if (naturalFilteredList.size() > 0) {
                log.info("The natural records to be transfered:" + naturalFilteredList.size());
                poiMService.poiConvertTomTom(naturalFilteredList, "natural", source, country, area);
            }
            if (officeFilteredList.size() > 0) {
                log.info("The office records to be transfered:" + officeFilteredList.size());
                poiMService.poiConvertTomTom(officeFilteredList, "office", source, country, area);
            }
            if (placeFilteredList.size() > 0) {
                log.info("The place records to be transfered:" + placeFilteredList.size());
                poiMService.poiConvertTomTom(placeFilteredList, "place", source, country, area);
            }
            if (publicTransportFilteredList.size() > 0) {
                log.info("The publicTransport records to be transfered:" + publicTransportFilteredList.size());
                poiMService.poiConvertTomTom(publicTransportFilteredList, "publicTransport", source, country, area);
            }
            if (railwayFilteredList.size() > 0) {
                log.info("The railway records to be transfered:" + railwayFilteredList.size());
                poiMService.poiConvertTomTom(railwayFilteredList, "railway", source, country, area);
            }
            if (shopFilteredList.size() > 0) {
                log.info("The shop records to be transfered:" + shopFilteredList.size());
                poiMService.poiConvertTomTom(shopFilteredList, "shop", source, country, area);
            }
            if (tourismFilteredList.size() > 0) {
                log.info("The tourism records to be transfered:" + tourismFilteredList.size());
                poiMService.poiConvertTomTom(tourismFilteredList, "tourism", source, country, area);
            }
            if (transportFilteredList.size() > 0) {
                log.info("The transport records to be transfered:" + transportFilteredList.size());
                poiMService.poiConvertTomTom(transportFilteredList, "transport", source, country, area);
            }
            if (utilityFilteredList.size() > 0) {
                log.info("The utility records to be transfered:" + utilityFilteredList.size());
                poiMService.poiConvertTomTom(utilityFilteredList, "utility", source, country, area);
            }
        }
        return ResponseResult.OK(true, true);
    }

    @GetMapping("poi/quarterDiff")
    public ResponseResult<PoiMDiffRes> poiQuarterDiff(String preQuarter,String currentQuarter,String country,@RequestParam(required = false,defaultValue = "false") Boolean diffDeleteDataFlag) {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        PoiMDiffRes poiMDiffRes = poiMService.quarterDiff(preQuarter, currentQuarter, country, diffDeleteDataFlag);

        return ResponseResult.OK(poiMDiffRes, true);
    }

    @GetMapping("poi/quarterDiffUpdate")
    public ResponseResult<String> poiQuarterDiffUpdate(String preQuarter,String currentQuarter,String country) {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        String result = poiMService.quarterDiffUpdate(preQuarter, currentQuarter, country);

        return ResponseResult.OK(result, true);
    }

    @GetMapping("poi/merge")
    public ResponseResult<String> poiMerge(String country,String date) {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        MybatisPlusConfig.myTableName.set("");
        String result = poiMService.poiMerge(country,date);

        return ResponseResult.OK(result, true);
    }
}
