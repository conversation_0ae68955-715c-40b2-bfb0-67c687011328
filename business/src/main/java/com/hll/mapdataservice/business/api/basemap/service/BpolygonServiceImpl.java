package com.hll.mapdataservice.business.api.basemap.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.google.common.collect.Lists;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.third.InheritIDService;
import com.hll.mapdataservice.business.third.dto.InheritIDDTO;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.mapper.BpolygonMapper;
import com.hll.mapdataservice.common.service.IBpolygonService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.common.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-02
 */
@Service
@Slf4j
public class BpolygonServiceImpl extends ServiceImpl<BpolygonMapper, Bpolygon> implements IBpolygonService {

    @Resource
    InheritIDService inheritIDService;
    @Resource
    BpolygonMapper bploygonMapper;
    @Async("asyncTaskExecutor")
    //@Async()
    public void bpolygonConvertWaterpoly(List<Waterpoly> waterpolyList, Boolean isCompileTransEng, String area,
                                         String country, CountDownLatch countDownLatch) {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            log.info("processing country:" + CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        log.info("tmp thread is:" + Thread.currentThread().getName());
        List<Bpolygon> bpolygonList = new ArrayList<>();

        try {
            if (waterpolyList.size() > 0) {
                for (Waterpoly waterpoly : waterpolyList
                ) {
                    Bpolygon bpolygon = new Bpolygon();
                    //id
                    bpolygon.setId(waterpoly.getPolygonId().toString());
                    //scource_id
                    bpolygon.setSourceId(waterpoly.getPolygonId().toString());
                    //kind
                    bpolygon.setKind(waterpoly.getFeatCod().toString());
                    //name
                    bpolygon.setName(waterpoly.getPolygonNm());
                    //nm_langcd
                    bpolygon.setNmLangcd(waterpoly.getNmLangcd());
                    //nm_tr
                    bpolygon.setNmTr(waterpoly.getPolyNmTr());
                    //trans_type
                    bpolygon.setTransType(waterpoly.getTransType());
                    //geometry
                    bpolygon.setGeometry(waterpoly.getGeom());
                    //poi_id
                    bpolygon.setPoiId(null);
                    //disp_class
                    bpolygon.setDispClass(waterpoly.getDispClass());
                    // UP_DATE
                    bpolygon.setUpDate(LocalDateTime.now());
                    // STATUS
                    bpolygon.setStatus(0);
                    // DATASOURCE
                    bpolygon.setDatasource("7");

                    bpolygonList.add(bpolygon);
                }
                log.info("bpolygonList size is:" + bpolygonList.size());
                // this.saveOrUpdateBatch(linList);
                List<List<Bpolygon>> bpolygonListPartition = Lists.partition(bpolygonList, 32767/BeanUtil.beanToMap(new Bpolygon()).keySet().size());
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }

                for (List<Bpolygon> partitionList : bpolygonListPartition) {
                    // log.info("linkMapper threadlocal info is:" + Thread.currentThread().getName());
                    // linMapper.mysqlInsertOrUpdateBath(partitionList);
                    List<Long> bpolygonIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(Bpolygon::getId).collect(Collectors.toList())));
                    for (int i = 0; i < partitionList.size(); i++) {
                        partitionList.get(i).setId(String.valueOf(bpolygonIds.get(i)));
                    }
                    bploygonMapper.mysqlInsertOrUpdateBath(partitionList);
                    // this.saveOrUpdateBatch(partitionList);
                }
            }
        } catch (Exception e) {
            log.error("bpolygon convert error,msg is {}", e);
            throw new RuntimeException("bpolygon convert error,msg is {}" + e.getMessage());
        } finally {
            countDownLatch.countDown();
        }
    }
    @Async("asyncTaskExecutor")
    public void bpolygonConvertLandusea(List<Landusea> landuseaList, Boolean isCompileTransEng, String area,
                                        String country, CountDownLatch countDownLatch) {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            log.info("processing country:" + CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        log.info("tmp thread is:" + Thread.currentThread().getName());
        List<Bpolygon> bpolygonList = new ArrayList<>();

        try {
            if (landuseaList.size() > 0) {
                for (Landusea landusea : landuseaList
                ) {
                    Bpolygon bpolygon = new Bpolygon();
                    //id
                    bpolygon.setId(landusea.getPolygonId().toString());
                    //scource_id
                    bpolygon.setSourceId(landusea.getPolygonId().toString());
                    //kind
                    bpolygon.setKind(landusea.getFeatCod().toString());
                    //name
                    bpolygon.setName(landusea.getPolygonNm());
                    //nm_langcd
                    bpolygon.setNmLangcd(landusea.getNmLangcd());
                    //nm_tr
                    bpolygon.setNmTr(landusea.getPolyNmTr());
                    //trans_type
                    bpolygon.setTransType(landusea.getTransType());
                    //geometry
                    bpolygon.setGeometry(landusea.getGeom());
                    //poi_id
                    bpolygon.setPoiId(null);
                    //disp_class
                    bpolygon.setDispClass(landusea.getDispClass());
                    // UP_DATE
                    bpolygon.setUpDate(LocalDateTime.now());
                    // STATUS
                    bpolygon.setStatus(0);
                    // DATASOURCE
                    bpolygon.setDatasource("7");

                    bpolygonList.add(bpolygon);
                }
                log.info("bpolygonList size is:" + bpolygonList.size());
                // this.saveOrUpdateBatch(linList);
                List<List<Bpolygon>> bpolygonListPartition = Lists.partition(bpolygonList, BeanUtil.beanToMap(new Bpolygon()).keySet().size());
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }

                for (List<Bpolygon> partitionList : bpolygonListPartition) {
                    // log.info("linkMapper threadlocal info is:" + Thread.currentThread().getName());
                    // linMapper.mysqlInsertOrUpdateBath(partitionList);
                    List<Long> bpolygonIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(Bpolygon::getId).collect(Collectors.toList())));
                    for (int i = 0; i < partitionList.size(); i++) {
                        partitionList.get(i).setId(String.valueOf(bpolygonIds.get(i)));
                    }
                    bploygonMapper.mysqlInsertOrUpdateBath(partitionList);
                    // this.saveOrUpdateBatch(partitionList);
                }
            }
        } catch (Exception e) {
            log.error("bpolygon convert error,msg is {}", e);
            throw new RuntimeException("bpolygon convert error,msg is {}" + e.getMessage());
        } finally {
            countDownLatch.countDown();
        }
    }
    @Async("asyncTaskExecutor")
    public void bpolygonConvertLanduseb(List<Landuseb> landusebList, Boolean isCompileTransEng, String area,
                                        String country, CountDownLatch countDownLatch) {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            log.info("processing country:" + CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        log.info("tmp thread is:" + Thread.currentThread().getName());
        List<Bpolygon> bpolygonList = new ArrayList<>();

        try {
            if (landusebList.size() > 0) {
                for (Landuseb landuseb : landusebList
                ) {
                    Bpolygon bpolygon = new Bpolygon();
                    //id
                    bpolygon.setId(landuseb.getPolygonId().toString());
                    //scource_id
                    bpolygon.setSourceId(landuseb.getPolygonId().toString());
                    //kind
                    bpolygon.setKind(landuseb.getFeatCod().toString());
                    //name
                    bpolygon.setName(landuseb.getPolygonNm());
                    //nm_langcd
                    bpolygon.setNmLangcd(landuseb.getNmLangcd());
                    //nm_tr
                    bpolygon.setNmTr(landuseb.getPolyNmTr());
                    //trans_type
                    bpolygon.setTransType(landuseb.getTransType());
                    //geometry
                    bpolygon.setGeometry(landuseb.getGeom());
                    //poi_id
                    bpolygon.setPoiId(null);
                    //disp_class
                    bpolygon.setDispClass(null);
                    // UP_DATE
                    bpolygon.setUpDate(LocalDateTime.now());
                    // STATUS
                    bpolygon.setStatus(0);
                    // DATASOURCE
                    bpolygon.setDatasource("7");

                    bpolygonList.add(bpolygon);
                }
                log.info("bpolygonList size is:" + bpolygonList.size());
                // this.saveOrUpdateBatch(linList);
                List<List<Bpolygon>> bpolygonListPartition = Lists.partition(bpolygonList, BeanUtil.beanToMap(new Bpolygon()).keySet().size());
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }

                for (List<Bpolygon> partitionList : bpolygonListPartition) {
                    // log.info("linkMapper threadlocal info is:" + Thread.currentThread().getName());
                    // linMapper.mysqlInsertOrUpdateBath(partitionList);
                    List<Long> bpolygonIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(Bpolygon::getId).collect(Collectors.toList())));
                    for (int i = 0; i < partitionList.size(); i++) {
                        partitionList.get(i).setId(String.valueOf(bpolygonIds.get(i)));
                    }
                    bploygonMapper.mysqlInsertOrUpdateBath(partitionList);
                    // this.saveOrUpdateBatch(partitionList);
                }
            }
        } catch (Exception e) {
            log.error("bpolygon convert error,msg is {}", e);
            throw new RuntimeException("bpolygon convert error,msg is {}" + e.getMessage());
        } finally {
            countDownLatch.countDown();
        }
    }
    @Async("asyncTaskExecutor")
    public void bpolygonConvertOceans(List<Oceans> oceansList, Boolean isCompileTransEng, String area,
                                      String country, CountDownLatch countDownLatch) {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            log.info("processing country:" + CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        log.info("tmp thread is:" + Thread.currentThread().getName());
        List<Bpolygon> bpolygonList = new ArrayList<>();

        try {
            if (oceansList.size() > 0) {
                for (Oceans oceans : oceansList
                ) {
                    Bpolygon bpolygon = new Bpolygon();
                    //id
                    bpolygon.setId(oceans.getPolygonId().toString());
                    //scource_id
                    bpolygon.setSourceId(oceans.getPolygonId().toString());
                    //kind
                    bpolygon.setKind(oceans.getFeatCod().toString());
                    //name
                    bpolygon.setName(oceans.getPolygonNm());
                    //nm_langcd
                    bpolygon.setNmLangcd(oceans.getNmLangcd());
                    //nm_tr
                    bpolygon.setNmTr(oceans.getPolyNmTr());
                    //trans_type
                    bpolygon.setTransType(oceans.getTransType());
                    //geometry
                    bpolygon.setGeometry(oceans.getGeom());
                    //poi_id
                    bpolygon.setPoiId(null);
                    //disp_class
                    bpolygon.setDispClass(oceans.getDispClass());
                    // UP_DATE
                    bpolygon.setUpDate(LocalDateTime.now());
                    // STATUS
                    bpolygon.setStatus(0);
                    // DATASOURCE
                    bpolygon.setDatasource("7");

                    bpolygonList.add(bpolygon);
                }
                log.info("bpolygonList size is:" + bpolygonList.size());
                // this.saveOrUpdateBatch(linList);
                List<List<Bpolygon>> bpolygonListPartition = Lists.partition(bpolygonList, BeanUtil.beanToMap(new Bpolygon()).keySet().size());
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }

                for (List<Bpolygon> partitionList : bpolygonListPartition) {
                    // log.info("linkMapper threadlocal info is:" + Thread.currentThread().getName());
                    // linMapper.mysqlInsertOrUpdateBath(partitionList);
                    List<Long> bpolygonIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(Bpolygon::getId).collect(Collectors.toList())));
                    for (int i = 0; i < partitionList.size(); i++) {
                        partitionList.get(i).setId(String.valueOf(bpolygonIds.get(i)));
                    }
                    bploygonMapper.mysqlInsertOrUpdateBath(partitionList);
                    // this.saveOrUpdateBatch(partitionList);
                }
            }
        } catch (Exception e) {
            log.error("bpolygon convert error,msg is {}", e);
            throw new RuntimeException("bpolygon convert error,msg is {}" + e.getMessage());
        } finally {
            countDownLatch.countDown();
        }
    }
    @Async("asyncTaskExecutor")
    public void bpolygonConvertIslands(List<Islands> islandsList, Boolean isCompileTransEng, String area,
                                      String country, CountDownLatch countDownLatch) {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            log.info("processing country:" + CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        log.info("tmp thread is:" + Thread.currentThread().getName());
        List<Bpolygon> bpolygonList = new ArrayList<>();

        try {
            if (islandsList.size() > 0) {
                for (Islands islands : islandsList
                ) {
                    Bpolygon bpolygon = new Bpolygon();
                    //id
                    bpolygon.setId(islands.getPolygonId().toString());
                    //scource_id
                    bpolygon.setSourceId(islands.getPolygonId().toString());
                    //kind
                    bpolygon.setKind(islands.getFeatCod().toString());
                    //name
                    bpolygon.setName(islands.getPolygonNm());
                    //nm_langcd
                    bpolygon.setNmLangcd(islands.getNmLangcd());
                    //nm_tr
                    bpolygon.setNmTr(islands.getPolyNmTr());
                    //trans_type
                    bpolygon.setTransType(islands.getTransType());
                    //geometry
                    bpolygon.setGeometry(islands.getGeom());
                    //poi_id
                    bpolygon.setPoiId(null);
                    //disp_class
                    bpolygon.setDispClass(null);
                    // UP_DATE
                    bpolygon.setUpDate(LocalDateTime.now());
                    // STATUS
                    bpolygon.setStatus(0);
                    // DATASOURCE
                    bpolygon.setDatasource("7");

                    bpolygonList.add(bpolygon);
                }
                log.info("bpolygonList size is:" + bpolygonList.size());
                // this.saveOrUpdateBatch(linList);
                List<List<Bpolygon>> bpolygonListPartition = Lists.partition(bpolygonList, BeanUtil.beanToMap(new Bpolygon()).keySet().size());
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }

                for (List<Bpolygon> partitionList : bpolygonListPartition) {
                    // log.info("linkMapper threadlocal info is:" + Thread.currentThread().getName());
                    // linMapper.mysqlInsertOrUpdateBath(partitionList);
                    List<Long> bpolygonIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(Bpolygon::getId).collect(Collectors.toList())));
                    for (int i = 0; i < partitionList.size(); i++) {
                        partitionList.get(i).setId(String.valueOf(bpolygonIds.get(i)));
                    }
                    bploygonMapper.mysqlInsertOrUpdateBath(partitionList);
                    // this.saveOrUpdateBatch(partitionList);
                }
            }
        } catch (Exception e) {
            log.error("bpolygon convert error,msg is {}", e);
            throw new RuntimeException("bpolygon convert error,msg is {}" + e.getMessage());
        } finally {
            countDownLatch.countDown();
        }
    }

}
