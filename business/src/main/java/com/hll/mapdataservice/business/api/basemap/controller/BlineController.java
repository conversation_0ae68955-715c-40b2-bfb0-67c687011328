package com.hll.mapdataservice.business.api.basemap.controller;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.hll.mapdataservice.business.api.basemap.service.*;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.AdminlineEnum;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.utils.CommonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.CountDownLatch;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-02
 */
@RestController
@ResponseBody
@Api(tags = "basemap")
@Component
@Slf4j
@RequestMapping("/api/basemap/bline")
public class BlineController {

    @Resource
    RailrdsServiceImpl railrdsService;
    @Resource
    Adminline1ServiceImpl adminline1Service;
    @Resource
    Adminline2ServiceImpl adminline2Service;
    @Resource
    WatersegServiceImpl watersegService;
    @Resource
    BlineServiceImpl blineService;

    @ApiOperation(value = "here basemap bline convert")
    @PostMapping("/convert")
    public ResponseResult<Boolean> hereBlineConvert(@RequestParam(value = "step",
            required = false,
            defaultValue = "1") int step,
                                                    @RequestParam(value = "version",
                                                            required = false) String version,
                                                    @RequestParam(value = "iscompiletranseng",
                                                            required = false,
                                                            defaultValue = "false") boolean isCompileTransEng,
                                                    @RequestParam(value = "area",
                                                            required = false,
                                                            defaultValue = "") String area,
                                                    @RequestParam(value = "country",
                                                            required = false,
                                                            defaultValue = "") String country)
            throws InterruptedException, SQLException {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        //railrds
        TimeInterval timer = DateUtil.timer();

        Integer listSize = railrdsService.count();
        CountDownLatch countDownLatch = new CountDownLatch(listSize / step + 1);
        log.info("The railrds records to be transfered:" + listSize);
        for (int i = 0; i <= listSize / step; i++) {
//            if (i == 0) {
//                step = 100;
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<Railrds> railrdsList = railrdsService.lambdaQuery()
                    .orderByDesc(Railrds::getLinkId).last("limit " + step + " offset " + i * step).list();
            log.info("process start limit " + step + " offset " + i * step);
            if (railrdsList.size() > 0) {
                // linkSw2021q133Service.linkConvert(streetsListi,nodeIdSet,nodeList);
                blineService.blineConvertRailrds(railrdsList,
                        isCompileTransEng, area, country, countDownLatch);
            }
//            }
        }
//        countDownLatch.await();
        log.info("here railrds convert to herebline cost time is {}s", timer.intervalSecond());

        //adminline1
        timer = DateUtil.timer();

        listSize = adminline1Service.count();
        countDownLatch = new CountDownLatch(listSize / step + 1);
        log.info("The adminline1 records to be transfered:" + listSize);
        for (int i = 0; i <= listSize / step; i++) {
//            if (i == 0) {
//                step = 100;
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<Adminline1> adminline1List = adminline1Service.lambdaQuery()
                    .orderByDesc(Adminline1::getLinkId).last("limit " + step + " offset " + i * step).list();
            log.info("process start limit " + step + " offset " + i * step);
            if (adminline1List.size() > 0) {
                // linkSw2021q133Service.linkConvert(streetsListi,nodeIdSet,nodeList);
                blineService.blineConvertAdminline1(adminline1List,
                        isCompileTransEng, area, country, countDownLatch);
            }
//            }
        }
//        countDownLatch.await();
        log.info("here adminline1 convert to herebline cost time is {}s", timer.intervalSecond());

        //adminline2
        if (AdminlineEnum.getAdminlineByAreaName(country) == 2) {


            timer = DateUtil.timer();
            listSize = adminline2Service.count();
            countDownLatch = new CountDownLatch(listSize / step + 1);
            log.info("The adminline2 records to be transfered:" + listSize);
            for (int i = 0; i <= listSize / step; i++) {
//            if (i == 0) {
//                step = 100;
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
                List<Adminline2> adminline2List = adminline2Service.lambdaQuery()
                        .orderByDesc(Adminline2::getLinkId).last("limit " + step + " offset " + i * step).list();
                log.info("process start limit " + step + " offset " + i * step);
                if (adminline2List.size() > 0) {
                    // linkSw2021q133Service.linkConvert(streetsListi,nodeIdSet,nodeList);
                    blineService.blineConvertAdminline2(adminline2List,
                            isCompileTransEng, area, country, countDownLatch);
                }
//            }
            }
//        countDownLatch.await();
            log.info("here adminlin2 convert to herebline cost time is {}s", timer.intervalSecond());
        }
        //waterseg
        timer = DateUtil.timer();
        listSize = watersegService.count();
        countDownLatch = new CountDownLatch(listSize / step + 1);
        log.info("The waterseg records to be transfered:" + listSize);
        for (int i = 0; i <= listSize / step; i++) {
//            if (i == 0) {
//                step = 100;
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<Waterseg> watersegList = watersegService.lambdaQuery()
                    .orderByDesc(Waterseg::getLinkId).last("limit " + step + " offset " + i * step).list();
            log.info("process start limit " + step + " offset " + i * step);
            if (watersegList.size() > 0) {
                // linkSw2021q133Service.linkConvert(streetsListi,nodeIdSet,nodeList);
                blineService.blineConvertWaterseg(watersegList,
                        isCompileTransEng, area, country, countDownLatch);
            }
//            }
        }
        countDownLatch.await();
        log.info("here waterseg convert to herebline cost time is {}s", timer.intervalSecond());

        return ResponseResult.OK(true, true);
    }

}
