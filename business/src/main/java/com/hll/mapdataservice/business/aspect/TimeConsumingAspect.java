package com.hll.mapdataservice.business.aspect;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Aspect
@Component
public class TimeConsumingAspect {
    private static final Logger logger = LoggerFactory.getLogger(TimeConsumingAspect.class);

    @Pointcut("execution(* com.hll.mapdataservice.business.api..controller.*.*(..))")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        String requestId = UUID.randomUUID().toString();
        MDC.put("requestId", requestId);

        long startTime = System.currentTimeMillis();

        // HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        //
        // String ip = request.getHeader("X-Forwarded-For");
        // if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
        //     ip = request.getRemoteAddr();
        // }

        // 获取方法参数值
        Object[] args = joinPoint.getArgs();
        // 获取方法参数名称
        String[] argNames = ((MethodSignature) joinPoint.getSignature()).getParameterNames();
        // 获取方法信息
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();

        logger.info("Request ID: {}, Start time: {}, Method: {}", requestId, DateUtil.now(), method.getName());
        // logger.info("Request ID: {}, Start time: {}, Method: {}, Parameters: {}", requestId, DateUtil.now(), method.getName(), getParameters(argNames, args));

        Object result = joinPoint.proceed();

        long timeConsuming = System.currentTimeMillis() - startTime;

        // logger.info("Request ID: {}, End time: {}, Caller IP: {}, Method: {}, Execution time: {} ms", requestId,DateUtil.now(), ip, joinPoint.getSignature().toShortString(), timeConsuming);
        logger.info("Request ID: {}, End time: {},  Method: {}, Execution time: {} ms", requestId, DateUtil.now(), joinPoint.getSignature().toShortString(), timeConsuming);

        MDC.remove("requestId");

        return result;
    }

    // private String getParameters(String[] argNames, Object[] args) {
    //     Map<String, Object> paramMap = new HashMap<>();
    //     if (args != null && argNames != null) {
    //         for (int i = 0; i < args.length; i++) {
    //             paramMap.put(argNames[i], args[i]);
    //         }
    //     }
    //     return paramMap.toString();
    // }

    private String getParameters(String[] argNames, Object[] args) {
        ObjectMapper mapper = new ObjectMapper();
        Map<String, String> paramMap = new HashMap<>();
        if (args != null && argNames != null) {
            for (int i = 0; i < args.length; i++) {
                try {
                    paramMap.put(argNames[i], mapper.writeValueAsString(args[i]));
                } catch (JsonProcessingException e) {
                    logger.error("Failed to convert object to JSON string", e);
                    paramMap.put(argNames[i], args[i].toString());
                }
            }
        }
        return paramMap.toString();
    }
}