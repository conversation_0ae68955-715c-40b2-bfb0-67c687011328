package com.hll.mapdataservice.business.api.basemap.controller;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.hll.mapdataservice.business.api.basemap.service.*;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.utils.CommonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-02
 */
@RestController
@ResponseBody
@Api(tags = "basemap")
@Component
@Slf4j
@RequestMapping("/api/basemap/bpolygon")
public class BpolygonController {

    @Resource
    WaterpolyServiceImpl waterpolyService;
    @Resource
    LanduseaServiceImpl landuseaService;
    @Resource
    LandusebServiceImpl landusebService;
    @Resource
    OceansServiceImpl oceansService;
    @Resource
    IslandsServiceImpl islandsService;
    @Resource
    BpolygonServiceImpl bpolygonService;
    @ApiOperation(value = "here basemap bpolygon convert")
    @PostMapping("/convert")
    public ResponseResult<Boolean> hereBpolygonConvert(@RequestParam(value = "step",
            required = false,
            defaultValue = "1") int step,
                                                    @RequestParam(value = "version",
                                                            required = false) String version,
                                                    @RequestParam(value = "iscompiletranseng",
                                                            required = false,
                                                            defaultValue = "false") boolean isCompileTransEng,
                                                    @RequestParam(value = "area",
                                                            required = false,
                                                            defaultValue = "") String area,
                                                    @RequestParam(value = "country",
                                                            required = false,
                                                            defaultValue = "") String country)
            throws InterruptedException, SQLException {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        //waterpoly
        TimeInterval timer = DateUtil.timer();

        Integer listSize = waterpolyService.count();
        CountDownLatch countDownLatch = new CountDownLatch(listSize / step + 1);
        log.info("The waterpoly records to be transfered:" + listSize);
        for (int i = 0; i <= listSize / step; i++) {
//            if (i == 0) {
//                step = 100;
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<Waterpoly> waterpolyList = waterpolyService.lambdaQuery()
                    .orderByDesc(Waterpoly::getPolygonId).last("limit " + step + " offset " + i * step).list();
            log.info("process start limit " + step + " offset " + i * step);
            if (waterpolyList.size() > 0) {
                // linkSw2021q133Service.linkConvert(streetsListi,nodeIdSet,nodeList);
                bpolygonService.bpolygonConvertWaterpoly(waterpolyList,
                        isCompileTransEng, area, country, countDownLatch);
            }
//            }
        }
//        countDownLatch.await();
        log.info("here waterpoly convert to herebpolygon cost time is {}s", timer.intervalSecond());

        //landusea
        timer = DateUtil.timer();

        listSize = landuseaService.count();
        countDownLatch = new CountDownLatch(listSize / step + 1);
        log.info("The landusea records to be transfered:" + listSize);
        for (int i = 0; i <= listSize / step; i++) {
//            if (i == 0) {
//                step = 100;
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<Landusea> landuseaList = landuseaService.lambdaQuery()
                    .orderByDesc(Landusea::getPolygonId).last("limit " + step + " offset " + i * step).list();
            log.info("process start limit " + step + " offset " + i * step);
            if (landuseaList.size() > 0) {
                // linkSw2021q133Service.linkConvert(streetsListi,nodeIdSet,nodeList);
                bpolygonService.bpolygonConvertLandusea(landuseaList,
                        isCompileTransEng, area, country, countDownLatch);
            }
//            }
        }
//        countDownLatch.await();
        log.info("here landusea convert to herebpolygon cost time is {}s", timer.intervalSecond());

        //landuseb
        timer = DateUtil.timer();
        listSize = landusebService.count();
        countDownLatch = new CountDownLatch(listSize / step + 1);
        log.info("The landuseb records to be transfered:" + listSize);
        for (int i = 0; i <= listSize / step; i++) {
//            if (i == 0) {
//                step = 100;
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<Landuseb> landusebList = landusebService.lambdaQuery()
                    .orderByDesc(Landuseb::getPolygonId).last("limit " + step + " offset " + i * step).list();
            log.info("process start limit " + step + " offset " + i * step);
            if (landusebList.size() > 0) {
                // linkSw2021q133Service.linkConvert(streetsListi,nodeIdSet,nodeList);
                bpolygonService.bpolygonConvertLanduseb(landusebList,
                        isCompileTransEng, area, country, countDownLatch);
            }
//            }
        }
//        countDownLatch.await();
        log.info("here landuseb convert to herebpolygon cost time is {}s", timer.intervalSecond());

        //oceans
        timer = DateUtil.timer();
        listSize = oceansService.count();
        countDownLatch = new CountDownLatch(listSize / step + 1);
        log.info("The oceans records to be transfered:" + listSize);
        for (int i = 0; i <= listSize / step; i++) {
//            if (i == 0) {
//                step = 100;
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<Oceans> oceansList = oceansService.lambdaQuery()
                    .orderByDesc(Oceans::getPolygonId).last("limit " + step + " offset " + i * step).list();
            log.info("process start limit " + step + " offset " + i * step);
            if (oceansList.size() > 0) {
                // linkSw2021q133Service.linkConvert(streetsListi,nodeIdSet,nodeList);
                bpolygonService.bpolygonConvertOceans(oceansList,
                        isCompileTransEng, area, country, countDownLatch);
            }
//            }
        }
        log.info("here oceans convert to herebpolygon cost time is {}s", timer.intervalSecond());

        //islands
        timer = DateUtil.timer();
        listSize = islandsService.count();
        countDownLatch = new CountDownLatch(listSize / step + 1);
        log.info("The islands records to be transfered:" + listSize);
        for (int i = 0; i <= listSize / step; i++) {
//            if (i == 0) {
//                step = 100;
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<Islands> islandsList = islandsService.lambdaQuery()
                    .orderByDesc(Islands::getPolygonId).last("limit " + step + " offset " + i * step).list();
            log.info("process start limit " + step + " offset " + i * step);
            if (islandsList.size() > 0) {
                // linkSw2021q133Service.linkConvert(streetsListi,nodeIdSet,nodeList);
                bpolygonService.bpolygonConvertIslands(islandsList,
                        isCompileTransEng, area, country, countDownLatch);
            }
//            }
        }
        log.info("here islands convert to herebpolygon cost time is {}s", timer.intervalSecond());

        countDownLatch.await();
        //log.info("here waterseg convert to herebpolygon cost time is {}s", timer.intervalSecond());

        return ResponseResult.OK(true, true);
    }

}
