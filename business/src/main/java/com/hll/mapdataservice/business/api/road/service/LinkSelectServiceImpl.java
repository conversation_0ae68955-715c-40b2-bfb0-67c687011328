package com.hll.mapdataservice.business.api.road.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hll.mapdataservice.common.entity.LinkSelect;
import com.hll.mapdataservice.common.mapper.LinkSelectMapper;
import com.hll.mapdataservice.common.service.ILinkSelectService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-26
 */
@Service
//@DS("db2")
public class LinkSelectServiceImpl extends ServiceImpl<LinkSelectMapper, LinkSelect> implements ILinkSelectService {

}
