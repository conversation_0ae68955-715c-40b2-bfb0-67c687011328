package com.hll.mapdataservice.business;

import com.hll.mapdataservice.business.sqlInjector.CustomizedSqlInjector;
import com.zaxxer.hikari.HikariDataSource;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.EnableAsync;
import org.yaml.snakeyaml.Yaml;

import javax.sql.DataSource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.util.Map;

@SpringBootApplication
@EnableFeignClients(basePackages = "com.hll.mapdataservice.business.third")
@MapperScan("com.hll.mapdataservice.common.mapper,com.hll.mapdataservice.business.mapper")
// @ComponentScan("com.hll.mapdataservice.business.common")
@EnableAsync
@EnableAspectJAutoProxy
public class MapdataserviceApplication {
    public static void main(String[] args) {
        //updateYaml();
        ConfigurableApplicationContext run = SpringApplication.run(MapdataserviceApplication.class, args);
        // DataSource datasource = run.getBean(DataSource.class);
        // if (datasource instanceof HikariDataSource) {
        //     HikariDataSource hikariDs = (HikariDataSource) datasource;
        //     System.out.println("HikariCP连接池配置:" + hikariDs.getJdbcUrl());
        //     System.out.println("HikariCP连接池配置:" + hikariDs.getUsername());
        //     System.out.println("HikariCP连接池配置:" + hikariDs.getPassword());
        //     System.out.println("HikariCP连接池配置:" + hikariDs.getDriverClassName());
        //     System.out.println("HikariCP连接池配置:" + hikariDs.getMaximumPoolSize());
        //     System.out.println("HikariCP连接池配置:" + hikariDs.getMinimumIdle());
        //     System.out.println("HikariCP连接池配置:" + hikariDs.getConnectionTimeout());
        // }
    }
    @Bean
    public CommandLineRunner commandLineRunner(DataSource dataSource) {
        return args -> {
            if (dataSource instanceof HikariDataSource) {
                HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
                System.out.println("HikariCP Maximum Pool Size: " + hikariDataSource.getMaximumPoolSize());
                System.out.println("HikariCP Connection Timeout: " + hikariDataSource.getConnectionTimeout());
                // 你可以添加更多的配置项来检查
            }
        };
    }

    /**
     * 自定义sql注入器
     *
     * @return
     */
    @Bean
    public CustomizedSqlInjector customizedSqlInjector() {
        return new CustomizedSqlInjector();
    }

//    @Bean(name = "proFileEntity")
//    @Profile("prd-phl")
//    public ProfileEntity proFileEntity1(){
//        ProfileEntity proFileEntity = new ProfileEntity();
//        proFileEntity.setName("");
//        return proFileEntity;
//    }
//
//
//    @Bean(name = "proFileEntity")
//    @Profile("prd-tha-area1")
//    public ProfileEntity proFileEntity(){
//        ProfileEntity proFileEntity = new ProfileEntity();
//        proFileEntity.setName("_area1");
//        return proFileEntity;
//    }
//
//    @Bean(name = "proFileEntity")
//    @Profile("test")
//    public ProfileEntity proFileEntity2(){
//        ProfileEntity proFileEntity = new ProfileEntity();
//        proFileEntity.setName("");
//        return proFileEntity;
//    }
//
//    @Bean(name = "proFileEntity")
//    @Profile("local")
//    public ProfileEntity proFileEntity3(){
//        ProfileEntity proFileEntity = new ProfileEntity();
//        proFileEntity.setName("");
//        return proFileEntity;
//    }

    public static void updateYaml() {
        String sourcePath = MapdataserviceApplication.class.getClassLoader().getResource("application.yml").getPath();
        Yaml yaml = new Yaml();
        FileWriter fileWriter;
        try {
            FileInputStream fileInputStream = new FileInputStream(sourcePath);
            Map<String, Object> yamlMap = yaml.load(fileInputStream);
            Map<String, Object> loggingMap = (Map<String, Object>) yamlMap.get("logging");
            Map<String, Object> fileMap = (Map<String, Object>) loggingMap.get("file");
            // dynamically get the project path of server to joint log complete path.
            fileMap.put("name", new File("").getCanonicalPath()+"/logs");

            fileWriter = new FileWriter(sourcePath);
            fileWriter.write(yaml.dumpAsMap(yamlMap));
            fileWriter.flush();
            fileWriter.close();
            fileInputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
