package com.hll.mapdataservice.business.config;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;


@Configuration
public class MapTTConfig {

    private List<String> aerialwayValues = new ArrayList<>();
    private List<String> aerowayValues = new ArrayList<>();
    private List<String> amenityValues = new ArrayList<>();
    private List<String> barrierValues = new ArrayList<>();
    private List<String> boundaryValues = new ArrayList<>();;
    private List<String> clubValues = new ArrayList<>();;
    private List<String> craftValues = new ArrayList<>();;
    private List<String> emeAmenityValues = new ArrayList<>();;
    private List<String> emergencyValues = new ArrayList<>();;
    private List<String> geologicalValues = new ArrayList<>();;
    private List<String> healthcareValues = new ArrayList<>();;
    private List<String> highwayValues = new ArrayList<>();;
    private List<String> historicValues = new ArrayList<>();;
    private List<String> landuseValues = new ArrayList<>();;
    private List<String> leisureValues = new ArrayList<>();;
    private List<String> manMadeValues = new ArrayList<>();;
    private List<String> militaryValues = new ArrayList<>();;
    private List<String> mountainPassValues = new ArrayList<>();;
    private List<String> naturalValues = new ArrayList<>();;
    private List<String> officeValues = new ArrayList<>();;
    private List<String> placeValues = new ArrayList<>();;
    private List<String> publicTransportValues = new ArrayList<>();;
    private List<String> railwayValues = new ArrayList<>();;
    private List<String> shopValues = new ArrayList<>();;
    private List<String> tourismValues = new ArrayList<>();;
    private List<String> transportValues = new ArrayList<>();;
    private List<String> utilityValues = new ArrayList<>();;
    private List<String> roadValues = new ArrayList<>();

    @Bean
    public void readMappingJsonExecutor() throws IOException
    {
        try {

            // 初始化 ObjectMapper
            ObjectMapper objectMapper = new ObjectMapper();

            InputStream inputStream = getClass().getClassLoader().getResourceAsStream("config/mapping-tt.json");

            // 读取 JSON 文件为 JsonNode
            JsonNode rootNode = objectMapper.readTree(inputStream);

            // 获取 aerialway 数组
            JsonNode aerialwayNode = rootNode.get("aerialway");

            // 将 aerialway 数组转换为 List<String>
            if (aerialwayNode.isArray()) {
                for (JsonNode value : aerialwayNode) {
                    aerialwayValues.add(value.asText());
                }
            }

            // 获取 aeroway 数组
            JsonNode aerowayNode = rootNode.get("aeroway");

            // 将 aeroway 数组转换为 List<String>
            if (aerowayNode.isArray()) {
                for (JsonNode value : aerowayNode) {
                    aerowayValues.add(value.asText());
                }
            }

            // 获取 amenity 数组
            JsonNode amenityNode = rootNode.get("amenity");

            // 将 amenity 数组转换为 List<String>
            if (amenityNode.isArray()) {
                for (JsonNode value : amenityNode) {
                    amenityValues.add(value.asText());
                }
            }

            // 获取 barrier 数组
            JsonNode barrierNode = rootNode.get("barrier");

            // 将 barrier 数组转换为 List<String>
            if (barrierNode.isArray()) {
                for (JsonNode value : barrierNode) {
                    barrierValues.add(value.asText());
                }
            }

            // 获取 boundary 数组
            JsonNode boundaryNode = rootNode.get("boundary");

            // 将 boundary 数组转换为 List<String>
            if (boundaryNode.isArray()) {
                for (JsonNode value : boundaryNode) {
                    boundaryValues.add(value.asText());
                }
            }

            // 获取 club 数组
            JsonNode clubNode = rootNode.get("club");

            // 将 club 数组转换为 List<String>
            if (clubNode.isArray()) {
                for (JsonNode value : clubNode) {
                    clubValues.add(value.asText());
                }
            }

            // 获取 craft 数组
            JsonNode craftNode = rootNode.get("craft");

            // 将 craft 数组转换为 List<String>
            if (craftNode.isArray()) {
                for (JsonNode value : craftNode) {
                    craftValues.add(value.asText());
                }
            }

            // 获取 emeAmenity 数组
            JsonNode emeAmenityNode = rootNode.get("emergency:amenity");

            // 将 emeAmenity 数组转换为 List<String>
            if (emeAmenityNode.isArray()) {
                for (JsonNode value : emeAmenityNode) {
                    emeAmenityValues.add(value.asText());
                }
            }

            // 获取 emergency 数组
            JsonNode emergencyNode = rootNode.get("emergency");

            // 将 emergency 数组转换为 List<String>
            if (emergencyNode.isArray()) {
                for (JsonNode value : emergencyNode) {
                    emergencyValues.add(value.asText());
                }
            }

            // 获取 geological 数组
            JsonNode geologicalNode = rootNode.get("geological");

            // 将 geological 数组转换为 List<String>
            if (geologicalNode.isArray()) {
                for (JsonNode value : geologicalNode) {
                    geologicalValues.add(value.asText());
                }
            }

            // 获取 healthcare 数组
            JsonNode healthcareNode = rootNode.get("healthcare");

            // 将 healthcare 数组转换为 List<String>
            if (healthcareNode.isArray()) {
                for (JsonNode value : healthcareNode) {
                    healthcareValues.add(value.asText());
                }
            }

            // 获取 highway 数组
            JsonNode highwayNode = rootNode.get("highway");

            // 将 highway 数组转换为 List<String>
            if (highwayNode.isArray()) {
                for (JsonNode value : highwayNode) {
                    highwayValues.add(value.asText());
                }
            }

            // 获取 historic 数组
            JsonNode historicNode = rootNode.get("historic");

            // 将 historic 数组转换为 List<String>
            if (historicNode.isArray()) {
                for (JsonNode value : historicNode) {
                    historicValues.add(value.asText());
                }
            }

            // 获取 landuse 数组
            JsonNode landuseNode = rootNode.get("landuse");

            // 将 landuse 数组转换为 List<String>
            if (landuseNode.isArray()) {
                for (JsonNode value : landuseNode) {
                    landuseValues.add(value.asText());
                }
            }

            // 获取 leisure 数组
            JsonNode leisureNode = rootNode.get("leisure");

            // 将 leisure 数组转换为 List<String>
            if (leisureNode.isArray()) {
                for (JsonNode value : leisureNode) {
                    leisureValues.add(value.asText());
                }
            }

            // 获取 manMade 数组
            JsonNode manMadeNode = rootNode.get("man_made");

            // 将 manMade 数组转换为 List<String>
            if (manMadeNode.isArray()) {
                for (JsonNode value : manMadeNode) {
                    manMadeValues.add(value.asText());
                }
            }

            // 获取 military 数组
            JsonNode militaryNode = rootNode.get("military");

            // 将 military 数组转换为 List<String>
            if (militaryNode.isArray()) {
                for (JsonNode value : militaryNode) {
                    militaryValues.add(value.asText());
                }
            }

            // 获取 mountainPass 数组
            JsonNode mountainPassNode = rootNode.get("mountain_pass");

            // 将 mountainPass 数组转换为 List<String>
            if (mountainPassNode.isArray()) {
                for (JsonNode value : mountainPassNode) {
                    mountainPassValues.add(value.asText());
                }
            }

            // 获取 natural 数组
            JsonNode naturalNode = rootNode.get("natural");

            // 将 natural 数组转换为 List<String>
            if (naturalNode.isArray()) {
                for (JsonNode value : naturalNode) {
                    naturalValues.add(value.asText());
                }
            }

            // 获取 office 数组
            JsonNode officeNode = rootNode.get("office");

            // 将 office 数组转换为 List<String>
            if (officeNode.isArray()) {
                for (JsonNode value : officeNode) {
                    officeValues.add(value.asText());
                }
            }

            // 获取 place 数组
            JsonNode placeNode = rootNode.get("place");

            // 将 place 数组转换为 List<String>
            if (placeNode.isArray()) {
                for (JsonNode value : placeNode) {
                    placeValues.add(value.asText());
                }
            }

            // 获取 publicTransport 数组
            JsonNode publicTransportNode = rootNode.get("public_transport");

            // 将 publicTransport 数组转换为 List<String>
            if (publicTransportNode.isArray()) {
                for (JsonNode value : publicTransportNode) {
                    publicTransportValues.add(value.asText());
                }
            }

            // 获取 railway 数组
            JsonNode railwayNode = rootNode.get("railway");

            // 将 railway 数组转换为 List<String>
            if (railwayNode.isArray()) {
                for (JsonNode value : railwayNode) {
                    railwayValues.add(value.asText());
                }
            }

            // 获取 shop 数组
            JsonNode shopNode = rootNode.get("shop");

            // 将 shop 数组转换为 List<String>
            if (shopNode.isArray()) {
                for (JsonNode value : shopNode) {
                    shopValues.add(value.asText());
                }
            }

            // 获取 tourism 数组
            JsonNode tourismNode = rootNode.get("tourism");

            // 将 tourism 数组转换为 List<String>
            if (tourismNode.isArray()) {
                for (JsonNode value : tourismNode) {
                    tourismValues.add(value.asText());
                }
            }

            // 获取 transport 数组
            JsonNode transportNode = rootNode.get("transport");

            // 将 transport 数组转换为 List<String>
            if (transportNode.isArray()) {
                for (JsonNode value : transportNode) {
                    transportValues.add(value.asText());
                }
            }

            // 获取 utility 数组
            JsonNode utilityNode = rootNode.get("utility");

            // 将 utility 数组转换为 List<String>
            if (utilityNode.isArray()) {
                for (JsonNode value : utilityNode) {
                    utilityValues.add(value.asText());
                }
            }

            // 获取 utility 数组
            JsonNode roadNode = rootNode.get("road");
            // 将 roads 数组转换为 List<String>
            if (roadNode.isArray()) {
                for (JsonNode value : roadNode) {
                    roadValues.add(value.asText());
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public List<String> getAerialwayValues() {
        return aerialwayValues;
    }

    public void setAerialwayValues(List<String> aerialwayValues) {
        this.aerialwayValues = aerialwayValues;
    }

    public List<String> getAerowayValues() {
        return aerowayValues;
    }

    public void setAerowayValues(List<String> aerowayValues) {
        this.aerowayValues = aerowayValues;
    }

    public List<String> getAmenityValues() {
        return amenityValues;
    }

    public void setAmenityValues(List<String> amenityValues) {
        this.amenityValues = amenityValues;
    }

    public List<String> getBarrierValues() {
        return barrierValues;
    }

    public void setBarrierValues(List<String> barrierValues) {
        this.barrierValues = barrierValues;
    }

    public List<String> getBoundaryValues() {
        return boundaryValues;
    }

    public void setBoundaryValues(List<String> boundaryValues) {
        this.boundaryValues = boundaryValues;
    }

    public List<String> getClubValues() {
        return clubValues;
    }

    public void setClubValues(List<String> clubValues) {
        this.clubValues = clubValues;
    }

    public List<String> getCraftValues() {
        return craftValues;
    }

    public void setCraftValues(List<String> craftValues) {
        this.craftValues = craftValues;
    }

    public List<String> getEmeAmenityValues() {
        return emeAmenityValues;
    }

    public void setEmeAmenityValues(List<String> emeAmenityValues) {
        this.emeAmenityValues = emeAmenityValues;
    }

    public List<String> getEmergencyValues() {
        return emergencyValues;
    }

    public void setEmergencyValues(List<String> emergencyValues) {
        this.emergencyValues = emergencyValues;
    }

    public List<String> getGeologicalValues() {
        return geologicalValues;
    }

    public void setGeologicalValues(List<String> geologicalValues) {
        this.geologicalValues = geologicalValues;
    }

    public List<String> getHealthcareValues() {
        return healthcareValues;
    }

    public void setHealthcareValues(List<String> healthcareValues) {
        this.healthcareValues = healthcareValues;
    }

    public List<String> getHighwayValues() {
        return highwayValues;
    }

    public void setHighwayValues(List<String> highwayValues) {
        this.highwayValues = highwayValues;
    }

    public List<String> getHistoricValues() {
        return historicValues;
    }

    public void setHistoricValues(List<String> historicValues) {
        this.historicValues = historicValues;
    }

    public List<String> getLanduseValues() {
        return landuseValues;
    }

    public void setLanduseValues(List<String> landuseValues) {
        this.landuseValues = landuseValues;
    }

    public List<String> getLeisureValues() {
        return leisureValues;
    }

    public void setLeisureValues(List<String> leisureValues) {
        this.leisureValues = leisureValues;
    }

    public List<String> getManMadeValues() {
        return manMadeValues;
    }

    public void setManMadeValues(List<String> manMadeValues) {
        this.manMadeValues = manMadeValues;
    }

    public List<String> getMilitaryValues() {
        return militaryValues;
    }

    public void setMilitaryValues(List<String> militaryValues) {
        this.militaryValues = militaryValues;
    }

    public List<String> getMountainPassValues() {
        return mountainPassValues;
    }

    public void setMountainPassValues(List<String> mountainPassValues) {
        this.mountainPassValues = mountainPassValues;
    }

    public List<String> getNaturalValues() {
        return naturalValues;
    }

    public void setNaturalValues(List<String> naturalValues) {
        this.naturalValues = naturalValues;
    }

    public List<String> getOfficeValues() {
        return officeValues;
    }

    public void setOfficeValues(List<String> officeValues) {
        this.officeValues = officeValues;
    }

    public List<String> getPlaceValues() {
        return placeValues;
    }

    public void setPlaceValues(List<String> placeValues) {
        this.placeValues = placeValues;
    }

    public List<String> getPublicTransportValues() {
        return publicTransportValues;
    }

    public void setPublicTransportValues(List<String> publicTransportValues) {
        this.publicTransportValues = publicTransportValues;
    }

    public List<String> getRailwayValues() {
        return railwayValues;
    }

    public void setRailwayValues(List<String> railwayValues) {
        this.railwayValues = railwayValues;
    }

    public List<String> getShopValues() {
        return shopValues;
    }

    public void setShopValues(List<String> shopValues) {
        this.shopValues = shopValues;
    }

    public List<String> getTourismValues() {
        return tourismValues;
    }

    public void setTourismValues(List<String> tourismValues) {
        this.tourismValues = tourismValues;
    }

    public List<String> getTransportValues() {
        return transportValues;
    }

    public void setTransportValues(List<String> transportValues) {
        this.transportValues = transportValues;
    }

    public List<String> getUtilityValues() {
        return utilityValues;
    }

    public void setUtilityValues(List<String> utilityValues) {
        this.utilityValues = utilityValues;
    }
    public List<String> getRoadValues() {
        return roadValues;
    }

    public void setRoadValues(List<String> roadValues) {
        this.roadValues = roadValues;
    }

}
