package com.hll.mapdataservice.business.third;

import com.hll.mapdataservice.business.config.FeignConfig;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@FeignClient(name = "googlenearbypoisearch", configuration = FeignConfig.class, url = "${third.googleNearByPoiSearch.url}")
public interface GoogleApiService {
    /**
     * http://192.168.106.199:8080/track/flow
     */
    @GetMapping(value = "/maps/api/place/nearbysearch/json")
//    Response getMatchResult(@RequestParam("location") String location,@RequestParam("radius") String radius,
//                            @RequestParam("key") String key);
    String getMatchResult(@RequestParam Map<String, Object> map);
}
