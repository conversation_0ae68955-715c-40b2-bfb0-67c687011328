package com.hll.mapdataservice.business.third;

import com.hll.mapdataservice.business.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "databasebackupservice", configuration = FeignConfig.class, url = "${third.databasebackupservice.url}")
public interface DatabaseBackupService {
    /**
     * http://***************:8080/track/flow
     */
    @GetMapping(value = "/common/dbbackup")
    String dbbackup(@RequestParam("dbName") String dbName,
                    @RequestParam("userName") String userName,
                    @RequestParam("host") String host,
                    @RequestParam("port") String port,
                    @RequestParam("backupPath") String backupPath,
                    @RequestParam("dbPath") String dbPath);
    @GetMapping(value = "/common/minioupload")
    String minioUpload(@RequestParam("endPoint") String endPoint,
                       @RequestParam("accessKey") String accessKey,
                       @RequestParam("secretKey") String secretKey,
                       @RequestParam("bucketName") String bucketName,
                       @RequestParam("objectName") String objectName,
                       @RequestParam("filePath") String filePath);
}
