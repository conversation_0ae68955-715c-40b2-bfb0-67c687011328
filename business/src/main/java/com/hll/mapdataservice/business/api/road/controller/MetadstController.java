package com.hll.mapdataservice.business.api.road.controller;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.hll.mapdataservice.business.api.road.service.LinkMServiceImpl;
import com.hll.mapdataservice.business.api.road.service.LinkServiceImpl;
import com.hll.mapdataservice.business.api.road.service.MtddstServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.LinkM;
import com.hll.mapdataservice.common.entity.Mtddst;
import com.hll.mapdataservice.common.service.IMetadstService;
import com.hll.mapdataservice.common.service.IMtddstService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @Date 2024/3/19
 */
@Slf4j
@RestController
@RequestMapping("/metadst")
public class MetadstController {

    @Resource
    private IMetadstService metadstService;
    @Resource
    private MtddstServiceImpl mtddstService;
    @Resource
    private LinkMServiceImpl linkMService;

    @GetMapping("/convert")
    public ResponseResult<String> convert(String country, String area, int step) {

        // 1. 获取here源，mtddst数据
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        Map<String, Mtddst> srcMap = new HashMap<>();
        mtddstService.lambdaQuery().isNotNull(Mtddst::getTimeZone).list().forEach(mtddst -> {
            srcMap.put(mtddst.getAreaId().toString(), mtddst);
        });

        // 2. 转换数据
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        // 2.1 获取要转换的数量
        // linkService.getNeededConvertCount()
        Integer convertNum = linkMService.lambdaQuery().select(LinkM::getLAdmin).groupBy(LinkM::getLAdmin).list().size();
        log.info("convertNum:{}", convertNum);
        // int loop = convertNum % step != 0 ? (convertNum / step) + 1 : convertNum / step;
        // CountDownLatch countDownLatch = new CountDownLatch(loop);
        // for (int i = 0; i < loop; i++) {
        // }

        List<LinkM> linkMList = linkMService.lambdaQuery().select(LinkM::getLAdmin, LinkM::getTAdmin).groupBy(LinkM::getLAdmin,LinkM::getTAdmin).list();
        Integer convert = metadstService.convert(country, area, srcMap, linkMList, step);
        if (convertNum!=convert) {
            return ResponseResult.OK("mtddst convert success,country is "+country+" area is "+area,true);
        }
        return ResponseResult.OK("mtddst convert success,country is "+country+" area is "+area,true);
    }
}
