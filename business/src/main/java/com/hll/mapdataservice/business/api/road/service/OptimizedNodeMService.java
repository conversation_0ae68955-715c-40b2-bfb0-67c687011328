package com.hll.mapdataservice.business.api.road.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.third.InheritIDService;
import com.hll.mapdataservice.business.third.dto.InheritIDDTO;
import com.hll.mapdataservice.common.entity.NodeM;
import com.hll.mapdataservice.common.mapper.NodeMMapper;
import com.hll.mapdataservice.common.service.INodeMService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.CountDownLatch;

/**
 * Optimized NodeM service implementation with better memory management
 * and performance optimizations
 * 
 * <AUTHOR> Version
 * @date 2024
 */
@Slf4j
@Service
public class OptimizedNodeMService extends ServiceImpl<NodeMMapper, NodeM> implements INodeMService {

    @Resource
    private InheritIDService inheritIDService;

    @Resource
    private NodeMMapper nodeMMapper;

    /**
     * Original handleId method for backward compatibility
     */
    @Override
    @Async("asyncTaskExecutor")
    public void handleId(String area, String country, CountDownLatch countDownLatch, List<NodeM> nodeList) {
        try {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            int fieldNum = BeanUtil.beanToMap(new NodeM()).keySet().size();
            int batchSize = 32767 / fieldNum;
            log.info("batchInsert size is {}", batchSize);
            List<List<NodeM>> splitList = ListUtil.split(nodeList, batchSize);
            for (List<NodeM> nodes : splitList) {
                List<NodeM> resNodeList = new ArrayList<>();
                for (NodeM node : nodes) {
                    if (node.getMainnodeid().length()==18) {
                        continue;
                    }
                    // 处理 mainnodeid
                    if (StrUtil.isNotEmpty(node.getMainnodeid())&&node.getMainnodeid().length() < 18) {
                        node.setMainnodeid(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(node.getMainnodeid()))).get(0)));
                    } else {
                        node.setMainnodeid(node.getMainnodeid());
                    }
                    // 处理 subnodeid
                    if (StrUtil.isNotEmpty(node.getSubnodeid())) {
                        if (node.getSubnodeid().contains("|")) {
                            List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(node.getSubnodeid().split("\\|"))));
                            String resSubNodeid = CollUtil.join(inheritID, "|");
                            node.setSubnodeid(resSubNodeid);
                        } else if (node.getSubnodeid().length() < 18) {
                            node.setSubnodeid(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(node.getSubnodeid()))).get(0)));
                        }
                    } else {
                        node.setSubnodeid(node.getSubnodeid());
                    }

                    // 处理 subnodeid2
                    if (StrUtil.isNotEmpty(node.getSubnodeid2())) {
                        if (node.getSubnodeid2().contains("|")) {
                            List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(node.getSubnodeid2().split("\\|"))));
                            String resSubNodeid = CollUtil.join(inheritID, "|");
                            node.setSubnodeid2(resSubNodeid);
                        } else if (node.getSubnodeid2().length() < 18) {
                            node.setSubnodeid2(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(node.getSubnodeid2()))).get(0)));
                        }
                    } else {
                        node.setSubnodeid2(node.getSubnodeid2());
                    }
                    resNodeList.add(node);
                }
                // log.info("insert datasource is {}", DynamicDataSourceContextHolder.peek());
                // nodeMMapper.mysqlInsertOrUpdateBath(resNodeList);
                this.saveOrUpdateBatch(resNodeList);
            }
        } catch (Exception e) {
            log.error("handle node inherited id error,detail is {}", e.getMessage());
            throw e;
        } finally {
            countDownLatch.countDown();
        }
    }

    /**
     * Optimized version of handleId with better memory management and performance
     */
    @Async("optimizedAsyncTaskExecutor")
    public CompletableFuture<Void> optimizedHandleId(String area, String country,
                                                     CountDownLatch countDownLatch,
                                                     List<NodeM> nodeList, ConcurrentLinkedQueue<Exception> exceptions) {
        try {
            // Log memory usage at start
            logMemoryUsage("Start of optimizedHandleId");
            
            // Set database context
            configureDatabaseContext(area, country,false);
            
            // Calculate optimal batch size with memory considerations
            int fieldNum = BeanUtil.beanToMap(new NodeM()).keySet().size();
            int maxBatchSize = 65535 / fieldNum;
            int optimizedBatchSize = Math.min(maxBatchSize, 3000); // Cap for memory management

            log.info("Processing {} nodes with optimized batch size: {}", nodeList.size(), optimizedBatchSize);
            
            List<List<NodeM>> splitList = ListUtil.split(nodeList, optimizedBatchSize);
            
            for (int batchIndex = 0; batchIndex < splitList.size(); batchIndex++) {
                List<NodeM> nodes = splitList.get(batchIndex);
                
                log.info("Processing batch {}/{} with {} nodes", batchIndex + 1, splitList.size(), nodes.size());
                
                // Process nodes in this batch with optimized logic
                List<NodeM> processedNodes = processNodeBatchOptimized(nodes);
                
                // Save processed nodes
                if (!processedNodes.isEmpty()) {
                    // this.saveOrUpdateBatch(processedNodes);
                    nodeMMapper.mysqlInsertOrUpdateBath(processedNodes);
                    log.info("Saved batch {}/{} with {} processed nodes", 
                            batchIndex + 1, splitList.size(), processedNodes.size());
                }
                
                // Cleanup after each batch
                processedNodes.clear();

                // Periodic garbage collection
                // if (batchIndex % 3 == 0 && batchIndex > 0) {
                //     System.gc();
                //     logMemoryUsage("After batch " + (batchIndex + 1));
                // }
            }
            
            log.info("Completed processing all {} batches", splitList.size());
            logMemoryUsage("End of optimizedHandleId");
            
        } catch (Exception e) {
            log.error("Error in optimized handle node inherited id, detail: {}", e.getMessage(), e);
            exceptions.offer(e);
        } finally {
            // Cleanup resources
            cleanupResources();
            countDownLatch.countDown();
        }
        
        return CompletableFuture.completedFuture(null);
    }

    /**
     * Process a batch of nodes with optimized ID inheritance logic
     */
    private List<NodeM> processNodeBatchOptimized(List<NodeM> nodes) {
        List<NodeM> processedNodes = new ArrayList<>();
        
        for (NodeM node : nodes) {
            // Skip nodes that already have 18-character main node IDs
            if (node.getMainnodeid() != null && node.getMainnodeid().length() == 18) {
                continue;
            }
            
            try {
                // Process main node ID
                processMainNodeId(node);
                
                // Process sub node ID
                processSubNodeId(node);
                
                // Process sub node ID 2
                processSubNodeId2(node);
                
                processedNodes.add(node);
                
            } catch (Exception e) {
                log.error("Error processing node {}: {}", node.getHllNodeid(), e.getMessage());
                // Continue processing other nodes
            }
        }
        
        return processedNodes;
    }

    /**
     * Process main node ID with optimized inheritance
     */
    private void processMainNodeId(NodeM node) {
        if (StrUtil.isNotEmpty(node.getMainnodeid()) && node.getMainnodeid().length() < 18) {
            List<Long> inheritID = inheritIDService.inheritID(
                new InheritIDDTO(12L, CollUtil.newArrayList(node.getMainnodeid())));
            node.setMainnodeid(String.valueOf(inheritID.get(0)));
        }
    }

    /**
     * Process sub node ID with optimized inheritance
     */
    private void processSubNodeId(NodeM node) {
        if (StrUtil.isNotEmpty(node.getSubnodeid())) {
            if (node.getSubnodeid().contains("|")) {
                List<Long> inheritID = inheritIDService.inheritID(
                    new InheritIDDTO(12L, CollUtil.newArrayList(node.getSubnodeid().split("\\|"))));
                String resSubNodeid = CollUtil.join(inheritID, "|");
                node.setSubnodeid(resSubNodeid);
            } else if (node.getSubnodeid().length() < 18) {
                List<Long> inheritID = inheritIDService.inheritID(
                    new InheritIDDTO(12L, CollUtil.newArrayList(node.getSubnodeid())));
                node.setSubnodeid(String.valueOf(inheritID.get(0)));
            }
        }
    }

    /**
     * Process sub node ID 2 with optimized inheritance
     */
    private void processSubNodeId2(NodeM node) {
        if (StrUtil.isNotEmpty(node.getSubnodeid2())) {
            if (node.getSubnodeid2().contains("|")) {
                List<Long> inheritID = inheritIDService.inheritID(
                    new InheritIDDTO(12L, CollUtil.newArrayList(node.getSubnodeid2().split("\\|"))));
                String resSubNodeid = CollUtil.join(inheritID, "|");
                node.setSubnodeid2(resSubNodeid);
            } else if (node.getSubnodeid2().length() < 18) {
                List<Long> inheritID = inheritIDService.inheritID(
                    new InheritIDDTO(12L, CollUtil.newArrayList(node.getSubnodeid2())));
                node.setSubnodeid2(String.valueOf(inheritID.get(0)));
            }
        }
    }

    /**
     * Monitor and log memory usage during processing
     */
    private void logMemoryUsage(String phase) {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        log.info("Memory Usage - {}: Used: {} MB, Free: {} MB, Total: {} MB, Max: {} MB", 
                phase,
                usedMemory / (1024 * 1024),
                freeMemory / (1024 * 1024),
                totalMemory / (1024 * 1024),
                maxMemory / (1024 * 1024));
                
        double memoryUsagePercent = (double) usedMemory / maxMemory * 100;
        log.info("Memory Usage Percentage - {}: {:.2f}%", phase, memoryUsagePercent);
        
        if (memoryUsagePercent > 80) {
            log.warn("High memory usage detected: {:.2f}% - Consider garbage collection", memoryUsagePercent);
        }
    }

    /**
     * Cleanup resources and suggest garbage collection
     */
    private void cleanupResources() {
        System.gc();
        log.debug("Resource cleanup completed and garbage collection suggested");
    }

    /**
     * Configure database context for source or target operations
     */
    private void configureDatabaseContext(String area, String country, boolean isSource) {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, isSource));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
    }
}
