package com.hll.mapdataservice.business.third;

import com.hll.mapdataservice.business.config.FeignConfig;
import com.hll.mapdataservice.business.third.dto.InheritIDDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Author: ares.chen
 * @Since: 2022/1/17
 */
@FeignClient(name="inheritIDService",configuration= FeignConfig.class,url = "${hll.client.id.url}")
public interface InheritIDService {

    @PostMapping("api/id/inherit")
    List<Long> inheritID(@RequestBody InheritIDDTO inheritIDDTO);


    @PostMapping("api/id")
    List<Long> createID(@RequestBody InheritIDDTO inheritIDDTO);
}
