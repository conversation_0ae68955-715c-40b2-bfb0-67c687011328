package com.hll.mapdataservice.business.api.road.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hll.mapdataservice.business.api.road.service.*;
import com.hll.mapdataservice.business.common.XmlFileUtils;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.third.InheritIDService;
import com.hll.mapdataservice.business.third.dto.InheritIDDTO;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.mapper.CalculationMapper;
import com.hll.mapdataservice.common.mapper.LinkMMapper;
import com.hll.mapdataservice.common.mapper.RestrictionMapper;
import com.hll.mapdataservice.common.service.ILinkService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import com.vividsolutions.jts.io.ParseException;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.MultiValueMap;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@SpringBootTest
class LinkSw2021q133ControllerTest {

    @Resource
    ILinkService linkService;

    @Test
    void hereUpdateMainSubNode() throws IOException, ParseException {

        String rdfCfFilePath = "/Users/<USER>/Downloads/rdf_cf.txt";
        String rdfCfNodeFilePath = "/Users/<USER>/Downloads/rdf_cf_node.txt";
        Map<String, String> rdfCfMap = new XmlFileUtils().rdfCfFileRead(rdfCfFilePath);
        System.out.println("read readRdfCf finished,size is:" + rdfCfMap.size());
        MultiValueMap<String, String> rdfCfNodeMap = new XmlFileUtils().rdfCfLinkFileRead(rdfCfNodeFilePath);
        System.out.println("read rdfCfNode finished,size is:" + rdfCfNodeMap.size());
        List<NodeSw2021q133> nodeSw2021q133List = new ArrayList<>();
        NodeSw2021q133 nodeSw2021q133 = new NodeSw2021q133();
        List<String> nodeIdlist = rdfCfNodeMap.get("711589826");
        if (nodeIdlist != null) {
            for (String nodeId : nodeIdlist
            ) {
                nodeSw2021q133.setNodeId(nodeId);
                nodeSw2021q133.setType("2");
                nodeSw2021q133List.add(nodeSw2021q133);
            }
        }
    }

    @Resource
    LinkMMapper linkMMapper;

    @Test
    void convert2rp() {
        MybatisPlusConfig.myTableName.set("");
        // linkRpService.convert2rp();
    }

    @Resource
    CalculationMapper calculationMapper;

    @Test
    void diffMerge() {
        String line1 = "MULTILINESTRING((114.06776 22.45579,114.06775 22.45584,114.06778 22.45593))";
        String line2 = "MULTILINESTRING((114.14739 22.49258,114.14744 22.49261))";
        double distance = calculationMapper.calLineDistance(line1, line2);
        System.out.println(distance);
    }

    @Test
    void calAngle() {
        double x1 = 109.307899;
        double y1 = 13.0860319;
        double x2 = 109.306661;
        double y2 = 13.0901286;
        System.out.println(calculationMapper.calAngle(x1, y1, x2, y2));
    }

    @Resource
    LinkMServiceImpl linkMService;

    @Test
    void spatialSearch() {
        String country = "hkg";
        String area = "";
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        String coor = "MULTILINESTRING((114.12726 22.27449,114.12743 22.2745,114.1276 22.27452,114.12776 22.27455,114.12794 22.2746,114.12808 22.27465))";
        String lastSql = "where st_contains(st_buffer(st_geomfromtext('" + coor + "',4326), 0.0002, 'endcap=round join=round'), geom)";
        List<LinkM> linkList = linkMService.lambdaQuery().last(lastSql).list();
        System.out.println(linkList);
    }

    @Resource
    PointAddressServiceImpl pointAddressService;

    @Resource
    PntAddrTransServiceImpl pntAddrTransService;

    @Test
    void selectPointAddress() {
        String country = "vnm";
        String area = "";
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
//        PointAddress byId = pointAddressService.getById(1);
        PntAddrTrans byId = pntAddrTransService.getById(1);
        System.out.println("查询结果" + byId);
    }

    @Resource
    HnPointAddressServiceImpl hnPointAddressService;

    @Test
    void selectHnPointAddress() {
        String country = "phl";
        String area = "";
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        List<HnPointAddress> list = hnPointAddressService.list();
        for (HnPointAddress hnPointAddress : list) {
            System.out.println("查询结果" + hnPointAddress);
        }

    }

    @Resource
    InheritIDService inheritIDService;

    @Test
    void getids() {
//        List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList("3448lxx5-c5ccb619b0110e09d8679b978060bd8a")));
        List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList("1090232801")));
        System.out.println(inheritID.get(0).toString());
    }

    @Resource
    RestrictionMapper restrictionMapper;

    @Test
    public void testSave() {

        String country = "phl";
        String area = "";
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }

        List<RestrictionVO> restrictionVOS = new ArrayList<>();
        RestrictionVO restrictionVO1 = new RestrictionVO();
        restrictionVO1.setMarket("Philippines");
        restrictionVO1.setCity("Manila");
        restrictionVO1.setCityId("51001");
        restrictionVO1.setRestrictedRoadName("EDSA");
        restrictionVO1.setRestrictedTime("6:00am~10:00am; 5:00pm ~10:00pm");
        restrictionVO1.setRestrictedServiceType("6W Long Distance");
        restrictionVO1.setRoadLine("120.9924231 14.53721, 120.9918427 14.5414527, 120.991069 14.5458199, 120.9895861 14.5519841, 120.9889537 14.553995, 120.987056 14.5594809, 120.9830353 14.5687235, 120.9793817 14.5760404, 120.9778684 14.5786185, 120.9740798 14.585353, 120.972865 14.5874366, 120.9709074 14.5904928, 120.971111 14.5906427, 120.9713678 14.5901534, 120.9724769 14.5883857, 120.97462 14.5848503, 120.9755599 14.5830604, 120.977077 14.5804322, 120.9794319 14.5761802, 120.9823449 14.570495, 120.9850163 14.564166, 120.9864513 14.5612117, 120.9871822 14.5595814, 120.9877093 14.5582756, 120.9896968 14.5518072, 120.9913839 14.5449234, 120.9922556 14.5398034, 120.992902 14.5344343, 120.9935028 14.5287532, 120.9936369 14.5239654, 120.9933043 14.5186477");
        restrictionVO1.setParams("6am;5000kg");
        restrictionVO1.setWhiteListWeekday("monday");
        restrictionVO1.setWhiteListDay("01-01");

        restrictionVOS.add(restrictionVO1);

        List<Restriction> restrictions = new ArrayList<>();
        for (RestrictionVO restrictionVO : restrictionVOS) {
            Restriction restriction = new Restriction();
            restriction.setId(UUID.randomUUID().toString());
            // restriction.setGroupId("");
            restriction.setMarket(restrictionVO.getMarket());
            restriction.setCity(restrictionVO.getCity());
            restriction.setCityId(restrictionVO.getCityId());
            restriction.setLinkId(restrictionVO.getHereLinkId());
            restriction.setRoadNameEn(restrictionVO.getRestrictedRoadName());
            restriction.setRoadNameMultiLang(restrictionVO.getRoadNameMultiLang());
            restriction.setGeom("SRID=4326;" + StrUtil.wrap(restrictionVO.getRoadLine(), "LINESTRING(", ")"));
            restriction.setRestrictedOrderVehicleId(restrictionVO.getRestrictedOrderVehicleId());

            RestrictedInfo restrictedInfo = new RestrictedInfo();

            // List<RestrictTime> restrictTimeList = new ArrayList<>();
            // String restrictedTime = restrictionVO.getRestrictedTime();
            // if (StrUtil.isNotEmpty(restrictedTime)) {
            //     if (restrictedTime.contains(";")) {
            //         String[] splitTime = restrictedTime.split(";");
            //         String[] splitFirst = splitTime[0].split("~");
            //         String[] splitSecond = splitTime[1].split("~");
            //         restrictTimeList.add(new RestrictTime(StrUtil.trim(splitFirst[0]), StrUtil.trim(splitFirst[1])));
            //         restrictTimeList.add(new RestrictTime(StrUtil.trim(splitSecond[0]), StrUtil.trim(splitSecond[1])));
            //     } else {
            //         String[] splitTime = restrictedTime.split("~");
            //         restrictTimeList.add(new RestrictTime(StrUtil.trim(splitTime[0]), StrUtil.trim(splitTime[1])));
            //     }
            // }
            // restrictedInfo.setRestrict_time(restrictTimeList);
            restrictedInfo.setRestrict_time(restrictionVO.getRestrictedTime());

            restrictedInfo.setWhite_list_weekday(restrictionVO.getWhiteListWeekday());
            restrictedInfo.setWhite_list_day(restrictionVO.getWhiteListDay());
            restrictedInfo.setRestricted_service_type(restrictionVO.getRestrictedServiceType());
            if (StrUtil.isNotEmpty(restrictionVO.getParams())) {
                String[] splitParam = restrictionVO.getParams().split(";");
                restrictedInfo.setParam(new Param(StrUtil.trim(splitParam[0]), StrUtil.trim(splitParam[1])));
            }

            restriction.setRestrictedInfo(JSONUtil.toJsonStr(restrictedInfo));
            restrictions.add(restriction);
        }

        // 3.数据入库
        restrictionMapper.mysqlInsertOrUpdateBath(restrictions);
    }

    @Test
    public void testSearch() {
        String country = "phl";
        String area = "";
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }

        Link firstLink = linkService.lambdaQuery().eq(Link::getId, "1183595997").one();
        // List<Link> list = linkRpService.lambdaQuery().eq(Link::getHllSNid, firstLink.getHllENid()).or().eq(Link::getHllENid, firstLink.getHllENid())
        //         .and(s -> s.ne(Link::getId, firstLink.getId())).list();

        List<Link> list = linkService.lambdaQuery().ne(Link::getId, firstLink.getId()).and(s -> s.eq(Link::getHllSNid, firstLink.getHllENid()).or().eq(Link::getHllENid, firstLink.getHllENid())).list();
        System.out.println(list);
    }

    @Test
    public void testSearch2() {
        String country = "phl";
        String area = "";
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        ArrayList<String> passLinkList = CollUtil.newArrayList("1175051130", "829955930", "829955932");
        QueryWrapper<LinkM> passLinkQueryWrapper = new QueryWrapper<>();
        passLinkQueryWrapper.in("link_id", passLinkList);
        // passLinkQueryWrapper.last("order by position(link_id in '" + String.join(",", passLinkList) + "')");
        passLinkQueryWrapper.last("order by array_position(ARRAY" + passLinkList.toArray() + "::VARCHAR[],link_id)");

        List<String> hllLinkIds = linkMMapper.selectList(passLinkQueryWrapper).stream().map(LinkM::getHllLinkid).collect(Collectors.toList());
        System.out.println(hllLinkIds);
    }

    @Test
    public void testsearch() {
        String country = "phl";
        String area = "";
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        ArrayList<String> passLinkList = CollUtil.newArrayList("1175051130", "829955930", "829955932");
        QueryWrapper<LinkM> passLinkQueryWrapper = new QueryWrapper<>();
        passLinkQueryWrapper.in("link_id", passLinkList);
        // passLinkQueryWrapper.last("order by position(link_id in '" + String.join(",", passLinkList) + "')");
        passLinkQueryWrapper.last("order by array_position(ARRAY[ " + String.join(",", passLinkList) + "]::VARCHAR[],link_id)");
        List<String> hllLinkIds = linkMMapper.selectList(passLinkQueryWrapper).stream().map(LinkM::getHllLinkid).collect(Collectors.toList());
        System.out.println(hllLinkIds);
    }

    @Resource
    RdmsServiceImpl rdmsService;
    @Resource
    CdmsServiceImpl cdmsService;

    @Test
    public void testPreloadCache() {
        DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry("mys", true));

        MybatisPlusConfig.myTableName.set("");
        List<Cdms> cdmsList = cdmsService.lambdaQuery().last("limit 200").list();

        preloadRdmsData(cdmsList);
    }


    private Map<Integer, List<Rdms>> preloadRdmsData(List<Cdms> cdmsList) {
        Map<Integer, List<Rdms>> rdmsCache = new HashMap<>();

        // Extract unique condition IDs
        Set<Integer> condIds = cdmsList.stream()
                .map(Cdms::getCondId)
                .collect(Collectors.toSet());

        if (!condIds.isEmpty()) {
            List<Rdms> allRdmsList = new ArrayList<>();
            if (condIds.size() > 65535) {
                CollUtil.split(condIds, 65535).forEach(condId -> {
                    allRdmsList.addAll(rdmsService.lambdaQuery()
                            .in(Rdms::getCondId, condId)
                            .list());
                });
            } else {
                allRdmsList.addAll(rdmsService.lambdaQuery()
                        .in(Rdms::getCondId, condIds)
                        .list());
            }
            // Batch query all RDMS data
            // List<Rdms> allRdms = rdmsService.lambdaQuery()
            //         .in(Rdms::getCondId, condIds)
            //         .list();

            // Group by condition ID for fast lookup
            rdmsCache = allRdmsList.stream()
                    .collect(Collectors.groupingBy(Rdms::getCondId));
        }

        return rdmsCache;
    }


    private void configureDatabaseContext(String area, String country, boolean isSource) {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, isSource));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
    }
}