// package com.hll.mapdataservice.business.api.common.service;
//
// import com.hll.mapdataservice.common.entity.DataMergingRequest;
// import com.hll.mapdataservice.common.entity.DataMergingResponse;
// import com.hll.mapdataservice.business.api.common.service.DataMergingServiceImpl;
// import com.hll.mapdataservice.common.mapper.DataMergingMapper;
// import org.junit.jupiter.api.BeforeEach;
// import org.junit.jupiter.api.Test;
// import org.mockito.InjectMocks;
// import org.mockito.Mock;
// import org.mockito.MockitoAnnotations;
//
// import java.util.List;
//
// import static org.junit.jupiter.api.Assertions.*;
// import static org.mockito.ArgumentMatchers.anyString;
// import static org.mockito.Mockito.when;
//
// /**
//  * Unit tests for DataMergingService
//  */
// public class DataMergingServiceTest {
//
//     @Mock
//     private DataMergingMapper dataMergingMapper;
//
//     @InjectMocks
//     private DataMergingServiceImpl dataMergingService;
//
//     @BeforeEach
//     void setUp() {
//         MockitoAnnotations.openMocks(this);
//     }
//
//     @Test
//     void testIsCountrySupported_ValidCountry() {
//         assertTrue(dataMergingService.isCountrySupported("twn"));
//         assertTrue(dataMergingService.isCountrySupported("bra"));
//         assertTrue(dataMergingService.isCountrySupported("mex"));
//     }
//
//     @Test
//     void testIsCountrySupported_InvalidCountry() {
//         assertFalse(dataMergingService.isCountrySupported("xyz"));
//         assertFalse(dataMergingService.isCountrySupported("invalid"));
//     }
//
//     @Test
//     void testGetCountryAreas_Taiwan() {
//         List<String> areas = dataMergingService.getCountryAreas("twn");
//         assertNotNull(areas);
//         assertEquals(3, areas.size());
//         assertTrue(areas.contains("area1"));
//         assertTrue(areas.contains("area2"));
//         assertTrue(areas.contains("area3"));
//     }
//
//     @Test
//     void testGetCountryAreas_Brazil() {
//         List<String> areas = dataMergingService.getCountryAreas("bra");
//         assertNotNull(areas);
//         assertEquals(5, areas.size());
//         assertTrue(areas.contains("area1"));
//         assertTrue(areas.contains("area5"));
//     }
//
//     @Test
//     void testMergePartitionData_UnsupportedCountry() {
//         DataMergingRequest request = new DataMergingRequest();
//         request.setCountryPrefix("xyz");
//
//         DataMergingResponse response = dataMergingService.mergePartitionData(request);
//
//         assertFalse(response.isSuccess());
//         assertTrue(response.getErrors().contains("Unsupported country prefix: xyz"));
//     }
//
//     @Test
//     void testMergePartitionData_DryRun() {
//         // Mock table existence checks
//         when(dataMergingMapper.checkTableExists(anyString())).thenReturn(1);
//         when(dataMergingMapper.getTableRecordCount(anyString())).thenReturn(1000L);
//
//         DataMergingRequest request = new DataMergingRequest();
//         request.setCountryPrefix("twn");
//         request.setDryRun(true);
//
//         DataMergingResponse response = dataMergingService.mergePartitionData(request);
//
//         assertEquals("twn", response.getCountryPrefix());
//         assertEquals(3, response.getPartitionCount());
//         assertTrue(response.isDryRun());
//         assertNotNull(response.getStartTime());
//         assertNotNull(response.getEndTime());
//     }
//
//     @Test
//     void testTableTypes() {
//         // Verify that all expected table types are processed
//         DataMergingRequest request = new DataMergingRequest();
//         request.setCountryPrefix("hkg"); // Single area country for simpler testing
//         request.setDryRun(true);
//
//         // Mock successful table checks
//         when(dataMergingMapper.checkTableExists(anyString())).thenReturn(1);
//         when(dataMergingMapper.getTableRecordCount(anyString())).thenReturn(100L);
//
//         DataMergingResponse response = dataMergingService.mergePartitionData(request);
//
//         // Should process 8 table types: link_m, node_m, relation_m, rule_m, link, node, relation, rule
//         assertEquals(8, response.getTotalMergeOperations());
//         assertTrue(response.getTableResults().containsKey("link_m"));
//         assertTrue(response.getTableResults().containsKey("node_m"));
//         assertTrue(response.getTableResults().containsKey("relation_m"));
//         assertTrue(response.getTableResults().containsKey("rule_m"));
//         assertTrue(response.getTableResults().containsKey("link"));
//         assertTrue(response.getTableResults().containsKey("node"));
//         assertTrue(response.getTableResults().containsKey("relation"));
//         assertTrue(response.getTableResults().containsKey("rule"));
//     }
// }
