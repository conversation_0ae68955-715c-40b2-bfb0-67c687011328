package com.hll.mapdataservice.business.third;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
@SpringBootTest
class MiddlePlatformServiceTest {

    @Resource
    MiddlePlatformService middlePlatformService;

    @Test
    void getGoogleAc() {
        Map<String, Object> params = new HashMap<>();
//        params.put("mapType", "google");
//        params.put("apiName", "google_ac");
//        params.put("appId", "global_algo");
//        params.put("otherParam", "async=false");
//        params.put("tid", "test_111");
//        params.put("sign", "1");
//        params.put("input", "hotel");
//        params.put("location", "22.310202,114.224335");
//        params.put("components", "country:hk");
//        params.put("language", "en");
//        params.put("radius", "50000");
        String googleAcResult =  middlePlatformService.getGoogleAc("input=hotel&location=22.310202,114.224335&components=country:hk&language=en&radius=50000");
        System.out.println("googleAcResult:" + googleAcResult);
        String hereAsResult = middlePlatformService.getHereAs("q=Jalan Dharmawangsa Raya No.31, Pulo, South Jakarta City, Jakarta, Indonesia&lang=en&in=circle:-6.175110,106.865040;r%3D50000");
        System.out.println("hereAsResult:" + hereAsResult);

    }


}