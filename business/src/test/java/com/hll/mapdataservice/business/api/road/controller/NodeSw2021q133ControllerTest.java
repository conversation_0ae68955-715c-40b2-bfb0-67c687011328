package com.hll.mapdataservice.business.api.road.controller;

import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.service.INodeMService;
import com.hll.mapdataservice.common.service.INodeService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <p>
 *
 * </p>
 *
 * @Author: ares.chen
 * @Since: 2021/8/17
 */
@SpringBootTest
class NodeSw2021q133ControllerTest {

    @Resource
    INodeService nodeService;
    @Resource
    INodeMService nodeMService;

    @Test
    void convert2rp() {
        MybatisPlusConfig.myTableName.set("");
        //nodeRpService.convert2rp();
    }
}